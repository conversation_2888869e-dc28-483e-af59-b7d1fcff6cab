require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function checkRecentData() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true,
        requestTimeout: 60000 // Increase timeout to 60 seconds
      }
    });

    // Check if there's data for June 25, 2025
    const todayData = await sql.query(`
      SELECT TOP 5 * 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE CONVERT(date, ExitDateTime) = '2025-06-25'
      ORDER BY ExitDateTime DESC
    `);
    
    console.log('Data for June 25, 2025:');
    console.log(JSON.stringify(todayData.recordset, null, 2));
    
    // Count records for June 25, 2025
    const todayCount = await sql.query(`
      SELECT COUNT(*) as Count
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE CONVERT(date, ExitDateTime) = '2025-06-25'
    `);
    
    console.log(`\nTotal records for June 25, 2025: ${todayCount.recordset[0].Count}`);

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkRecentData();