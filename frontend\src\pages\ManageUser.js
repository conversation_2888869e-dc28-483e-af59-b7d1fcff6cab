// frontend/src/pages/ManageUser.js
import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { userApi } from '../api/userApi';
import UserList from '../components/User/UserList';
import UserDialog from '../components/User/UserDialog';
import { useAuth } from '../contexts/authContext';
import usePermissionFilter from '../hooks/usePermissionFilter';
import { PermissionButton } from '../components/auth/PermissionButton';
import { useToast } from '../hooks/useToast';

export default function ManageUser() {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const toast = useToast();
  const [, setSelectedUser] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState(null);

  // Pagination state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Sorting state
  const [sortConfig, setSortConfig] = useState({ key: 'Username', direction: 'asc' });

  // Filter state
  const [filters, setFilters] = useState({});

  // Get users data
  const { data, isLoading: usersLoading } = useQuery({
    queryKey: ['users', filters, sortConfig],
    queryFn: () => userApi.getUsers(filters),
  });

  // Make sure we have the correct data structure
  const rawUsers = Array.isArray(data) ? data :
                  (data?.data ? data.data :
                  (data?.users ? data.users : []));

  // Apply permission filtering to users data
  const { filteredData: users } = usePermissionFilter(
    rawUsers,
    { companyIdField: 'CompanyId' }
  );

  const totalUsers = users.length;

  const createMutation = useMutation({
    mutationFn: userApi.createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.showCrudSuccess('create', 'User');
      setDialogOpen(false);
    },
    onError: (error) => {
      console.error('Error creating user:', error);
      toast.showCrudError('create', 'User', error.response?.data?.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: (userData) => userApi.updateUser(userData.id, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.showCrudSuccess('update', 'User');
      setDialogOpen(false);
      setEditingUser(null);
    },
    onError: (error) => {
      console.error('Error updating user:', error);
      toast.showCrudError('update', 'User', error.response?.data?.message);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: userApi.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      toast.showCrudSuccess('delete', 'User');
    },
    onError: (error) => {
      console.error('Error deleting user:', error);
      toast.showCrudError('delete', 'User', error.response?.data?.message);
    },
  });

  // Handle opening the dialog for creating a new user
  const handleAddUser = () => {
    setEditingUser(null);
    setDialogOpen(true);
  };

  // Handle opening the dialog for editing a user
  const handleEditUser = (user) => {
    setEditingUser(user);
    setDialogOpen(true);
  };

  // Handle deleting a user
  const handleDeleteUser = (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      deleteMutation.mutate(userId);
    }
  };

  // Handle dialog submission
  const handleDialogSubmit = (userData) => {
    if (editingUser) {
      updateMutation.mutate(userData);
    } else {
      createMutation.mutate(userData);
    }
  };

  // Handle sorting
  const handleSort = (newSortConfig) => {
    setSortConfig(newSortConfig);
  };

  // Handle filtering
  const handleFilter = (newFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  };

  // Handle clearing filters
  const handleClearFilters = () => {
    setFilters({});
  };

  // Handle page change
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1); // Reset to first page when page size changes
  };

  // Calculate paginated data
  const paginatedUsers = Array.isArray(users) ?
    users.slice((page - 1) * pageSize, page * pageSize) : [];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
          {user && (
            <PermissionButton
              requiredModule="Users"
              requiredPermissions={["Create"]}
              onClick={handleAddUser}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="w-5 h-5 mr-2" />
              Add User
            </PermissionButton>
          )}
        </div>

        <div className="bg-white shadow rounded-lg">
          <UserList
            users={paginatedUsers}
            totalUsers={totalUsers}
            onEdit={handleEditUser}
            onDelete={handleDeleteUser}
            onSelect={setSelectedUser}
            loading={usersLoading}
            page={page}
            pageSize={pageSize}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            sortConfig={sortConfig}
            onSort={handleSort}
            filters={filters}
            onFilter={handleFilter}
            onClearFilters={handleClearFilters}
          />
        </div>

        {dialogOpen && (
          <UserDialog
            isOpen={dialogOpen}
            onClose={() => {
              setDialogOpen(false);
              setEditingUser(null);
            }}
            onSubmit={handleDialogSubmit}
            initialData={editingUser}
            title={editingUser ? 'Edit User' : 'Create User'}
          />
        )}
      </div>
    </div>
  );
}
