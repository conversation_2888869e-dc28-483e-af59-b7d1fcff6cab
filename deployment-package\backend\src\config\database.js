// backend/src/config/database.js - MODIFIED VERSION

const sql = require('mssql');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    enableArithAbort: true,
    connectionTimeout: 30000,
    requestTimeout: 30000,
    // Explicitly disable DAC usage
    admin: false
  },
  pool: {
    max: 10,         // Increased from default
    min: 0,          // Start with no connections
    idleTimeoutMillis: 30000  // Close idle connections after 30 seconds
  }
};

// Create a connection pool
let pool = null;

// Initialize the connection pool
const initializePool = async () => {
  try {
    if (pool) {
      console.log('Closing existing connection pool');
      try {
        await pool.close();
        console.log('Existing connection pool closed successfully');
      } catch (closeError) {
        console.error('Error closing existing pool (continuing anyway):', closeError.message);
        // Continue even if close fails
      }
    }
    
    console.log('Creating new connection pool with config:', {
      server: dbConfig.server,
      database: dbConfig.database,
      port: dbConfig.port,
      user: dbConfig.user,
      // Not logging password for security
      options: {
        ...dbConfig.options,
        // Not showing full options for brevity
      }
    });
    
    pool = await sql.connect(dbConfig);
    console.log('Database connection pool established');
    
    // Get database info with a simple query to test connection
    const result = await pool.request().query(`
      SELECT 
        DB_NAME() as DatabaseName,
        @@CONNECTIONS as TotalConnections,
        @@MAX_CONNECTIONS as MaxConnections
    `);
    
    if (result.recordset.length > 0) {
      const { DatabaseName, TotalConnections, MaxConnections } = result.recordset[0];
      console.log(`Connected to database: ${DatabaseName}`);
      console.log(`Total connections: ${TotalConnections}`);
      console.log(`Max connections: ${MaxConnections}`);
      console.log('Database connected successfully');
    }
    
    return pool;
  } catch (error) {
    console.error('SQL Server connection pool error:', error);
    throw error;
  }
};

// Execute a query with retry logic
const query = async (queryText, params = {}, maxRetries = 3) => {
  if (!pool) {
    await initializePool();
  }
  
  let retries = maxRetries;
  
  while (retries >= 0) {
    try {
      const request = pool.request();
      
      // Add parameters to the request
      Object.entries(params).forEach(([key, value]) => {
        request.input(key, value);
      });
      
      // Execute the query
      const result = await request.query(queryText);
      return result;
    } catch (error) {
      console.error('Query execution error:', error.message);
      
      // Log the specific error type for debugging
      console.log(`Database error type: ${error.code}, message: ${error.message}`);
      
      // No need for special DAC handling as we've configured to never use DAC
      
      // If we have retries left, try again
      if (retries > 0) {
        console.log(`Retrying query, ${retries} attempts left`);
        retries--;
        
        // If connection pool error, reinitialize the pool
        if (error.code === 'ELOGIN' || error.code === 'ESOCKET') {
          console.log('Closed existing connection pool');
          if (pool) {
            try {
              await pool.close();
            } catch (closeError) {
              console.error('Error closing pool:', closeError.message);
            }
          }
          await initializePool();
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        throw error; // No more retries, throw the error
      }
    }
  }
};

// Close the connection pool
const closePool = async () => {
  if (pool) {
    try {
      await pool.close();
      pool = null;
      console.log('Database connection pool closed');
    } catch (error) {
      console.error('Error closing database connection pool:', error);
      throw error;
    }
  }
};

// Initialize the pool when the module is imported
initializePool().catch(error => {
  console.error('Failed to initialize database connection pool:', error);
  process.exit(1); // Exit the process if initial connection fails
});

// Handle process termination
process.on('SIGINT', async () => {
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closePool();
  process.exit(0);
});

module.exports = {
  query,
  closePool,
  sql
};
