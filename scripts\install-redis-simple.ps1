# Simple Redis installation script for PWVMS
# Run this script as Administrator

Write-Host "Installing Redis for PWVMS..." -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if Chocolatey is installed
if (-not (Test-Command choco)) {
    Write-Host "Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Refresh environment variables
    refreshenv
}

# Install Redis using Chocolatey
Write-Host "Installing Redis..." -ForegroundColor Yellow
choco install redis-64 -y

# Wait for installation to complete
Start-Sleep -Seconds 5

# Check if Redis was installed successfully
if (Test-Command redis-server) {
    Write-Host "Redis installed successfully!" -ForegroundColor Green
} else {
    Write-Host "Redis installation failed!" -ForegroundColor Red
    exit 1
}

# Start Redis service
Write-Host "Starting Redis service..." -ForegroundColor Yellow
try {
    Start-Service -Name "Redis" -ErrorAction SilentlyContinue
    Write-Host "Redis service started successfully!" -ForegroundColor Green
} catch {
    Write-Host "Starting Redis manually..." -ForegroundColor Yellow
    Start-Process -FilePath "redis-server" -WindowStyle Hidden
}

# Test Redis connection
Write-Host "Testing Redis connection..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

try {
    $testResult = redis-cli ping
    if ($testResult -eq "PONG") {
        Write-Host "Redis is running and responding to commands!" -ForegroundColor Green
    } else {
        Write-Host "Redis may not be responding correctly." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Could not test Redis connection. Please verify manually." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Redis setup completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "1. Install Node.js Redis dependencies in your backend folder" -ForegroundColor White
Write-Host "2. Restart your PWVMS backend server" -ForegroundColor White
Write-Host "3. Check the health-check endpoint to verify Redis connection" -ForegroundColor White
Write-Host ""
Write-Host "Redis Management Commands:" -ForegroundColor Cyan
Write-Host "- Test connection: redis-cli ping" -ForegroundColor White
Write-Host "- Redis CLI: redis-cli" -ForegroundColor White
Write-Host ""