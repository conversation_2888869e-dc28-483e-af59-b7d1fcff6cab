// <PERSON>ript to add Security module and permissions
require('dotenv').config({ path: './backend/.env' });
const sql = require('mssql');

async function addSecurityModule() {
  console.log('Starting to add Security module...');
  
  try {
    // Connect to the database
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    
    console.log('Connected to database successfully.');
    
    // Check if Security module exists
    const moduleCheck = await sql.query("SELECT * FROM Modules WHERE Name = 'Security'");
    let moduleId;
    
    if (moduleCheck.recordset.length === 0) {
      console.log('Creating Security module...');
      const moduleInsert = await sql.query(`
        INSERT INTO Modules (Name, Description, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES ('Security', 'Security settings and permissions', 'security', 10, 1, 1, GETDATE());
        SELECT SCOPE_IDENTITY() AS Id;
      `);
      moduleId = moduleInsert.recordset[0].Id;
      console.log(`Security module created with ID: ${moduleId}`);
    } else {
      moduleId = moduleCheck.recordset[0].Id;
      console.log(`Security module already exists with ID: ${moduleId}`);
    }
    
    // Check if Security submodule exists
    const submoduleCheck = await sql.query("SELECT * FROM SubModules WHERE ModuleId = @moduleId AND Name = 'Security'", {
      moduleId: moduleId
    });
    let submoduleId;
    
    if (submoduleCheck.recordset.length === 0) {
      console.log('Creating Security submodule...');
      const submoduleInsert = await sql.query(`
        INSERT INTO SubModules (ModuleId, Name, Icon, Path, IsActive, CreatedBy, CreatedOn)
        VALUES (@moduleId, 'Security', 'security', '/security', 1, 1, GETDATE());
        SELECT SCOPE_IDENTITY() AS Id;
      `, {
        moduleId: moduleId
      });
      submoduleId = submoduleInsert.recordset[0].Id;
      console.log(`Security submodule created with ID: ${submoduleId}`);
    } else {
      submoduleId = submoduleCheck.recordset[0].Id;
      console.log(`Security submodule already exists with ID: ${submoduleId}`);
    }
    
    // Get View permission ID
    const permissionCheck = await sql.query("SELECT * FROM Permissions WHERE Name = 'View'");
    const permissionId = permissionCheck.recordset[0].Id;
    console.log(`View permission ID: ${permissionId}`);
    
    // Check if SubModulePermission exists
    const submodulePermissionCheck = await sql.query(`
      SELECT * FROM SubModulePermissions 
      WHERE SubModuleId = @submoduleId AND PermissionId = @permissionId
    `, {
      submoduleId: submoduleId,
      permissionId: permissionId
    });
    let submodulePermissionId;
    
    if (submodulePermissionCheck.recordset.length === 0) {
      console.log('Creating SubModulePermission...');
      const submodulePermissionInsert = await sql.query(`
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@submoduleId, @permissionId, 1, 1, GETDATE());
        SELECT SCOPE_IDENTITY() AS Id;
      `, {
        submoduleId: submoduleId,
        permissionId: permissionId
      });
      submodulePermissionId = submodulePermissionInsert.recordset[0].Id;
      console.log(`SubModulePermission created with ID: ${submodulePermissionId}`);
    } else {
      submodulePermissionId = submodulePermissionCheck.recordset[0].Id;
      console.log(`SubModulePermission already exists with ID: ${submodulePermissionId}`);
    }
    
    // Get role IDs
    const rolesCheck = await sql.query("SELECT * FROM Roles WHERE Name IN ('SuperAdmin', 'CompanyAdmin')");
    const superAdminRoleId = rolesCheck.recordset.find(r => r.Name === 'SuperAdmin').Id;
    const companyAdminRoleId = rolesCheck.recordset.find(r => r.Name === 'CompanyAdmin').Id;
    console.log(`SuperAdmin role ID: ${superAdminRoleId}`);
    console.log(`CompanyAdmin role ID: ${companyAdminRoleId}`);
    
    // Check if SuperAdmin has permission
    const superAdminPermissionCheck = await sql.query(`
      SELECT * FROM RolePermissions 
      WHERE RoleId = @roleId AND SubModulePermissionId = @submodulePermissionId
    `, {
      roleId: superAdminRoleId,
      submodulePermissionId: submodulePermissionId
    });
    
    if (superAdminPermissionCheck.recordset.length === 0) {
      console.log('Granting permission to SuperAdmin...');
      await sql.query(`
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@roleId, @submodulePermissionId, 1, 1, GETDATE())
      `, {
        roleId: superAdminRoleId,
        submodulePermissionId: submodulePermissionId
      });
      console.log('Permission granted to SuperAdmin');
    } else {
      console.log('SuperAdmin already has the permission');
    }
    
    // Check if CompanyAdmin has permission
    const companyAdminPermissionCheck = await sql.query(`
      SELECT * FROM RolePermissions 
      WHERE RoleId = @roleId AND SubModulePermissionId = @submodulePermissionId
    `, {
      roleId: companyAdminRoleId,
      submodulePermissionId: submodulePermissionId
    });
    
    if (companyAdminPermissionCheck.recordset.length === 0) {
      console.log('Granting permission to CompanyAdmin...');
      await sql.query(`
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@roleId, @submodulePermissionId, 1, 1, GETDATE())
      `, {
        roleId: companyAdminRoleId,
        submodulePermissionId: submodulePermissionId
      });
      console.log('Permission granted to CompanyAdmin');
    } else {
      console.log('CompanyAdmin already has the permission');
    }
    
    console.log('Security module and permissions added successfully!');
    
    await sql.close();
    console.log('Database connection closed.');
    
  } catch (err) {
    console.error('Error adding Security module:', err);
    if (sql.connected) {
      await sql.close();
      console.log('Database connection closed due to error.');
    }
    process.exit(1);
  }
}

addSecurityModule().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});