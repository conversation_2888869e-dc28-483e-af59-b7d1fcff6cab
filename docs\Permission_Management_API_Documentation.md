# Permission Management API Documentation

## Overview
This document provides detailed API documentation for the Permission Management system in ParkwizOps. All endpoints require SuperAdmin authentication.

## Base URL
```
http://localhost:5000/api/permission-management
```

## Authentication
All endpoints require JWT authentication with SuperAdmin role.

```http
Authorization: Bearer <jwt_token>
```

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "message": "Error description",
  "error": "Error type"
}
```

### Common HTTP Status Codes
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden (Non-SuperAdmin access)
- `404` - Not Found
- `500` - Internal Server Error

---

## Endpoints

### 1. Get Modules Tree

Retrieves the complete hierarchy of modules, submodules, and their associated permissions.

```http
GET /modules-tree
```

#### Response
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Dashboard",
      "description": "Dashboard module for system overview",
      "icon": "LayoutDashboard",
      "displayOrder": 1,
      "isActive": true,
      "subModules": [
        {
          "id": 1,
          "name": "Overview",
          "description": "Dashboard overview page",
          "route": "/dashboard",
          "icon": "BarChart",
          "displayOrder": 1,
          "isActive": true,
          "permissions": [
            {
              "id": 1,
              "name": "View",
              "description": "View dashboard data",
              "subModulePermissionId": 1
            },
            {
              "id": 2,
              "name": "Create",
              "description": "Create dashboard items",
              "subModulePermissionId": 2
            }
          ]
        }
      ]
    }
  ],
  "message": "Modules tree retrieved successfully"
}
```

#### Error Responses
```json
{
  "success": false,
  "message": "Access denied. Only SuperAdmin can manage permissions."
}
```

---

### 2. Get All Roles

Retrieves all active roles in the system.

```http
GET /roles
```

#### Response
```json
{
  "success": true,
  "data": [
    {
      "Id": 1,
      "Name": "SuperAdmin",
      "IsActive": true,
      "CreatedOn": "2024-01-01T00:00:00.000Z"
    },
    {
      "Id": 2,
      "Name": "CompanyAdmin",
      "IsActive": true,
      "CreatedOn": "2024-01-01T00:00:00.000Z"
    },
    {
      "Id": 3,
      "Name": "PlazaManager",
      "IsActive": true,
      "CreatedOn": "2024-01-01T00:00:00.000Z"
    }
  ],
  "message": "Roles retrieved successfully"
}
```

---

### 3. Get Role Permissions

Retrieves all permissions assigned to a specific role.

```http
GET /roles/{roleId}/permissions
```

#### Parameters
- `roleId` (integer, required) - The ID of the role

#### Response
```json
{
  "success": true,
  "data": [
    {
      "RolePermissionId": 1,
      "RoleId": 2,
      "SubModulePermissionId": 1,
      "RolePermissionIsActive": true,
      "SubModuleId": 1,
      "PermissionId": 1,
      "SubModuleName": "Overview",
      "ModuleId": 1,
      "ModuleName": "Dashboard",
      "PermissionName": "View"
    }
  ],
  "message": "Role permissions retrieved successfully"
}
```

#### Error Responses
```json
{
  "success": false,
  "message": "Role not found"
}
```

---

### 4. Update Role Permissions

Updates permissions for a specific role. This endpoint supports both adding new permissions and modifying existing ones.

```http
PUT /roles/{roleId}/permissions
```

#### Parameters
- `roleId` (integer, required) - The ID of the role

#### Request Body
```json
{
  "permissions": [
    {
      "subModulePermissionId": 1,
      "isActive": true
    },
    {
      "subModulePermissionId": 2,
      "isActive": false
    }
  ]
}
```

#### Request Body Schema
- `permissions` (array, required) - Array of permission updates
  - `subModulePermissionId` (integer, required) - The SubModulePermission ID
  - `isActive` (boolean, required) - Whether to grant (true) or revoke (false) the permission

#### Response
```json
{
  "success": true,
  "message": "Role permissions updated successfully"
}
```

#### Error Responses
```json
{
  "success": false,
  "message": "Invalid permission data provided"
}
```

---

### 5. Get Permission Matrix

Retrieves the complete permission matrix showing all roles and their permissions across all modules.

```http
GET /matrix
```

#### Response
```json
{
  "success": true,
  "data": [
    {
      "RoleId": 1,
      "RoleName": "SuperAdmin",
      "ModuleId": 1,
      "ModuleName": "Dashboard",
      "ModuleDisplayOrder": 1,
      "SubModuleId": 1,
      "SubModuleName": "Overview",
      "SubModuleDisplayOrder": 1,
      "PermissionId": 1,
      "PermissionName": "View",
      "SubModulePermissionId": 1,
      "HasPermission": 1
    }
  ],
  "message": "Permission matrix retrieved successfully"
}
```

---

## Usage Examples

### JavaScript/Frontend Integration

#### Using Fetch API
```javascript
// Get modules tree
const getModulesTree = async () => {
  const response = await fetch('/api/permission-management/modules-tree', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  return response.json();
};

// Update role permissions
const updateRolePermissions = async (roleId, permissions) => {
  const response = await fetch(`/api/permission-management/roles/${roleId}/permissions`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ permissions })
  });
  return response.json();
};
```

#### Using Axios
```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: '/api/permission-management',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Get role permissions
const getRolePermissions = async (roleId) => {
  const response = await api.get(`/roles/${roleId}/permissions`);
  return response.data;
};
```

### cURL Examples

#### Get Modules Tree
```bash
curl -X GET "http://localhost:5000/api/permission-management/modules-tree" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

#### Update Role Permissions
```bash
curl -X PUT "http://localhost:5000/api/permission-management/roles/2/permissions" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "permissions": [
      {
        "subModulePermissionId": 1,
        "isActive": true
      },
      {
        "subModulePermissionId": 2,
        "isActive": false
      }
    ]
  }'
```

---

## Data Models

### Module
```typescript
interface Module {
  id: number;
  name: string;
  description: string;
  icon: string;
  displayOrder: number;
  isActive: boolean;
  subModules: SubModule[];
}
```

### SubModule
```typescript
interface SubModule {
  id: number;
  name: string;
  description: string;
  route: string;
  icon: string;
  displayOrder: number;
  isActive: boolean;
  permissions: Permission[];
}
```

### Permission
```typescript
interface Permission {
  id: number;
  name: string;
  description: string;
  subModulePermissionId: number;
}
```

### Role
```typescript
interface Role {
  Id: number;
  Name: string;
  IsActive: boolean;
  CreatedOn: string;
}
```

### Permission Update
```typescript
interface PermissionUpdate {
  subModulePermissionId: number;
  isActive: boolean;
}
```

---

## Rate Limiting
Currently no rate limiting is implemented, but it's recommended to implement rate limiting for production use.

## Caching
Permission data is suitable for caching. Consider implementing Redis caching for better performance.

## Security Considerations

1. **Authentication Required**: All endpoints require valid JWT token
2. **Role-Based Access**: Only SuperAdmin users can access these endpoints
3. **Input Validation**: All inputs are validated server-side
4. **SQL Injection Protection**: Parameterized queries used throughout
5. **Audit Logging**: All permission changes are logged

## Error Handling Best Practices

1. Always check the `success` field in responses
2. Handle network errors gracefully
3. Implement retry logic for transient failures
4. Show user-friendly error messages
5. Log errors for debugging

## Testing

### Unit Tests
Test individual API endpoints with various inputs and edge cases.

### Integration Tests
Test the complete permission management workflow.

### Load Tests
Test API performance under high load.

---

## Support

For technical support or questions about the Permission Management API, please refer to:
- Main documentation: `Session_Implementation_Documentation.md`
- Database schema: `ParkwizOps_Permission_System_Documentation.md`
- Implementation summary: `IMPLEMENTATION_SUMMARY.md`
