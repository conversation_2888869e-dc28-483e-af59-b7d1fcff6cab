# Frontend Components Documentation

## Overview
This document provides detailed documentation for the frontend components implemented for the Permission Management system and Hard Delete functionality in ParkwizOps.

## Table of Contents
- [Permission Management Components](#permission-management-components)
- [Component Architecture](#component-architecture)
- [Props and State Management](#props-and-state-management)
- [Styling and UI Guidelines](#styling-and-ui-guidelines)
- [Integration Guide](#integration-guide)
- [Testing Components](#testing-components)

---

## Permission Management Components

### 1. ManagePermissions (Main Page)

**File**: `frontend/src/pages/ManagePermissions.js`

#### Purpose
Main container component for the permission management system. Handles routing, authentication, and orchestrates child components.

#### Features
- SuperAdmin access control
- View mode switching (Matrix vs Role Editor)
- Search and filtering
- Real-time data updates with React Query
- Toast notifications for user feedback

#### Props
None (uses React Router and context)

#### State Management
```javascript
const [viewMode, setViewMode] = useState('matrix'); // 'matrix' or 'role-editor'
const [selectedRole, setSelectedRole] = useState(null);
const [searchTerm, setSearchTerm] = useState('');
const [filterModule, setFilterModule] = useState('');
```

#### Key Functions
- `handleViewModeChange(mode)` - Switch between matrix and role editor views
- `handleRoleSelect(role)` - Select role for detailed editing
- `handlePermissionUpdate(roleId, permissions)` - Update role permissions
- `handleBulkUpdate(roleId, updates)` - Bulk permission operations

#### Usage Example
```jsx
import ManagePermissions from './pages/ManagePermissions';

// In App.js or routing configuration
<Route path="manage-permissions" element={
  <ProtectedRoute allowedRoles={["SuperAdmin"]}>
    <ManagePermissions />
  </ProtectedRoute>
} />
```

---

### 2. PermissionMatrix Component

**File**: `frontend/src/components/PermissionManagement/PermissionMatrix.js`

#### Purpose
Displays a visual matrix of roles vs modules/permissions with interactive controls for bulk operations.

#### Features
- Expandable module hierarchy
- Role-based permission toggles
- Bulk grant/revoke operations
- Search and filter integration
- Color-coded permission status

#### Props
```typescript
interface PermissionMatrixProps {
  modules: Module[];
  roles: Role[];
  matrix: MatrixData[];
  searchTerm: string;
  filterModule: string;
  onPermissionUpdate: (roleId: number, permissions: PermissionUpdate[]) => void;
  onBulkUpdate: (roleId: number, updates: BulkUpdate[]) => void;
  onRoleSelect: (role: Role) => void;
  isLoading: boolean;
}
```

#### State Management
```javascript
const [expandedModules, setExpandedModules] = useState(new Set());
const [selectedCells, setSelectedCells] = useState(new Set());
```

#### Key Functions
- `toggleModule(moduleId)` - Expand/collapse module sections
- `handlePermissionToggle(roleId, subModulePermissionId, currentValue)` - Toggle individual permissions
- `handleBulkToggle(roleId, moduleId, subModuleId, permissionType, newValue)` - Bulk operations
- `getPermissionStatus(roleId, moduleId, subModuleId, permissionId)` - Get permission state

#### Usage Example
```jsx
<PermissionMatrix
  modules={modulesData?.data || []}
  roles={rolesData?.data || []}
  matrix={matrixData?.data || []}
  searchTerm={searchTerm}
  filterModule={filterModule}
  onPermissionUpdate={handlePermissionUpdate}
  onBulkUpdate={handleBulkUpdate}
  onRoleSelect={handleRoleSelect}
  isLoading={isLoading}
/>
```

---

### 3. RolePermissionEditor Component

**File**: `frontend/src/components/PermissionManagement/RolePermissionEditor.js`

#### Purpose
Detailed permission editor for individual roles with advanced features like permission copying and change tracking.

#### Features
- Role selection sidebar
- Hierarchical permission display
- Bulk operations per module/submodule
- Copy permissions between roles
- Change tracking with save/reset functionality
- Unsaved changes indicator

#### Props
```typescript
interface RolePermissionEditorProps {
  roles: Role[];
  modules: Module[];
  selectedRole: Role | null;
  onRoleSelect: (role: Role) => void;
  onPermissionUpdate: (roleId: number, permissions: PermissionUpdate[]) => void;
  onBulkUpdate: (roleId: number, updates: BulkUpdate[]) => void;
  searchTerm: string;
  filterModule: string;
  isLoading: boolean;
}
```

#### State Management
```javascript
const [expandedModules, setExpandedModules] = useState(new Set());
const [localPermissions, setLocalPermissions] = useState({});
const [hasChanges, setHasChanges] = useState(false);
```

#### Key Functions
- `handlePermissionToggle(subModulePermissionId)` - Toggle individual permissions
- `handleBulkToggle(moduleId, subModuleId, newValue)` - Bulk operations
- `handleSave()` - Save permission changes
- `handleReset()` - Reset to original state
- `handleCopyFromRole(fromRoleId)` - Copy permissions from another role

#### Usage Example
```jsx
<RolePermissionEditor
  roles={rolesData?.data || []}
  modules={modulesData?.data || []}
  selectedRole={selectedRole}
  onRoleSelect={setSelectedRole}
  onPermissionUpdate={handlePermissionUpdate}
  onBulkUpdate={handleBulkUpdate}
  searchTerm={searchTerm}
  filterModule={filterModule}
  isLoading={isLoading}
/>
```

---

## Component Architecture

### Data Flow
```
ManagePermissions (Container)
├── React Query (Data Management)
├── Toast Context (Notifications)
├── Auth Context (User Authentication)
└── Child Components
    ├── PermissionMatrix
    └── RolePermissionEditor
```

### State Management Strategy
- **React Query**: Server state management and caching
- **Local State**: UI state (expanded sections, search terms)
- **Context**: Authentication and notifications
- **Props**: Component communication

### API Integration
```javascript
// React Query hooks for data fetching
const { data: modulesData, isLoading: modulesLoading } = useQuery({
  queryKey: ['permission-modules-tree'],
  queryFn: permissionManagementApi.getModulesTree,
  enabled: user?.role === 'SuperAdmin'
});

// Mutations for data updates
const updatePermissionsMutation = useMutation({
  mutationFn: ({ roleId, permissions }) => 
    permissionManagementApi.updateRolePermissions(roleId, permissions),
  onSuccess: () => {
    queryClient.invalidateQueries(['permission-matrix']);
    toast.showSuccess('Permissions updated successfully');
  }
});
```

---

## Props and State Management

### Common Props Pattern
```typescript
// Standard props for permission components
interface BasePermissionProps {
  isLoading?: boolean;
  onError?: (error: Error) => void;
  onSuccess?: (message: string) => void;
}

// Permission update callback
type PermissionUpdateCallback = (
  roleId: number, 
  permissions: PermissionUpdate[]
) => void;

// Bulk update callback
type BulkUpdateCallback = (
  roleId: number, 
  updates: BulkUpdate[]
) => void;
```

### State Management Best Practices
1. **Minimize Local State**: Use React Query for server state
2. **Lift State Up**: Share state between components via props
3. **Use Reducers**: For complex state logic
4. **Memoization**: Use useMemo for expensive calculations
5. **Effect Cleanup**: Proper cleanup in useEffect

---

## Styling and UI Guidelines

### Design System
- **Colors**: Blue theme for primary actions, green for success, red for danger
- **Icons**: Lucide React icons for consistency
- **Typography**: Tailwind CSS utility classes
- **Spacing**: Consistent padding and margins using Tailwind

### Component Styling Patterns
```jsx
// Button styles
const buttonStyles = {
  primary: "bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700",
  secondary: "bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200",
  danger: "bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
};

// Card styles
const cardStyles = "bg-white rounded-lg shadow-sm border p-4";

// Input styles
const inputStyles = "px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500";
```

### Responsive Design
```jsx
// Mobile-first responsive classes
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Content */}
</div>

// Responsive text sizes
<h1 className="text-xl md:text-2xl lg:text-3xl font-bold">
  Permission Management
</h1>
```

### Accessibility Features
- **Keyboard Navigation**: Tab order and focus management
- **Screen Readers**: Proper ARIA labels and descriptions
- **Color Contrast**: WCAG compliant color combinations
- **Focus Indicators**: Visible focus states for all interactive elements

---

## Integration Guide

### Adding to Existing Project

#### 1. Install Dependencies
```bash
npm install @tanstack/react-query lucide-react
```

#### 2. Setup React Query
```jsx
// In App.js or main component
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      {/* Your app components */}
    </QueryClientProvider>
  );
}
```

#### 3. Add Routes
```jsx
// In your routing configuration
import ManagePermissions from './pages/ManagePermissions';

<Route path="manage-permissions" element={
  <ProtectedRoute allowedRoles={["SuperAdmin"]}>
    <ManagePermissions />
  </ProtectedRoute>
} />
```

#### 4. Add Navigation
```jsx
// In your sidebar/navigation component
{user?.role === 'SuperAdmin' && (
  <NavLink to="/dashboard/manage-permissions">
    <Shield className="w-4 h-4" />
    <span>Permission Management</span>
  </NavLink>
)}
```

### Customization Options

#### Theme Customization
```jsx
// Custom theme configuration
const theme = {
  colors: {
    primary: 'blue',
    secondary: 'gray',
    success: 'green',
    danger: 'red'
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '1.5rem'
  }
};
```

#### Component Overrides
```jsx
// Custom permission matrix with different styling
<PermissionMatrix
  className="custom-matrix-styles"
  buttonClassName="custom-button-styles"
  tableClassName="custom-table-styles"
  // ... other props
/>
```

---

## Testing Components

### Unit Testing with Jest and React Testing Library

#### Example Test for PermissionMatrix
```javascript
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import PermissionMatrix from '../PermissionMatrix';

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } }
  });
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('PermissionMatrix', () => {
  const mockProps = {
    modules: [/* mock data */],
    roles: [/* mock data */],
    matrix: [/* mock data */],
    searchTerm: '',
    filterModule: '',
    onPermissionUpdate: jest.fn(),
    onBulkUpdate: jest.fn(),
    onRoleSelect: jest.fn(),
    isLoading: false
  };

  test('renders permission matrix correctly', () => {
    render(<PermissionMatrix {...mockProps} />, { wrapper: createWrapper() });
    
    expect(screen.getByText('Permission Matrix')).toBeInTheDocument();
    expect(screen.getByText('SuperAdmin')).toBeInTheDocument();
  });

  test('handles permission toggle', () => {
    render(<PermissionMatrix {...mockProps} />, { wrapper: createWrapper() });
    
    const permissionToggle = screen.getByTestId('permission-toggle-1');
    fireEvent.click(permissionToggle);
    
    expect(mockProps.onPermissionUpdate).toHaveBeenCalled();
  });
});
```

#### Integration Testing
```javascript
// Test complete permission management workflow
describe('Permission Management Integration', () => {
  test('complete permission update workflow', async () => {
    // Mock API responses
    // Render ManagePermissions component
    // Simulate user interactions
    // Verify API calls and UI updates
  });
});
```

### E2E Testing with Cypress
```javascript
// cypress/integration/permission-management.spec.js
describe('Permission Management', () => {
  beforeEach(() => {
    cy.loginAsSuperAdmin();
    cy.visit('/dashboard/manage-permissions');
  });

  it('should allow SuperAdmin to update permissions', () => {
    cy.get('[data-testid="role-CompanyAdmin"]').click();
    cy.get('[data-testid="permission-toggle-1"]').click();
    cy.get('[data-testid="save-button"]').click();
    cy.get('.toast-success').should('contain', 'Permissions updated');
  });
});
```

---

## Performance Optimization

### React Query Optimization
```javascript
// Optimized query configuration
const { data: modulesData } = useQuery({
  queryKey: ['permission-modules-tree'],
  queryFn: permissionManagementApi.getModulesTree,
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false
});
```

### Component Memoization
```javascript
// Memoize expensive calculations
const filteredModules = useMemo(() => {
  return modules.filter(module => 
    module.name.toLowerCase().includes(searchTerm.toLowerCase())
  );
}, [modules, searchTerm]);

// Memoize callback functions
const handlePermissionUpdate = useCallback((roleId, permissions) => {
  updatePermissionsMutation.mutate({ roleId, permissions });
}, [updatePermissionsMutation]);
```

### Virtual Scrolling for Large Lists
```javascript
// For large permission lists
import { FixedSizeList as List } from 'react-window';

const PermissionList = ({ permissions }) => (
  <List
    height={400}
    itemCount={permissions.length}
    itemSize={50}
    itemData={permissions}
  >
    {PermissionItem}
  </List>
);
```

---

## Troubleshooting

### Common Issues

#### 1. Component Not Rendering
- Check authentication context
- Verify user role (SuperAdmin required)
- Check React Query provider setup

#### 2. API Calls Failing
- Verify backend server is running
- Check authentication token
- Verify API endpoint URLs

#### 3. State Not Updating
- Check React Query cache invalidation
- Verify mutation success callbacks
- Check component re-render triggers

#### 4. Styling Issues
- Verify Tailwind CSS is properly configured
- Check for CSS conflicts
- Ensure responsive classes are applied correctly

### Debug Tools
- React Developer Tools
- React Query Devtools
- Browser Network tab for API debugging
- Console logs for state debugging

---

## Best Practices

1. **Component Composition**: Break down complex components into smaller, reusable pieces
2. **Error Boundaries**: Implement error boundaries for graceful error handling
3. **Loading States**: Always show loading indicators for async operations
4. **Accessibility**: Follow WCAG guidelines for accessibility
5. **Performance**: Use React.memo, useMemo, and useCallback appropriately
6. **Testing**: Write comprehensive tests for all components
7. **Documentation**: Keep component documentation up to date

This documentation provides a comprehensive guide for understanding, using, and maintaining the frontend components of the Permission Management system.
