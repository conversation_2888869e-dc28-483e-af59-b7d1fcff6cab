# Dashboard Time Ranges Analysis

## Overview
This document provides a comprehensive analysis of the time ranges used by the PWVMS dashboard data fetching system.

**Analysis Date:** June 30, 2025, 16:51:55 IST  
**System Time:** 2025-06-30T11:21:55.293Z

## Date Range Calculations

The dashboard uses the following date range logic:

### 1. TODAY
- **Start:** 2025-06-29T18:30:00.000Z (Mon Jun 30 2025 00:00:00 GMT+0530)
- **End:** 2025-06-30T18:29:59.999Z (Mon Jun 30 2025 23:59:59 GMT+0530)
- **Duration:** 1 day
- **Data Available:** 24,180 transactions

### 2. YESTERDAY
- **Start:** 2025-06-28T18:30:00.000Z (Sun Jun 29 2025 00:00:00 GMT+0530)
- **End:** 2025-06-29T18:29:59.999Z (Sun Jun 29 2025 23:59:59 GMT+0530)
- **Duration:** 1 day
- **Data Available:** 29,392 transactions

### 3. WEEK (Last 7 Days)
- **Start:** 2025-06-23T18:30:00.000Z (Tue Jun 24 2025 00:00:00 GMT+0530)
- **End:** 2025-06-30T18:29:59.999Z (Mon Jun 30 2025 23:59:59 GMT+0530)
- **Duration:** 7 days
- **Data Available:** 85,480 transactions

### 4. MONTH (Last 30 Days)
- **Start:** 2025-05-31T18:30:00.000Z (Sun Jun 01 2025 00:00:00 GMT+0530)
- **End:** 2025-06-30T18:29:59.999Z (Mon Jun 30 2025 23:59:59 GMT+0530)
- **Duration:** 30 days
- **Data Available:** 122,398 transactions (NEW table only - ⚠️ MISSING 387,643 from OLD table)
- **Total Available:** 510,041 transactions (if both tables were used)

### 5. YEAR (Last 365 Days)
- **Start:** 2024-06-30T18:30:00.000Z (Mon Jul 01 2024 00:00:00 GMT+0530)
- **End:** 2025-06-30T18:29:59.999Z (Mon Jun 30 2025 23:59:59 GMT+0530)
- **Duration:** 365 days (limited to 90 days for performance)
- **Data Available:** 122,398 transactions (NEW table only - ⚠️ MISSING 1,114,454 from OLD table)
- **Total Available:** 1,236,852 transactions (if both tables were used)

### 6. SPECIFIC DATE (Example: 2025-06-21)
- **Start:** 2025-06-21T00:00:00.000Z (Sat Jun 21 2025 05:30:00 GMT+0530)
- **End:** 2025-06-21T23:59:59.999Z (Sun Jun 22 2025 05:29:59 GMT+0530)
- **Duration:** 1 day
- **Data Available:** 15,270 transactions (949 from OLD table + 14,321 from NEW table)

## Data Sources

The system uses two database tables:

### OLD Table (tblParkwiz_Parking_Data_OLD)
- **Data Range:** 2025-03-31T06:25:30.000Z to 2025-06-21T10:33:51.000Z
- **Total Records:** 1,114,454 transactions
- **Coverage:** March 31, 2025 to June 21, 2025

### NEW Table (tblParkwiz_Parking_Data)
- **Data Range:** 2025-06-21T06:00:01.000Z to 2025-06-30T16:51:58.000Z
- **Total Records:** 121,840 transactions
- **Coverage:** June 21, 2025 to June 30, 2025 (current)

## Recent Data Analysis (Last 7 Days)

| Date | Transactions | First Transaction | Last Transaction |
|------|-------------|------------------|------------------|
| 2025-06-30 | 7,201 | 2025-06-30T00:00:00.000Z | 2025-06-30T16:51:58.000Z |
| 2025-06-29 | 31,865 | 2025-06-29T00:00:08.000Z | 2025-06-29T23:59:54.000Z |
| 2025-06-28 | 29,149 | 2025-06-28T00:00:07.000Z | 2025-06-28T23:59:43.000Z |
| 2025-06-27 | 4,616 | 2025-06-27T00:00:24.000Z | 2025-06-27T23:59:53.000Z |
| 2025-06-26 | 3,667 | 2025-06-26T00:01:27.000Z | 2025-06-26T23:59:50.000Z |
| 2025-06-25 | 3,562 | 2025-06-25T00:00:11.000Z | 2025-06-25T23:59:10.000Z |
| 2025-06-24 | 4,013 | 2025-06-24T00:00:14.000Z | 2025-06-24T23:58:11.000Z |
| 2025-06-23 | 3,022 | 2025-06-23T11:23:59.000Z | 2025-06-23T23:59:46.000Z |

## Key Findings

1. **Time Zone Handling:** The system correctly handles IST (GMT+0530) timezone conversion
2. **⚠️ CRITICAL ISSUE:** Dashboard is ONLY using the NEW table (`tblParkwiz_Parking_Data`) and completely ignoring the OLD table (`tblParkwiz_Parking_Data_OLD`)
3. **Missing Historical Data:** For the "Last 30 Days" range, dashboard shows 122,398 transactions but is missing 387,643 transactions from the OLD table
4. **Data Loss:** Dashboard is missing over 75% of available historical data (387,643 out of 510,041 total transactions)
5. **Performance Optimization:** The system limits queries to 90 days maximum for better performance
6. **Data Availability:** Recent data (last 7 days) shows consistent transaction recording
7. **Volume Trends:** Higher transaction volumes on weekends (June 28-29: ~30k each day)

## Date Range Options Available

The dashboard provides these date range selections:
- **Today** - Current day (00:00 to 23:59)
- **Yesterday** - Previous day (00:00 to 23:59)
- **Last 7 Days** - Rolling 7-day window
- **Last 30 Days** - Rolling 30-day window
- **Last 12 Months** - Rolling 365-day window (limited to 90 days for performance)
- **Specific Date** - Any single date in YYYY-MM-DD format

## Performance Considerations

- Queries spanning more than 90 days are automatically limited to the most recent 90 days
- ⚠️ The system currently ONLY uses the NEW table, missing historical data from the OLD table
- Indexes and query optimization hints are used for better performance

## Recommendations

1. **🚨 URGENT: Fix Dashboard Data Source** - Modify the dashboard controller to query both tables or create a unified view
2. **Data Integration:** Implement UNION queries to combine data from both OLD and NEW tables
3. **Performance Optimization:** Create indexed views or materialized views for better performance when querying both tables
4. **Data Migration:** Consider migrating OLD table data to the NEW table structure
5. **Monitoring:** Set up monitoring for data gaps or inconsistencies between tables
6. **User Notification:** Inform users about the current data limitation and expected fix timeline
7. **Caching:** Implement caching for frequently accessed date ranges after fixing the data source issue