require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

// Copy of the calculateDateRange function from the dashboard controller
function calculateDateRange(dateRange) {
  // Use June 21, 2025 as the reference date (based on available data)
  const referenceDate = new Date('2025-06-21T12:00:00.000Z');
  let startDate, endDate;
  
  switch(dateRange) {
    case 'today':
      // For today, use June 21, 2025
      startDate = new Date(referenceDate);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'yesterday':
      // For yesterday, use June 20, 2025
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 1);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setDate(endDate.getDate() - 1);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'week':
      // For week, use the last 7 days from June 21, 2025
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 6);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'month':
      // For month, use the last 30 days from June 21, 2025
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 29);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'year':
      // For year, use the last 365 days from June 21, 2025
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 364);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    default:
      // Default to today (June 21, 2025)
      startDate = new Date(referenceDate);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
  }
  
  return { startDate, endDate };
}

async function testDashboardWithJune21() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Test different date ranges
    const dateRanges = ['today', 'yesterday', 'week', 'month'];
    
    for (const dateRange of dateRanges) {
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      console.log(`\n=== Testing dashboard with date range: ${dateRange} ===`);
      console.log('- Start date:', startDate.toISOString());
      console.log('- End date:', endDate.toISOString());
      
      const request = new sql.Request();
      request.input('startDate', sql.DateTime, startDate);
      request.input('endDate', sql.DateTime, endDate);
      
      const summaryResult = await request.query(`
        SELECT 
          ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
          COUNT(*) as TransactionCount,
          COUNT(DISTINCT CASE WHEN VehicleNumber <> 'NA' AND VehicleNumber IS NOT NULL THEN VehicleNumber ELSE NULL END) as VehicleCount,
          ISNULL(AVG(ISNULL(ParkedDuration, 0)), 0) as AvgDuration
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @startDate AND @endDate
      `);
      
      console.log('\nDashboard Summary:');
      console.log('- Total Revenue:', summaryResult.recordset[0].TotalRevenue);
      console.log('- Transaction Count:', summaryResult.recordset[0].TransactionCount);
      console.log('- Vehicle Count:', summaryResult.recordset[0].VehicleCount);
      console.log('- Average Duration (minutes):', summaryResult.recordset[0].AvgDuration);
      
      const paymentMethodResult = await request.query(`
        SELECT 
          PaymentMode, 
          COUNT(*) as TransactionCount, 
          ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue 
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @startDate AND @endDate 
        GROUP BY PaymentMode 
        ORDER BY Revenue DESC
      `);
      
      console.log('\nRevenue by Payment Method:');
      paymentMethodResult.recordset.forEach(method => {
        console.log(`- ${method.PaymentMode}: ${method.TransactionCount} transactions, ₹${method.Revenue.toFixed(2)}`);
      });
    }

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

testDashboardWithJune21();