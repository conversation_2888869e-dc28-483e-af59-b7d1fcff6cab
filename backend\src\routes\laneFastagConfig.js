/**
 * ============================================================================
 * # LANE FASTAG CONFIGURATION ROUTES
 * ============================================================================
 * This router defines all API endpoints for lane Fastag configuration operations.
 * It maps HTTP requests to controller methods for CRUD operations on the
 * tblLaneFastagConfiguration table.
 *
 * @module routes/laneFastagConfigurationRoutes
 * @requires express
 * @requires ../controllers/laneFastagConfigController
 * ============================================================================
 */

const express = require('express');
const router = express.Router();
const laneFastagController = require('../controllers/laneFastagConfigController');
const auth = require('../middleware/auth');
const { checkPermission } = require('../Utils/PermissionHelper');

/**
 * ============================================================================
 * # GET ROUTES
 * ============================================================================
 * Routes for retrieving lane Fastag configuration data
 */

/**
 * @route GET /api/lane-fastag
 * @description Get all lane Fastag configurations
 * @access Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/',
  auth(['View']),
  checkPermission('Fastag', 'View'),
  laneFastagController.getAllLaneFastagConfigurations);

/**
 * @route GET /api/lane-fastag/:id
 * @description Get a specific lane Fastag configuration by ID
 * @param {number} id - RecordID of the configuration
 * @access Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id',
  auth(['View']),
  checkPermission('Fastag', 'View'),
  laneFastagController.getLaneFastagConfigurationById);

/**
 * @route GET /api/lane-fastag/plaza/:plazaId
 * @description Get all lane Fastag configurations for a specific plaza
 * @param {number} plazaId - PlazaID to filter configurations by
 * @access Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/plaza/:plazaId',
  auth(['View']),
  checkPermission('Fastag', 'View'),
  laneFastagController.getLaneFastagConfigurationsByPlazaId);

/**
 * @route GET /api/lane-fastag/diagnostic/check
 * @description Diagnostic endpoint to check user company associations and FasTag configurations
 * @access Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/diagnostic/check',
  auth(['View']),
  laneFastagController.diagnosticCheck);

/**
 * ============================================================================
 * # POST ROUTES
 * ============================================================================
 * Routes for creating lane Fastag configurations
 */

/**
 * @route POST /api/lane-fastag
 * @description Create a new lane Fastag configuration
 * @access Private (SuperAdmin, CompanyAdmin)
 */
router.post('/',
  auth(['Create']),
  checkPermission('Fastag', 'Create'),
  laneFastagController.createLaneFastagConfiguration);

/**
 * @route POST /api/lane-fastag/bulk
 * @description Bulk create or update multiple lane Fastag configurations
 * @access Private (SuperAdmin, CompanyAdmin)
 */
router.post('/bulk',
  auth(['Create']),
  checkPermission('Fastag', 'Create'),
  laneFastagController.bulkCreateOrUpdateLaneFastagConfigurations);

/**
 * ============================================================================
 * # PUT ROUTES
 * ============================================================================
 * Routes for updating lane Fastag configurations
 */

/**
 * @route PUT /api/lane-fastag/:id
 * @description Update an existing lane Fastag configuration
 * @param {number} id - RecordID of the configuration to update
 * @access Private (SuperAdmin, CompanyAdmin)
 */
router.put('/:id',
  auth(['Edit']),
  checkPermission('Fastag', 'Edit'),
  laneFastagController.updateLaneFastagConfiguration);

/**
 * ============================================================================
 * # DELETE ROUTES
 * ============================================================================
 * Routes for removing lane Fastag configurations
 */

/**
 * @route DELETE /api/lane-fastag/:id
 * @description Delete a lane Fastag configuration
 * @param {number} id - RecordID of the configuration to delete
 * @access Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id',
  auth(['Delete']),
  checkPermission('Fastag', 'Delete'),
  laneFastagController.deleteLaneFastagConfiguration);

module.exports = router;