const db = require('./src/config/database');

async function checkSchema() {
  try {
    console.log('=== CHECKING DATABASE SCHEMA ===');
    
    // Check RoleModules table structure
    const roleModulesStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'RoleModules'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('RoleModules table structure:', roleModulesStructure.recordset);
    
    // Check all tables in the database
    const allTables = await db.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    console.log('\nAll tables in database:', allTables.recordset.map(t => t.TABLE_NAME));
    
    // Check if Company table exists and its structure
    const companyTableCheck = await db.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'Company' AND TABLE_TYPE = 'BASE TABLE'
    `);
    
    if (companyTableCheck.recordset.length > 0) {
      const companyStructure = await db.query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Company'
        ORDER BY ORDINAL_POSITION
      `);
      console.log('\nCompany table structure:', companyStructure.recordset);
    } else {
      console.log('\nCompany table does not exist');
    }
    
    // Check sample data from RoleModules
    const sampleRoleModules = await db.query('SELECT TOP 5 * FROM RoleModules');
    console.log('\nSample RoleModules data:', sampleRoleModules.recordset);
    
    // Check Users table structure
    const usersStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Users'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('\nUsers table structure:', usersStructure.recordset);
    
    // Check sample users
    const sampleUsers = await db.query('SELECT TOP 3 Id, Username, Email, RoleId FROM Users');
    console.log('\nSample Users:', sampleUsers.recordset);
    
    // Check Plaza table structure
    const plazaStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Plaza'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('\nPlaza table structure:', plazaStructure.recordset);
    
    // Check sample plazas
    const samplePlazas = await db.query('SELECT TOP 3 * FROM Plaza');
    console.log('\nSample Plazas:', samplePlazas.recordset);
    
    process.exit(0);
  } catch (error) {
    console.error('Schema check failed:', error);
    process.exit(1);
  }
}

checkSchema();
