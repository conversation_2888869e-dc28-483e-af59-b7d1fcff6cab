/**
 * ===============================================================================
 * # Status Utilities
 * ===============================================================================
 * 
 * Utility functions for handling status values across different formats
 * in the parking management system.
 */

/**
 * Check if a status value represents an active state
 * Handles multiple status formats: true/false, 1/0, '1'/'0', 'Y'/'N'
 * @param {*} status - The status value to check
 * @returns {boolean} True if the status represents an active state
 */
export const isStatusActive = (status) => {
  return status === true || 
         status === 1 || 
         status === '1' || 
         status === 'Y' || 
         status === 'true';
};

/**
 * Convert a status value to a standardized boolean
 * @param {*} status - The status value to convert
 * @returns {boolean} True for active, false for inactive
 */
export const normalizeStatus = (status) => {
  return isStatusActive(status);
};

/**
 * Convert a boolean status to the database format (Y/N)
 * @param {boolean} isActive - Whether the status should be active
 * @returns {string} 'Y' for active, 'N' for inactive
 */
export const statusToDatabase = (isActive) => {
  return isActive ? 'Y' : 'N';
};

/**
 * Get a human-readable status label
 * @param {*} status - The status value
 * @returns {string} 'Active' or 'Inactive'
 */
export const getStatusLabel = (status) => {
  return isStatusActive(status) ? 'Active' : 'Inactive';
};