-- Fix Security module permissions
-- First, check if Security module exists
DECLARE @SecurityModuleId INT;
SELECT @SecurityModuleId = Id FROM Modules WHERE Name = 'Security';

-- If Security module doesn't exist, create it
IF @SecurityModuleId IS NULL
BEGIN
    INSERT INTO Modules (Name, Description, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES ('Security', 'Security settings and permissions', 'security', 10, 1, 1, GETDATE());
    
    SET @SecurityModuleId = SCOPE_IDENTITY();
    
    -- Create Security submodule
    DECLARE @SecuritySubModuleId INT;
    INSERT INTO SubModules (ModuleId, Name, Icon, Path, IsActive, CreatedBy, CreatedOn)
    VALUES (@SecurityModuleId, 'Security', 'security', '/security', 1, 1, GETDATE());
    
    SET @SecuritySubModuleId = SCOPE_IDENTITY();
    
    -- Add permissions for the Security submodule
    DECLARE @ViewPermissionId INT;
    SELECT @ViewPermissionId = Id FROM Permissions WHERE Name = 'View';
    
    -- Create SubModulePermission entry
    DECLARE @SubModulePermissionId INT;
    INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
    VALUES (@SecuritySubModuleId, @ViewPermissionId, 1, 1, GETDATE());
    
    SET @SubModulePermissionId = SCOPE_IDENTITY();
    
    -- Grant permission to SuperAdmin
    DECLARE @SuperAdminRoleId INT;
    SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
    
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
    
    -- Grant permission to CompanyAdmin
    DECLARE @CompanyAdminRoleId INT;
    SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
    
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
    
    PRINT 'Security module and permissions created successfully';
END
ELSE
BEGIN
    PRINT 'Security module already exists';
    
    -- Check if Security submodule exists
    DECLARE @SecuritySubModuleId INT;
    SELECT @SecuritySubModuleId = Id FROM SubModules WHERE ModuleId = @SecurityModuleId AND Name = 'Security';
    
    -- If Security submodule doesn't exist, create it
    IF @SecuritySubModuleId IS NULL
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, Icon, Path, IsActive, CreatedBy, CreatedOn)
        VALUES (@SecurityModuleId, 'Security', 'security', '/security', 1, 1, GETDATE());
        
        SET @SecuritySubModuleId = SCOPE_IDENTITY();
        
        -- Add permissions for the Security submodule
        DECLARE @ViewPermissionId INT;
        SELECT @ViewPermissionId = Id FROM Permissions WHERE Name = 'View';
        
        -- Create SubModulePermission entry
        DECLARE @SubModulePermissionId INT;
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SecuritySubModuleId, @ViewPermissionId, 1, 1, GETDATE());
        
        SET @SubModulePermissionId = SCOPE_IDENTITY();
        
        -- Grant permission to SuperAdmin
        DECLARE @SuperAdminRoleId INT;
        SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
        
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
        
        -- Grant permission to CompanyAdmin
        DECLARE @CompanyAdminRoleId INT;
        SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
        
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
        
        PRINT 'Security submodule and permissions created successfully';
    END
    ELSE
    BEGIN
        PRINT 'Security submodule already exists';
        
        -- Check if View permission exists for Security submodule
        DECLARE @ViewPermissionId INT;
        SELECT @ViewPermissionId = Id FROM Permissions WHERE Name = 'View';
        
        DECLARE @SubModulePermissionId INT;
        SELECT @SubModulePermissionId = Id FROM SubModulePermissions 
        WHERE SubModuleId = @SecuritySubModuleId AND PermissionId = @ViewPermissionId;
        
        -- If permission doesn't exist, create it
        IF @SubModulePermissionId IS NULL
        BEGIN
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@SecuritySubModuleId, @ViewPermissionId, 1, 1, GETDATE());
            
            SET @SubModulePermissionId = SCOPE_IDENTITY();
            
            PRINT 'View permission created for Security submodule';
        END
        ELSE
        BEGIN
            PRINT 'View permission already exists for Security submodule';
        END
        
        -- Grant permission to SuperAdmin if not already granted
        DECLARE @SuperAdminRoleId INT;
        SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
        
        IF NOT EXISTS (SELECT 1 FROM RolePermissions 
                      WHERE RoleId = @SuperAdminRoleId AND SubModulePermissionId = @SubModulePermissionId)
        BEGIN
            INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
            
            PRINT 'Permission granted to SuperAdmin';
        END
        ELSE
        BEGIN
            PRINT 'SuperAdmin already has the permission';
        END
        
        -- Grant permission to CompanyAdmin if not already granted
        DECLARE @CompanyAdminRoleId INT;
        SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
        
        IF NOT EXISTS (SELECT 1 FROM RolePermissions 
                      WHERE RoleId = @CompanyAdminRoleId AND SubModulePermissionId = @SubModulePermissionId)
        BEGIN
            INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
            
            PRINT 'Permission granted to CompanyAdmin';
        END
        ELSE
        BEGIN
            PRINT 'CompanyAdmin already has the permission';
        END
    END
END