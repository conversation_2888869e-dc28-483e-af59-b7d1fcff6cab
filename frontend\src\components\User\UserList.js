// frontend/src/components/User/UserList.js
import React, { useState, useMemo } from 'react';
import {
  ArrowDown, ArrowUp, Edit2, Trash2,
  Mail, Phone, User, Calendar, ChevronRight, ChevronLeft,
  Search, Filter, X, Shield
} from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';

export default function UserList({
  users,
  totalUsers,
  onEdit,
  onDelete,
  onSelect,
  loading = false,
  page = 1,
  pageSize = 10,
  onPageChange,
  onPageSizeChange,
  sortConfig = { key: 'Username', direction: 'asc' },
  onSort,
  filters = {},
  onFilter,
  onClearFilters
}) {
  // Ensure users is always an array
  const usersList = Array.isArray(users) ? users : [];
  // Ensure totalUsers is a number
  const totalUserCount = typeof totalUsers === 'number' ? totalUsers : usersList.length;
  // Generate pagination range
  const totalPages = Math.ceil(totalUserCount / pageSize);
  const pageRange = useMemo(() => {
    const delta = 2; // Number of pages to show before and after current page
    const range = [];
    const rangeWithDots = [];
    let l;

    for (let i = 1; i <= totalPages; i++) {
      if (i === 1 || i === totalPages || (i >= page - delta && i <= page + delta)) {
        range.push(i);
      }
    }

    for (let i of range) {
      if (l) {
        if (i - l === 2) {
          rangeWithDots.push(l + 1);
        } else if (i - l !== 1) {
          rangeWithDots.push('...');
        }
      }
      rangeWithDots.push(i);
      l = i;
    }

    return rangeWithDots;
  }, [totalPages, page]);

  // Local state for search input
  const [searchInput, setSearchInput] = useState(filters.search || '');

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearchInput(e.target.value);
  };

  // Handle search submit
  const handleSearchSubmit = (e) => {
    e.preventDefault();
    onFilter({ ...filters, search: searchInput });
  };

  // Handle sort
  const handleSort = (key) => {
    const direction = sortConfig.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    onSort({ key, direction });
  };

  // Get sort icon
  const getSortIcon = (key) => {
    if (sortConfig.key !== key) return null;
    return sortConfig.direction === 'asc' ? <ArrowUp className="w-4 h-4" /> : <ArrowDown className="w-4 h-4" />;
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearchInput('');
    onClearFilters();
  };

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      {/* Search and Filter Bar */}
      <div className="p-4 border-b">
        <div className="flex flex-col md:flex-row gap-4 justify-between">
          <form onSubmit={handleSearchSubmit} className="flex w-full md:w-auto">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search users..."
                value={searchInput}
                onChange={handleSearchChange}
                className="pl-10 pr-4 py-2 border rounded-l-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Search className="absolute left-3 top-2.5 text-gray-400 w-5 h-5" />
            </div>
            <button
              type="submit"
              className="bg-blue-600 text-white px-4 py-2 rounded-r-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
          </form>

          <div className="flex gap-2">
            {Object.keys(filters).length > 0 && (
              <button
                onClick={handleClearFilters}
                className="flex items-center gap-1 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                <X className="w-4 h-4" />
                Clear Filters
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('Username')}
              >
                <div className="flex items-center gap-1">
                  <span>Username</span>
                  {getSortIcon('Username')}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('FirstName')}
              >
                <div className="flex items-center gap-1">
                  <span>Name</span>
                  {getSortIcon('FirstName')}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('Email')}
              >
                <div className="flex items-center gap-1">
                  <span>Email</span>
                  {getSortIcon('Email')}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => handleSort('RoleName')}
              >
                <div className="flex items-center gap-1">
                  <span>Role</span>
                  {getSortIcon('RoleName')}
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                <div className="flex items-center gap-1">
                  <Shield className="w-4 h-4" />
                  <span>Companies & Plazas</span>
                </div>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                  Loading...
                </td>
              </tr>
            ) : usersList.length === 0 ? (
              <tr>
                <td colSpan="6" className="px-6 py-4 text-center text-gray-500">
                  No users found
                </td>
              </tr>
            ) : (
              usersList.map((user) => (
                <tr
                  key={user.Id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onSelect && onSelect(user)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-gray-100 rounded-full flex items-center justify-center">
                        <User className="h-6 w-6 text-gray-500" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{user.Username}</div>
                        <div className="text-sm text-gray-500">{user.UserCode || 'N/A'}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{`${user.FirstName} ${user.LastName}`}</div>
                    <div className="text-sm text-gray-500">{user.Mobile || 'No phone'}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{user.Email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {user.RoleName}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm max-w-xs">
                      {/* Companies */}
                      {user.companies && Array.isArray(user.companies) && user.companies.length > 0 && (
                        <div className="mb-2">
                          <div className="font-medium text-gray-700 mb-1 text-xs">Companies:</div>
                          <div className="flex flex-wrap gap-1">
                            {user.companies.slice(0, 3).map((company) => (
                              <span
                                key={company.Id || company.CompanyName}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors cursor-default"
                                title={company.CompanyName || 'Unknown Company'}
                              >
                                {company.CompanyName && company.CompanyName.length > 15
                                  ? `${company.CompanyName.substring(0, 15)}...`
                                  : (company.CompanyName || 'Unknown')}
                              </span>
                            ))}
                            {user.companies.length > 3 && (
                              <span
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors cursor-help"
                                title={`${user.companies.length - 3} more companies: ${user.companies.slice(3).map(c => c.CompanyName).join(', ')}`}
                              >
                                +{user.companies.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Plazas */}
                      {user.plazas && Array.isArray(user.plazas) && user.plazas.length > 0 && (
                        <div>
                          <div className="font-medium text-gray-700 mb-1 text-xs">Plazas:</div>
                          <div className="flex flex-wrap gap-1">
                            {user.plazas.slice(0, 3).map((plaza) => (
                              <span
                                key={plaza.Id || plaza.PlazaName}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 hover:bg-purple-200 transition-colors cursor-default"
                                title={plaza.PlazaName || 'Unknown Plaza'}
                              >
                                {plaza.PlazaName && plaza.PlazaName.length > 15
                                  ? `${plaza.PlazaName.substring(0, 15)}...`
                                  : (plaza.PlazaName || 'Unknown')}
                              </span>
                            ))}
                            {user.plazas.length > 3 && (
                              <span
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 hover:bg-gray-200 transition-colors cursor-help"
                                title={`${user.plazas.length - 3} more plazas: ${user.plazas.slice(3).map(p => p.PlazaName).join(', ')}`}
                              >
                                +{user.plazas.length - 3} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}

                      {/* No assignments */}
                      {(!user.companies || !Array.isArray(user.companies) || user.companies.length === 0) &&
                       (!user.plazas || !Array.isArray(user.plazas) || user.plazas.length === 0) && (
                        <span className="text-gray-500 italic text-xs">No assignments</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center space-x-3">
                      <PermissionButton
                        requiredModule="Users"
                        requiredPermissions={["Edit"]}
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(user);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        aria-label={`Edit ${user.Username}`}
                      >
                        <Edit2 className="w-5 h-5" />
                      </PermissionButton>
                      <PermissionButton
                        requiredModule="Users"
                        requiredPermissions={["Delete"]}
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(user.Id);
                        }}
                        className="text-red-600 hover:text-red-900"
                        aria-label={`Delete ${user.Username}`}
                      >
                        <Trash2 className="w-5 h-5" />
                      </PermissionButton>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                page === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                page === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{Math.min((page - 1) * pageSize + 1, totalUserCount)}</span> to{' '}
                <span className="font-medium">{Math.min(page * pageSize, totalUserCount)}</span> of{' '}
                <span className="font-medium">{totalUserCount}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {onPageChange && (
                  <button
                    onClick={() => onPageChange(page - 1)}
                    disabled={page === 1}
                    className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                      page === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span className="sr-only">Previous</span>
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                )}
                {pageRange.map((p, i) => (
                  <button
                    key={i}
                    onClick={() => typeof p === 'number' && onPageChange && onPageChange(p)}
                    className={`relative inline-flex items-center px-4 py-2 border ${
                      p === page
                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    } text-sm font-medium`}
                  >
                    {p}
                  </button>
                ))}
                {onPageChange && (
                  <button
                    onClick={() => onPageChange(page + 1)}
                    disabled={page === totalPages}
                    className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                      page === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    }`}
                  >
                    <span className="sr-only">Next</span>
                    <ChevronRight className="h-5 w-5" />
                  </button>
                )}
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
