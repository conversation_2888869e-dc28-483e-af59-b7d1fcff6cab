// frontend/src/components/Toast/Toast.js
import React, { useEffect, useState } from 'react';
import { X, CheckCircle, XCircle, AlertTriangle, Info } from 'lucide-react';

const Toast = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleRemove = () => {
    setIsRemoving(true);
    setTimeout(() => {
      onRemove(toast.id);
    }, 300); // Match animation duration
  };

  const getToastClasses = () => {
    const baseClass = 'toast';
    const typeClass = toast.type || 'info';
    const animationClass = isVisible && !isRemoving ? 'toast-enter-active' : 'toast-exit-active';

    return `${baseClass} ${typeClass} ${animationClass}`;
  };

  const getIcon = () => {
    const iconProps = { className: "h-5 w-5" };

    switch (toast.type) {
      case 'success':
        return <CheckCircle {...iconProps} style={{ color: '#10b981' }} />;
      case 'error':
        return <XCircle {...iconProps} style={{ color: '#ef4444' }} />;
      case 'warning':
        return <AlertTriangle {...iconProps} style={{ color: '#f59e0b' }} />;
      case 'info':
      default:
        return <Info {...iconProps} style={{ color: '#3b82f6' }} />;
    }
  };

  return (
    <div className={getToastClasses()}>
      <div className="toast-content">
        <div className="toast-icon">
          {getIcon()}
        </div>
        <div className="toast-message">
          <p className="toast-title">
            {toast.message}
          </p>
          {toast.description && (
            <p className="toast-description">
              {toast.description}
            </p>
          )}
        </div>
        <button
          className="toast-close"
          onClick={handleRemove}
          aria-label="Close notification"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default Toast;
