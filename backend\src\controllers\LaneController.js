const db = require('../config/database');

/**
 * ===============================================================================
 * # Lane Controller
 * ===============================================================================
 *
 * This controller handles all CRUD operations for the tblLaneDetails table in the
 * parking management system. It provides endpoints for:
 *
 * - Retrieving all lanes or specific lanes by ID or Plaza
 * - Creating new lanes with extensive configuration options
 * - Updating existing lane configurations
 * - Deleting lanes from the system
 * - Toggling the active status of lanes (activate/deactivate)
 *
 * The controller implements proper error handling, input validation, and
 * standardized response formatting for all operations.
 *
 * @module LaneController
 */
const laneController = {
  /**
   * ===============================================================================
   * ## GET ALL LANES
   * ===============================================================================
   *
   * Retrieves all lanes from the database with associated plaza and company information.
   * Results are ordered by LaneID in descending order (newest first).
   *
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lanes array or error message
   */
  getAllLanes: async (req, res) => {
    try {
      // Base query with joins
      let query = `
        SELECT
          l.*,                 /* All lane fields */
          p.PlazaName,         /* Plaza name from Plaza table */
          c.CompanyName        /* Company name from tblCompanyMaster table */
        FROM tblLaneDetails l
        LEFT JOIN Plaza p ON l.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster c ON l.CompanyID = c.Id
        WHERE 1=1              /* Base condition for adding filters */
      `;

      const queryParams = {};

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Add ordering
      query += ` ORDER BY l.LaneID DESC /* Sort by newest lanes first */`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Return successful response with lanes data
      res.json({
        success: true,
        lanes: result.recordset,
        message: 'Lanes retrieved successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in getAllLanes controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lanes',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANE BY ID
   * ===============================================================================
   *
   * Retrieves a specific lane by its ID with associated plaza and company information.
   * Returns a 404 error if the lane is not found.
   *
   * @param {Object} req - Express request object with lane ID in params
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lane object or error message
   */
  getLaneById: async (req, res) => {
    try {
      // Extract lane ID from request parameters
      const { id } = req.params;

      // Base query with joins
      let query = `
        SELECT
          l.*,                 /* All lane fields */
          p.PlazaName,         /* Plaza name from Plaza table */
          c.CompanyName        /* Company name from tblCompanyMaster table */
        FROM tblLaneDetails l
        LEFT JOIN Plaza p ON l.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster c ON l.CompanyID = c.Id
        WHERE l.LaneID = @id   /* Filter by lane ID */
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Check if lane exists or user has access to it
      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have access to this lane'
        });
      }

      // Return successful response with lane data
      res.json({
        success: true,
        lane: result.recordset[0],
        message: 'Lane retrieved successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in getLaneById controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lane',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANES BY PLAZA ID
   * ===============================================================================
   *
   * Retrieves all lanes associated with a specific plaza ID.
   * Results are ordered by LaneNumber for logical presentation.
   *
   * @param {Object} req - Express request object with plazaId in params
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lanes array or error message
   */
  getLanesByPlaza: async (req, res) => {
    try {
      // Extract plaza ID from request parameters
      const { plazaId } = req.params;

      // Base query with joins
      let query = `
        SELECT
          l.*,                    /* All lane fields */
          p.PlazaName,            /* Plaza name from Plaza table */
          c.CompanyName           /* Company name from tblCompanyMaster table */
        FROM tblLaneDetails l
        LEFT JOIN Plaza p ON l.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster c ON l.CompanyID = c.Id
        WHERE l.PlazaID = @plazaId /* Filter by plaza ID */
      `;

      const queryParams = { plazaId };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Add ordering
      query += ` ORDER BY l.LaneNumber /* Sort by lane number for logical order */`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Return successful response with lanes data
      res.json({
        success: true,
        lanes: result.recordset,
        message: 'Lanes retrieved successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in getLanesByPlaza controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lanes for plaza',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## CREATE NEW LANE
   * ===============================================================================
   *
   * Creates a new lane entry in the database with all associated configuration parameters.
   * Validates required fields before attempting to create the lane.
   *
   * @param {Object} req - Express request object with lane data in body
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message or error details
   */
  createLane: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // DEBUGGING: Log incoming request data
      // -------------------------------------------------------------------------------
      console.log('=== LANE CREATION REQUEST ===');
      console.log('Request body:', JSON.stringify(req.body, null, 2));
      console.log('User:', req.user ? { id: req.user.id, role: req.user.role } : 'No user');
      console.log('Request headers:', {
        'content-type': req.headers['content-type'],
        'authorization': req.headers.authorization ? 'Present' : 'Missing'
      });

      // -------------------------------------------------------------------------------
      // Extract all possible lane parameters from request body
      // -------------------------------------------------------------------------------
      const {
        // Core lane identification and configuration
        PlazaID,            // Plaza where lane is located
        CompanyID,          // Company that owns/operates the lane
        LaneNumber,         // Unique number identifying the lane
        LaneType,           // Type of lane (Entry/Exit)
        LaneDetails,        // Additional details about the lane
        TypeCode,           // Code indicating lane type
        DataForPrint,       // Data to be printed on receipts
        LaneIP,             // IP address of the lane controller
        VehicleType,        // Type of vehicle the lane serves
        UpdatedBy,          // User who last updated the lane
        ActiveStatus,       // Whether the lane is active (true/false)

        // Display and interface configuration
        iDisplayComport,    // COM port for display
        DisplayPole,        // Display pole configuration
        CashDrawer,         // Cash drawer configuration

        // Lane operation configuration
        MultipleExit,       // Whether multiple exits are allowed
        Antipassback,       // Anti-passback configuration
        HFPasscard,         // High-frequency pass card configuration
        HFPassPort,         // High-frequency pass port configuration
        CoinReaderPort,     // Port for coin reader
        APS_Exit,           // Automated parking system exit configuration

        // Feature flags
        flgKioskCamera,     // Kiosk camera enabled flag
        flgReceiptPrint,    // Receipt printing enabled flag
        flgGKeyDetails,     // G-key details flag
        flgCKeyCard,        // C-key card flag
        flgP4S,             // P4S feature flag
        LOTFee,             // LOT fee configuration
        flgPasscard,        // Pass card enabled flag

        // Payment gateway configuration
        PGTID,              // Payment gateway ID
        pgActivationKey,    // Payment gateway activation key
        Passcard_Reader_Type, // Type of pass card reader
        PayTmPG,            // PayTm payment gateway configuration

        // Camera configuration
        FlgLPRCamera,       // License plate recognition camera flag
        LPRCamIP,           // LPR camera IP address
        LPRCamID,           // LPR camera ID
        LPRCamPass,         // LPR camera password

        // Additional configuration
        iGraceMinute,       // Grace period in minutes
        flgPaperSensor,     // Paper sensor flag
        PGSLevel,           // PGS level configuration
        PrinterMake,        // Printer manufacturer
        BarcodeType,        // Type of barcode used
        PrinterPort,        // Printer port configuration

        // Payment configuration
        sPaytmWallet,       // PayTm wallet configuration
        sPaytmMID,          // PayTm merchant ID
        sPaytmKey,          // PayTm key
        fRecyclerStatus,    // Recycler status
        sSMSKey,            // SMS key for notifications

        // Additional lane configuration
        flgCCUpdateEx,      // CC update exchange flag
        LaneNumber2,        // Secondary lane number
        VehicleType2,       // Secondary vehicle type
        flgSubLane,         // Sub-lane flag
        RecyclerType        // Type of recycler
      } = req.body;

      // -------------------------------------------------------------------------------
      // Validate required fields
      // -------------------------------------------------------------------------------
      if (!PlazaID || !CompanyID || !LaneNumber || !LaneType) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing',
          requiredFields: ['PlazaID', 'CompanyID', 'LaneNumber', 'LaneType']
        });
      }

      // -------------------------------------------------------------------------------
      // Validate field lengths and formats to prevent database truncation errors
      // -------------------------------------------------------------------------------
      const validationErrors = [];

      // Validate TypeCode (char(2) in database)
      if (TypeCode && TypeCode.length > 2) {
        validationErrors.push({
          field: 'TypeCode',
          error: `TypeCode must be 2 characters or less. Current length: ${TypeCode.length}`,
          maxLength: 2,
          currentValue: TypeCode
        });
      }

      // Validate LaneNumber (char(2) in database)
      if (LaneNumber && LaneNumber.toString().length > 2) {
        validationErrors.push({
          field: 'LaneNumber',
          error: `LaneNumber must be 2 characters or less. Current length: ${LaneNumber.toString().length}`,
          maxLength: 2,
          currentValue: LaneNumber
        });
      }

      // Validate LaneType (char(30) in database)
      if (LaneType && LaneType.length > 30) {
        validationErrors.push({
          field: 'LaneType',
          error: `LaneType must be 30 characters or less. Current length: ${LaneType.length}`,
          maxLength: 30,
          currentValue: LaneType.substring(0, 50) + '...' // Show first 50 chars for context
        });
      }

      // Validate LaneDetails (char(50) in database)
      if (LaneDetails && LaneDetails.length > 50) {
        validationErrors.push({
          field: 'LaneDetails',
          error: `LaneDetails must be 50 characters or less. Current length: ${LaneDetails.length}`,
          maxLength: 50,
          currentValue: LaneDetails.substring(0, 50) + '...'
        });
      }

      // Validate DataForPrint (char(30) in database)
      if (DataForPrint && DataForPrint.length > 30) {
        validationErrors.push({
          field: 'DataForPrint',
          error: `DataForPrint must be 30 characters or less. Current length: ${DataForPrint.length}`,
          maxLength: 30,
          currentValue: DataForPrint.substring(0, 30) + '...'
        });
      }

      // Validate LaneIP (char(16) in database)
      if (LaneIP && LaneIP.length > 16) {
        validationErrors.push({
          field: 'LaneIP',
          error: `LaneIP must be 16 characters or less. Current length: ${LaneIP.length}`,
          maxLength: 16,
          currentValue: LaneIP
        });
      }

      // Validate VehicleType (char(25) in database)
      if (VehicleType && VehicleType.length > 25) {
        validationErrors.push({
          field: 'VehicleType',
          error: `VehicleType must be 25 characters or less. Current length: ${VehicleType.length}`,
          maxLength: 25,
          currentValue: VehicleType.substring(0, 25) + '...'
        });
      }

      // Validate UpdatedBy (char(10) in database)
      if (UpdatedBy && UpdatedBy.toString().length > 10) {
        validationErrors.push({
          field: 'UpdatedBy',
          error: `UpdatedBy must be 10 characters or less. Current length: ${UpdatedBy.toString().length}`,
          maxLength: 10,
          currentValue: UpdatedBy
        });
      }

      // Validate ActiveStatus (char(1) in database) - Convert boolean to Y/N
      let processedActiveStatus = ActiveStatus;
      if (typeof ActiveStatus === 'boolean') {
        processedActiveStatus = ActiveStatus ? 'Y' : 'N';
        console.log(`🔄 ActiveStatus: ${ActiveStatus} (boolean) → "${processedActiveStatus}" (string)`);
      } else if (typeof ActiveStatus === 'string') {
        // Handle string boolean values
        if (ActiveStatus.toLowerCase() === 'true') {
          processedActiveStatus = 'Y';
        } else if (ActiveStatus.toLowerCase() === 'false') {
          processedActiveStatus = 'N';
        }
        console.log(`🔄 ActiveStatus: "${ActiveStatus}" (string) → "${processedActiveStatus}" (processed)`);
      }
      
      if (processedActiveStatus && processedActiveStatus.toString().length > 1) {
        validationErrors.push({
          field: 'ActiveStatus',
          error: `ActiveStatus must be 1 character. Current length: ${processedActiveStatus.toString().length}`,
          maxLength: 1,
          currentValue: processedActiveStatus
        });
      }

      // Additional validations for other char fields
      const charFieldValidations = [
        { field: 'iDisplayComport', value: iDisplayComport, maxLength: 1 },
        { field: 'DisplayPole', value: DisplayPole, maxLength: 5 },
        { field: 'CashDrawer', value: CashDrawer, maxLength: 5 },
        { field: 'MultipleExit', value: MultipleExit, maxLength: 5 },
        { field: 'Antipassback', value: Antipassback, maxLength: 5 },
        { field: 'HFPasscard', value: HFPasscard, maxLength: 5 },
        { field: 'HFPassPort', value: HFPassPort, maxLength: 2 },
        { field: 'CoinReaderPort', value: CoinReaderPort, maxLength: 2 },
        { field: 'APS_Exit', value: APS_Exit, maxLength: 5 },
        { field: 'flgKioskCamera', value: flgKioskCamera, maxLength: 5 },
        { field: 'flgReceiptPrint', value: flgReceiptPrint, maxLength: 5 },
        { field: 'flgGKeyDetails', value: flgGKeyDetails, maxLength: 5 },
        { field: 'flgCKeyCard', value: flgCKeyCard, maxLength: 5 },
        { field: 'flgP4S', value: flgP4S, maxLength: 5 },
        { field: 'flgPasscard', value: flgPasscard, maxLength: 1 },
        { field: 'PGTID', value: PGTID, maxLength: 8 },
        { field: 'pgActivationKey', value: pgActivationKey, maxLength: 16 },
        { field: 'Passcard_Reader_Type', value: Passcard_Reader_Type, maxLength: 5 },
        { field: 'PayTmPG', value: PayTmPG, maxLength: 5 },
        { field: 'FlgLPRCamera', value: FlgLPRCamera, maxLength: 5 },
        { field: 'LPRCamIP', value: LPRCamIP, maxLength: 15 },
        { field: 'LPRCamID', value: LPRCamID, maxLength: 10 },
        { field: 'LPRCamPass', value: LPRCamPass, maxLength: 10 },
        { field: 'flgPaperSensor', value: flgPaperSensor, maxLength: 1 },
        { field: 'PGSLevel', value: PGSLevel, maxLength: 5 },
        { field: 'PrinterMake', value: PrinterMake, maxLength: 10 },
        { field: 'BarcodeType', value: BarcodeType, maxLength: 2 },
        { field: 'PrinterPort', value: PrinterPort, maxLength: 2 },
        { field: 'sPaytmWallet', value: sPaytmWallet, maxLength: 5 },
        { field: 'sPaytmMID', value: sPaytmMID, maxLength: 50 },
        { field: 'sPaytmKey', value: sPaytmKey, maxLength: 25 },
        { field: 'fRecyclerStatus', value: fRecyclerStatus, maxLength: 5 },
        { field: 'sSMSKey', value: sSMSKey, maxLength: 100 },
        { field: 'flgCCUpdateEx', value: flgCCUpdateEx, maxLength: 1 },
        { field: 'LaneNumber2', value: LaneNumber2, maxLength: 2 },
        { field: 'VehicleType2', value: VehicleType2, maxLength: 25 },
        { field: 'flgSubLane', value: flgSubLane, maxLength: 1 },
        { field: 'RecyclerType', value: RecyclerType, maxLength: 25 }
      ];

      charFieldValidations.forEach(validation => {
        if (validation.value && validation.value.toString().length > validation.maxLength) {
          validationErrors.push({
            field: validation.field,
            error: `${validation.field} must be ${validation.maxLength} characters or less. Current length: ${validation.value.toString().length}`,
            maxLength: validation.maxLength,
            currentValue: validation.value.toString().substring(0, Math.min(50, validation.value.toString().length)) + (validation.value.toString().length > 50 ? '...' : '')
          });
        }
      });

      // If there are validation errors, return them to the user
      if (validationErrors.length > 0) {
        console.log('❌ VALIDATION FAILED:');
        console.log('Number of validation errors:', validationErrors.length);
        validationErrors.forEach((error, index) => {
          console.log(`${index + 1}. ${error.field}: ${error.error}`);
          console.log(`   Current Value: "${error.currentValue}"`);
          console.log(`   Max Length: ${error.maxLength}`);
        });
        
        const response = {
          success: false,
          error: 'Field validation failed',
          message: 'One or more fields exceed the maximum allowed length',
          validationErrors: validationErrors,
          suggestion: 'Please reduce the length of the specified fields and try again'
        };
        
        console.log('Sending validation error response:', JSON.stringify(response, null, 2));
        return res.status(400).json(response);
      }
      
      console.log('✅ VALIDATION PASSED: All fields are within length limits');

      // -------------------------------------------------------------------------------
      // Check user permissions based on role
      // -------------------------------------------------------------------------------
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // Check if CompanyAdmin has access to this company
          const companyAccessQuery = `
            SELECT COUNT(*) as count
            FROM UserCompany
            WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
          `;

          const companyAccessResult = await db.query(companyAccessQuery, {
            userId: req.user.id,
            companyId: CompanyID
          });

          if (companyAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a lane for this company'
            });
          }

          // Check if CompanyAdmin has access to this plaza
          const plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE uc.UserId = @userId AND p.Id = @plazaId AND uc.IsActive = 1 AND p.IsActive = 1
          `;

          const plazaAccessResult = await db.query(plazaAccessQuery, {
            userId: req.user.id,
            plazaId: PlazaID
          });

          if (plazaAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a lane for this plaza'
            });
          }
        } else if (req.user.role === 'PlazaManager') {
          return res.status(403).json({
            success: false,
            message: 'Plaza Managers cannot create lanes'
          });
        }
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : (UpdatedBy || 1);

      // -------------------------------------------------------------------------------
      // Insert new lane into database
      // -------------------------------------------------------------------------------
      const result = await db.query(`
        INSERT INTO tblLaneDetails (
          /* Core lane identification fields */
          PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode,
          DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus,

          /* Display and interface configuration */
          iDisplayComport, DisplayPole, CashDrawer, MultipleExit, Antipassback,

          /* Lane operation configuration */
          HFPasscard, HFPassPort, CoinReaderPort, APS_Exit, flgKioskCamera,

          /* Feature flags and payment configuration */
          flgReceiptPrint, flgGKeyDetails, flgCKeyCard, flgP4S, LOTFee,
          flgPasscard, PGTID, pgActivationKey, Passcard_Reader_Type, PayTmPG,

          /* Camera configuration */
          FlgLPRCamera, LPRCamIP, LPRCamID, LPRCamPass, iGraceMinute,

          /* Additional configuration */
          flgPaperSensor, PGSLevel, PrinterMake, BarcodeType, PrinterPort,
          sPaytmWallet, sPaytmMID, sPaytmKey, fRecyclerStatus, sSMSKey,

          /* Additional lane configuration */
          flgCCUpdateEx, LaneNumber2, VehicleType2, flgSubLane, RecyclerType,

          /* Timestamp for creation */
          UpdatedDateTime
        ) VALUES (
          @PlazaID, @CompanyID, @LaneNumber, @LaneType, @LaneDetails, @TypeCode,
          @DataForPrint, @LaneIP, @VehicleType, @UpdatedBy, @ActiveStatus,
          @iDisplayComport, @DisplayPole, @CashDrawer, @MultipleExit, @Antipassback,
          @HFPasscard, @HFPassPort, @CoinReaderPort, @APS_Exit, @flgKioskCamera,
          @flgReceiptPrint, @flgGKeyDetails, @flgCKeyCard, @flgP4S, @LOTFee,
          @flgPasscard, @PGTID, @pgActivationKey, @Passcard_Reader_Type, @PayTmPG,
          @FlgLPRCamera, @LPRCamIP, @LPRCamID, @LPRCamPass, @iGraceMinute,
          @flgPaperSensor, @PGSLevel, @PrinterMake, @BarcodeType, @PrinterPort,
          @sPaytmWallet, @sPaytmMID, @sPaytmKey, @fRecyclerStatus, @sSMSKey,
          @flgCCUpdateEx, @LaneNumber2, @VehicleType2, @flgSubLane, @RecyclerType,
          GETDATE()
        )
      `, {
        // Core lane identification fields with validation
        PlazaID,
        CompanyID,
        LaneNumber,
        LaneType,
        LaneDetails: LaneDetails || '',
        TypeCode: TypeCode || null,
        DataForPrint: DataForPrint || null,
        LaneIP: LaneIP || null,
        VehicleType: VehicleType || '',
        UpdatedBy: updatedBy, // Use the current user ID
        ActiveStatus: processedActiveStatus !== undefined ? processedActiveStatus : 'Y', // Use processed status (Y/N)

        // Optional fields with null fallback for all other parameters
        iDisplayComport: iDisplayComport || '',
        DisplayPole: DisplayPole !== undefined ? DisplayPole : false,
        CashDrawer: CashDrawer !== undefined ? CashDrawer : false,
        MultipleExit: MultipleExit !== undefined ? MultipleExit : false,
        Antipassback: Antipassback !== undefined ? Antipassback : false,
        HFPasscard: HFPasscard !== undefined ? HFPasscard : false,
        HFPassPort: HFPassPort || '',
        CoinReaderPort: CoinReaderPort || '',
        APS_Exit: APS_Exit !== undefined ? APS_Exit : false,
        flgKioskCamera: flgKioskCamera !== undefined ? flgKioskCamera : false,
        flgReceiptPrint: flgReceiptPrint !== undefined ? flgReceiptPrint : false,
        flgGKeyDetails: flgGKeyDetails !== undefined ? flgGKeyDetails : false,
        flgCKeyCard: flgCKeyCard !== undefined ? flgCKeyCard : false,
        flgP4S: flgP4S !== undefined ? flgP4S : false,
        LOTFee: LOTFee || 0,
        flgPasscard: flgPasscard !== undefined ? (flgPasscard ? 'Y' : 'N') : 'N',
        PGTID: PGTID || '12345678',
        pgActivationKey: pgActivationKey || '1234567812345678',
        Passcard_Reader_Type: Passcard_Reader_Type || 'False',
        PayTmPG: PayTmPG !== undefined ? (PayTmPG ? 'True' : 'False') : 'False',
        FlgLPRCamera: FlgLPRCamera !== undefined ? (FlgLPRCamera ? 'True' : 'False') : 'False',
        LPRCamIP: LPRCamIP || '*******',
        LPRCamID: LPRCamID || 'NA',
        LPRCamPass: LPRCamPass || 'NA',
        iGraceMinute: iGraceMinute || 0,
        flgPaperSensor: flgPaperSensor !== undefined ? (flgPaperSensor ? 'Y' : 'N') : 'N',
        PGSLevel: PGSLevel || 'L1',
        PrinterMake: PrinterMake || 'NA',
        BarcodeType: BarcodeType || 'NA',
        PrinterPort: PrinterPort || '00',
        sPaytmWallet: sPaytmWallet || 'False',
        sPaytmMID: sPaytmMID || 'NA',
        sPaytmKey: sPaytmKey || 'NA',
        fRecyclerStatus: fRecyclerStatus !== undefined ? (fRecyclerStatus ? 'True' : 'False') : 'False',
        sSMSKey: sSMSKey || 'NA',
        flgCCUpdateEx: flgCCUpdateEx !== undefined ? (flgCCUpdateEx ? 'Y' : 'N') : 'N',
        LaneNumber2: LaneNumber2 || '0',
        VehicleType2: VehicleType2 || 'NA',
        flgSubLane: flgSubLane !== undefined ? (flgSubLane ? 'Y' : 'N') : 'N',
        RecyclerType: RecyclerType || 'NA'
      });

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      console.log('✅ LANE CREATED SUCCESSFULLY');
      console.log('Lane ID:', result.recordset[0]?.LaneID || 'Unknown');
      console.log('Response data:', { success: true, message: 'Lane created successfully' });
      
      res.status(201).json({
        success: true,
        message: 'Lane created successfully',
        result
      });
    } catch (error) {
      // Log error and return error response
      console.error('❌ CREATE LANE FAILED:');
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
      console.error('Error details:', error);
      
      res.status(500).json({ success: false, error: 'Failed to create lane', details: error.message });
    }
  },

  /**
   * ===============================================================================
   * ## UPDATE EXISTING LANE
   * ===============================================================================
   *
   * Updates an existing lane's configuration in the database.
   * Validates required fields and checks if the lane exists before updating.
   *
   * @param {Object} req - Express request object with lane ID in params and updated data in body
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message or error details
   */
  updateLane: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract lane ID and updated data
      // -------------------------------------------------------------------------------
      const { id } = req.params;
      const {
        // Core lane identification and configuration
        PlazaID,            // Plaza where lane is located
        CompanyID,          // Company that owns/operates the lane
        LaneNumber,         // Unique number identifying the lane
        LaneType,           // Type of lane (Entry/Exit)
        LaneDetails,        // Additional details about the lane
        TypeCode,           // Code indicating lane type
        DataForPrint,       // Data to be printed on receipts
        LaneIP,             // IP address of the lane controller
        VehicleType,        // Type of vehicle the lane serves
        UpdatedBy,          // User who last updated the lane
        ActiveStatus,       // Whether the lane is active (true/false)

        // Display and interface configuration
        iDisplayComport,    // COM port for display
        DisplayPole,        // Display pole configuration
        CashDrawer,         // Cash drawer configuration

        // Lane operation configuration
        MultipleExit,       // Whether multiple exits are allowed
        Antipassback,       // Anti-passback configuration
        HFPasscard,         // High-frequency pass card configuration
        HFPassPort,         // High-frequency pass port configuration
        CoinReaderPort,     // Port for coin reader
        APS_Exit,           // Automated parking system exit configuration

        // Feature flags
        flgKioskCamera,     // Kiosk camera enabled flag
        flgReceiptPrint,    // Receipt printing enabled flag
        flgGKeyDetails,     // G-key details flag
        flgCKeyCard,        // C-key card flag
        flgP4S,             // P4S feature flag
        LOTFee,             // LOT fee configuration
        flgPasscard,        // Pass card enabled flag

        // Payment gateway configuration
        PGTID,              // Payment gateway ID
        pgActivationKey,    // Payment gateway activation key
        Passcard_Reader_Type, // Type of pass card reader
        PayTmPG,            // PayTm payment gateway configuration

        // Camera configuration
        FlgLPRCamera,       // License plate recognition camera flag
        LPRCamIP,           // LPR camera IP address
        LPRCamID,           // LPR camera ID
        LPRCamPass,         // LPR camera password

        // Additional configuration
        iGraceMinute,       // Grace period in minutes
        flgPaperSensor,     // Paper sensor flag
        PGSLevel,           // PGS level configuration
        PrinterMake,        // Printer manufacturer
        BarcodeType,        // Type of barcode used
        PrinterPort,        // Printer port configuration

        // Payment configuration
        sPaytmWallet,       // PayTm wallet configuration
        sPaytmMID,          // PayTm merchant ID
        sPaytmKey,          // PayTm key
        fRecyclerStatus,    // Recycler status
        sSMSKey,            // SMS key for notifications

        // Additional lane configuration
        flgCCUpdateEx,      // CC update exchange flag
        LaneNumber2,        // Secondary lane number
        VehicleType2,       // Secondary vehicle type
        flgSubLane,         // Sub-lane flag
        RecyclerType        // Type of recycler
      } = req.body;

      // -------------------------------------------------------------------------------
      // Validate required fields
      // -------------------------------------------------------------------------------
      if (!PlazaID || !CompanyID || !LaneNumber || !LaneType) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing',
          requiredFields: ['PlazaID', 'CompanyID', 'LaneNumber', 'LaneType']
        });
      }

      // -------------------------------------------------------------------------------
      // Validate field lengths and formats to prevent database truncation errors
      // -------------------------------------------------------------------------------
      const validationErrors = [];

      // Validate TypeCode (char(2) in database)
      if (TypeCode && TypeCode.length > 2) {
        validationErrors.push({
          field: 'TypeCode',
          error: `TypeCode must be 2 characters or less. Current length: ${TypeCode.length}`,
          maxLength: 2,
          currentValue: TypeCode
        });
      }

      // Validate LaneNumber (char(2) in database)
      if (LaneNumber && LaneNumber.toString().length > 2) {
        validationErrors.push({
          field: 'LaneNumber',
          error: `LaneNumber must be 2 characters or less. Current length: ${LaneNumber.toString().length}`,
          maxLength: 2,
          currentValue: LaneNumber
        });
      }

      // Validate LaneType (char(30) in database)
      if (LaneType && LaneType.length > 30) {
        validationErrors.push({
          field: 'LaneType',
          error: `LaneType must be 30 characters or less. Current length: ${LaneType.length}`,
          maxLength: 30,
          currentValue: LaneType.substring(0, 50) + '...'
        });
      }

      // Validate LaneDetails (char(50) in database)
      if (LaneDetails && LaneDetails.length > 50) {
        validationErrors.push({
          field: 'LaneDetails',
          error: `LaneDetails must be 50 characters or less. Current length: ${LaneDetails.length}`,
          maxLength: 50,
          currentValue: LaneDetails.substring(0, 50) + '...'
        });
      }

      // Validate other critical fields
      const updateCharFieldValidations = [
        { field: 'DataForPrint', value: DataForPrint, maxLength: 30 },
        { field: 'LaneIP', value: LaneIP, maxLength: 16 },
        { field: 'VehicleType', value: VehicleType, maxLength: 25 },
        { field: 'UpdatedBy', value: UpdatedBy, maxLength: 10 },
        { field: 'ActiveStatus', value: ActiveStatus, maxLength: 1 },
        { field: 'TypeCode', value: TypeCode, maxLength: 2 },
        { field: 'iDisplayComport', value: iDisplayComport, maxLength: 1 },
        { field: 'DisplayPole', value: DisplayPole, maxLength: 5 },
        { field: 'CashDrawer', value: CashDrawer, maxLength: 5 },
        { field: 'MultipleExit', value: MultipleExit, maxLength: 5 },
        { field: 'Antipassback', value: Antipassback, maxLength: 5 },
        { field: 'HFPasscard', value: HFPasscard, maxLength: 5 },
        { field: 'HFPassPort', value: HFPassPort, maxLength: 2 },
        { field: 'CoinReaderPort', value: CoinReaderPort, maxLength: 2 },
        { field: 'APS_Exit', value: APS_Exit, maxLength: 5 },
        { field: 'flgKioskCamera', value: flgKioskCamera, maxLength: 5 },
        { field: 'flgReceiptPrint', value: flgReceiptPrint, maxLength: 5 },
        { field: 'flgGKeyDetails', value: flgGKeyDetails, maxLength: 5 },
        { field: 'flgCKeyCard', value: flgCKeyCard, maxLength: 5 },
        { field: 'flgP4S', value: flgP4S, maxLength: 5 },
        { field: 'flgPasscard', value: flgPasscard, maxLength: 1 },
        { field: 'PGTID', value: PGTID, maxLength: 8 },
        { field: 'pgActivationKey', value: pgActivationKey, maxLength: 16 },
        { field: 'Passcard_Reader_Type', value: Passcard_Reader_Type, maxLength: 5 },
        { field: 'PayTmPG', value: PayTmPG, maxLength: 5 },
        { field: 'FlgLPRCamera', value: FlgLPRCamera, maxLength: 5 },
        { field: 'LPRCamIP', value: LPRCamIP, maxLength: 15 },
        { field: 'LPRCamID', value: LPRCamID, maxLength: 10 },
        { field: 'LPRCamPass', value: LPRCamPass, maxLength: 10 },
        { field: 'flgPaperSensor', value: flgPaperSensor, maxLength: 1 },
        { field: 'PGSLevel', value: PGSLevel, maxLength: 5 },
        { field: 'PrinterMake', value: PrinterMake, maxLength: 10 },
        { field: 'BarcodeType', value: BarcodeType, maxLength: 2 },
        { field: 'PrinterPort', value: PrinterPort, maxLength: 2 },
        { field: 'sPaytmWallet', value: sPaytmWallet, maxLength: 5 },
        { field: 'sPaytmMID', value: sPaytmMID, maxLength: 50 },
        { field: 'sPaytmKey', value: sPaytmKey, maxLength: 25 },
        { field: 'fRecyclerStatus', value: fRecyclerStatus, maxLength: 5 },
        { field: 'sSMSKey', value: sSMSKey, maxLength: 100 },
        { field: 'flgCCUpdateEx', value: flgCCUpdateEx, maxLength: 1 },
        { field: 'LaneNumber2', value: LaneNumber2, maxLength: 2 },
        { field: 'VehicleType2', value: VehicleType2, maxLength: 25 },
        { field: 'flgSubLane', value: flgSubLane, maxLength: 1 },
        { field: 'RecyclerType', value: RecyclerType, maxLength: 25 }
      ];

      updateCharFieldValidations.forEach(validation => {
        if (validation.value && validation.value.toString().length > validation.maxLength) {
          validationErrors.push({
            field: validation.field,
            error: `${validation.field} must be ${validation.maxLength} characters or less. Current length: ${validation.value.toString().length}`,
            maxLength: validation.maxLength,
            currentValue: validation.value.toString().substring(0, Math.min(50, validation.value.toString().length)) + (validation.value.toString().length > 50 ? '...' : '')
          });
        }
      });

      // If there are validation errors, return them to the user
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          error: 'Field validation failed',
          message: 'One or more fields exceed the maximum allowed length',
          validationErrors: validationErrors,
          suggestion: 'Please reduce the length of the specified fields and try again'
        });
      }

      // -------------------------------------------------------------------------------
      // Check if lane exists and user has access to it
      // -------------------------------------------------------------------------------
      let query = `
        SELECT l.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.LaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only update lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only update lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      const checkResult = await db.query(query, queryParams);

      if (checkResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have permission to update it'
        });
      }

      const existingLane = checkResult.recordset[0];

      // -------------------------------------------------------------------------------
      // Check user permissions for the new company/plaza if they're being changed
      // -------------------------------------------------------------------------------
      if (req.user && req.user.role !== 'SuperAdmin') {
        // If company is being changed, check if user has access to the new company
        if (CompanyID !== existingLane.CompanyID) {
          if (req.user.role === 'CompanyAdmin') {
            const companyAccessQuery = `
              SELECT COUNT(*) as count
              FROM UserCompany
              WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
            `;

            const companyAccessResult = await db.query(companyAccessQuery, {
              userId: req.user.id,
              companyId: CompanyID
            });

            if (companyAccessResult.recordset[0].count === 0) {
              return res.status(403).json({
                success: false,
                message: 'You do not have permission to move this lane to the specified company'
              });
            }
          } else if (req.user.role === 'PlazaManager') {
            return res.status(403).json({
              success: false,
              message: 'Plaza Managers cannot change the company of a lane'
            });
          }
        }

        // If plaza is being changed, check if user has access to the new plaza
        if (PlazaID !== existingLane.PlazaID) {
          if (req.user.role === 'CompanyAdmin') {
            const plazaAccessQuery = `
              SELECT COUNT(*) as count
              FROM Plaza p
              JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
              WHERE uc.UserId = @userId AND p.Id = @plazaId AND uc.IsActive = 1 AND p.IsActive = 1
            `;

            const plazaAccessResult = await db.query(plazaAccessQuery, {
              userId: req.user.id,
              plazaId: PlazaID
            });

            if (plazaAccessResult.recordset[0].count === 0) {
              return res.status(403).json({
                success: false,
                message: 'You do not have permission to move this lane to the specified plaza'
              });
            }
          } else if (req.user.role === 'PlazaManager') {
            const plazaAccessQuery = `
              SELECT COUNT(*) as count
              FROM UserPlaza
              WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
            `;

            const plazaAccessResult = await db.query(plazaAccessQuery, {
              userId: req.user.id,
              plazaId: PlazaID
            });

            if (plazaAccessResult.recordset[0].count === 0) {
              return res.status(403).json({
                success: false,
                message: 'You do not have permission to move this lane to the specified plaza'
              });
            }
          }
        }
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : (UpdatedBy || 1);

      // -------------------------------------------------------------------------------
      // Process ActiveStatus to ensure consistent format (Y/N)
      // -------------------------------------------------------------------------------
      let processedActiveStatus = ActiveStatus;
      if (typeof ActiveStatus === 'boolean') {
        processedActiveStatus = ActiveStatus ? 'Y' : 'N';
      } else if (typeof ActiveStatus === 'string') {
        // Handle string representations
        if (ActiveStatus.toLowerCase() === 'true' || ActiveStatus === '1') {
          processedActiveStatus = 'Y';
        } else if (ActiveStatus.toLowerCase() === 'false' || ActiveStatus === '0') {
          processedActiveStatus = 'N';
        }
        // If it's already 'Y' or 'N', keep it as is
      } else if (typeof ActiveStatus === 'number') {
        processedActiveStatus = ActiveStatus === 1 ? 'Y' : 'N';
      }

      // -------------------------------------------------------------------------------
      // Update lane in database
      // -------------------------------------------------------------------------------
      const result = await db.query(`
        UPDATE tblLaneDetails SET
          /* Core lane identification fields */
          PlazaID = @PlazaID,
          CompanyID = @CompanyID,
          LaneNumber = @LaneNumber,
          LaneType = @LaneType,
          LaneDetails = @LaneDetails,
          TypeCode = @TypeCode,
          DataForPrint = @DataForPrint,
          LaneIP = @LaneIP,
          VehicleType = @VehicleType,
          ActiveStatus = @ActiveStatus,

          /* Display and interface configuration */
          iDisplayComport = @iDisplayComport,
          DisplayPole = @DisplayPole,
          CashDrawer = @CashDrawer,
          MultipleExit = @MultipleExit,
          Antipassback = @Antipassback,

          /* Lane operation configuration */
          HFPasscard = @HFPasscard,
          HFPassPort = @HFPassPort,
          CoinReaderPort = @CoinReaderPort,
          APS_Exit = @APS_Exit,
          flgKioskCamera = @flgKioskCamera,

          /* Feature flags and payment configuration */
          flgReceiptPrint = @flgReceiptPrint,
          flgGKeyDetails = @flgGKeyDetails,
          flgCKeyCard = @flgCKeyCard,
          flgP4S = @flgP4S,
          LOTFee = @LOTFee,
          flgPasscard = @flgPasscard,
          PGTID = @PGTID,
          pgActivationKey = @pgActivationKey,
          Passcard_Reader_Type = @Passcard_Reader_Type,
          PayTmPG = @PayTmPG,

          /* Camera configuration */
          FlgLPRCamera = @FlgLPRCamera,
          LPRCamIP = @LPRCamIP,
          LPRCamID = @LPRCamID,
          LPRCamPass = @LPRCamPass,
          iGraceMinute = @iGraceMinute,

          /* Additional configuration */
          flgPaperSensor = @flgPaperSensor,
          PGSLevel = @PGSLevel,
          PrinterMake = @PrinterMake,
          BarcodeType = @BarcodeType,
          PrinterPort = @PrinterPort,
          sPaytmWallet = @sPaytmWallet,
          sPaytmMID = @sPaytmMID,
          sPaytmKey = @sPaytmKey,
          fRecyclerStatus = @fRecyclerStatus,
          sSMSKey = @sSMSKey,

          /* Additional lane configuration */
          flgCCUpdateEx = @flgCCUpdateEx,
          LaneNumber2 = @LaneNumber2,
          VehicleType2 = @VehicleType2,
          flgSubLane = @flgSubLane,
          RecyclerType = @RecyclerType,

          /* Update metadata */
          UpdatedBy = @UpdatedBy,
          UpdatedDateTime = GETDATE()
        WHERE LaneID = @id
      `, {
        // Lane ID for WHERE clause
        id,

        // Core lane identification fields with validation
        PlazaID,
        CompanyID,
        LaneNumber,
        LaneType,
        LaneDetails: LaneDetails || null,
        TypeCode: TypeCode || null,
        DataForPrint: DataForPrint || null,
        LaneIP: LaneIP || null,
        VehicleType: VehicleType || null,
        UpdatedBy: updatedBy, // Use the current user ID
        ActiveStatus: processedActiveStatus !== undefined ? processedActiveStatus : 'Y', // Use processed status (Y/N)

        // Optional fields with null fallback for all other parameters
        iDisplayComport: iDisplayComport || null,
        DisplayPole: DisplayPole || null,
        CashDrawer: CashDrawer || null,
        MultipleExit: MultipleExit || null,
        Antipassback: Antipassback || null,
        HFPasscard: HFPasscard || null,
        HFPassPort: HFPassPort || null,
        CoinReaderPort: CoinReaderPort || null,
        APS_Exit: APS_Exit || null,
        flgKioskCamera: flgKioskCamera || null,
        flgReceiptPrint: flgReceiptPrint || null,
        flgGKeyDetails: flgGKeyDetails || null,
        flgCKeyCard: flgCKeyCard || null,
        flgP4S: flgP4S || null,
        LOTFee: LOTFee || null,
        flgPasscard: flgPasscard || 'N',
        PGTID: PGTID || null,
        pgActivationKey: pgActivationKey || null,
        Passcard_Reader_Type: Passcard_Reader_Type || 'False',
        PayTmPG: PayTmPG || 'False',
        FlgLPRCamera: FlgLPRCamera || 'False',
        LPRCamIP: LPRCamIP || null,
        LPRCamID: LPRCamID || null,
        LPRCamPass: LPRCamPass || null,
        iGraceMinute: iGraceMinute || 0,
        flgPaperSensor: flgPaperSensor || 'N',
        PGSLevel: PGSLevel || 'L1',
        PrinterMake: PrinterMake || 'NA',
        BarcodeType: BarcodeType || 'NA',
        PrinterPort: PrinterPort || '00',
        sPaytmWallet: sPaytmWallet || null,
        sPaytmMID: sPaytmMID || null,
        sPaytmKey: sPaytmKey || null,
        fRecyclerStatus: fRecyclerStatus || 'False',
        sSMSKey: sSMSKey || 'NA',
        flgCCUpdateEx: flgCCUpdateEx || 'N',
        LaneNumber2: LaneNumber2 || '0',
        VehicleType2: VehicleType2 || 'NA',
        flgSubLane: flgSubLane || 'N',
        RecyclerType: RecyclerType || null
      });

      // -------------------------------------------------------------------------------
      // Check if update was successful
      // -------------------------------------------------------------------------------
      if (result.rowsAffected[0] === 0) {
        return res.status(404).json({ success: false, error: 'Lane not found or no changes made' });
      }

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      res.json({
        success: true,
        message: 'Lane updated successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Update lane failed:', error.message);
      res.status(500).json({ success: false, error: 'Failed to update lane', details: error.message });
    }
  },

  /**
   * ===============================================================================
   * ## DELETE LANE
   * ===============================================================================
   *
   * Permanently removes a lane from the database.
   * This is a destructive operation that cannot be undone.
   *
   * @param {Object} req - Express request object with lane ID in params
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message or error
   */
  deleteLane: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract lane ID from request parameters
      // -------------------------------------------------------------------------------
      const { id } = req.params;

      // -------------------------------------------------------------------------------
      // Check if lane exists and user has access to it
      // -------------------------------------------------------------------------------
      let query = `
        SELECT l.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.LaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only delete lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager cannot delete lanes
          return res.status(403).json({
            success: false,
            message: 'Plaza Managers cannot delete lanes'
          });
        }
      }

      const checkResult = await db.query(query, queryParams);

      if (checkResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have permission to delete it'
        });
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : 1;

      // -------------------------------------------------------------------------------
      // Execute soft delete operation (update ActiveStatus to 'N')
      // -------------------------------------------------------------------------------
      const result = await db.query(`
        UPDATE tblLaneDetails
        SET ActiveStatus = 'N',
            UpdatedBy = @updatedBy,
            UpdatedDateTime = GETDATE()
        WHERE LaneID = @id
      `, {
        id,
        updatedBy
      });

      // Check if any rows were affected
      if (result.rowsAffected[0] === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or already deleted'
        });
      }

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      res.json({
        success: true,
        message: 'Lane deleted successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in deleteLane controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to delete lane',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## TOGGLE LANE ACTIVE STATUS
   * ===============================================================================
   *
   * Toggles a lane's active status between active (1) and inactive (0).
   * This endpoint handles the activation/deactivation of lanes in the system.
   *
   * @param {Object} req - Express request object with lane ID in params and UpdatedBy in body
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message and new status
   */
  toggleLaneStatus: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract parameters
      // -------------------------------------------------------------------------------
      const { id } = req.params;

      // -------------------------------------------------------------------------------
      // Check if lane exists and user has access to it
      // -------------------------------------------------------------------------------
      let query = `
        SELECT l.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.LaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only toggle lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only toggle lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      const statusResult = await db.query(query, queryParams);

      // Check if lane exists and user has access to it
      if (statusResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have permission to modify it'
        });
      }

      // -------------------------------------------------------------------------------
      // Determine new status value
      // -------------------------------------------------------------------------------
      // Get current status (could be 'Y'/'N', '1'/'0', 1/0, or true/false)
      const currentStatus = statusResult.recordset[0].ActiveStatus;
      console.log('Current status from database:', currentStatus, typeof currentStatus);

      // Determine if current status is "active" (true, 1, '1', 'Y', 'true')
      const isCurrentlyActive = currentStatus === true || 
                               currentStatus === 1 || 
                               currentStatus === '1' || 
                               currentStatus === 'Y' || 
                               currentStatus === 'true';

      // Toggle status - if currently active, make inactive; if inactive, make active
      // Use the same format as the database expects (based on current value format)
      let newStatus;
      if (typeof currentStatus === 'string') {
        if (currentStatus === 'Y' || currentStatus === 'N') {
          // Database uses Y/N format
          newStatus = isCurrentlyActive ? 'N' : 'Y';
        } else {
          // Database uses '1'/'0' string format
          newStatus = isCurrentlyActive ? '0' : '1';
        }
      } else if (typeof currentStatus === 'boolean') {
        // Database uses boolean format
        newStatus = !isCurrentlyActive;
      } else {
        // Database uses integer format
        newStatus = isCurrentlyActive ? 0 : 1;
      }
      
      console.log('New status to set:', newStatus, typeof newStatus);

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : 1;

      // -------------------------------------------------------------------------------
      // Update lane status in database
      // -------------------------------------------------------------------------------
      await db.query(`
        UPDATE tblLaneDetails
        SET ActiveStatus = @newStatus,        /* New status value (0 or 1) */
            UpdatedBy = @updatedBy,           /* User who made the change */
            UpdatedDateTime = GETDATE()       /* Timestamp of the change */
        WHERE LaneID = @id
      `, {
        id,
        newStatus,
        updatedBy
      });

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      // Determine if new status is "active" for response
      const isNewStatusActive = newStatus === true || 
                               newStatus === 1 || 
                               newStatus === '1' || 
                               newStatus === 'Y' || 
                               newStatus === 'true';

      res.json({
        success: true,
        message: `Lane ${isNewStatusActive ? 'activated' : 'deactivated'} successfully`,
        newStatus: isNewStatusActive, // Boolean for frontend compatibility
        newStatusValue: newStatus,    // Actual value from database
        laneId: id                    // Include the lane ID for frontend state updates
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in toggleLaneStatus controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle lane status',
        message: error.message
      });
    }
  }
};

module.exports = laneController;
