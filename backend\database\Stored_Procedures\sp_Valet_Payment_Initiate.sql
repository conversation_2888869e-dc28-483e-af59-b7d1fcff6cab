-- =============================================
-- Author: Valet System
-- Create date: 2024-12-07
-- Description: Initiate payment process for valet transaction
-- =============================================
CREATE OR ALTER PROCEDURE sp_Valet_Payment_Initiate
    @TransactionId INT,
    @PaymentMethod NVARCHAR(50),
    @Amount DECIMAL(10,2),
    @CustomerId INT,
    @CustomerName NVARCHAR(100) = NULL,
    @CustomerEmail NVARCHAR(100) = NULL,
    @CustomerMobile NVARCHAR(15) = NULL,
    @CreatedBy INT,
    @PaymentId INT OUTPUT,
    @OrderId NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate transaction exists
        IF NOT EXISTS (SELECT 1 FROM ParkingTransactions WHERE Id = @TransactionId)
        BEGIN
            RAISERROR('Transaction not found', 16, 1);
            RETURN;
        END
        
        -- Validate customer exists
        IF NOT EXISTS (SELECT 1 FROM Customer WHERE Id = @CustomerId AND IsActive = 1)
        BEGIN
            RAISERROR('Customer not found or inactive', 16, 1);
            RETURN;
        END
        
        -- Validate amount
        IF @Amount <= 0
        BEGIN
            RAISERROR('Amount must be greater than 0', 16, 1);
            RETURN;
        END
        
        -- Generate unique order ID
        SET @OrderId = 'VALET_' + CAST(GETDATE() AS NVARCHAR(20)) + '_' + CAST(@TransactionId AS NVARCHAR(10));
        SET @OrderId = REPLACE(REPLACE(REPLACE(@OrderId, ' ', ''), ':', ''), '-', '');
        
        -- Insert payment record
        INSERT INTO PaymentGatewayTransactions (
            TransactionId,
            GatewayType,
            GatewayOrderId,
            Amount,
            Currency,
            PaymentMethod,
            Status,
            CustomerName,
            CustomerEmail,
            CustomerMobile,
            CreatedBy,
            CreatedOn
        )
        VALUES (
            @TransactionId,
            @PaymentMethod,
            @OrderId,
            @Amount,
            'INR',
            @PaymentMethod,
            CASE WHEN @PaymentMethod = 'CASH' THEN 'PENDING_CASH' ELSE 'PENDING' END,
            @CustomerName,
            @CustomerEmail,
            @CustomerMobile,
            @CreatedBy,
            GETDATE()
        );
        
        SET @PaymentId = SCOPE_IDENTITY();
        
        -- Update transaction status
        UPDATE ParkingTransactions
        SET Status = 'PAYMENT_INITIATED',
            ModifiedBy = @CreatedBy,
            ModifiedOn = GETDATE()
        WHERE Id = @TransactionId;
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
