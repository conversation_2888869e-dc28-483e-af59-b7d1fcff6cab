// Test the controller logic in complete isolation
const db = require('./backend/src/config/database');

// Mock response object
class MockResponse {
  constructor() {
    this.statusCode = 200;
    this.data = null;
    this.sent = false;
  }

  status(code) {
    this.statusCode = code;
    return this;
  }

  json(data) {
    if (this.sent) {
      console.log('⚠️  WARNING: Attempting to send response multiple times!');
      return;
    }
    this.data = data;
    this.sent = true;
    console.log('✅ Response sent:', JSON.stringify(data, null, 2));
    return this;
  }
}

// Mock request object
const mockReq = {
  user: { role: 'SuperAdmin' }
};

async function testControllerIsolated() {
  console.log('🔍 Testing controller logic in isolation...\n');

  try {
    // Test the exact controller logic
    console.log('1. Starting getModulesTree logic...');
    
    const mockRes = new MockResponse();

    // Replicate the exact controller logic
    console.log('PermissionManagementController - Getting modules tree');

    // First, let's try a simple query to test if the issue is with the complex query
    console.log('Testing simple modules query first...');
    const simpleQuery = 'SELECT Id, Name, Description, Icon, DisplayOrder, IsActive FROM Modules WHERE IsActive = 1';
    const simpleResult = await db.query(simpleQuery);
    console.log(`Simple query returned ${simpleResult.recordset.length} modules`);

    // If simple query works, try the complex one with timeout handling
    console.log('Now trying complex query...');
    const query = `
      SELECT 
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.Description as ModuleDescription,
        m.Icon as ModuleIcon,
        m.DisplayOrder as ModuleDisplayOrder,
        m.IsActive as ModuleIsActive,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        sm.Icon as SubModuleIcon,
        sm.IsActive as SubModuleIsActive,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        
        smp.Id as SubModulePermissionId
        
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId AND sm.IsActive = 1
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
      LEFT JOIN Permissions p ON smp.PermissionId = p.Id AND p.IsActive = 1
      WHERE m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.Id, p.Name
    `;

    const result = await db.query(query);
    console.log(`Complex query returned ${result.recordset.length} rows`);

    // If no results from complex query, return simple modules structure
    if (result.recordset.length === 0) {
      console.log('Complex query returned no results, falling back to simple structure');
      const fallbackModules = simpleResult.recordset.map(module => ({
        id: module.Id,
        name: module.Name,
        description: module.Description,
        icon: module.Icon,
        displayOrder: module.DisplayOrder,
        isActive: module.IsActive,
        subModules: []
      }));

      mockRes.json({
        success: true,
        data: fallbackModules,
        message: 'Modules tree retrieved successfully (fallback mode)'
      });
      
      console.log('✅ Fallback response sent successfully');
      return;
    }

    // Transform flat result into hierarchical structure
    const modulesMap = new Map();

    result.recordset.forEach(row => {
      // Create module if not exists
      if (!modulesMap.has(row.ModuleId)) {
        modulesMap.set(row.ModuleId, {
          id: row.ModuleId,
          name: row.ModuleName,
          description: row.ModuleDescription,
          icon: row.ModuleIcon,
          displayOrder: row.ModuleDisplayOrder,
          isActive: row.ModuleIsActive,
          subModules: new Map()
        });
      }

      const module = modulesMap.get(row.ModuleId);

      // Create submodule if not exists and if SubModuleId is not null
      if (row.SubModuleId && !module.subModules.has(row.SubModuleId)) {
        module.subModules.set(row.SubModuleId, {
          id: row.SubModuleId,
          name: row.SubModuleName,
          description: row.SubModuleName,
          route: row.SubModuleRoute,
          icon: row.SubModuleIcon,
          displayOrder: row.SubModuleId,
          isActive: row.SubModuleIsActive,
          permissions: []
        });
      }

      // Add permission if exists
      if (row.PermissionId && row.SubModuleId) {
        const subModule = module.subModules.get(row.SubModuleId);
        if (subModule && !subModule.permissions.find(p => p.id === row.PermissionId)) {
          subModule.permissions.push({
            id: row.PermissionId,
            name: row.PermissionName,
            description: row.PermissionDescription,
            subModulePermissionId: row.SubModulePermissionId
          });
        }
      }
    });

    // Convert Maps to Arrays for JSON response
    const modules = Array.from(modulesMap.values()).map(module => ({
      ...module,
      subModules: Array.from(module.subModules.values())
    }));

    console.log(`Transformed into ${modules.length} modules`);
    if (modules.length > 0) {
      console.log(`First module: ${modules[0].name} with ${modules[0].subModules.length} submodules`);
    }

    // Send the response
    mockRes.json({
      success: true,
      data: modules,
      message: 'Modules tree retrieved successfully'
    });

    console.log('✅ Main response sent successfully');

  } catch (error) {
    console.error('❌ Controller test failed:', error);
    const mockRes = new MockResponse();
    mockRes.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  } finally {
    await db.closePool();
    console.log('\n🏁 Controller isolation test completed');
    process.exit(0);
  }
}

testControllerIsolated();