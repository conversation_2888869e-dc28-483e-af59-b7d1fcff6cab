const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/valet';

// Test data
const testData = {
    mobileNumber: '9876543210',
    plazaId: 1,
    plazaValetPointId: 1,
    customerData: {
        firstName: '<PERSON>',
        lastName: 'Doe',
        email: '<EMAIL>',
        vehicleNumber: 'KA01AB1234',
        guestName: 'Jane <PERSON>',
        location: 'Main Entrance'
    }
};

let customerId = null;
let sessionId = null;
let transactionId = null;

// Helper function to make requests
const makeRequest = async (method, endpoint, data = null, useAuth = false) => {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: useAuth ? { 'Authorization': `Bearer fake-token-for-testing` } : {}
        };
        
        if (data) {
            config.data = data;
        }
        
        const response = await axios(config);
        return { success: true, data: response.data, status: response.status };
    } catch (error) {
        return { 
            success: false, 
            error: error.response?.data || error.message,
            status: error.response?.status || 500
        };
    }
};

// Test functions based on actual routes
const testEndpoints = {
    // 0. Health Check
    async testHealthCheck() {
        console.log('\n🔍 Testing Valet System Health...');
        const result = await makeRequest('GET', '/health');
        console.log('Health Check:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    // 1. OTP Endpoints (Working)
    async testOTPGeneration() {
        console.log('\n🔍 Testing OTP Generation...');
        const result = await makeRequest('POST', '/otp/generate', { 
            mobileNumber: testData.mobileNumber,
            plazaId: testData.plazaId 
        });
        console.log('OTP Generation:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    async testOTPVerification() {
        console.log('\n🔍 Testing OTP Verification...');
        const result = await makeRequest('POST', '/otp/verify', { 
            mobileNumber: testData.mobileNumber,
            otp: '123456' // Test OTP
        });
        console.log('OTP Verification:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    // 2. Customer Management (Public routes)
    async testCustomerRegistration() {
        console.log('\n🔍 Testing Customer Registration...');
        const result = await makeRequest('POST', '/customers/register', {
            mobileNumber: testData.mobileNumber,
            plazaValetPointId: testData.plazaValetPointId,
            ...testData.customerData
        });
        console.log('Customer Registration:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        if (result.success && result.data.customerId) {
            customerId = result.data.customerId;
        }
        return result;
    },

    async testGetCustomerByMobile() {
        console.log('\n🔍 Testing Get Customer by Mobile...');
        const result = await makeRequest('GET', `/customers/mobile/${testData.mobileNumber}`);
        console.log('Get Customer by Mobile:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    // 3. Transaction Endpoints (Public routes)
    async testCreateTransaction() {
        console.log('\n🔍 Testing Create Transaction...');
        const result = await makeRequest('POST', '/transactions/create', {
            mobileNumber: testData.mobileNumber,
            vehicleNumber: testData.customerData.vehicleNumber,
            plazaValetPointId: testData.plazaValetPointId,
            guestName: testData.customerData.guestName,
            location: testData.customerData.location,
            isAnyValuableItem: false
        });
        console.log('Create Transaction:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        if (result.success && result.data.transactionId) {
            transactionId = result.data.transactionId;
        }
        return result;
    },

    async testGetTransactionByIdentifier() {
        console.log('\n🔍 Testing Get Transaction by Identifier...');
        const result = await makeRequest('GET', '/transactions/search/123456'); // Test identifier
        console.log('Get Transaction by Identifier:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    // 4. QR Code Endpoints (Require Auth)
    async testQRCodeGeneration() {
        console.log('\n🔍 Testing QR Code Generation (with auth)...');
        const result = await makeRequest('POST', '/qrcode/generate', { 
            plazaId: testData.plazaId 
        }, true);
        console.log('QR Generation:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    async testQRCodeScanning() {
        console.log('\n🔍 Testing QR Code Scanning (with auth)...');
        const result = await makeRequest('GET', '/qrcode/scan/test-qr-data-plaza-1', null, true);
        console.log('QR Scanning:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    // 5. Payment Endpoints (Require Auth)
    async testGetPaymentOptions() {
        console.log('\n🔍 Testing Get Payment Options (with auth)...');
        const result = await makeRequest('GET', `/payments/options/${testData.plazaId}`, null, true);
        console.log('Get Payment Options:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    async testInitiatePayment() {
        console.log('\n🔍 Testing Initiate Payment (with auth)...');
        const result = await makeRequest('POST', '/payments/initiate', {
            customerId: customerId || 1,
            plazaId: testData.plazaId,
            amount: 100,
            paymentMethod: 'CASH'
        }, true);
        console.log('Initiate Payment:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    },

    // 6. Customer Flow Endpoints (Require Auth)
    async testInitializeCustomerFlow() {
        console.log('\n🔍 Testing Initialize Customer Flow (with auth)...');
        const result = await makeRequest('POST', '/flow/initialize', {
            customerId: customerId || 1,
            plazaValetPointId: testData.plazaValetPointId
        }, true);
        console.log('Initialize Customer Flow:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        if (result.success && result.data.sessionId) {
            sessionId = result.data.sessionId;
        }
        return result;
    },

    // 7. SMS Endpoints (Require Auth)
    async testSendOTPSMS() {
        console.log('\n🔍 Testing Send OTP SMS (with auth)...');
        const result = await makeRequest('POST', '/sms/send-otp', {
            mobileNumber: testData.mobileNumber,
            otp: '123456',
            plazaName: 'Test Plaza'
        }, true);
        console.log('Send OTP SMS:', result.success ? '✅ SUCCESS' : '❌ FAILED', result.success ? result.data : result.error);
        return result;
    }
};

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting CORRECTED Valet API Endpoint Testing...');
    console.log('====================================================');
    
    const results = {};
    
    // Run all tests in sequence
    for (const [testName, testFunction] of Object.entries(testEndpoints)) {
        try {
            results[testName] = await testFunction();
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            console.log(`❌ ${testName} CRASHED:`, error.message);
            results[testName] = { success: false, error: error.message };
        }
    }
    
    // Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('================');
    
    const successful = Object.values(results).filter(r => r.success).length;
    const total = Object.keys(results).length;
    
    console.log(`✅ Successful: ${successful}/${total}`);
    console.log(`❌ Failed: ${total - successful}/${total}`);
    
    if (successful === total) {
        console.log('\n🎉 ALL TESTS PASSED! Valet API is fully functional!');
    } else {
        console.log('\n⚠️  Some tests failed. Check individual results above.');
        console.log('\n📝 Note: Auth-required endpoints may fail with fake tokens.');
        console.log('   Public endpoints (OTP, Customer, Transaction) should work.');
    }
    
    return results;
}

// Run the tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests, testEndpoints };
