require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function checkTables() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    const result = await sql.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE' 
      ORDER BY TABLE_NAME
    `);
    
    console.log('Tables in database:');
    result.recordset.forEach(table => console.log(table.TABLE_NAME));

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkTables();