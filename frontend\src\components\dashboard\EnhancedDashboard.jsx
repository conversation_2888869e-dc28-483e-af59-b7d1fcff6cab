// frontend/src/components/dashboard/EnhancedDashboard.jsx
import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { RefreshCw, Zap, Database, Clock, TrendingUp } from 'lucide-react';
import realtimeService from '../../services/realtimeService';
import axios from 'axios';
import toast from 'react-hot-toast';

/**
 * ===============================================================================
 * # Enhanced Dashboard with Redis Caching
 * ===============================================================================
 * 
 * Advanced dashboard component that leverages Redis caching for:
 * - Lightning-fast data loading
 * - Real-time updates
 * - Cache status monitoring
 * - Performance metrics
 */

const EnhancedDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [realtimeData, setRealtimeData] = useState(null);
  const [cacheStatus, setCacheStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [cacheHit, setCacheHit] = useState(false);
  const [filters, setFilters] = useState({
    dateRange: 'today',
    companyId: null,
    plazaId: null
  });

  /**
   * ===============================================================================
   * ## DATA FETCHING
   * ===============================================================================
   */

  /**
   * Fetch dashboard summary with cache awareness
   */
  const fetchDashboardSummary = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);

      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });

      const response = await axios.get(`/api/dashboard/enhanced/summary?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setDashboardData(response.data.data);
        setCacheHit(response.data.cached || false);
        setLastUpdated(new Date());
        
        // Show cache status in toast
        if (response.data.cached) {
          toast.success('⚡ Data loaded from cache', { duration: 2000 });
        } else {
          toast.success('📊 Fresh data loaded', { duration: 2000 });
        }
      }
    } catch (error) {
      console.error('Failed to fetch dashboard summary:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  /**
   * Fetch real-time data
   */
  const fetchRealtimeData = useCallback(async () => {
    try {
      const response = await axios.get('/api/dashboard/enhanced/realtime', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setRealtimeData(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch real-time data:', error);
    }
  }, []);

  /**
   * Fetch cache status
   */
  const fetchCacheStatus = useCallback(async () => {
    try {
      const status = await realtimeService.getCacheStatus();
      setCacheStatus(status);
    } catch (error) {
      console.error('Failed to fetch cache status:', error);
    }
  }, []);

  /**
   * ===============================================================================
   * ## CACHE MANAGEMENT
   * ===============================================================================
   */

  /**
   * Clear dashboard cache
   */
  const handleClearCache = async () => {
    try {
      const success = await realtimeService.clearDashboardCache();
      if (success) {
        toast.success('🗑️ Cache cleared successfully');
        // Refresh data after clearing cache
        await fetchDashboardSummary();
      } else {
        toast.error('Failed to clear cache');
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
      toast.error('Failed to clear cache');
    }
  };

  /**
   * Force refresh (bypass cache)
   */
  const handleForceRefresh = async () => {
    try {
      // Add a timestamp to bypass cache
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.append(key, value);
      });
      params.append('_t', Date.now().toString());

      const response = await axios.get(`/api/dashboard/enhanced/summary?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.data.success) {
        setDashboardData(response.data.data);
        setCacheHit(false);
        setLastUpdated(new Date());
        toast.success('🔄 Data refreshed from database');
      }
    } catch (error) {
      console.error('Failed to force refresh:', error);
      toast.error('Failed to refresh data');
    }
  };

  /**
   * ===============================================================================
   * ## EFFECTS AND LIFECYCLE
   * ===============================================================================
   */

  useEffect(() => {
    // Initialize real-time service
    const token = localStorage.getItem('token');
    realtimeService.initialize(token);

    // Subscribe to real-time updates
    realtimeService.subscribe('DASHBOARD_UPDATE', (data) => {
      setRealtimeData(data);
    });

    realtimeService.subscribe('CACHE_CLEARED', () => {
      fetchDashboardSummary(false);
    });

    // Start polling for real-time updates
    realtimeService.startPolling(30000); // 30 seconds

    // Initial data fetch
    fetchDashboardSummary();
    fetchRealtimeData();
    fetchCacheStatus();

    // Cleanup on unmount
    return () => {
      realtimeService.stopPolling();
      realtimeService.disconnect();
    };
  }, [fetchDashboardSummary, fetchRealtimeData, fetchCacheStatus]);

  /**
   * Refetch when filters change
   */
  useEffect(() => {
    fetchDashboardSummary();
  }, [filters, fetchDashboardSummary]);

  /**
   * ===============================================================================
   * ## RENDER HELPERS
   * ===============================================================================
   */

  /**
   * Render cache status badge
   */
  const renderCacheStatus = () => {
    if (cacheHit) {
      return (
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <Zap className="w-3 h-3 mr-1" />
          Cached
        </Badge>
      );
    } else {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          <Database className="w-3 h-3 mr-1" />
          Fresh
        </Badge>
      );
    }
  };

  /**
   * Render performance metrics
   */
  const renderPerformanceMetrics = () => {
    if (!dashboardData?.metadata) return null;

    const loadTime = dashboardData.metadata.queryTime ? 
      new Date(dashboardData.metadata.queryTime).getTime() - new Date().getTime() : 0;

    return (
      <div className="flex items-center space-x-4 text-sm text-gray-500">
        <div className="flex items-center">
          <Clock className="w-4 h-4 mr-1" />
          Load: {Math.abs(loadTime)}ms
        </div>
        <div className="flex items-center">
          <TrendingUp className="w-4 h-4 mr-1" />
          {cacheHit ? 'Cache Hit' : 'Database Query'}
        </div>
      </div>
    );
  };

  /**
   * ===============================================================================
   * ## MAIN RENDER
   * ===============================================================================
   */

  if (loading && !dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600"></div>
        <span className="ml-2">Loading enhanced dashboard...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Cache Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enhanced Dashboard</h1>
          <p className="text-gray-600">Powered by Redis caching for lightning-fast performance</p>
        </div>
        
        <div className="flex items-center space-x-3">
          {renderCacheStatus()}
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleForceRefresh}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Force Refresh
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearCache}
          >
            <Database className="w-4 h-4 mr-2" />
            Clear Cache
          </Button>
        </div>
      </div>

      {/* Performance Metrics */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold">Performance Metrics</h3>
              {renderPerformanceMetrics()}
            </div>
            
            <div className="text-right">
              <p className="text-sm text-gray-500">Last Updated</p>
              <p className="font-medium">
                {lastUpdated ? lastUpdated.toLocaleTimeString() : 'Never'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Dashboard Summary Cards */}
      {dashboardData?.summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <span className="text-2xl">💰</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ₹{dashboardData.summary.totalRevenue.toLocaleString()}
              </div>
              {dashboardData.trends?.revenueChange && (
                <p className={`text-xs ${
                  dashboardData.trends.revenueChange >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {dashboardData.trends.revenueChange >= 0 ? '+' : ''}
                  {dashboardData.trends.revenueChange}% from previous period
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Transactions</CardTitle>
              <span className="text-2xl">🚗</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {dashboardData.summary.transactionCount.toLocaleString()}
              </div>
              {dashboardData.trends?.transactionChange && (
                <p className={`text-xs ${
                  dashboardData.trends.transactionChange >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {dashboardData.trends.transactionChange >= 0 ? '+' : ''}
                  {dashboardData.trends.transactionChange}% from previous period
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unique Vehicles</CardTitle>
              <span className="text-2xl">🚙</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {dashboardData.summary.vehicleCount.toLocaleString()}
              </div>
              {dashboardData.trends?.vehicleChange && (
                <p className={`text-xs ${
                  dashboardData.trends.vehicleChange >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {dashboardData.trends.vehicleChange >= 0 ? '+' : ''}
                  {dashboardData.trends.vehicleChange}% from previous period
                </p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
              <span className="text-2xl">⏱️</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(dashboardData.summary.avgDuration)} min
              </div>
              {dashboardData.trends?.durationChange && (
                <p className={`text-xs ${
                  dashboardData.trends.durationChange >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {dashboardData.trends.durationChange >= 0 ? '+' : ''}
                  {dashboardData.trends.durationChange}% from previous period
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Real-time Data */}
      {realtimeData?.realTime && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
              Real-time Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-500">Last 15 Minutes</p>
                <p className="text-xl font-bold">{realtimeData.realTime.last15MinTransactions}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Hour</p>
                <p className="text-xl font-bold">{realtimeData.realTime.lastHourTransactions}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Today's Revenue</p>
                <p className="text-xl font-bold">₹{realtimeData.realTime.todayRevenue.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Active Plazas</p>
                <p className="text-xl font-bold">{realtimeData.realTime.activePlazas}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cache Status (for SuperAdmin) */}
      {cacheStatus && (
        <Card>
          <CardHeader>
            <CardTitle>Cache Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-gray-500">Redis Status</p>
                <Badge variant={cacheStatus.redis.connected ? "default" : "destructive"}>
                  {cacheStatus.redis.connected ? "Connected" : "Disconnected"}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-gray-500">Memory Usage</p>
                <p className="font-medium">{cacheStatus.redis.memory?.used || 'N/A'}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Keys</p>
                <p className="font-medium">{cacheStatus.redis.keyCount || 0}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Connections</p>
                <p className="font-medium">{cacheStatus.redis.connectionCount || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default EnhancedDashboard;