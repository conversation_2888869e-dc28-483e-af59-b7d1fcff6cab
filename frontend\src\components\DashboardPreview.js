import React from 'react';
import { DashboardCard } from './DashboardCard';
import { EnhancedDashboardCard } from './EnhancedDashboardCard';
import { formatCurrency, formatNumber } from '../utils/formatters';
import {
  CreditCard,
  Truck,
  Bike
} from 'lucide-react';

/**
 * Dashboard Preview Component
 * 
 * Demonstrates the new 3-card dashboard layout with sample data
 * This component can be used for testing and demonstration purposes
 */
const DashboardPreview = () => {
  // Sample data matching the expected backend response structure
  const sampleData = {
    totalRevenue: 15250.75,
    fourWheeler: {
      revenue: 12200.50,
      entryCount: 185,
      exitCount: 180,
      remainingCount: 5,
      revenueTrend: 12.5,
      entryTrend: 8.3,
      exitTrend: 7.1,
      remainingTrend: -2.4
    },
    twoWheeler: {
      revenue: 3050.25,
      entryCount: 95,
      exitCount: 90,
      remainingCount: 5,
      revenueTrend: 15.2,
      entryTrend: 10.8,
      exitTrend: 9.5,
      remainingTrend: 1.2
    },
    revenueTrend: 14.8
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            Dashboard Preview - New Layout
          </h1>
        </div>
        
        {/* Description */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h2 className="text-lg font-semibold text-blue-900 mb-2">
            New Dashboard Structure
          </h2>
          <p className="text-blue-800">
            The dashboard now features 3 optimized cards instead of 4, providing better space utilization and enhanced information density. 
            The Four Wheeler and Two Wheeler cards display multiple related metrics in a compact, organized layout.
          </p>
        </div>

        {/* KPI Cards - Enhanced 3-card layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Card 1: Total Revenue with Summary Metrics (Enhanced Single) */}
          <div className="animate-fadeInUp">
            <EnhancedDashboardCard
              title="Total Revenue"
              type="enhanced-single"
              value={formatCurrency(sampleData.totalRevenue)}
              trend={sampleData.revenueTrend}
              icon={CreditCard}
              color="bg-blue-500"
              summaryMetrics={[
                {
                  label: "Total Entry",
                  value: formatNumber(280),
                  trend: 8.5
                },
                {
                  label: "Total Exit",
                  value: formatNumber(270),
                  trend: 7.2
                },
                {
                  label: "Remaining",
                  value: formatNumber(10),
                  trend: -1.5
                }
              ]}
            />
          </div>

          {/* Card 2: Four Wheeler Metrics (Multi Metric) */}
          <div className="animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
            <EnhancedDashboardCard
              title="Four Wheeler"
              type="multi"
              icon={Truck}
              color="bg-green-500"
              metrics={[
                {
                  label: "Revenue",
                  value: formatCurrency(sampleData.fourWheeler.revenue),
                  trend: sampleData.fourWheeler.revenueTrend,
                  isMain: true
                },
                {
                  label: "Entry",
                  value: formatNumber(sampleData.fourWheeler.entryCount),
                  trend: sampleData.fourWheeler.entryTrend
                },
                {
                  label: "Exit",
                  value: formatNumber(sampleData.fourWheeler.exitCount),
                  trend: sampleData.fourWheeler.exitTrend
                },
                {
                  label: "Remaining",
                  value: formatNumber(sampleData.fourWheeler.remainingCount),
                  trend: sampleData.fourWheeler.remainingTrend
                }
              ]}
            />
          </div>

          {/* Card 3: Two Wheeler Metrics (Multi Metric) */}
          <div className="animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
            <EnhancedDashboardCard
              title="Two Wheeler"
              type="multi"
              icon={Bike}
              color="bg-purple-500"
              metrics={[
                {
                  label: "Revenue",
                  value: formatCurrency(sampleData.twoWheeler.revenue),
                  trend: sampleData.twoWheeler.revenueTrend,
                  isMain: true
                },
                {
                  label: "Entry",
                  value: formatNumber(sampleData.twoWheeler.entryCount),
                  trend: sampleData.twoWheeler.entryTrend
                },
                {
                  label: "Exit",
                  value: formatNumber(sampleData.twoWheeler.exitCount),
                  trend: sampleData.twoWheeler.exitTrend
                },
                {
                  label: "Remaining",
                  value: formatNumber(sampleData.twoWheeler.remainingCount),
                  trend: sampleData.twoWheeler.remainingTrend
                }
              ]}
            />
          </div>
        </div>

        {/* Comparison Section */}
        <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-6 hover:shadow-xl hover:border-amber-200 transition-all duration-300">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Layout Comparison
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Old Layout */}
            <div>
              <h4 className="font-medium text-gray-700 mb-3">Previous Layout (4 Cards)</h4>
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-gray-100 p-3 rounded text-center text-sm">
                  Total Revenue
                </div>
                <div className="bg-gray-100 p-3 rounded text-center text-sm">
                  Total Transactions
                </div>
                <div className="bg-gray-100 p-3 rounded text-center text-sm">
                  Vehicles Processed
                </div>
                <div className="bg-gray-100 p-3 rounded text-center text-sm">
                  Avg. Duration
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                4 separate metrics, 25% width each
              </p>
            </div>

            {/* New Layout */}
            <div>
              <h4 className="font-medium text-gray-700 mb-3">New Layout (3 Cards)</h4>
              <div className="grid grid-cols-3 gap-2">
                <div className="bg-blue-100 p-3 rounded text-center text-sm">
                  Total Revenue
                </div>
                <div className="bg-green-100 p-3 rounded text-center text-sm">
                  Four Wheeler
                  <div className="text-xs mt-1">4 metrics</div>
                </div>
                <div className="bg-purple-100 p-3 rounded text-center text-sm">
                  Two Wheeler
                  <div className="text-xs mt-1">4 metrics</div>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-2">
                9 total metrics, 33% width each, better spacing
              </p>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-6 hover:shadow-xl hover:border-amber-200 transition-all duration-300">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Key Benefits
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-blue-600 font-bold text-lg">3</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Better Spacing</h4>
              <p className="text-sm text-gray-600">
                3 cards provide better proportions and more breathing room
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-green-600 font-bold text-lg">9</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">More Information</h4>
              <p className="text-sm text-gray-600">
                Increased from 4 to 9 metrics with better organization
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                <span className="text-purple-600 font-bold text-lg">📱</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Mobile Optimized</h4>
              <p className="text-sm text-gray-600">
                Responsive design that works great on all devices
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPreview;