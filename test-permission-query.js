const db = require('./backend/src/config/database');

async function testPermissionQuery() {
  try {
    console.log('🔍 Testing permission management query...\n');

    const query = `
      SELECT 
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.Description as ModuleDescription,
        m.Icon as ModuleIcon,
        m.DisplayOrder as ModuleDisplayOrder,
        m.IsActive as ModuleIsActive,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        sm.Icon as SubModuleIcon,
        sm.IsActive as SubModuleIsActive,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        
        smp.Id as SubModulePermissionId
        
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId AND sm.IsActive = 1
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
      LEFT JOIN Permissions p ON smp.PermissionId = p.Id AND p.IsActive = 1
      WHERE m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.Id, p.Name
    `;

    console.log('Executing query...');
    const result = await db.query(query);
    console.log(`Query returned ${result.recordset.length} rows`);

    if (result.recordset.length > 0) {
      console.log('\nFirst 5 rows:');
      result.recordset.slice(0, 5).forEach((row, index) => {
        console.log(`Row ${index + 1}:`);
        console.log(`  Module: ${row.ModuleName} (ID: ${row.ModuleId})`);
        console.log(`  SubModule: ${row.SubModuleName || 'NULL'} (ID: ${row.SubModuleId || 'NULL'})`);
        console.log(`  Permission: ${row.PermissionName || 'NULL'} (ID: ${row.PermissionId || 'NULL'})`);
        console.log(`  SubModulePermissionId: ${row.SubModulePermissionId || 'NULL'}`);
        console.log('');
      });

      // Group by modules
      const modulesMap = new Map();
      result.recordset.forEach(row => {
        if (!modulesMap.has(row.ModuleId)) {
          modulesMap.set(row.ModuleId, {
            id: row.ModuleId,
            name: row.ModuleName,
            subModules: new Set()
          });
        }
        if (row.SubModuleId) {
          modulesMap.get(row.ModuleId).subModules.add(row.SubModuleName);
        }
      });

      console.log('\nModules summary:');
      Array.from(modulesMap.values()).forEach(module => {
        console.log(`  ${module.name}: ${module.subModules.size} submodules`);
        if (module.subModules.size > 0) {
          console.log(`    SubModules: ${Array.from(module.subModules).join(', ')}`);
        }
      });
    } else {
      console.log('No rows returned. Let me check individual tables...');
      
      // Check modules
      const modules = await db.query('SELECT COUNT(*) as count FROM Modules WHERE IsActive = 1');
      console.log(`Active Modules: ${modules.recordset[0].count}`);
      
      // Check submodules
      const submodules = await db.query('SELECT COUNT(*) as count FROM SubModules WHERE IsActive = 1');
      console.log(`Active SubModules: ${submodules.recordset[0].count}`);
      
      // Check permissions
      const permissions = await db.query('SELECT COUNT(*) as count FROM Permissions WHERE IsActive = 1');
      console.log(`Active Permissions: ${permissions.recordset[0].count}`);
      
      // Check submodule permissions
      const smp = await db.query('SELECT COUNT(*) as count FROM SubModulePermissions WHERE IsActive = 1');
      console.log(`Active SubModulePermissions: ${smp.recordset[0].count}`);
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await db.closePool();
    process.exit(0);
  }
}

testPermissionQuery();