-- =============================================
-- Valet System Database Migration Script
-- COPY valet tables from pwvms to ParkwizOps (data remains in pwvms)
-- Each valet controller will be associated with PlazaValetPoint (desk)
-- =============================================

USE ParkwizOps;
GO

PRINT 'Starting Valet System Database Migration...';
PRINT '==========================================';
PRINT 'NOTE: This migration COPIES data from pwvms database.';
PRINT 'Original data in pwvms will remain intact.';
PRINT '';

-- =============================================
-- Step 1: Create Customer Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customer' AND xtype='U')
BEGIN
    PRINT 'Creating Customer table...';
    CREATE TABLE Customer (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(500) NULL,
        MobileNumber NVARCHAR(500) NULL,
        AddressId DECIMAL(18,0) NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ Customer table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ Customer table already exists.';
END

-- =============================================
-- Step 2: Create CustomerVehicle Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustomerVehicle' AND xtype='U')
BEGIN
    PRINT 'Creating CustomerVehicle table...';
    CREATE TABLE CustomerVehicle (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        CustomerId DECIMAL(18,0) NULL,
        VehicleNumber NVARCHAR(500) NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ CustomerVehicle table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ CustomerVehicle table already exists.';
END

-- =============================================
-- Step 3: Create OTP Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OTP' AND xtype='U')
BEGIN
    PRINT 'Creating OTP table...';
    CREATE TABLE OTP (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        MobileNumber NVARCHAR(500) NULL,
        OTP NVARCHAR(500) NULL,
        ExpireTime INT NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ OTP table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ OTP table already exists.';
END

-- =============================================
-- Step 4: Create ParkingTransactions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ParkingTransactions' AND xtype='U')
BEGIN
    PRINT 'Creating ParkingTransactions table...';
    CREATE TABLE ParkingTransactions (
        Id DECIMAL(18,0) IDENTITY(1,1) PRIMARY KEY,
        PNRNumber UNIQUEIDENTIFIER NOT NULL DEFAULT NEWID(),
        ParkingPin DECIMAL(18,0) NOT NULL DEFAULT RAND(),
        CompanyId DECIMAL(18,0) NOT NULL,
        PlazaId DECIMAL(18,0) NOT NULL,
        CustomerVehicleNumber NVARCHAR(100) NOT NULL,
        CustomerMobileNumber NVARCHAR(100) NULL,
        CustomerName NVARCHAR(100) NULL,
        IsAnyValuableItem BIT NOT NULL DEFAULT 0,
        AnyValuableItem NVARCHAR(100) NULL,
        ValetFee DECIMAL(18,2) NOT NULL DEFAULT 0,
        ParkingFee DECIMAL(18,2) NOT NULL DEFAULT 0,
        TotalFee DECIMAL(18,2) NOT NULL DEFAULT 0,
        Source INT NOT NULL,
        PayAt INT NOT NULL,
        PaymentType INT NULL,
        IsPaymentCompleted BIT NOT NULL DEFAULT 0,
        IsPromoCodeTransaction BIT NOT NULL DEFAULT 0,
        PromocodeId DECIMAL(18,0) NULL,
        VehicleType INT NULL DEFAULT 1,
        EntryBy DECIMAL(18,0) NULL,
        EntryDateTime DATETIME NOT NULL DEFAULT GETDATE(),
        ZoneId DECIMAL(18,0) NULL,
        DriverId DECIMAL(18,0) NULL,
        DriverAssignedBy DECIMAL(18,0) NULL,
        DriverAssignedOn DATETIME NULL,
        ParkingBayId DECIMAL(18,0) NULL,
        ParkedBayAssignedBy DECIMAL(18,0) NULL,
        ParkedBayAssignedOn DATETIME NULL,
        RequestMyVehicleDateTime DATETIME NULL,
        IsRequestMyVehicleLater BIT NOT NULL DEFAULT 0,
        RequestMyVehicleLaterTimeInterval INT NOT NULL DEFAULT 0,
        RequestMyVehicleLaterTime DATETIME NULL,
        RequestMyVehicleBy DECIMAL(18,0) NULL,
        PlazaValetPointId DECIMAL(18,0) NULL,
        PickupDriverId DECIMAL(18,0) NULL,
        AssignDriverOn DATETIME NULL,
        PickupVehicleOn DATETIME NULL,
        ArrivedAtPlazaValetPointOn DATETIME NULL,
        AssignToControllerId DECIMAL(18,0) NULL,
        AssignToControllerBy DECIMAL(18,0) NULL,
        AssignToControllerOn DATETIME NULL,
        PhoneUPITransactionId DECIMAL(18,0) NULL,
        PhoneUPITransactionNumber NVARCHAR(100) NULL,
        IsVoid BIT NOT NULL DEFAULT 0,
        ExitDateTime DATETIME NULL,
        ExitBy DECIMAL(18,0) NULL,
        IsTransactionCanceled BIT NULL,
        CancelTransactionReason INT NULL,
        CancelBy DECIMAL(18,0) NULL,
        CancelOn DATETIME NULL,
        IsCancelTransactionRefunded BIT NULL,
        CancelCancelTransactionRefundedPaymentType INT NULL,
        CancelCancelTransactionRefundedPaymentDateTime DATETIME NULL,
        CancelTransactionRemark NVARCHAR(100) NULL,
        TransactionStatus INT NOT NULL DEFAULT 1,
        IsKeyHandover BIT NOT NULL DEFAULT 0,
        KeyAcceptedBy DECIMAL(18,0) NULL,
        razorPayTransactionId DECIMAL(18,0) NULL,
        razorPayTransactionNumber NVARCHAR(100) NULL
    );
    PRINT '✅ ParkingTransactions table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ParkingTransactions table already exists.';
END

-- =============================================
-- Step 5: Create ParkingTransactionCashPayments Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ParkingTransactionCashPayments' AND xtype='U')
BEGIN
    PRINT 'Creating ParkingTransactionCashPayments table...';
    CREATE TABLE ParkingTransactionCashPayments (
        Id DECIMAL(18,0) IDENTITY(1,1) PRIMARY KEY,
        TransactionId DECIMAL(18,0) NOT NULL,
        Cash DECIMAL(18,2) NULL,
        IsRecived BIT NOT NULL DEFAULT 0,
        RecivedBy DECIMAL(18,0) NULL,
        RecivedOn DATETIME NULL
    );
    PRINT '✅ ParkingTransactionCashPayments table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ParkingTransactionCashPayments table already exists.';
END

-- =============================================
-- Step 6: Create ParkingTransactionVehicleImages Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ParkingTransactionVehicleImages' AND xtype='U')
BEGIN
    PRINT 'Creating ParkingTransactionVehicleImages table...';
    CREATE TABLE ParkingTransactionVehicleImages (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        TransactionId DECIMAL(18,0) NOT NULL,
        Image VARBINARY(MAX) NOT NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ ParkingTransactionVehicleImages table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ParkingTransactionVehicleImages table already exists.';
END

-- =============================================
-- Step 7: Create ParkingBay Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ParkingBay' AND xtype='U')
BEGIN
    PRINT 'Creating ParkingBay table...';
    CREATE TABLE ParkingBay (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        CompanyId INT NOT NULL DEFAULT 0,
        PlazaId INT NOT NULL DEFAULT 0,
        ParkingZoneId INT NOT NULL DEFAULT 0,
        ParkingBayName NVARCHAR(100) NOT NULL,
        Latitude NVARCHAR(500) NULL,
        Longitude NVARCHAR(500) NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ ParkingBay table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ParkingBay table already exists.';
END

-- =============================================
-- Step 8: Create ParkingZone Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ParkingZone' AND xtype='U')
BEGIN
    PRINT 'Creating ParkingZone table...';
    CREATE TABLE ParkingZone (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        CompanyId INT NOT NULL DEFAULT 0,
        PlazaId INT NOT NULL DEFAULT 0,
        ParkingZoneName NVARCHAR(100) NOT NULL,
        NumberOfParking DECIMAL(18,0) NOT NULL DEFAULT 0,
        Latitude NVARCHAR(500) NULL,
        Longitude NVARCHAR(500) NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ ParkingZone table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ParkingZone table already exists.';
END

PRINT '';
PRINT '✅ Valet System Database Migration Phase 1 Completed Successfully!';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Run valet_database_migration_part2.sql for remaining tables';
PRINT '2. Execute data migration scripts';
PRINT '3. Create indexes and stored procedures';
PRINT '4. Set up module permissions';
PRINT '';
