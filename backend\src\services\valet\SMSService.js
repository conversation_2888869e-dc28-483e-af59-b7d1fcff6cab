const axios = require('axios');
const db = require('../../config/database');

/**
 * SMS Service for Valet System
 * Handles SMS delivery with multiple provider support, message templates, delivery tracking, and rate limiting
 */

class SMSService {
  constructor() {
    this.provider = process.env.SMS_PROVIDER || 'TWILIO'; // TWILIO, MSG91, TEXTLOCAL, AWS_SNS
    this.rateLimitPerMinute = parseInt(process.env.SMS_RATE_LIMIT) || 60;
    this.rateLimitWindow = 60 * 1000; // 1 minute in milliseconds
    this.messageQueue = [];
    this.rateLimitTracker = new Map();
    
    // Initialize provider configuration
    this.initializeProvider();
  }

  /**
   * Initialize SMS provider configuration
   */
  initializeProvider() {
    switch (this.provider) {
      case 'TWILIO':
        this.twilioConfig = {
          accountSid: process.env.TWILIO_ACCOUNT_SID,
          authToken: process.env.TWILIO_AUTH_TOKEN,
          fromNumber: process.env.TWILIO_FROM_NUMBER,
          baseURL: 'https://api.twilio.com/2010-04-01'
        };
        break;
        
      case 'MSG91':
        this.msg91Config = {
          authKey: process.env.MSG91_AUTH_KEY,
          senderId: process.env.MSG91_SENDER_ID,
          route: process.env.MSG91_ROUTE || '4',
          baseURL: 'https://api.msg91.com/api'
        };
        break;
        
      case 'TEXTLOCAL':
        this.textlocalConfig = {
          apiKey: process.env.TEXTLOCAL_API_KEY,
          sender: process.env.TEXTLOCAL_SENDER,
          baseURL: 'https://api.textlocal.in'
        };
        break;
        
      case 'AWS_SNS':
        this.awsConfig = {
          accessKeyId: process.env.AWS_ACCESS_KEY_ID,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
          region: process.env.AWS_REGION || 'ap-south-1'
        };
        break;
        
      default:
        console.warn(`Unsupported SMS provider: ${this.provider}. SMS functionality will be simulated.`);
    }
  }

  /**
   * Send SMS with rate limiting and queue management
   */
  async sendSMS(smsData) {
    try {
      const { mobileNumber, message, smsType, customerId, transactionId } = smsData;

      // Validate input
      if (!mobileNumber || !message) {
        throw new Error('Mobile number and message are required');
      }

      // Check rate limiting
      if (!this.checkRateLimit(mobileNumber)) {
        throw new Error('Rate limit exceeded for this mobile number');
      }

      // Log SMS attempt in database
      const smsId = await this.logSMSAttempt({
        mobileNumber,
        message,
        smsType,
        customerId,
        transactionId
      });

      // Send SMS based on provider
      let deliveryResult;
      
      switch (this.provider) {
        case 'TWILIO':
          deliveryResult = await this.sendViaTwilio(mobileNumber, message);
          break;
          
        case 'MSG91':
          deliveryResult = await this.sendViaMsg91(mobileNumber, message);
          break;
          
        case 'TEXTLOCAL':
          deliveryResult = await this.sendViaTextLocal(mobileNumber, message);
          break;
          
        case 'AWS_SNS':
          deliveryResult = await this.sendViaAWSSNS(mobileNumber, message);
          break;
          
        default:
          deliveryResult = await this.simulateSMSDelivery(mobileNumber, message);
      }

      // Update SMS status in database
      await this.updateSMSStatus(smsId, deliveryResult);

      // Update rate limit tracker
      this.updateRateLimit(mobileNumber);

      return {
        success: deliveryResult.success,
        smsId,
        mobileNumber,
        message,
        provider: this.provider,
        deliveryStatus: deliveryResult.success ? 'SENT' : 'FAILED',
        error: deliveryResult.error || null,
        sentAt: new Date()
      };

    } catch (error) {
      console.error('SMS sending error:', error);
      
      return {
        success: false,
        error: error.message,
        provider: this.provider
      };
    }
  }

  /**
   * Send SMS via Twilio
   */
  async sendViaTwilio(mobileNumber, message) {
    try {
      if (!this.twilioConfig.accountSid || !this.twilioConfig.authToken) {
        throw new Error('Twilio credentials not configured');
      }

      const auth = Buffer.from(
        `${this.twilioConfig.accountSid}:${this.twilioConfig.authToken}`
      ).toString('base64');

      const formattedNumber = this.formatMobileNumber(mobileNumber, 'IN');
      
      const response = await axios.post(
        `${this.twilioConfig.baseURL}/Accounts/${this.twilioConfig.accountSid}/Messages.json`,
        new URLSearchParams({
          From: this.twilioConfig.fromNumber,
          To: formattedNumber,
          Body: message
        }),
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: 30000
        }
      );

      return {
        success: true,
        messageId: response.data.sid,
        status: response.data.status,
        provider: 'TWILIO'
      };

    } catch (error) {
      console.error('Twilio SMS error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        provider: 'TWILIO'
      };
    }
  }

  /**
   * Send SMS via MSG91
   */
  async sendViaMsg91(mobileNumber, message) {
    try {
      if (!this.msg91Config.authKey) {
        throw new Error('MSG91 credentials not configured');
      }

      const response = await axios.post(
        `${this.msg91Config.baseURL}/sendhttp.php`,
        {
          authkey: this.msg91Config.authKey,
          mobiles: mobileNumber,
          message: message,
          sender: this.msg91Config.senderId,
          route: this.msg91Config.route,
          country: '91'
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      return {
        success: response.data.type === 'success',
        messageId: response.data.message,
        status: response.data.type,
        provider: 'MSG91'
      };

    } catch (error) {
      console.error('MSG91 SMS error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        provider: 'MSG91'
      };
    }
  }

  /**
   * Send SMS via TextLocal
   */
  async sendViaTextLocal(mobileNumber, message) {
    try {
      if (!this.textlocalConfig.apiKey) {
        throw new Error('TextLocal credentials not configured');
      }

      const response = await axios.post(
        `${this.textlocalConfig.baseURL}/send/`,
        new URLSearchParams({
          apikey: this.textlocalConfig.apiKey,
          numbers: mobileNumber,
          message: message,
          sender: this.textlocalConfig.sender
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          timeout: 30000
        }
      );

      return {
        success: response.data.status === 'success',
        messageId: response.data.messages?.[0]?.id,
        status: response.data.status,
        provider: 'TEXTLOCAL'
      };

    } catch (error) {
      console.error('TextLocal SMS error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        provider: 'TEXTLOCAL'
      };
    }
  }

  /**
   * Send SMS via AWS SNS
   */
  async sendViaAWSSNS(mobileNumber, message) {
    try {
      // This would require AWS SDK integration
      // For now, return simulation
      console.log('AWS SNS SMS sending not implemented yet');
      
      return await this.simulateSMSDelivery(mobileNumber, message);

    } catch (error) {
      console.error('AWS SNS SMS error:', error);
      
      return {
        success: false,
        error: error.message,
        provider: 'AWS_SNS'
      };
    }
  }

  /**
   * Simulate SMS delivery for testing
   */
  async simulateSMSDelivery(mobileNumber, message) {
    try {
      console.log(`SMS Simulation - Sending to ${mobileNumber}: ${message}`);
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate 95% success rate
      const success = Math.random() > 0.05;
      
      return {
        success,
        messageId: `SIM_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        status: success ? 'SENT' : 'FAILED',
        provider: 'SIMULATION'
      };
      
    } catch (error) {
      console.error('SMS simulation error:', error);
      
      return {
        success: false,
        error: error.message,
        provider: 'SIMULATION'
      };
    }
  }

  /**
   * Log SMS attempt in database
   */
  async logSMSAttempt(smsData) {
    try {
      const { mobileNumber, message, smsType, customerId, transactionId } = smsData;

      const insertQuery = `
        INSERT INTO SMSNotifications (
          MobileNumber, Message, SMSType, CustomerId, TransactionId, 
          Status, Provider, CreatedOn
        )
        OUTPUT INSERTED.Id
        VALUES (
          @mobileNumber, @message, @smsType, @customerId, @transactionId,
          'PENDING', @provider, GETDATE()
        )
      `;

      const result = await db.query(insertQuery, {
        mobileNumber,
        message,
        smsType: smsType || 'GENERAL',
        customerId: customerId || null,
        transactionId: transactionId || null,
        provider: this.provider
      });

      return result.recordset[0].Id;

    } catch (error) {
      console.error('SMS logging error:', error);
      throw error;
    }
  }

  /**
   * Update SMS status in database
   */
  async updateSMSStatus(smsId, deliveryResult) {
    try {
      const updateQuery = `
        UPDATE SMSNotifications
        SET Status = @status,
            MessageId = @messageId,
            DeliveryResponse = @response,
            SentOn = @sentOn,
            UpdatedOn = GETDATE()
        WHERE Id = @smsId
      `;

      await db.query(updateQuery, {
        smsId,
        status: deliveryResult.success ? 'SENT' : 'FAILED',
        messageId: deliveryResult.messageId || null,
        response: JSON.stringify(deliveryResult),
        sentOn: deliveryResult.success ? new Date() : null
      });

    } catch (error) {
      console.error('SMS status update error:', error);
      throw error;
    }
  }

  /**
   * Check rate limiting for mobile number
   */
  checkRateLimit(mobileNumber) {
    const now = Date.now();
    const windowStart = now - this.rateLimitWindow;
    
    if (!this.rateLimitTracker.has(mobileNumber)) {
      return true;
    }
    
    const attempts = this.rateLimitTracker.get(mobileNumber);
    const recentAttempts = attempts.filter(timestamp => timestamp > windowStart);
    
    return recentAttempts.length < this.rateLimitPerMinute;
  }

  /**
   * Update rate limit tracker
   */
  updateRateLimit(mobileNumber) {
    const now = Date.now();
    const windowStart = now - this.rateLimitWindow;
    
    if (!this.rateLimitTracker.has(mobileNumber)) {
      this.rateLimitTracker.set(mobileNumber, []);
    }
    
    const attempts = this.rateLimitTracker.get(mobileNumber);
    const recentAttempts = attempts.filter(timestamp => timestamp > windowStart);
    recentAttempts.push(now);
    
    this.rateLimitTracker.set(mobileNumber, recentAttempts);
  }

  /**
   * Format mobile number for international use
   */
  formatMobileNumber(mobileNumber, countryCode = 'IN') {
    // Remove any non-digit characters
    const cleanNumber = mobileNumber.replace(/\D/g, '');
    
    switch (countryCode) {
      case 'IN':
        // Indian mobile numbers
        if (cleanNumber.length === 10) {
          return `+91${cleanNumber}`;
        } else if (cleanNumber.length === 12 && cleanNumber.startsWith('91')) {
          return `+${cleanNumber}`;
        }
        break;
        
      default:
        return `+${cleanNumber}`;
    }
    
    return cleanNumber;
  }

  /**
   * Get SMS delivery statistics
   */
  async getSMSStats(filters = {}) {
    try {
      const { startDate, endDate, smsType, status } = filters;
      
      let whereClause = 'WHERE 1=1';
      const params = {};
      
      if (startDate) {
        whereClause += ' AND CreatedOn >= @startDate';
        params.startDate = startDate;
      }
      
      if (endDate) {
        whereClause += ' AND CreatedOn <= @endDate';
        params.endDate = endDate;
      }
      
      if (smsType) {
        whereClause += ' AND SMSType = @smsType';
        params.smsType = smsType;
      }
      
      if (status) {
        whereClause += ' AND Status = @status';
        params.status = status;
      }

      const statsQuery = `
        SELECT 
          COUNT(*) as TotalSMS,
          SUM(CASE WHEN Status = 'SENT' THEN 1 ELSE 0 END) as SentSMS,
          SUM(CASE WHEN Status = 'FAILED' THEN 1 ELSE 0 END) as FailedSMS,
          SUM(CASE WHEN Status = 'PENDING' THEN 1 ELSE 0 END) as PendingSMS,
          SMSType,
          Provider
        FROM SMSNotifications
        ${whereClause}
        GROUP BY SMSType, Provider
      `;

      const result = await db.query(statsQuery, params);
      
      return {
        success: true,
        data: result.recordset
      };

    } catch (error) {
      console.error('SMS stats error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new SMSService();
