# Comprehensive Notification System Implementation Plan

## Phase 1: PlazaManager Action Buttons Enhancement

### 1.1 Current Issue Analysis
- ✅ **User Management**: PlazaManager has action buttons (redirects to unauthorized)
- ❌ **Other Modules**: PlazaManager missing action buttons in:
  - Company Management
  - Plaza Management  
  - Lane Management
  - ANPR Configuration
  - Digital Pay Configuration
  - Fastag Configuration

### 1.2 Action Buttons Implementation Plan

#### **Frontend Changes Required**:
```javascript
// Update PermissionButton.js to handle PlazaManager for ALL modules
const isPlazaManagerViewingAnyModule = 
  hasRole('PlazaManager') && 
  ['Companies', 'Plazas', 'Lanes', 'ANPR', 'DigitalPay', 'Fastag', 'Users'].includes(requiredModule) &&
  (requiredPermissions.includes('Edit') || requiredPermissions.includes('Delete') || requiredPermissions.includes('Create'));
```

#### **Modules to Update**:
1. **Company Management** - Show buttons, redirect to unauthorized
2. **Plaza Management** - Show buttons, redirect to unauthorized  
3. **Lane Management** - Show buttons, redirect to unauthorized
4. **ANPR Configuration** - Show buttons, redirect to unauthorized
5. **Digital Pay Configuration** - Show buttons, redirect to unauthorized
6. **Fastag Configuration** - Show buttons, redirect to unauthorized

---

## Phase 2: Toast Notification System

### 2.1 Toast Notification Architecture

#### **Frontend Toast System**:
```javascript
// Create ToastContext for global toast management
const ToastContext = {
  showSuccess: (message) => {},
  showError: (message) => {},
  showWarning: (message) => {},
  showInfo: (message) => {}
}

// Toast Types:
- SUCCESS: Green - "User created successfully"
- ERROR: Red - "Failed to delete company"  
- WARNING: Yellow - "Unauthorized action attempted"
- INFO: Blue - "Data updated"
```

#### **Toast Triggers**:
| Action | Success Toast | Error Toast |
|--------|---------------|-------------|
| **Create** | "✅ [Entity] created successfully" | "❌ Failed to create [entity]" |
| **Update** | "✅ [Entity] updated successfully" | "❌ Failed to update [entity]" |
| **Delete** | "✅ [Entity] deleted successfully" | "❌ Failed to delete [entity]" |
| **Unauthorized** | - | "⚠️ You are not authorized to perform this action" |

### 2.2 Implementation Components

#### **Files to Create**:
1. `frontend/src/contexts/ToastContext.js` - Global toast state management
2. `frontend/src/components/Toast/ToastContainer.js` - Toast display component
3. `frontend/src/components/Toast/Toast.js` - Individual toast component
4. `frontend/src/hooks/useToast.js` - Custom hook for toast operations

#### **Integration Points**:
- All API calls (create/update/delete operations)
- Error handling in try-catch blocks
- Success responses from backend
- Unauthorized action attempts

---

## Phase 3: Email Notification System

### 3.1 Email Notification Architecture

#### **Hierarchical Notification Structure**:
```
SuperAdmin (Top Level)
    ↓ Gets notified when CompanyAdmin performs actions
CompanyAdmin (Middle Level)  
    ↓ Gets notified when PlazaManager performs actions
PlazaManager (Bottom Level)
    ↓ No subordinates to monitor
```

#### **Email Notification Matrix**:

| Actor | Action | Notify To | Email Type |
|-------|--------|-----------|------------|
| **SuperAdmin** | Creates CompanyAdmin | CompanyAdmin | Welcome Email |
| **SuperAdmin** | Creates Company | CompanyAdmin | Company Assignment |
| **CompanyAdmin** | Creates PlazaManager | PlazaManager | Welcome Email |
| **CompanyAdmin** | Creates Plaza | SuperAdmin | Activity Notification |
| **CompanyAdmin** | Deletes anything | SuperAdmin | Deletion Alert |
| **PlazaManager** | Creates Lane/ANPR | CompanyAdmin | Activity Notification |
| **PlazaManager** | Deletes anything | CompanyAdmin | Deletion Alert |

### 3.2 Email Templates

#### **Welcome Email Templates**:
```html
<!-- CompanyAdmin Welcome -->
Subject: Welcome to ParkwizOps - Company Admin Access
Body: "You have been granted Company Admin access to ParkwizOps..."

<!-- PlazaManager Welcome -->  
Subject: Welcome to ParkwizOps - Plaza Manager Access
Body: "You have been granted Plaza Manager access to ParkwizOps..."
```

#### **Activity Notification Templates**:
```html
<!-- Creation Notifications -->
Subject: New [Entity] Created by [User]
Body: "[User] has created a new [entity]: [details]"

<!-- Deletion Alerts -->
Subject: ⚠️ [Entity] Deleted by [User] 
Body: "ALERT: [User] has deleted [entity]: [details]"
```

### 3.3 Backend Email Service Implementation

#### **Files to Create**:
1. `backend/src/services/EmailService.js` - Core email functionality
2. `backend/src/services/NotificationService.js` - Notification logic
3. `backend/src/templates/` - Email HTML templates
4. `backend/src/config/emailConfig.js` - Email configuration

#### **Email Service Structure**:
```javascript
class EmailService {
  static async sendWelcomeEmail(userEmail, role, credentials) {}
  static async sendActivityNotification(action, entity, actor, recipient) {}
  static async sendDeletionAlert(entity, actor, recipient) {}
  static async sendHierarchicalNotification(action, data) {}
}
```

---

## Phase 4: Implementation Roadmap

### 4.1 Phase 1: PlazaManager Action Buttons (Week 1)
**Day 1-2**: Update PermissionButton.js for all modules
**Day 3-4**: Test action buttons across all pages
**Day 5**: Fix any UI/UX issues

### 4.2 Phase 2: Toast Notification System (Week 2)  
**Day 1-2**: Create toast components and context
**Day 3-4**: Integrate toasts with all CRUD operations
**Day 5**: Test toast notifications across all modules

### 4.3 Phase 3: Email Notification Backend (Week 3)
**Day 1-2**: Set up email service and templates
**Day 3-4**: Implement hierarchical notification logic
**Day 5**: Test email sending functionality

### 4.4 Phase 4: Integration & Testing (Week 4)
**Day 1-2**: Integrate email notifications with CRUD operations
**Day 3-4**: End-to-end testing of complete notification system
**Day 5**: Performance optimization and bug fixes

---

## Phase 5: Technical Specifications

### 5.1 Toast Notification Specs
- **Duration**: 5 seconds (configurable)
- **Position**: Top-right corner
- **Animation**: Slide-in from right, fade-out
- **Stacking**: Multiple toasts stack vertically
- **Dismissible**: Click to dismiss or auto-dismiss

### 5.2 Email Notification Specs
- **Email Provider**: SMTP (configurable for Gmail/Outlook/SendGrid)
- **Template Engine**: HTML with dynamic content injection
- **Delivery**: Asynchronous (non-blocking)
- **Retry Logic**: 3 attempts with exponential backoff
- **Logging**: All email attempts logged for audit

### 5.3 Database Schema Additions
```sql
-- Email Notifications Log
CREATE TABLE EmailNotifications (
  Id INT IDENTITY(1,1) PRIMARY KEY,
  RecipientEmail VARCHAR(255),
  Subject VARCHAR(500),
  Body TEXT,
  EmailType VARCHAR(50), -- 'welcome', 'activity', 'deletion'
  SentAt DATETIME,
  Status VARCHAR(20), -- 'sent', 'failed', 'pending'
  RetryCount INT DEFAULT 0,
  CreatedBy INT,
  CreatedAt DATETIME DEFAULT GETDATE()
);

-- Activity Notifications Log  
CREATE TABLE ActivityNotifications (
  Id INT IDENTITY(1,1) PRIMARY KEY,
  ActorUserId INT,
  Action VARCHAR(50), -- 'create', 'update', 'delete'
  EntityType VARCHAR(50), -- 'user', 'company', 'plaza', etc.
  EntityId INT,
  NotifiedUserId INT,
  NotificationMethod VARCHAR(20), -- 'email', 'toast'
  CreatedAt DATETIME DEFAULT GETDATE()
);
```

---

## Phase 6: Success Metrics

### 6.1 PlazaManager Action Buttons
- ✅ All modules show action buttons for PlazaManager
- ✅ All buttons redirect to unauthorized page with clear messages
- ✅ Consistent UI/UX across all modules

### 6.2 Toast Notifications
- ✅ 100% CRUD operations show appropriate toasts
- ✅ Error handling displays user-friendly messages
- ✅ Toast performance doesn't impact app speed

### 6.3 Email Notifications  
- ✅ Welcome emails sent within 30 seconds of user creation
- ✅ Activity notifications sent within 1 minute of actions
- ✅ 95%+ email delivery success rate
- ✅ Hierarchical notifications working correctly

---

## Next Steps

1. **Immediate**: Start with Phase 1 (PlazaManager action buttons)
2. **Priority**: Implement toast notification system for better UX
3. **Strategic**: Build email notification system for audit trail
4. **Long-term**: Add advanced notification preferences and settings

This comprehensive plan will create a robust, user-friendly notification system that enhances security, improves user experience, and provides proper audit trails for all system activities.
