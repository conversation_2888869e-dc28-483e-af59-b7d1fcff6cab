// seeder-pass-registration.js
const db = require('../config/database');
const { faker } = require('@faker-js/faker');

async function seedPassRegistrations() {
  try {
    // Connect to database
    await db.connect();
    console.log('Database connection successful');

    // First, check if the table exists and get its structure
    const tableStructureQuery = `
      SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'tbl_Parkwiz_Pass_Reg'
      ORDER BY ORDINAL_POSITION
    `;
    
    const tableStructure = await db.query(tableStructureQuery);
    console.log('\n📋 Table Structure:');
    tableStructure.recordset.forEach(column => {
      console.log(`- ${column.COLUMN_NAME} (${column.DATA_TYPE}${column.CHARACTER_MAXIMUM_LENGTH ? `(${column.CHARACTER_MAXIMUM_LENGTH})` : ''}, ${column.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });

    // Get some sample plaza codes for reference
    const plazaQuery = `SELECT TOP 5 PlazaCode, PlazaName FROM Plaza`;
    let plazas = [];
    try {
      const plazaResult = await db.query(plazaQuery);
      plazas = plazaResult.recordset;
      console.log(`Found ${plazas.length} plazas for reference`);
    } catch (error) {
      console.warn('Could not fetch plaza data, using mock data instead:', error.message);
      plazas = [
        { PlazaCode: 'PLZ001', PlazaName: 'Main Highway Plaza' },
        { PlazaCode: 'PLZ002', PlazaName: 'Downtown Toll Plaza' },
        { PlazaCode: 'PLZ003', PlazaName: 'Riverside Toll Gate' },
        { PlazaCode: 'PLZ004', PlazaName: 'Mountain Pass Plaza' },
        { PlazaCode: 'PLZ005', PlazaName: 'Coastal Highway Plaza' }
      ];
    }

    // Sample data for vehicle types
    const vehicleTypes = ['Car', 'Truck', 'Bus', 'Motorcycle', 'SUV', 'Van'];
    
    // Sample data for pass types
    const passTypes = ['Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annual'];
    
    // Sample data for payment modes
    const paymentModes = ['Cash', 'Credit Card', 'Debit Card', 'UPI', 'Net Banking', 'Wallet'];
    
    // Sample data for payment types
    const paymentTypes = ['Full', 'Partial', 'Installment'];

    // Generate 20 sample pass registrations
    const sampleSize = 20;
    console.log(`\nGenerating ${sampleSize} sample pass registrations...`);
    
    for (let i = 0; i < sampleSize; i++) {
      const plaza = plazas[Math.floor(Math.random() * plazas.length)];
      const vehicleType = vehicleTypes[Math.floor(Math.random() * vehicleTypes.length)];
      const passType = passTypes[Math.floor(Math.random() * passTypes.length)];
      const paymentMode = paymentModes[Math.floor(Math.random() * paymentModes.length)];
      const paymentType = paymentTypes[Math.floor(Math.random() * paymentTypes.length)];
      
      // Generate a random contract end date between 1 month and 1 year from now
      const contractEndDate = new Date();
      contractEndDate.setDate(contractEndDate.getDate() + Math.floor(Math.random() * 365) + 30);
      
      // Generate a random issue date in the past 6 months
      const issueDate = new Date();
      issueDate.setDate(issueDate.getDate() - Math.floor(Math.random() * 180));
      
      // Generate a random vehicle number
      const vehicleNo = `${faker.string.alpha(2).toUpperCase()}-${faker.number.int({ min: 10, max: 99 })}-${faker.string.alpha(1).toUpperCase()}-${faker.number.int({ min: 1000, max: 9999 })}`;
      
      // Generate a random GIN (Global Identification Number)
      const gin = `GIN${faker.number.int({ min: 100000, max: 999999 })}`;
      
      // Generate a random tag ID
      const tagId = `TAG${faker.number.int({ min: 10000, max: 99999 })}`;
      
      // Generate a random passcard number
      const passcardNumber = `PASS${faker.number.int({ min: 100000, max: 999999 })}`;
      
      // Generate a random paid amount based on pass type
      let paidAmount = 0;
      switch (passType) {
        case 'Daily':
          paidAmount = faker.number.int({ min: 50, max: 200 });
          break;
        case 'Weekly':
          paidAmount = faker.number.int({ min: 300, max: 800 });
          break;
        case 'Monthly':
          paidAmount = faker.number.int({ min: 1000, max: 3000 });
          break;
        case 'Quarterly':
          paidAmount = faker.number.int({ min: 2500, max: 8000 });
          break;
        case 'Annual':
          paidAmount = faker.number.int({ min: 8000, max: 25000 });
          break;
      }

      // Generate a random application ID (numeric)
      const applicationID = faker.number.int({ min: 10000, max: 99999 });

      // Insert the record
      await db.query(`
        INSERT INTO tbl_Parkwiz_Pass_Reg (
          PlazaCode, PlazaName, ApplicationID, GIN, IssueDate, HolderName, CompanyName,
          VehicleType, ContactNo, PassType, PassTariffType, EmailID,
          ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
          TagMasterTagID, flgANPRPassEnable, MarkDelete, PaymentMode, PaymentType
        ) VALUES (
          @PlazaCode, @PlazaName, @ApplicationID, @GIN, @IssueDate, @HolderName, @CompanyName,
          @VehicleType, @ContactNo, @PassType, @PassTariffType, @EmailID,
          @ContractEndDate, @VehicleNo, @PasscardNumber, @PaidAmount,
          @TagMasterTagID, @flgANPRPassEnable, 0, @PaymentMode, @PaymentType
        )
      `, {
        PlazaCode: plaza.PlazaCode,
        PlazaName: plaza.PlazaName,
        ApplicationID: applicationID, // Numeric value
        GIN: gin,
        IssueDate: issueDate,
        HolderName: faker.person.fullName().substring(0, 25), // Limit to 25 chars
        CompanyName: faker.company.name().substring(0, 50), // Limit to 50 chars
        VehicleType: vehicleType.substring(0, 20), // Limit to 20 chars
        ContactNo: faker.phone.number('+91##########').substring(0, 21), // Limit to 21 chars
        PassType: passType.substring(0, 15), // Limit to 15 chars
        PassTariffType: `${passType} Standard`.substring(0, 25), // Limit to 25 chars
        EmailID: faker.internet.email().substring(0, 50), // Limit to 50 chars
        ContractEndDate: contractEndDate,
        VehicleNo: vehicleNo.substring(0, 15), // Limit to 15 chars
        PasscardNumber: passcardNumber.substring(0, 50), // Limit to 50 chars
        PaidAmount: paidAmount, // Numeric value
        TagMasterTagID: tagId.substring(0, 24), // Limit to 24 chars
        flgANPRPassEnable: Math.random() > 0.5 ? 'True' : 'False', // Limit to 5 chars
        PaymentMode: paymentMode.substring(0, 50), // Limit to 50 chars
        PaymentType: paymentType.substring(0, 50) // Limit to 50 chars
      });
      
      console.log(`✅ Created pass registration for vehicle ${vehicleNo}`);
    }

    console.log('\n✅ Seeding completed successfully');
  } catch (error) {
    console.error('❌ Error seeding pass registrations:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('\nDatabase connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the seeder
seedPassRegistrations();
