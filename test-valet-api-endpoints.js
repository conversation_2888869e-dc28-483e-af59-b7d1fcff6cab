const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/valet';

// Test data
const testData = {
    mobileNumber: '9876543210',
    plazaId: 1,
    customerData: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        vehicleNumber: 'KA01AB1234',
        guestName: '<PERSON>',
        location: 'Main Entrance'
    }
};

let authToken = null;
let customerId = null;
let sessionId = null;
let transactionId = null;

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null, useAuth = false) => {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: useAuth && authToken ? { 'Authorization': `Bearer ${authToken}` } : {}
        };
        
        if (data) {
            config.data = data;
        }
        
        const response = await axios(config);
        return { success: true, data: response.data, status: response.status };
    } catch (error) {
        return { 
            success: false, 
            error: error.response?.data || error.message,
            status: error.response?.status || 500
        };
    }
};

// Test functions
const testEndpoints = {
    // 0. Health Check
    async testHealthCheck() {
        console.log('\n🔍 Testing Valet System Health...');
        const result = await makeRequest('GET', '/health');
        console.log('Health Check:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 1. QR Code Endpoints
    async testQRCodeGeneration() {
        console.log('\n🔍 Testing QR Code Generation...');
        const result = await makeRequest('POST', '/qrcode/generate', { plazaId: testData.plazaId });
        console.log('QR Generation:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    async testQRCodeScanning() {
        console.log('\n🔍 Testing QR Code Scanning...');
        const result = await makeRequest('POST', '/qrcode/scan', { qrData: 'test-qr-data-plaza-1' });
        console.log('QR Scanning:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 2. OTP Endpoints
    async testOTPGeneration() {
        console.log('\n🔍 Testing OTP Generation...');
        const result = await makeRequest('POST', '/otp/generate', { 
            mobileNumber: testData.mobileNumber,
            plazaId: testData.plazaId 
        });
        console.log('OTP Generation:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    async testOTPVerification() {
        console.log('\n🔍 Testing OTP Verification...');
        const result = await makeRequest('POST', '/otp/verify', { 
            mobileNumber: testData.mobileNumber,
            otp: '123456' // Test OTP
        });
        console.log('OTP Verification:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 3. Customer Management Endpoints
    async testCustomerRegistration() {
        console.log('\n🔍 Testing Customer Registration...');
        const result = await makeRequest('POST', '/customers/register', {
            mobileNumber: testData.mobileNumber,
            ...testData.customerData
        });
        console.log('Customer Registration:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        if (result.success && result.data.customerId) {
            customerId = result.data.customerId;
        }
        return result;
    },

    async testGetCustomerProfile() {
        console.log('\n🔍 Testing Get Customer Profile...');
        if (!customerId) {
            console.log('❌ SKIPPED - No customer ID available');
            return { success: false, error: 'No customer ID' };
        }
        const result = await makeRequest('GET', `/customers/profile/${customerId}`);
        console.log('Get Customer Profile:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 4. Customer Flow Endpoints
    async testInitiateCustomerFlow() {
        console.log('\n🔍 Testing Initiate Customer Flow...');
        const result = await makeRequest('POST', '/flow/initiate', {
            customerId: customerId || 1,
            plazaValetPointId: 1
        });
        console.log('Initiate Customer Flow:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        if (result.success && result.data.sessionId) {
            sessionId = result.data.sessionId;
        }
        return result;
    },

    async testUpdateCustomerFlow() {
        console.log('\n🔍 Testing Update Customer Flow...');
        if (!sessionId) {
            console.log('❌ SKIPPED - No session ID available');
            return { success: false, error: 'No session ID' };
        }
        const result = await makeRequest('PUT', `/flow/update/${sessionId}`, {
            step: 'VEHICLE_DETAILS',
            data: testData.customerData
        });
        console.log('Update Customer Flow:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 5. Payment Endpoints
    async testGetPaymentOptions() {
        console.log('\n🔍 Testing Get Payment Options...');
        const result = await makeRequest('GET', `/payments/options/${testData.plazaId}`);
        console.log('Get Payment Options:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    async testInitiatePayment() {
        console.log('\n🔍 Testing Initiate Payment...');
        const result = await makeRequest('POST', '/payments/initiate', {
            customerId: customerId || 1,
            plazaId: testData.plazaId,
            amount: 100,
            paymentMethod: 'CASH'
        });
        console.log('Initiate Payment:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 6. Transaction Endpoints
    async testCreateTransaction() {
        console.log('\n🔍 Testing Create Transaction...');
        const result = await makeRequest('POST', '/transactions/create', {
            customerId: customerId || 1,
            plazaId: testData.plazaId,
            vehicleNumber: testData.customerData.vehicleNumber,
            guestName: testData.customerData.guestName,
            location: testData.customerData.location,
            valetFee: 50,
            parkingFee: 50
        });
        console.log('Create Transaction:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        if (result.success && result.data.transactionId) {
            transactionId = result.data.transactionId;
        }
        return result;
    },

    async testGetTransactionByPin() {
        console.log('\n🔍 Testing Get Transaction by PIN...');
        const result = await makeRequest('GET', '/transactions/pin/123456'); // Test PIN
        console.log('Get Transaction by PIN:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    },

    // 7. SMS Endpoints
    async testSendSMS() {
        console.log('\n🔍 Testing Send SMS...');
        const result = await makeRequest('POST', '/sms/send', {
            mobileNumber: testData.mobileNumber,
            message: 'Test SMS from Valet System',
            smsType: 'TEST'
        });
        console.log('Send SMS:', result.success ? '✅ SUCCESS' : '❌ FAILED', result);
        return result;
    }
};

// Main test runner
async function runAllTests() {
    console.log('🚀 Starting Valet API Endpoint Testing...');
    console.log('==========================================');
    
    const results = {};
    
    // Run all tests in sequence
    for (const [testName, testFunction] of Object.entries(testEndpoints)) {
        try {
            results[testName] = await testFunction();
            // Small delay between tests
            await new Promise(resolve => setTimeout(resolve, 500));
        } catch (error) {
            console.log(`❌ ${testName} CRASHED:`, error.message);
            results[testName] = { success: false, error: error.message };
        }
    }
    
    // Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('================');
    
    const successful = Object.values(results).filter(r => r.success).length;
    const total = Object.keys(results).length;
    
    console.log(`✅ Successful: ${successful}/${total}`);
    console.log(`❌ Failed: ${total - successful}/${total}`);
    
    if (successful === total) {
        console.log('\n🎉 ALL TESTS PASSED! Valet API is fully functional!');
    } else {
        console.log('\n⚠️  Some tests failed. Check individual results above.');
    }
    
    return results;
}

// Run the tests
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = { runAllTests, testEndpoints };
