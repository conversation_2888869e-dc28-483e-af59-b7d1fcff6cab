const express = require('express');
const router = express.Router();
const laneController = require('../controllers/LaneController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/lanes
 * @desc    Get all lanes
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), laneController.getAllLanes);

/**
 * @route   GET /api/lanes/:id
 * @desc    Get lane by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), laneController.getLaneById);

/**
 * @route   GET /api/lanes/plaza/:plazaId
 * @desc    Get lanes by plaza ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/plaza/:plazaId', auth(['View']), laneController.getLanesByPlaza);

/**
 * @route   POST /api/lanes
 * @desc    Create a new lane
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), laneController.createLane);

/**
 * @route   PUT /api/lanes/:id
 * @desc    Update a lane
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.put('/:id', auth(['Edit']), laneController.updateLane);

/**
 * @route   DELETE /api/lanes/:id
 * @desc    Delete a lane
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), laneController.deleteLane);

/**
 * @route   PATCH /api/lanes/:id/toggle-status
 * @desc    Toggle lane active status
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.patch('/:id/toggle-status', auth(['Edit']), laneController.toggleLaneStatus);

module.exports = router;
