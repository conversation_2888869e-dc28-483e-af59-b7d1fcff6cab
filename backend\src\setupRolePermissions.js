// Script to set up role permissions in the database
const db = require('./config/database');

async function setupRolePermissions() {
  try {
    // Connect to database
    await db.connect();
    console.log('Database connection successful');

    // First, check if Permissions table has the necessary permissions
    const permissionsQuery = `SELECT * FROM Permissions`;
    const permissionsResult = await db.query(permissionsQuery);
    
    if (permissionsResult.recordset.length === 0) {
      console.log('No permissions found in the database. Creating basic permissions...');
      
      // Create basic permissions
      await db.query(`
        INSERT INTO Permissions (Name, Description, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ('View', 'Permission to view resources', 1, 1, GETDATE()),
        ('Create', 'Permission to create resources', 1, 1, GETDATE()),
        ('Edit', 'Permission to edit resources', 1, 1, GETDATE()),
        ('Delete', 'Permission to delete resources', 1, 1, GETDATE()),
        ('Export', 'Permission to export data', 1, 1, GETDATE()),
        ('Import', 'Permission to import data', 1, 1, GETDATE()),
        ('Approve', 'Permission to approve actions', 1, 1, GETDATE())
      `);
      
      console.log('Basic permissions created');
    } else {
      console.log(`Found ${permissionsResult.recordset.length} permissions in the database`);
    }

    // Get role IDs
    const rolesQuery = `SELECT Id, Name FROM Roles WHERE Name IN ('CompanyAdmin', 'PlazaManager')`;
    const rolesResult = await db.query(rolesQuery);
    
    if (rolesResult.recordset.length < 2) {
      console.error('Could not find CompanyAdmin or PlazaManager roles');
      return;
    }
    
    const companyAdminRoleId = rolesResult.recordset.find(r => r.Name === 'CompanyAdmin').Id;
    const plazaManagerRoleId = rolesResult.recordset.find(r => r.Name === 'PlazaManager').Id;
    
    console.log(`CompanyAdmin Role ID: ${companyAdminRoleId}`);
    console.log(`PlazaManager Role ID: ${plazaManagerRoleId}`);

    // Clear existing role permissions for these roles
    await db.query(`
      DELETE FROM RolePermissions 
      WHERE RoleId IN (@companyAdminRoleId, @plazaManagerRoleId)
    `, {
      companyAdminRoleId,
      plazaManagerRoleId
    });
    
    console.log('Cleared existing role permissions');

    // Get all SubModulePermissions
    const subModulePermissionsQuery = `
      SELECT smp.Id, sm.Name AS SubModuleName, p.Name AS PermissionName, m.Name AS ModuleName
      FROM SubModulePermissions smp
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE smp.IsActive = 1
    `;
    
    const subModulePermissionsResult = await db.query(subModulePermissionsQuery);
    console.log(`Found ${subModulePermissionsResult.recordset.length} SubModulePermissions`);

    // Set up CompanyAdmin permissions
    console.log('Setting up CompanyAdmin permissions...');
    
    // CompanyAdmin has access to everything except system configuration
    const companyAdminPermissions = subModulePermissionsResult.recordset.filter(smp => 
      !['System Settings', 'Countries', 'States', 'Cities'].includes(smp.SubModuleName) &&
      smp.ModuleName !== 'Configuration'
    );
    
    for (const permission of companyAdminPermissions) {
      await db.query(`
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@roleId, @subModulePermissionId, 1, 1, GETDATE())
      `, {
        roleId: companyAdminRoleId,
        subModulePermissionId: permission.Id
      });
    }
    
    console.log(`Added ${companyAdminPermissions.length} permissions for CompanyAdmin role`);

    // Set up PlazaManager permissions
    console.log('Setting up PlazaManager permissions...');
    
    // PlazaManager has View access to Dashboard and Plaza Management
    // PlazaManager has full access to Lane Management
    // PlazaManager has View and Export access to Reports
    const plazaManagerPermissions = subModulePermissionsResult.recordset.filter(smp => 
      (smp.ModuleName === 'Dashboard' && smp.PermissionName === 'View') ||
      (smp.ModuleName === 'Plaza Management' && smp.PermissionName === 'View') ||
      (smp.ModuleName === 'Lane Management') ||
      (smp.ModuleName === 'Reports' && ['View', 'Export'].includes(smp.PermissionName))
    );
    
    for (const permission of plazaManagerPermissions) {
      await db.query(`
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@roleId, @subModulePermissionId, 1, 1, GETDATE())
      `, {
        roleId: plazaManagerRoleId,
        subModulePermissionId: permission.Id
      });
    }
    
    console.log(`Added ${plazaManagerPermissions.length} permissions for PlazaManager role`);

    console.log('Role permissions setup complete');
  } catch (error) {
    console.error('Database connection or query error:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('\nDatabase connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the function
setupRolePermissions();
