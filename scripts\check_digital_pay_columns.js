// Script to check the exact columns in the Digital Pay table
const sql = require('mssql');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from backend/.env
dotenv.config({ path: path.join(__dirname, 'backend', '.env') });

async function checkColumns() {
  try {
    console.log('Connecting to database...');
    const pool = await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    console.log('Connected to database successfully');

    // Get the exact column names from the table
    console.log('\nGetting exact column names from tblLaneDigitalPayConfiguration:');
    const columnsResult = await pool.request().query(`
      SELECT name 
      FROM sys.columns 
      WHERE object_id = OBJECT_ID('tblLaneDigitalPayConfiguration')
      ORDER BY column_id
    `);
    
    console.log('Column names:');
    columnsResult.recordset.forEach(col => {
      console.log(`- ${col.name}`);
    });

    await sql.close();
    console.log('Database connection closed');
  } catch (err) {
    console.error('Error:', err);
  }
}

checkColumns();