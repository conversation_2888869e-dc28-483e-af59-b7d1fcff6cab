const db = require('../config/database');

/**
 * ===============================================================================
 * # Lane Controller
 * ===============================================================================
 *
 * This controller handles all CRUD operations for the tblLaneDetails table in the
 * parking management system. It provides endpoints for:
 *
 * - Retrieving all lanes or specific lanes by ID or Plaza
 * - Creating new lanes with extensive configuration options
 * - Updating existing lane configurations
 * - Deleting lanes from the system
 * - Toggling the active status of lanes (activate/deactivate)
 *
 * The controller implements proper error handling, input validation, and
 * standardized response formatting for all operations.
 *
 * @module LaneController
 */
const laneController = {
  /**
   * ===============================================================================
   * ## GET ALL LANES
   * ===============================================================================
   *
   * Retrieves all lanes from the database with associated plaza and company information.
   * Results are ordered by LaneID in descending order (newest first).
   *
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lanes array or error message
   */
  getAllLanes: async (req, res) => {
    try {
      // Base query with joins
      let query = `
        SELECT
          l.*,                 /* All lane fields */
          p.PlazaName,         /* Plaza name from Plaza table */
          c.CompanyName        /* Company name from tblCompanyMaster table */
        FROM tblLaneDetails l
        LEFT JOIN Plaza p ON l.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster c ON l.CompanyID = c.Id
        WHERE 1=1              /* Base condition for adding filters */
      `;

      const queryParams = {};

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Add ordering
      query += ` ORDER BY l.LaneID DESC /* Sort by newest lanes first */`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Return successful response with lanes data
      res.json({
        success: true,
        lanes: result.recordset,
        message: 'Lanes retrieved successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in getAllLanes controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lanes',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANE BY ID
   * ===============================================================================
   *
   * Retrieves a specific lane by its ID with associated plaza and company information.
   * Returns a 404 error if the lane is not found.
   *
   * @param {Object} req - Express request object with lane ID in params
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lane object or error message
   */
  getLaneById: async (req, res) => {
    try {
      // Extract lane ID from request parameters
      const { id } = req.params;

      // Base query with joins
      let query = `
        SELECT
          l.*,                 /* All lane fields */
          p.PlazaName,         /* Plaza name from Plaza table */
          c.CompanyName        /* Company name from tblCompanyMaster table */
        FROM tblLaneDetails l
        LEFT JOIN Plaza p ON l.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster c ON l.CompanyID = c.Id
        WHERE l.LaneID = @id   /* Filter by lane ID */
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Check if lane exists or user has access to it
      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have access to this lane'
        });
      }

      // Return successful response with lane data
      res.json({
        success: true,
        lane: result.recordset[0],
        message: 'Lane retrieved successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in getLaneById controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lane',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANES BY PLAZA ID
   * ===============================================================================
   *
   * Retrieves all lanes associated with a specific plaza ID.
   * Results are ordered by LaneNumber for logical presentation.
   *
   * @param {Object} req - Express request object with plazaId in params
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lanes array or error message
   */
  getLanesByPlaza: async (req, res) => {
    try {
      // Extract plaza ID from request parameters
      const { plazaId } = req.params;

      // Base query with joins
      let query = `
        SELECT
          l.*,                    /* All lane fields */
          p.PlazaName,            /* Plaza name from Plaza table */
          c.CompanyName           /* Company name from tblCompanyMaster table */
        FROM tblLaneDetails l
        LEFT JOIN Plaza p ON l.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster c ON l.CompanyID = c.Id
        WHERE l.PlazaID = @plazaId /* Filter by plaza ID */
      `;

      const queryParams = { plazaId };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Add ordering
      query += ` ORDER BY l.LaneNumber /* Sort by lane number for logical order */`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Return successful response with lanes data
      res.json({
        success: true,
        lanes: result.recordset,
        message: 'Lanes retrieved successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in getLanesByPlaza controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lanes for plaza',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## CREATE NEW LANE
   * ===============================================================================
   *
   * Creates a new lane entry in the database with all associated configuration parameters.
   * Validates required fields before attempting to create the lane.
   *
   * @param {Object} req - Express request object with lane data in body
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message or error details
   */
  createLane: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract all possible lane parameters from request body
      // -------------------------------------------------------------------------------
      const {
        // Core lane identification and configuration
        PlazaID,            // Plaza where lane is located
        CompanyID,          // Company that owns/operates the lane
        LaneNumber,         // Unique number identifying the lane
        LaneType,           // Type of lane (Entry/Exit)
        LaneDetails,        // Additional details about the lane
        TypeCode,           // Code indicating lane type
        DataForPrint,       // Data to be printed on receipts
        LaneIP,             // IP address of the lane controller
        VehicleType,        // Type of vehicle the lane serves
        UpdatedBy,          // User who last updated the lane
        ActiveStatus,       // Whether the lane is active (true/false)

        // Display and interface configuration
        iDisplayComport,    // COM port for display
        DisplayPole,        // Display pole configuration
        CashDrawer,         // Cash drawer configuration

        // Lane operation configuration
        MultipleExit,       // Whether multiple exits are allowed
        Antipassback,       // Anti-passback configuration
        HFPasscard,         // High-frequency pass card configuration
        HFPassPort,         // High-frequency pass port configuration
        CoinReaderPort,     // Port for coin reader
        APS_Exit,           // Automated parking system exit configuration

        // Feature flags
        flgKioskCamera,     // Kiosk camera enabled flag
        flgReceiptPrint,    // Receipt printing enabled flag
        flgGKeyDetails,     // G-key details flag
        flgCKeyCard,        // C-key card flag
        flgP4S,             // P4S feature flag
        LOTFee,             // LOT fee configuration
        flgPasscard,        // Pass card enabled flag

        // Payment gateway configuration
        PGTID,              // Payment gateway ID
        pgActivationKey,    // Payment gateway activation key
        Passcard_Reader_Type, // Type of pass card reader
        PayTmPG,            // PayTm payment gateway configuration

        // Camera configuration
        FlgLPRCamera,       // License plate recognition camera flag
        LPRCamIP,           // LPR camera IP address
        LPRCamID,           // LPR camera ID
        LPRCamPass,         // LPR camera password

        // Additional configuration
        iGraceMinute,       // Grace period in minutes
        flgPaperSensor,     // Paper sensor flag
        PGSLevel,           // PGS level configuration
        PrinterMake,        // Printer manufacturer
        BarcodeType,        // Type of barcode used
        PrinterPort,        // Printer port configuration

        // Payment configuration
        sPaytmWallet,       // PayTm wallet configuration
        sPaytmMID,          // PayTm merchant ID
        sPaytmKey,          // PayTm key
        fRecyclerStatus,    // Recycler status
        sSMSKey,            // SMS key for notifications

        // Additional lane configuration
        flgCCUpdateEx,      // CC update exchange flag
        LaneNumber2,        // Secondary lane number
        VehicleType2,       // Secondary vehicle type
        flgSubLane,         // Sub-lane flag
        RecyclerType        // Type of recycler
      } = req.body;

      // -------------------------------------------------------------------------------
      // Validate required fields
      // -------------------------------------------------------------------------------
      if (!PlazaID || !CompanyID || !LaneNumber || !LaneType) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing',
          requiredFields: ['PlazaID', 'CompanyID', 'LaneNumber', 'LaneType']
        });
      }

      // -------------------------------------------------------------------------------
      // Check user permissions based on role
      // -------------------------------------------------------------------------------
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // Check if CompanyAdmin has access to this company
          const companyAccessQuery = `
            SELECT COUNT(*) as count
            FROM UserCompany
            WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
          `;

          const companyAccessResult = await db.query(companyAccessQuery, {
            userId: req.user.id,
            companyId: CompanyID
          });

          if (companyAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a lane for this company'
            });
          }

          // Check if CompanyAdmin has access to this plaza
          const plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE uc.UserId = @userId AND p.Id = @plazaId AND uc.IsActive = 1 AND p.IsActive = 1
          `;

          const plazaAccessResult = await db.query(plazaAccessQuery, {
            userId: req.user.id,
            plazaId: PlazaID
          });

          if (plazaAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a lane for this plaza'
            });
          }
        } else if (req.user.role === 'PlazaManager') {
          return res.status(403).json({
            success: false,
            message: 'Plaza Managers cannot create lanes'
          });
        }
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : (UpdatedBy || 1);

      // -------------------------------------------------------------------------------
      // Insert new lane into database
      // -------------------------------------------------------------------------------
      const result = await db.query(`
        INSERT INTO tblLaneDetails (
          /* Core lane identification fields */
          PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode,
          DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus,

          /* Display and interface configuration */
          iDisplayComport, DisplayPole, CashDrawer, MultipleExit, Antipassback,

          /* Lane operation configuration */
          HFPasscard, HFPassPort, CoinReaderPort, APS_Exit, flgKioskCamera,

          /* Feature flags and payment configuration */
          flgReceiptPrint, flgGKeyDetails, flgCKeyCard, flgP4S, LOTFee,
          flgPasscard, PGTID, pgActivationKey, Passcard_Reader_Type, PayTmPG,

          /* Camera configuration */
          FlgLPRCamera, LPRCamIP, LPRCamID, LPRCamPass, iGraceMinute,

          /* Additional configuration */
          flgPaperSensor, PGSLevel, PrinterMake, BarcodeType, PrinterPort,
          sPaytmWallet, sPaytmMID, sPaytmKey, fRecyclerStatus, sSMSKey,

          /* Additional lane configuration */
          flgCCUpdateEx, LaneNumber2, VehicleType2, flgSubLane, RecyclerType,

          /* Timestamp for creation */
          UpdatedDateTime
        ) VALUES (
          @PlazaID, @CompanyID, @LaneNumber, @LaneType, @LaneDetails, @TypeCode,
          @DataForPrint, @LaneIP, @VehicleType, @UpdatedBy, @ActiveStatus,
          @iDisplayComport, @DisplayPole, @CashDrawer, @MultipleExit, @Antipassback,
          @HFPasscard, @HFPassPort, @CoinReaderPort, @APS_Exit, @flgKioskCamera,
          @flgReceiptPrint, @flgGKeyDetails, @flgCKeyCard, @flgP4S, @LOTFee,
          @flgPasscard, @PGTID, @pgActivationKey, @Passcard_Reader_Type, @PayTmPG,
          @FlgLPRCamera, @LPRCamIP, @LPRCamID, @LPRCamPass, @iGraceMinute,
          @flgPaperSensor, @PGSLevel, @PrinterMake, @BarcodeType, @PrinterPort,
          @sPaytmWallet, @sPaytmMID, @sPaytmKey, @fRecyclerStatus, @sSMSKey,
          @flgCCUpdateEx, @LaneNumber2, @VehicleType2, @flgSubLane, @RecyclerType,
          GETDATE()
        )
      `, {
        // Core lane identification fields with validation
        PlazaID,
        CompanyID,
        LaneNumber,
        LaneType,
        LaneDetails: LaneDetails || null,
        TypeCode: TypeCode || null,
        DataForPrint: DataForPrint || null,
        LaneIP: LaneIP || null,
        VehicleType: VehicleType || null,
        UpdatedBy: updatedBy, // Use the current user ID
        ActiveStatus: ActiveStatus !== undefined ? ActiveStatus : true, // Default to active if not specified

        // Optional fields with null fallback for all other parameters
        iDisplayComport: iDisplayComport || null,
        DisplayPole: DisplayPole || null,
        CashDrawer: CashDrawer || null,
        MultipleExit: MultipleExit || null,
        Antipassback: Antipassback || null,
        HFPasscard: HFPasscard || null,
        HFPassPort: HFPassPort || null,
        CoinReaderPort: CoinReaderPort || null,
        APS_Exit: APS_Exit || null,
        flgKioskCamera: flgKioskCamera || null,
        flgReceiptPrint: flgReceiptPrint || null,
        flgGKeyDetails: flgGKeyDetails || null,
        flgCKeyCard: flgCKeyCard || null,
        flgP4S: flgP4S || null,
        LOTFee: LOTFee || null,
        flgPasscard: flgPasscard || null,
        PGTID: PGTID || null,
        pgActivationKey: pgActivationKey || null,
        Passcard_Reader_Type: Passcard_Reader_Type || null,
        PayTmPG: PayTmPG || null,
        FlgLPRCamera: FlgLPRCamera || null,
        LPRCamIP: LPRCamIP || null,
        LPRCamID: LPRCamID || null,
        LPRCamPass: LPRCamPass || null,
        iGraceMinute: iGraceMinute || null,
        flgPaperSensor: flgPaperSensor || null,
        PGSLevel: PGSLevel || null,
        PrinterMake: PrinterMake || null,
        BarcodeType: BarcodeType || null,
        PrinterPort: PrinterPort || null,
        sPaytmWallet: sPaytmWallet || null,
        sPaytmMID: sPaytmMID || null,
        sPaytmKey: sPaytmKey || null,
        fRecyclerStatus: fRecyclerStatus || null,
        sSMSKey: sSMSKey || null,
        flgCCUpdateEx: flgCCUpdateEx || null,
        LaneNumber2: LaneNumber2 || null,
        VehicleType2: VehicleType2 || null,
        flgSubLane: flgSubLane || null,
        RecyclerType: RecyclerType || null
      });

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      res.status(201).json({
        success: true,
        message: 'Lane created successfully',
        result
      });
    } catch (error) {
      // Log error and return error response
      console.error('Create lane failed:', error.message);
      res.status(500).json({ success: false, error: 'Failed to create lane', details: error.message });
    }
  },

  /**
   * ===============================================================================
   * ## UPDATE EXISTING LANE
   * ===============================================================================
   *
   * Updates an existing lane's configuration in the database.
   * Validates required fields and checks if the lane exists before updating.
   *
   * @param {Object} req - Express request object with lane ID in params and updated data in body
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message or error details
   */
  updateLane: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract lane ID and updated data
      // -------------------------------------------------------------------------------
      const { id } = req.params;
      const {
        // Core lane identification and configuration
        PlazaID,            // Plaza where lane is located
        CompanyID,          // Company that owns/operates the lane
        LaneNumber,         // Unique number identifying the lane
        LaneType,           // Type of lane (Entry/Exit)
        LaneDetails,        // Additional details about the lane
        TypeCode,           // Code indicating lane type
        DataForPrint,       // Data to be printed on receipts
        LaneIP,             // IP address of the lane controller
        VehicleType,        // Type of vehicle the lane serves
        UpdatedBy,          // User who last updated the lane
        ActiveStatus,       // Whether the lane is active (true/false)

        // Display and interface configuration
        iDisplayComport,    // COM port for display
        DisplayPole,        // Display pole configuration
        CashDrawer,         // Cash drawer configuration

        // Lane operation configuration
        MultipleExit,       // Whether multiple exits are allowed
        Antipassback,       // Anti-passback configuration
        HFPasscard,         // High-frequency pass card configuration
        HFPassPort,         // High-frequency pass port configuration
        CoinReaderPort,     // Port for coin reader
        APS_Exit,           // Automated parking system exit configuration

        // Feature flags
        flgKioskCamera,     // Kiosk camera enabled flag
        flgReceiptPrint,    // Receipt printing enabled flag
        flgGKeyDetails,     // G-key details flag
        flgCKeyCard,        // C-key card flag
        flgP4S,             // P4S feature flag
        LOTFee,             // LOT fee configuration
        flgPasscard,        // Pass card enabled flag

        // Payment gateway configuration
        PGTID,              // Payment gateway ID
        pgActivationKey,    // Payment gateway activation key
        Passcard_Reader_Type, // Type of pass card reader
        PayTmPG,            // PayTm payment gateway configuration

        // Camera configuration
        FlgLPRCamera,       // License plate recognition camera flag
        LPRCamIP,           // LPR camera IP address
        LPRCamID,           // LPR camera ID
        LPRCamPass,         // LPR camera password

        // Additional configuration
        iGraceMinute,       // Grace period in minutes
        flgPaperSensor,     // Paper sensor flag
        PGSLevel,           // PGS level configuration
        PrinterMake,        // Printer manufacturer
        BarcodeType,        // Type of barcode used
        PrinterPort,        // Printer port configuration

        // Payment configuration
        sPaytmWallet,       // PayTm wallet configuration
        sPaytmMID,          // PayTm merchant ID
        sPaytmKey,          // PayTm key
        fRecyclerStatus,    // Recycler status
        sSMSKey,            // SMS key for notifications

        // Additional lane configuration
        flgCCUpdateEx,      // CC update exchange flag
        LaneNumber2,        // Secondary lane number
        VehicleType2,       // Secondary vehicle type
        flgSubLane,         // Sub-lane flag
        RecyclerType        // Type of recycler
      } = req.body;

      // -------------------------------------------------------------------------------
      // Validate required fields
      // -------------------------------------------------------------------------------
      if (!PlazaID || !CompanyID || !LaneNumber || !LaneType) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing',
          requiredFields: ['PlazaID', 'CompanyID', 'LaneNumber', 'LaneType']
        });
      }

      // -------------------------------------------------------------------------------
      // Check if lane exists and user has access to it
      // -------------------------------------------------------------------------------
      let query = `
        SELECT l.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.LaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only update lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only update lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      const checkResult = await db.query(query, queryParams);

      if (checkResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have permission to update it'
        });
      }

      const existingLane = checkResult.recordset[0];

      // -------------------------------------------------------------------------------
      // Check user permissions for the new company/plaza if they're being changed
      // -------------------------------------------------------------------------------
      if (req.user && req.user.role !== 'SuperAdmin') {
        // If company is being changed, check if user has access to the new company
        if (CompanyID !== existingLane.CompanyID) {
          if (req.user.role === 'CompanyAdmin') {
            const companyAccessQuery = `
              SELECT COUNT(*) as count
              FROM UserCompany
              WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
            `;

            const companyAccessResult = await db.query(companyAccessQuery, {
              userId: req.user.id,
              companyId: CompanyID
            });

            if (companyAccessResult.recordset[0].count === 0) {
              return res.status(403).json({
                success: false,
                message: 'You do not have permission to move this lane to the specified company'
              });
            }
          } else if (req.user.role === 'PlazaManager') {
            return res.status(403).json({
              success: false,
              message: 'Plaza Managers cannot change the company of a lane'
            });
          }
        }

        // If plaza is being changed, check if user has access to the new plaza
        if (PlazaID !== existingLane.PlazaID) {
          if (req.user.role === 'CompanyAdmin') {
            const plazaAccessQuery = `
              SELECT COUNT(*) as count
              FROM Plaza p
              JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
              WHERE uc.UserId = @userId AND p.Id = @plazaId AND uc.IsActive = 1 AND p.IsActive = 1
            `;

            const plazaAccessResult = await db.query(plazaAccessQuery, {
              userId: req.user.id,
              plazaId: PlazaID
            });

            if (plazaAccessResult.recordset[0].count === 0) {
              return res.status(403).json({
                success: false,
                message: 'You do not have permission to move this lane to the specified plaza'
              });
            }
          } else if (req.user.role === 'PlazaManager') {
            const plazaAccessQuery = `
              SELECT COUNT(*) as count
              FROM UserPlaza
              WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
            `;

            const plazaAccessResult = await db.query(plazaAccessQuery, {
              userId: req.user.id,
              plazaId: PlazaID
            });

            if (plazaAccessResult.recordset[0].count === 0) {
              return res.status(403).json({
                success: false,
                message: 'You do not have permission to move this lane to the specified plaza'
              });
            }
          }
        }
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : (UpdatedBy || 1);

      // -------------------------------------------------------------------------------
      // Update lane in database
      // -------------------------------------------------------------------------------
      const result = await db.query(`
        UPDATE tblLaneDetails SET
          /* Core lane identification fields */
          PlazaID = @PlazaID,
          CompanyID = @CompanyID,
          LaneNumber = @LaneNumber,
          LaneType = @LaneType,
          LaneDetails = @LaneDetails,
          TypeCode = @TypeCode,
          DataForPrint = @DataForPrint,
          LaneIP = @LaneIP,
          VehicleType = @VehicleType,
          ActiveStatus = @ActiveStatus,

          /* Display and interface configuration */
          iDisplayComport = @iDisplayComport,
          DisplayPole = @DisplayPole,
          CashDrawer = @CashDrawer,
          MultipleExit = @MultipleExit,
          Antipassback = @Antipassback,

          /* Lane operation configuration */
          HFPasscard = @HFPasscard,
          HFPassPort = @HFPassPort,
          CoinReaderPort = @CoinReaderPort,
          APS_Exit = @APS_Exit,
          flgKioskCamera = @flgKioskCamera,

          /* Feature flags and payment configuration */
          flgReceiptPrint = @flgReceiptPrint,
          flgGKeyDetails = @flgGKeyDetails,
          flgCKeyCard = @flgCKeyCard,
          flgP4S = @flgP4S,
          LOTFee = @LOTFee,
          flgPasscard = @flgPasscard,
          PGTID = @PGTID,
          pgActivationKey = @pgActivationKey,
          Passcard_Reader_Type = @Passcard_Reader_Type,
          PayTmPG = @PayTmPG,

          /* Camera configuration */
          FlgLPRCamera = @FlgLPRCamera,
          LPRCamIP = @LPRCamIP,
          LPRCamID = @LPRCamID,
          LPRCamPass = @LPRCamPass,
          iGraceMinute = @iGraceMinute,

          /* Additional configuration */
          flgPaperSensor = @flgPaperSensor,
          PGSLevel = @PGSLevel,
          PrinterMake = @PrinterMake,
          BarcodeType = @BarcodeType,
          PrinterPort = @PrinterPort,
          sPaytmWallet = @sPaytmWallet,
          sPaytmMID = @sPaytmMID,
          sPaytmKey = @sPaytmKey,
          fRecyclerStatus = @fRecyclerStatus,
          sSMSKey = @sSMSKey,

          /* Additional lane configuration */
          flgCCUpdateEx = @flgCCUpdateEx,
          LaneNumber2 = @LaneNumber2,
          VehicleType2 = @VehicleType2,
          flgSubLane = @flgSubLane,
          RecyclerType = @RecyclerType,

          /* Update metadata */
          UpdatedBy = @UpdatedBy,
          UpdatedDateTime = GETDATE()
        WHERE LaneID = @id
      `, {
        // Lane ID for WHERE clause
        id,

        // Core lane identification fields with validation
        PlazaID,
        CompanyID,
        LaneNumber,
        LaneType,
        LaneDetails: LaneDetails || null,
        TypeCode: TypeCode || null,
        DataForPrint: DataForPrint || null,
        LaneIP: LaneIP || null,
        VehicleType: VehicleType || null,
        UpdatedBy: updatedBy, // Use the current user ID
        ActiveStatus: ActiveStatus !== undefined ? ActiveStatus : true, // Default to active if not specified

        // Optional fields with null fallback for all other parameters
        iDisplayComport: iDisplayComport || null,
        DisplayPole: DisplayPole || null,
        CashDrawer: CashDrawer || null,
        MultipleExit: MultipleExit || null,
        Antipassback: Antipassback || null,
        HFPasscard: HFPasscard || null,
        HFPassPort: HFPassPort || null,
        CoinReaderPort: CoinReaderPort || null,
        APS_Exit: APS_Exit || null,
        flgKioskCamera: flgKioskCamera || null,
        flgReceiptPrint: flgReceiptPrint || null,
        flgGKeyDetails: flgGKeyDetails || null,
        flgCKeyCard: flgCKeyCard || null,
        flgP4S: flgP4S || null,
        LOTFee: LOTFee || null,
        flgPasscard: flgPasscard || null,
        PGTID: PGTID || null,
        pgActivationKey: pgActivationKey || null,
        Passcard_Reader_Type: Passcard_Reader_Type || null,
        PayTmPG: PayTmPG || null,
        FlgLPRCamera: FlgLPRCamera || null,
        LPRCamIP: LPRCamIP || null,
        LPRCamID: LPRCamID || null,
        LPRCamPass: LPRCamPass || null,
        iGraceMinute: iGraceMinute || null,
        flgPaperSensor: flgPaperSensor || null,
        PGSLevel: PGSLevel || null,
        PrinterMake: PrinterMake || null,
        BarcodeType: BarcodeType || null,
        PrinterPort: PrinterPort || null,
        sPaytmWallet: sPaytmWallet || null,
        sPaytmMID: sPaytmMID || null,
        sPaytmKey: sPaytmKey || null,
        fRecyclerStatus: fRecyclerStatus || null,
        sSMSKey: sSMSKey || null,
        flgCCUpdateEx: flgCCUpdateEx || null,
        LaneNumber2: LaneNumber2 || null,
        VehicleType2: VehicleType2 || null,
        flgSubLane: flgSubLane || null,
        RecyclerType: RecyclerType || null
      });

      // -------------------------------------------------------------------------------
      // Check if update was successful
      // -------------------------------------------------------------------------------
      if (result.rowsAffected[0] === 0) {
        return res.status(404).json({ success: false, error: 'Lane not found or no changes made' });
      }

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      res.json({
        success: true,
        message: 'Lane updated successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Update lane failed:', error.message);
      res.status(500).json({ success: false, error: 'Failed to update lane', details: error.message });
    }
  },

  /**
   * ===============================================================================
   * ## DELETE LANE
   * ===============================================================================
   *
   * Permanently removes a lane from the database.
   * This is a destructive operation that cannot be undone.
   *
   * @param {Object} req - Express request object with lane ID in params
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message or error
   */
  deleteLane: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract lane ID from request parameters
      // -------------------------------------------------------------------------------
      const { id } = req.params;

      // -------------------------------------------------------------------------------
      // Check if lane exists and user has access to it
      // -------------------------------------------------------------------------------
      let query = `
        SELECT l.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.LaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only delete lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager cannot delete lanes
          return res.status(403).json({
            success: false,
            message: 'Plaza Managers cannot delete lanes'
          });
        }
      }

      const checkResult = await db.query(query, queryParams);

      if (checkResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have permission to delete it'
        });
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : 1;

      // -------------------------------------------------------------------------------
      // Execute soft delete operation (update ActiveStatus to 0)
      // -------------------------------------------------------------------------------
      const result = await db.query(`
        UPDATE tblLaneDetails
        SET ActiveStatus = 0,
            UpdatedBy = @updatedBy,
            UpdatedDateTime = GETDATE()
        WHERE LaneID = @id
      `, {
        id,
        updatedBy
      });

      // Check if any rows were affected
      if (result.rowsAffected[0] === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or already deleted'
        });
      }

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      res.json({
        success: true,
        message: 'Lane deleted successfully'
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in deleteLane controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to delete lane',
        message: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## TOGGLE LANE ACTIVE STATUS
   * ===============================================================================
   *
   * Toggles a lane's active status between active (1) and inactive (0).
   * This endpoint handles the activation/deactivation of lanes in the system.
   *
   * @param {Object} req - Express request object with lane ID in params and UpdatedBy in body
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with success message and new status
   */
  toggleLaneStatus: async (req, res) => {
    try {
      // -------------------------------------------------------------------------------
      // Extract parameters
      // -------------------------------------------------------------------------------
      const { id } = req.params;

      // -------------------------------------------------------------------------------
      // Check if lane exists and user has access to it
      // -------------------------------------------------------------------------------
      let query = `
        SELECT l.*, p.CompanyId as PlazaCompanyId,
               CAST(l.ActiveStatus AS INT) AS ActiveStatusInt
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.LaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only toggle lanes from companies they have access to
          query += ` AND l.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only toggle lanes from plazas they are assigned to
          query += ` AND l.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      const statusResult = await db.query(query, queryParams);

      // Check if lane exists and user has access to it
      if (statusResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane not found or you do not have permission to modify it'
        });
      }

      // -------------------------------------------------------------------------------
      // Determine new status value
      // -------------------------------------------------------------------------------
      // Get current status (could be string '0'/'1' or integer 0/1)
      const currentStatus = statusResult.recordset[0].ActiveStatusInt;
      console.log('Current status from database:', currentStatus, typeof currentStatus);

      // Toggle between '0' and '1' (inactive/active) - ensure we're using strings for consistency
      const newStatus = currentStatus === 1 || currentStatus === '1' ? '0' : '1';
      console.log('New status to set:', newStatus, typeof newStatus);

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : 1;

      // -------------------------------------------------------------------------------
      // Update lane status in database
      // -------------------------------------------------------------------------------
      await db.query(`
        UPDATE tblLaneDetails
        SET ActiveStatus = @newStatus,        /* New status value (0 or 1) */
            UpdatedBy = @updatedBy,           /* User who made the change */
            UpdatedDateTime = GETDATE()       /* Timestamp of the change */
        WHERE LaneID = @id
      `, {
        id,
        newStatus,
        updatedBy
      });

      // -------------------------------------------------------------------------------
      // Return success response
      // -------------------------------------------------------------------------------
      res.json({
        success: true,
        message: `Lane ${newStatus === '1' ? 'activated' : 'deactivated'} successfully`,
        newStatus: newStatus === '1', // Boolean for frontend compatibility
        newStatusValue: newStatus,    // Actual string value from database
        laneId: id                    // Include the lane ID for frontend state updates
      });
    } catch (error) {
      // Log error and return error response
      console.error('Error in toggleLaneStatus controller:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to toggle lane status',
        message: error.message
      });
    }
  }
};

module.exports = laneController;
