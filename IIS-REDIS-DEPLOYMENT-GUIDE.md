# 🚀 PWVMS IIS Deployment with Redis Integration Guide

## 📋 Prerequisites

Before deploying, ensure you have:

### ✅ **Required Software:**
1. **Windows Server** with IIS enabled
2. **Node.js** (v16 or higher)
3. **IISNode** module
4. **URL Rewrite Module** for IIS
5. **Redis** (Memurai for Windows)

### ✅ **Required Permissions:**
- Administrator access to the server
- IIS management permissions
- File system write permissions

---

## 🔧 **Step-by-Step Deployment**

### **Step 1: Install Prerequisites**

#### **Install IIS Features:**
```powershell
# Run as Administrator
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-ManagementConsole, IIS-HttpErrors, IIS-HttpRedirect, IIS-StaticContent, IIS-DefaultDocument, IIS-ApplicationDevelopment, IIS-NetFxExtensibility45, IIS-ASPNET45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-WebSockets, IIS-ApplicationInit, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic -All
```

#### **Install Node.js:**
- Download from: https://nodejs.org/
- Install LTS version

#### **Install IISNode:**
- Download from: https://github.com/Azure/iisnode/releases
- Install the MSI package

#### **Install URL Rewrite Module:**
- Download from: https://www.iis.net/downloads/microsoft/url-rewrite
- Install the MSI package

#### **Install Redis (Memurai):**
- Download from: https://www.memurai.com/
- Install and start the service

### **Step 2: Deploy PWVMS**

#### **Option A: Automated Deployment (Recommended)**
```powershell
# Navigate to your PWVMS directory
cd d:\PWVMS

# Run the deployment script as Administrator
.\deploy-to-iis-with-redis.ps1
```

#### **Option B: Manual Deployment**
If you prefer manual deployment, follow these steps:

1. **Create IIS Site Directory:**
   ```powershell
   New-Item -ItemType Directory -Path "C:\inetpub\wwwroot\PWVMS" -Force
   ```

2. **Build Frontend:**
   ```powershell
   cd frontend
   npm install
   npm run build
   ```

3. **Copy Files:**
   ```powershell
   # Copy backend
   Copy-Item -Path "backend\*" -Destination "C:\inetpub\wwwroot\PWVMS\backend" -Recurse -Force
   
   # Copy frontend build
   Copy-Item -Path "frontend\build\*" -Destination "C:\inetpub\wwwroot\PWVMS\frontend\build" -Recurse -Force
   ```

4. **Install Backend Dependencies:**
   ```powershell
   cd C:\inetpub\wwwroot\PWVMS\backend
   npm install --production
   ```

### **Step 3: Configure Environment**

1. **Update Environment File:**
   ```powershell
   # Edit the production environment file
   notepad C:\inetpub\wwwroot\PWVMS\backend\.env
   ```

2. **Key Configuration Values:**
   ```env
   # Database (update with your values)
   DB_SERVER=your_sql_server
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_NAME=ParkwizOps
   
   # Redis (usually localhost for same server)
   REDIS_HOST=localhost
   REDIS_PORT=6379
   REDIS_PASSWORD=
   
   # Security (generate strong secrets)
   JWT_SECRET=your_super_secure_jwt_secret
   SESSION_SECRET=your_super_secure_session_secret
   ```

### **Step 4: Create IIS Site**

1. **Create Application Pool:**
   ```powershell
   New-WebAppPool -Name "PWVMS_AppPool"
   Set-ItemProperty -Path "IIS:\AppPools\PWVMS_AppPool" -Name "managedRuntimeVersion" -Value ""
   ```

2. **Create Website:**
   ```powershell
   New-Website -Name "PWVMS" -PhysicalPath "C:\inetpub\wwwroot\PWVMS" -ApplicationPool "PWVMS_AppPool" -Port 80
   ```

3. **Set Permissions:**
   ```powershell
   $acl = Get-Acl "C:\inetpub\wwwroot\PWVMS"
   $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\PWVMS_AppPool", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
   $acl.SetAccessRule($accessRule)
   Set-Acl "C:\inetpub\wwwroot\PWVMS" $acl
   ```

---

## 🔴 **Redis Configuration**

### **Verify Redis is Running:**
```powershell
# Check Redis service
Get-Service -Name "Memurai"

# Test Redis connection
& "C:\Program Files\Memurai\memurai-cli.exe" ping
```

### **Redis Configuration for PWVMS:**
The application will automatically connect to Redis using these settings:
- **Host:** localhost
- **Port:** 6379
- **Database:** 0 (default)
- **Password:** None (default)

### **Redis Performance Benefits:**
- ✅ **Dashboard data caching** (60s - 1 hour TTL)
- ✅ **Session management**
- ✅ **Real-time data updates**
- ✅ **API response caching**

---

## 🌐 **Web.config Configuration**

The deployment script creates a `web.config` file with:
- **API routing** to Node.js backend
- **Static file serving** for React frontend
- **React Router support**
- **Security headers**
- **Compression enabled**
- **Error handling**

---

## 🔍 **Verification Steps**

### **1. Check Services:**
```powershell
# Check IIS site
Get-Website -Name "PWVMS"

# Check application pool
Get-IISAppPool -Name "PWVMS_AppPool"

# Check Redis service
Get-Service -Name "Memurai"
```

### **2. Test Application:**
1. **Open browser:** http://localhost (or your server IP)
2. **Check API:** http://localhost/api/health
3. **Login to dashboard**
4. **Verify Redis caching** in browser network tab

### **3. Check Logs:**
- **IIS Logs:** `C:\inetpub\logs\LogFiles\W3SVC1\`
- **Application Logs:** `C:\inetpub\wwwroot\PWVMS\logs\`
- **Node.js Logs:** Check IIS Manager → PWVMS site → Logging

---

## 🚨 **Troubleshooting**

### **Common Issues:**

#### **1. Site Not Loading:**
```powershell
# Restart application pool
Restart-WebAppPool -Name "PWVMS_AppPool"

# Check permissions
icacls "C:\inetpub\wwwroot\PWVMS" /grant "IIS AppPool\PWVMS_AppPool:(OI)(CI)F"
```

#### **2. API Errors:**
- Check `.env` file configuration
- Verify database connection
- Check Node.js version compatibility

#### **3. Redis Connection Issues:**
```powershell
# Restart Redis service
Restart-Service -Name "Memurai"

# Check Redis logs
Get-EventLog -LogName Application -Source "Memurai"
```

#### **4. Performance Issues:**
- Verify Redis is running and connected
- Check cache hit rates in application logs
- Monitor memory usage

---

## 📊 **Performance Monitoring**

### **Redis Monitoring:**
```powershell
# Check Redis memory usage
& "C:\Program Files\Memurai\memurai-cli.exe" info memory

# Check cache keys
& "C:\Program Files\Memurai\memurai-cli.exe" keys "*"

# Monitor Redis performance
& "C:\Program Files\Memurai\memurai-cli.exe" monitor
```

### **Application Monitoring:**
- **Dashboard load times** should be <1 second after first load
- **API response times** should be <200ms for cached data
- **Memory usage** should be stable with Redis caching

---

## 🔄 **Updates and Maintenance**

### **Deploying Updates:**
1. **Stop application pool:**
   ```powershell
   Stop-WebAppPool -Name "PWVMS_AppPool"
   ```

2. **Update files:**
   ```powershell
   # Copy new backend files
   Copy-Item -Path "backend\src\*" -Destination "C:\inetpub\wwwroot\PWVMS\backend\src" -Recurse -Force
   
   # Update frontend if needed
   Copy-Item -Path "frontend\build\*" -Destination "C:\inetpub\wwwroot\PWVMS\frontend\build" -Recurse -Force
   ```

3. **Clear Redis cache:**
   ```powershell
   & "C:\Program Files\Memurai\memurai-cli.exe" flushall
   ```

4. **Start application pool:**
   ```powershell
   Start-WebAppPool -Name "PWVMS_AppPool"
   ```

### **Regular Maintenance:**
- **Monitor Redis memory usage**
- **Check application logs**
- **Update Node.js dependencies**
- **Backup database regularly**

---

## 🎯 **Success Indicators**

✅ **Deployment Successful When:**
- Website loads at http://localhost
- Login page appears correctly
- Dashboard loads with data
- API endpoints respond quickly
- Redis cache shows hit/miss logs
- No errors in IIS logs

✅ **Redis Working When:**
- First dashboard load: Slow (database query)
- Second dashboard load: Fast (<200ms, cached)
- Logs show "served from Redis cache"
- Redis CLI responds to `ping` command

---

## 📞 **Support**

If you encounter issues:
1. Check the troubleshooting section above
2. Review application logs
3. Verify all prerequisites are installed
4. Ensure Redis service is running
5. Check database connectivity

**Your PWVMS application with Redis caching should now be running on IIS with significantly improved performance!** 🚀