import React, { useState, useRef } from 'react';
import {
    X,
  Search,
  Edit,
  Trash2,
  ToggleLeft,
  ToggleRight,
  Download,
  Columns,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';

export default function AnprList({ configurations, onEdit, onDelete, onSelect }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'ANPRID', direction: 'descending' }); // Default sort by ID descending
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [visibleColumns, setVisibleColumns] = useState({
    PlazaName: true,
    CompanyName: true,
    LaneNumberDetail: true,
    PMSLaneNumber: true,
    flgEnableANPR: true,
    ANPRVendor: true,
    ActiveStatus: true,
    AllowBlacklistedVehicle: true,
    Actions: true
  });
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const tableRef = useRef(null);

  // Filter configurations based on search term
  const filteredConfigurations = configurations.filter(config =>
    (config.PlazaName && config.PlazaName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (config.CompanyName && config.CompanyName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (config.LaneNumberDetail && String(config.LaneNumberDetail).toLowerCase().includes(searchTerm.toLowerCase())) ||
    (config.PMSLaneNumber && String(config.PMSLaneNumber).toLowerCase().includes(searchTerm.toLowerCase())) ||
    (config.ANPRVendor && config.ANPRVendor.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort configurations based on sortConfig
  const sortedConfigurations = [...filteredConfigurations].sort((a, b) => {
    let valA = a[sortConfig.key];
    let valB = b[sortConfig.key];

    // Handle potential null/undefined values
    if (valA == null && valB == null) return 0;
    if (valA == null) return 1; // Nulls last
    if (valB == null) return -1; // Nulls last

    // Convert boolean-like strings ('1'/'0') to numbers for sorting if needed
    if (['flgEnableANPR', 'ActiveStatus', 'AllowBlacklistedVehicle'].includes(sortConfig.key)) {
        valA = valA === '1' ? 1 : 0;
        valB = valB === '1' ? 1 : 0;
    }

    // Basic comparison
    if (valA < valB) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (valA > valB) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedConfigurations.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedConfigurations.length / itemsPerPage);

  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const getSortIndicator = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? ' u2191' : ' u2193'; // Up/Down arrow
    }
    return '';
  };

  const toggleColumnVisibility = (column) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column]
    }));
  };

  const exportToCSV = () => {
    const headers = Object.keys(visibleColumns)
      .filter(col => visibleColumns[col] && col !== 'Actions')
      .map(col => col); // Use the keys directly as headers

    let csvContent = headers.join(',') + '\n';

    sortedConfigurations.forEach(config => {
      const row = headers.map(header => {
        let value = config[header];
        // Handle boolean-like fields for display
        if (['flgEnableANPR', 'ActiveStatus', 'AllowBlacklistedVehicle'].includes(header)) {
          value = value === '1' ? 'Yes' : 'No';
        }
        // Escape commas and quotes in values
        const formattedValue = value !== null && value !== undefined ? `"${String(value).replace(/"/g, '""')}"` : '""';
        return formattedValue;
      }).join(',');
      csvContent += row + '\n';
    });

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `anpr_configs_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderValue = (config, key) => {
    const value = config[key];
    if (['flgEnableANPR', 'ActiveStatus', 'AllowBlacklistedVehicle'].includes(key)) {
      const isActive = value === '1';
      return (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
          {isActive ? 'Yes' : 'No'}
        </span>
      );
    }
    return value;
  };

  return (
    <div className="overflow-hidden">
      <div className="p-4 border-b bg-gray-50">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* Search Input */}
          <div className="relative w-full md:w-1/3">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search ANPR Configs..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 shadow-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2 w-full md:w-auto">
            {/* Items per page selector */}
            <select
              className="border border-gray-300 rounded-md px-3 py-2 text-sm bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
            </select>

            {/* Column visibility toggle */}
            <div className="relative">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="flex items-center space-x-1 bg-white border border-gray-300 rounded-md px-3 py-2 text-sm hover:bg-gray-50 shadow-sm"
                title="Toggle columns"
              >
                <Columns size={16} />
                <span className="hidden md:inline">Columns</span>
              </button>

              {/* Column Selector Modal/Dropdown */}
              {showColumnSelector && (
                <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4" onClick={() => setShowColumnSelector(false)}>
                  <div className="bg-white rounded-lg p-6 max-w-md w-full max-h-[80vh] overflow-auto shadow-xl" onClick={(e) => e.stopPropagation()}>
                    <div className="flex justify-between items-center mb-4 border-b pb-2">
                      <h3 className="text-lg font-semibold text-gray-800">Toggle Columns</h3>
                      <button
                        onClick={() => setShowColumnSelector(false)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X size={20} />
                      </button>
                    </div>
                    <div className="space-y-3">
                      {Object.keys(visibleColumns).map(column => (
                        <div key={column} className="flex items-center justify-between p-2 rounded hover:bg-gray-100">
                          <label htmlFor={`column-${column}`} className="text-sm font-medium text-gray-700">
                            {column.replace(/([A-Z])/g, ' $1').trim()} {/* Add spaces before caps */}
                          </label>
                          <input
                            type="checkbox"
                            id={`column-${column}`}
                            checked={visibleColumns[column]}
                            onChange={() => toggleColumnVisibility(column)}
                            className="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 cursor-pointer"
                            disabled={column === 'Actions'} // Disable toggling for Actions column
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Export Button */}
            <button
              onClick={exportToCSV}
              className="flex items-center space-x-1 bg-green-600 text-white rounded-md px-3 py-2 text-sm hover:bg-green-700 shadow-sm"
              title="Export to CSV"
            >
              <Download size={16} />
              <span className="hidden md:inline">Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table ref={tableRef} className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-100">
            <tr>
              {Object.keys(visibleColumns).map(key => (
                visibleColumns[key] && (
                  <th
                    key={key}
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-200"
                    onClick={() => key !== 'Actions' && requestSort(key)}
                  >
                    {key.replace(/([A-Z])/g, ' $1').trim()} {/* Add spaces before caps */}
                    {key !== 'Actions' && getSortIndicator(key)}
                  </th>
                )
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentItems.length > 0 ? (
              currentItems.map((config) => (
                <tr key={config.ANPRID} className="hover:bg-gray-50">
                  {Object.keys(visibleColumns).map(key => (
                    visibleColumns[key] && (
                      <td key={key} className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                        {key === 'Actions' ? (
                          <div className="flex space-x-2">
                            <PermissionButton
                              requiredModule="ANPR"
                              requiredPermissions={["Edit"]}
                              companyId={config.CompanyID}
                              plazaId={config.PlazaID}
                              onClick={() => onEdit(config)}
                              className="text-blue-600 hover:text-blue-800"
                              title="Edit"
                            >
                              <Edit size={18} />
                            </PermissionButton>
                            <PermissionButton
                              requiredModule="ANPR"
                              requiredPermissions={["Delete"]}
                              companyId={config.CompanyID}
                              plazaId={config.PlazaID}
                              onClick={() => onDelete(config.ANPRID)}
                              className="text-red-600 hover:text-red-800"
                              title="Delete (Mark Inactive)"
                            >
                              <Trash2 size={18} />
                            </PermissionButton>
                            {/* Add Toggle Status button if needed, requires backend endpoint */}
                            {/* <PermissionButton
                              requiredModule="ANPR"
                              requiredPermissions={["Edit"]}
                              companyId={config.CompanyID}
                              plazaId={config.PlazaID}
                              onClick={() => onToggleStatus(config.ANPRID)} // Assuming onToggleStatus exists
                              className={config.ActiveStatus === '1' ? 'text-green-600 hover:text-green-800' : 'text-gray-400 hover:text-gray-600'}
                              title={config.ActiveStatus === '1' ? 'Deactivate' : 'Activate'}
                            >
                              {config.ActiveStatus === '1' ? <ToggleRight size={18} /> : <ToggleLeft size={18} />}
                            </PermissionButton> */}
                          </div>
                        ) : (
                          renderValue(config, key)
                        )}
                      </td>
                    )
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-6 py-4 text-center text-sm text-gray-500">
                  No ANPR configurations found.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 bg-gray-50">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => paginate(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to <span className="font-medium">{Math.min(indexOfLastItem, sortedConfigurations.length)}</span> of{' '}
                <span className="font-medium">{sortedConfigurations.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => paginate(1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  title="First Page"
                >
                  <ChevronsLeft size={20} />
                </button>
                <button
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  title="Previous Page"
                >
                  <ChevronLeft size={20} />
                </button>
                {/* Page numbers (simplified for brevity) */}
                <span className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  title="Next Page"
                >
                  <ChevronRight size={20} />
                </button>
                <button
                  onClick={() => paginate(totalPages)}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                  title="Last Page"
                >
                  <ChevronsRight size={20} />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}