// frontend/src/components/auth/PermissionLink.js
import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/authContext';

/**
 * PermissionLink component that conditionally renders a Link based on user permissions
 *
 * @param {Object} props - Component props
 * @param {string[]} [props.requiredPermissions] - Array of permissions required to render the link
 * @param {string} [props.requiredModule] - Module name for permission check
 * @param {string[]} [props.allowedRoles] - Array of roles allowed to render the link
 * @param {number} [props.companyId] - Company ID to check access for
 * @param {number} [props.plazaId] - Plaza ID to check access for
 * @param {React.ReactNode} [props.children] - Link content
 * @param {string} props.to - Link destination
 * @param {string} [props.className] - Additional CSS classes
 * @param {Object} [props.rest] - Additional props to pass to the Link
 * @returns {React.ReactNode} - The Link or null
 */
const PermissionLink = ({
  requiredPermissions = [],
  requiredModule,
  allowedRoles = [],
  companyId,
  plazaId,
  children,
  to,
  className = '',
  ...rest
}) => {
  const {
    user,
    hasPermission,
    hasRole,
    hasCompanyAccess,
    hasPlazaAccess,
    isSuperAdmin
  } = useAuth();

  // If no user is logged in, don't render the link
  if (!user) {
    return null;
  }

  // SuperAdmin can access everything
  if (isSuperAdmin()) {
    return (
      <Link
        to={to}
        className={className}
        {...rest}
      >
        {children}
      </Link>
    );
  }

  // Check role-based access if specified
  if (allowedRoles.length > 0 && !allowedRoles.some(role => hasRole(role))) {
    return null;
  }

  // Check permission-based access if specified
  if (requiredModule && requiredPermissions.length > 0) {
    const hasRequiredPermission = requiredPermissions.some(
      permission => hasPermission(requiredModule, permission)
    );

    if (!hasRequiredPermission) {
      return null;
    }
  }

  // Check company access if specified
  if (companyId && !hasCompanyAccess(companyId)) {
    return null;
  }

  // Check plaza access if specified
  if (plazaId && !hasPlazaAccess(plazaId)) {
    return null;
  }

  // User is authorized
  return (
    <Link
      to={to}
      className={className}
      {...rest}
    >
      {children}
    </Link>
  );
};

// Export both as default and named export for backward compatibility
export { PermissionLink };
export default PermissionLink;
