// backend/src/services/NotificationService.js
const EmailService = require('./EmailService');
const db = require('../config/database');

class NotificationService {
  /**
   * Send hierarchical notifications based on user roles
   */
  async sendHierarchicalNotification(actorUser, action, entityType, entityData = null) {
    try {
      console.log(`📧 Processing hierarchical notification: ${action} ${entityType} by ${actorUser.Username}`);

      // Determine who should be notified based on actor's role
      const recipients = await this.getNotificationRecipients(actorUser, action, entityType);

      // Send notifications to each recipient
      for (const recipient of recipients) {
        try {
          if (action === 'delete') {
            await EmailService.sendDeletionAlert(
              recipient.Email,
              actorUser,
              entityType,
              entityData
            );
          } else {
            await EmailService.sendActivityNotification(
              recipient.Email,
              actorUser,
              action,
              entityType,
              entityData
            );
          }

          // Log the notification
          await this.logNotification(actorUser.Id, action, entityType, entityData?.Id, recipient.Id, 'email');
          
          console.log(`✅ Notification sent to ${recipient.Email} (${recipient.RoleName})`);
        } catch (error) {
          console.error(`❌ Failed to send notification to ${recipient.Email}:`, error.message);
        }
      }

      return recipients.length;
    } catch (error) {
      console.error('❌ Hierarchical notification failed:', error.message);
      throw error;
    }
  }

  /**
   * Get notification recipients based on hierarchical rules
   */
  async getNotificationRecipients(actorUser, action, entityType) {
    const recipients = [];

    switch (actorUser.role) {
      case 'SuperAdmin':
        // SuperAdmin actions don't notify anyone (they're at the top)
        break;

      case 'CompanyAdmin':
        // CompanyAdmin actions notify SuperAdmin
        const superAdmins = await this.getSuperAdmins();
        recipients.push(...superAdmins);
        break;

      case 'PlazaManager':
        // PlazaManager actions notify CompanyAdmin of their company
        const companyAdmins = await this.getCompanyAdminsForPlazaManager(actorUser.Id);
        recipients.push(...companyAdmins);
        break;

      default:
        console.warn(`Unknown role: ${actorUser.role}`);
    }

    return recipients;
  }

  /**
   * Get all SuperAdmin users
   */
  async getSuperAdmins() {
    const query = `
      SELECT 
        u.Id,
        u.Username,
        u.Email,
        u.FirstName,
        u.LastName,
        r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE r.Name = 'SuperAdmin' 
      AND u.IsActive = 1
      AND u.Email IS NOT NULL
      AND u.Email != ''
    `;

    const result = await db.query(query);
    return result.recordset;
  }

  /**
   * Get CompanyAdmin users for a specific PlazaManager
   */
  async getCompanyAdminsForPlazaManager(plazaManagerId) {
    const query = `
      SELECT DISTINCT
        ca.Id,
        ca.Username,
        ca.Email,
        ca.FirstName,
        ca.LastName,
        r.Name as RoleName
      FROM Users ca
      JOIN Roles r ON ca.RoleId = r.Id
      JOIN UserCompany uc ON ca.Id = uc.UserId
      JOIN Company c ON uc.CompanyId = c.Id
      JOIN Plaza p ON c.Id = p.CompanyId
      JOIN UserPlaza up ON p.Id = up.PlazaId
      WHERE r.Name = 'CompanyAdmin'
      AND up.UserId = @plazaManagerId
      AND ca.IsActive = 1
      AND uc.IsActive = 1
      AND up.IsActive = 1
      AND ca.Email IS NOT NULL
      AND ca.Email != ''
    `;

    const result = await db.query(query, { plazaManagerId });
    return result.recordset;
  }

  /**
   * Send welcome email notification
   */
  async sendWelcomeNotification(newUser, createdByUser, credentials = null) {
    try {
      // Send welcome email to the new user
      await EmailService.sendWelcomeEmail(newUser.Email, newUser, credentials);

      // Log the notification
      await this.logNotification(
        createdByUser.Id,
        'create',
        'user',
        newUser.Id,
        newUser.Id,
        'email'
      );

      return true;
    } catch (error) {
      console.error(`Failed to send welcome notification to ${newUser.Email}:`, error.message);
      throw error;
    }
  }

  /**
   * Log notification to database
   */
  async logNotification(actorUserId, action, entityType, entityId, notifiedUserId, method) {
    try {
      const query = `
        INSERT INTO ActivityNotifications (
          ActorUserId, Action, EntityType, EntityId, NotifiedUserId, 
          NotificationMethod, CreatedAt
        ) VALUES (
          @actorUserId, @action, @entityType, @entityId, @notifiedUserId,
          @method, GETDATE()
        )
      `;

      await db.query(query, {
        actorUserId,
        action,
        entityType,
        entityId,
        notifiedUserId,
        method
      });
    } catch (error) {
      console.error('Failed to log notification:', error.message);
    }
  }

  /**
   * Get notification history for a user
   */
  async getNotificationHistory(userId, limit = 50) {
    const query = `
      SELECT TOP (@limit)
        an.Id,
        an.Action,
        an.EntityType,
        an.EntityId,
        an.NotificationMethod,
        an.CreatedAt,
        actor.Username as ActorUsername,
        actor.FirstName as ActorFirstName,
        actor.LastName as ActorLastName,
        actor_role.Name as ActorRole
      FROM ActivityNotifications an
      JOIN Users actor ON an.ActorUserId = actor.Id
      JOIN Roles actor_role ON actor.RoleId = actor_role.Id
      WHERE an.NotifiedUserId = @userId
      ORDER BY an.CreatedAt DESC
    `;

    const result = await db.query(query, { userId, limit });
    return result.recordset;
  }

  /**
   * Get system-wide activity summary for SuperAdmin
   */
  async getActivitySummary(days = 7) {
    const query = `
      SELECT 
        an.Action,
        an.EntityType,
        COUNT(*) as Count,
        actor_role.Name as ActorRole
      FROM ActivityNotifications an
      JOIN Users actor ON an.ActorUserId = actor.Id
      JOIN Roles actor_role ON actor.RoleId = actor_role.Id
      WHERE an.CreatedAt >= DATEADD(day, -@days, GETDATE())
      GROUP BY an.Action, an.EntityType, actor_role.Name
      ORDER BY Count DESC
    `;

    const result = await db.query(query, { days });
    return result.recordset;
  }

  /**
   * Check if user should receive notifications for specific entity type
   */
  async shouldNotify(userId, entityType, action) {
    // Get user role and permissions
    const userQuery = `
      SELECT 
        u.Id,
        r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Id = @userId AND u.IsActive = 1
    `;

    const userResult = await db.query(userQuery, { userId });
    if (userResult.recordset.length === 0) {
      return false;
    }

    const user = userResult.recordset[0];

    // SuperAdmin gets all notifications
    if (user.RoleName === 'SuperAdmin') {
      return true;
    }

    // CompanyAdmin gets notifications for their company entities
    if (user.RoleName === 'CompanyAdmin') {
      return ['plaza', 'lane', 'anpr', 'digitalpay', 'fastag', 'user'].includes(entityType.toLowerCase());
    }

    // PlazaManager doesn't receive notifications (they're at the bottom of hierarchy)
    return false;
  }
}

module.exports = new NotificationService();
