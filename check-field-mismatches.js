const sql = require('mssql');
require('dotenv').config({ path: './backend/.env' });

const config = {
    server: process.env.DB_SERVER,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    options: {
        encrypt: true,
        trustServerCertificate: true
    }
};

async function checkFieldMismatches() {
    try {
        await sql.connect(config);
        console.log('🔍 Checking Field Mismatches Between Controllers and Database...\n');

        // 1. Check Customer table fields vs controller expectations
        console.log('📋 1. CUSTOMER TABLE FIELD ANALYSIS:');
        console.log('====================================');
        
        const customerColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Customer'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('   Database Customer table has:');
        customerColumns.recordset.forEach(col => {
            console.log(`      ✅ ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });
        
        console.log('\n   Controller expects:');
        const customerExpectedFields = [
            'Id', 'Name', 'MobileNumber', 'AddressId', 'IsActive', 
            'CreatedBy', 'CreatedOn', 'ModifiedBy', 'ModifiedOn'
        ];
        
        const customerActualFields = customerColumns.recordset.map(col => col.COLUMN_NAME);
        
        customerExpectedFields.forEach(field => {
            if (customerActualFields.includes(field)) {
                console.log(`      ✅ ${field} - EXISTS`);
            } else {
                console.log(`      ❌ ${field} - MISSING`);
            }
        });

        // 2. Check Users table fields vs controller expectations
        console.log('\n📋 2. USERS TABLE FIELD ANALYSIS:');
        console.log('=================================');
        
        const usersColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Users'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('   Database Users table has:');
        usersColumns.recordset.forEach(col => {
            console.log(`      ✅ ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });
        
        console.log('\n   Controller expects (based on code analysis):');
        const usersExpectedFields = [
            'Id', 'FirstName', 'LastName', 'Email', 'Mobile', 'RoleId',
            'Username', 'Password', 'HashPassword', 'IsActive'
        ];
        
        const usersActualFields = usersColumns.recordset.map(col => col.COLUMN_NAME);
        
        usersExpectedFields.forEach(field => {
            if (usersActualFields.includes(field)) {
                console.log(`      ✅ ${field} - EXISTS`);
            } else {
                console.log(`      ❌ ${field} - MISSING`);
            }
        });

        // 3. Check OTP table fields vs controller expectations
        console.log('\n📋 3. OTP TABLE FIELD ANALYSIS:');
        console.log('===============================');
        
        const otpColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'OTP'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('   Database OTP table has:');
        otpColumns.recordset.forEach(col => {
            console.log(`      ✅ ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });
        
        console.log('\n   Controller expects:');
        const otpExpectedFields = [
            'Id', 'MobileNumber', 'OTP', 'ExpireTime', 'IsActive', 
            'CreatedBy', 'CreatedOn', 'ModifiedBy', 'ModifiedOn'
        ];
        
        const otpActualFields = otpColumns.recordset.map(col => col.COLUMN_NAME);
        
        otpExpectedFields.forEach(field => {
            if (otpActualFields.includes(field)) {
                console.log(`      ✅ ${field} - EXISTS`);
            } else {
                console.log(`      ❌ ${field} - MISSING`);
            }
        });

        // 4. Check ValetControllers table fields
        console.log('\n📋 4. VALETCONTROLLERS TABLE FIELD ANALYSIS:');
        console.log('===========================================');
        
        const valetControllersColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'ValetControllers'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('   Database ValetControllers table has:');
        valetControllersColumns.recordset.forEach(col => {
            console.log(`      ✅ ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });

        // 5. Check ValetDrivers table fields
        console.log('\n📋 5. VALETDRIVERS TABLE FIELD ANALYSIS:');
        console.log('=======================================');
        
        const valetDriversColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'ValetDrivers'
            ORDER BY ORDINAL_POSITION
        `);
        
        console.log('   Database ValetDrivers table has:');
        valetDriversColumns.recordset.forEach(col => {
            console.log(`      ✅ ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
        });

        // 6. Check for common field naming issues
        console.log('\n📋 6. COMMON FIELD NAMING ISSUES:');
        console.log('=================================');
        
        // Check if Users table has MobileNumber vs Mobile
        const hasMobileNumber = usersActualFields.includes('MobileNumber');
        const hasMobile = usersActualFields.includes('Mobile');
        
        if (hasMobile && !hasMobileNumber) {
            console.log('   ⚠️  Users table uses "Mobile" but some controllers may expect "MobileNumber"');
        }
        
        // Check Customer table MobileNumber field
        const customerHasMobileNumber = customerActualFields.includes('MobileNumber');
        if (customerHasMobileNumber) {
            console.log('   ✅ Customer table correctly uses "MobileNumber"');
        } else {
            console.log('   ❌ Customer table missing "MobileNumber" field');
        }

        // 7. Test actual stored procedures exist
        console.log('\n📋 7. STORED PROCEDURES VERIFICATION:');
        console.log('====================================');
        
        const storedProcedures = [
            'sp_Valet_Customer_GetByMobile',
            'sp_Valet_Customer_Create',
            'sp_Valet_OTP_Generate',
            'sp_Valet_OTP_Verify',
            'sp_Valet_OTP_GetStatus',
            'sp_Valet_OTP_Cleanup'
        ];
        
        for (const procName of storedProcedures) {
            try {
                const procCheck = await sql.query(`
                    SELECT ROUTINE_NAME 
                    FROM INFORMATION_SCHEMA.ROUTINES 
                    WHERE ROUTINE_TYPE = 'PROCEDURE' AND ROUTINE_NAME = '${procName}'
                `);
                
                if (procCheck.recordset.length > 0) {
                    console.log(`   ✅ ${procName} - EXISTS`);
                } else {
                    console.log(`   ❌ ${procName} - MISSING`);
                }
            } catch (error) {
                console.log(`   ❌ ${procName} - ERROR: ${error.message}`);
            }
        }

        console.log('\n📊 SUMMARY & CRITICAL ISSUES:');
        console.log('==============================');
        
        let criticalIssues = [];
        
        // Check for critical missing fields
        if (!customerActualFields.includes('MobileNumber')) {
            criticalIssues.push('Customer table missing MobileNumber field');
        }
        
        if (!otpActualFields.includes('MobileNumber')) {
            criticalIssues.push('OTP table missing MobileNumber field');
        }
        
        if (!otpActualFields.includes('OTP')) {
            criticalIssues.push('OTP table missing OTP field');
        }
        
        if (criticalIssues.length === 0) {
            console.log('✅ No critical field mismatches found!');
            console.log('✅ All essential valet system fields are properly configured.');
            console.log('🚀 Database schema is compatible with valet controllers.');
        } else {
            console.log('❌ CRITICAL ISSUES FOUND:');
            criticalIssues.forEach(issue => {
                console.log(`   - ${issue}`);
            });
        }

    } catch (error) {
        console.error('❌ Error checking field mismatches:', error);
    } finally {
        await sql.close();
    }
}

checkFieldMismatches();
