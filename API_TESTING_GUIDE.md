# 🧪 **VALET SYSTEM API TESTING GUIDE**

## 📋 **PRE-TESTING SETUP**

### **1. Execute Stored Procedures**
```sql
-- Run this in Azure Data Studio or SSMS
-- File: EXECUTE_ALL_VALET_STORED_PROCEDURES.sql
-- This will create all 4 stored procedures in ParkwizOps database
```

### **2. Start Backend Server**
```bash
cd backend
npm start
# Server should start on http://localhost:5000
```

### **3. Required Database Tables**
Ensure these tables exist in your ParkwizOps database:
- `Customer`
- `Plaza` 
- `PlazaValetPoint`
- `ParkingTransactions`
- `PaymentGatewayTransactions`
- `SMSNotifications`
- `CustomerSessions`
- `ValetQRCodes`

---

## 🔗 **API ENDPOINT TESTING**

### **BASE URL**: `http://localhost:5000/api/valet`

---

## 1. 🔗 **QR CODE & CUSTOMER ENTRY FLOW**

### **Step 1: Generate QR Code for Valet Point**
```http
POST /api/valet/qrcode/generate
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "plazaValetPointId": 1,
  "qrType": "VALET_POINT"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "QR code generated successfully",
  "data": {
    "id": 1,
    "qrData": "VALET_1_1733123456789_abc12345",
    "qrType": "VALET_POINT",
    "plazaValetPointId": 1,
    "plazaName": "Test Plaza",
    "companyName": "Test Company"
  }
}
```

### **Step 2: Validate QR Code (Customer Scans)**
```http
GET /api/valet/qrcode/scan/VALET_1_1733123456789_abc12345
```

**Expected Response:**
```json
{
  "success": true,
  "message": "QR code is valid",
  "data": {
    "qrData": "VALET_1_1733123456789_abc12345",
    "plazaValetPointId": 1,
    "plazaName": "Test Plaza",
    "valetCharges": 100.00,
    "isActive": true
  }
}
```

---

## 2. 👤 **CUSTOMER REGISTRATION & OTP FLOW**

### **Step 3: Register Customer with Mobile Number**
```http
POST /api/valet/customers/register
Content-Type: application/json

{
  "mobileNumber": "**********",
  "plazaValetPointId": 1
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Customer registered successfully",
  "data": {
    "customerId": 1,
    "mobileNumber": "**********",
    "isNewCustomer": true,
    "otpSent": true
  }
}
```

### **Step 4: Send OTP SMS**
```http
POST /api/valet/sms/send-otp
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "mobileNumber": "**********",
  "otp": "123456",
  "customerId": 1
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "OTP sent successfully",
  "data": {
    "smsId": 1,
    "mobileNumber": "**********",
    "deliveryStatus": "SENT",
    "provider": "SIMULATION"
  }
}
```

### **Step 5: Verify OTP**
```http
POST /api/valet/otp/verify
Content-Type: application/json

{
  "mobileNumber": "**********",
  "otp": "123456",
  "customerId": 1
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "OTP verified successfully",
  "data": {
    "customerId": 1,
    "mobileNumber": "**********",
    "isVerified": true
  }
}
```

---

## 3. 🔄 **CUSTOMER FLOW MANAGEMENT**

### **Step 6: Initialize Customer Session**
```http
POST /api/valet/flow/initialize
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "plazaValetPointId": 1,
  "customerId": 1
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Customer session initialized successfully",
  "data": {
    "sessionId": 1,
    "customerId": 1,
    "currentStep": "INITIALIZED",
    "nextStep": "CUSTOMER_DETAILS",
    "valetPoint": {
      "id": 1,
      "name": "Main Entrance",
      "plazaName": "Test Plaza",
      "charges": 100.00
    }
  }
}
```

### **Step 7: Update Session with Customer Details**
```http
PUT /api/valet/flow/update/1
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "step": "CUSTOMER_DETAILS",
  "customerId": 1,
  "formData": {
    "customerName": "John Doe",
    "customerEmail": "<EMAIL>"
  }
}
```

### **Step 8: Update Session with Vehicle Details**
```http
PUT /api/valet/flow/update/1
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "step": "VEHICLE_DETAILS",
  "customerId": 1,
  "formData": {
    "vehicleNumber": "MH01AB1234",
    "vehicleType": "Car",
    "vehicleColor": "White",
    "vehicleMake": "Honda",
    "vehicleModel": "City"
  }
}
```

---

## 4. 💳 **PAYMENT PROCESSING FLOW**

### **Step 9: Get Payment Options**
```http
GET /api/valet/payments/options/1
Authorization: Bearer YOUR_JWT_TOKEN
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Payment options retrieved successfully",
  "data": {
    "plazaId": 1,
    "plazaName": "Test Plaza",
    "valetCharges": 100.00,
    "availableMethods": [
      {
        "method": "RAZORPAY",
        "name": "RazorPay",
        "types": ["UPI", "Card", "NetBanking", "Wallet"]
      },
      {
        "method": "PHONEPE",
        "name": "PhonePe",
        "types": ["UPI", "Card", "Wallet"]
      },
      {
        "method": "CASH",
        "name": "Cash Payment",
        "types": ["Cash"]
      }
    ]
  }
}
```

### **Step 10: Initiate Payment**
```http
POST /api/valet/payments/initiate
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "transactionId": 1,
  "paymentMethod": "RAZORPAY",
  "amount": 100.00,
  "customerId": 1,
  "customerName": "John Doe",
  "customerEmail": "<EMAIL>",
  "customerMobile": "**********"
}
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Payment initiated successfully",
  "data": {
    "paymentId": 1,
    "orderId": "VALET_20241207_1_1234",
    "amount": 100.00,
    "currency": "INR",
    "paymentMethod": "RAZORPAY",
    "status": "PENDING"
  }
}
```

### **Step 11: Update Payment Status (Webhook Simulation)**
```http
PUT /api/valet/payments/status/1
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "status": "SUCCESS",
  "gatewayTransactionId": "pay_abc123xyz",
  "gatewayResponse": "{\"payment_id\":\"pay_abc123xyz\",\"status\":\"captured\"}"
}
```

---

## 5. 📱 **SMS NOTIFICATIONS TESTING**

### **Step 12: Send VRN Notification**
```http
POST /api/valet/sms/send-vrn-notification
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "mobileNumber": "**********",
  "vehicleNumber": "MH01AB1234",
  "vrn": "VRN123456",
  "trackingUrl": "https://app.com/track/VRN123456",
  "customerId": 1,
  "transactionId": 1
}
```

### **Step 13: Send Pickup Ready Notification**
```http
POST /api/valet/sms/send-pickup-ready
Content-Type: application/json
Authorization: Bearer YOUR_JWT_TOKEN

{
  "mobileNumber": "**********",
  "vehicleNumber": "MH01AB1234",
  "pickupUrl": "https://app.com/pickup/VRN123456",
  "customerId": 1,
  "transactionId": 1
}
```

---

## 6. 🧪 **TESTING CHECKLIST**

### **✅ QR Code System**
- [ ] Generate QR code for valet point
- [ ] Validate QR code on scan
- [ ] Handle invalid QR codes

### **✅ Customer Registration**
- [ ] Register new customer with mobile
- [ ] Handle existing customer login
- [ ] Validate mobile number format

### **✅ OTP System**
- [ ] Send OTP SMS
- [ ] Verify correct OTP
- [ ] Handle incorrect OTP
- [ ] Handle expired OTP

### **✅ Customer Flow**
- [ ] Initialize session
- [ ] Update session with form data
- [ ] Validate step progression
- [ ] Complete session

### **✅ Payment Processing**
- [ ] Get payment options
- [ ] Initiate RazorPay payment
- [ ] Initiate PhonePe payment
- [ ] Handle cash payments
- [ ] Process payment callbacks

### **✅ SMS Notifications**
- [ ] Send OTP SMS
- [ ] Send VRN notification
- [ ] Send pickup ready SMS
- [ ] Send payment success SMS

---

## 🚨 **COMMON TESTING ISSUES**

### **Database Connection Issues**
```bash
# Check if backend connects to database
# Look for this in console:
"Database connected: ParkwizOps"
```

### **Missing Tables**
```sql
-- Check if required tables exist
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_NAME IN ('Customer', 'Plaza', 'PlazaValetPoint', 'ParkingTransactions');
```

### **Authentication Issues**
```bash
# Make sure JWT token is valid
# Use SuperAdmin login to get token first
```

---

## 🎯 **NEXT STEPS AFTER TESTING**

1. ✅ **Verify all APIs work correctly**
2. ✅ **Test error scenarios**
3. ✅ **Check database records are created**
4. ✅ **Validate SMS delivery simulation**
5. ✅ **Test payment gateway integration**
6. 🚀 **Start Frontend Development**

**Ready to test? Let's start with the stored procedures execution!** 🚀
