const express = require('express');
const router = express.Router();
const addressController = require('../controllers/AddressController');
const auth = require('../middleware/auth');

/**
 * @route   POST /api/addresses
 * @desc    Create a new address
 * @access  Private (All authenticated users)
 */
router.post('/', auth(['Create']), addressController.createAddress);

/**
 * @route   GET /api/addresses
 * @desc    Get all addresses
 * @access  Private (All authenticated users)
 */
router.get('/', auth(['View']), addressController.getAllAddresses);

/**
 * @route   GET /api/addresses/:id
 * @desc    Get an address by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', auth(['View']), addressController.getAddressById);

/**
 * @route   PUT /api/addresses/:id
 * @desc    Update an address
 * @access  Private (All authenticated users)
 */
router.put('/:id', auth(['Edit']), addressController.updateAddress);

/**
 * @route   DELETE /api/addresses/:id
 * @desc    Delete an address
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), addressController.deleteAddress);

module.exports = router;