const db = require('./backend/src/config/database');

async function debugPermissionTables() {
  try {
    console.log('🔍 Debugging Permission Management Database...\n');

    // Check if tables exist
    const tables = ['Modules', 'SubModules', 'Permissions', 'SubModulePermissions', 'Roles', 'RolePermissions'];
    
    for (const table of tables) {
      try {
        const result = await db.query(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ ${table}: ${result.recordset[0].count} records`);
      } catch (error) {
        console.log(`❌ ${table}: Table not found or error - ${error.message}`);
      }
    }

    console.log('\n📋 Sample data from each table:');

    // Get sample data from Modules
    try {
      const modules = await db.query('SELECT TOP 5 * FROM Modules ORDER BY DisplayOrder');
      console.log('\n🏗️ Modules:');
      modules.recordset.forEach(m => {
        console.log(`  - ${m.Id}: ${m.Name} (Active: ${m.IsActive})`);
      });
    } catch (error) {
      console.log('❌ Error fetching Modules:', error.message);
    }

    // Get sample data from Roles
    try {
      const roles = await db.query('SELECT * FROM Roles WHERE IsActive = 1');
      console.log('\n👥 Roles:');
      roles.recordset.forEach(r => {
        console.log(`  - ${r.Id}: ${r.Name} (Active: ${r.IsActive})`);
      });
    } catch (error) {
      console.log('❌ Error fetching Roles:', error.message);
    }

    // Test the exact query from the controller
    console.log('\n🔍 Testing exact controller query...');
    try {
      const query = `
        SELECT 
          m.Id as ModuleId,
          m.Name as ModuleName,
          m.Description as ModuleDescription,
          m.Icon as ModuleIcon,
          m.DisplayOrder as ModuleDisplayOrder,
          m.IsActive as ModuleIsActive,
          
          sm.Id as SubModuleId,
          sm.Name as SubModuleName,
          sm.Description as SubModuleDescription,
          sm.Route as SubModuleRoute,
          sm.Icon as SubModuleIcon,
          sm.DisplayOrder as SubModuleDisplayOrder,
          sm.IsActive as SubModuleIsActive,
          
          p.Id as PermissionId,
          p.Name as PermissionName,
          p.Description as PermissionDescription,
          
          smp.Id as SubModulePermissionId
          
        FROM Modules m
        LEFT JOIN SubModules sm ON m.Id = sm.ModuleId
        LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId
        LEFT JOIN Permissions p ON smp.PermissionId = p.Id
        WHERE m.IsActive = 1
        ORDER BY m.DisplayOrder, sm.DisplayOrder, p.Name
      `;

      const result = await db.query(query);
      console.log(`📊 Controller query returned ${result.recordset.length} rows`);
      
      if (result.recordset.length > 0) {
        console.log('First few rows:');
        result.recordset.slice(0, 3).forEach((row, index) => {
          console.log(`  Row ${index + 1}: Module=${row.ModuleName}, SubModule=${row.SubModuleName}, Permission=${row.PermissionName}`);
        });
      }
    } catch (error) {
      console.log('❌ Error with controller query:', error.message);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await db.closePool();
    process.exit(0);
  }
}

debugPermissionTables();