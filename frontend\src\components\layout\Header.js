// frontend/src/components/layout/Header.js
import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Bell,
  Search,
  Menu,
  User,
  LogOut,
  Settings,
  ChevronDown,
  UserCircle,
  Key
} from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import ThemeToggle from './ThemeToggle';

const Header = ({ toggleSidebar }) => {
  const { user, logout } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const navigate = useNavigate();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <header
      className="h-16 px-4 flex items-center justify-between sticky top-0 z-30 theme-bg-sidebar theme-transition theme-shadow"
      style={{
        borderBottom: '1px solid var(--color-border)',
        backdropFilter: 'blur(10px)'
      }}
    >
      <div className="flex items-center h-full">
        <button
          onClick={toggleSidebar}
          className="lg:hidden p-2 rounded-md transition-colors theme-bg-secondary theme-text-primary theme-transition"
          aria-label="Toggle menu"
        >
          <Menu className="w-5 h-5" />
        </button>

        <div className="ml-4 lg:ml-0 h-full flex items-center">
          <h2 className="text-lg font-medium theme-text-primary theme-transition hidden md:block">
            
          </h2>
        </div>
      </div>

      <div className="flex-1 max-w-xl mx-4 hidden md:block">
        <div className="relative">
          <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 theme-text-muted theme-transition" />
          <input
            type="text"
            placeholder="Search..."
            className="w-full pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all theme-bg-input theme-text-primary theme-transition"
            style={{
              border: '1px solid var(--color-border)',
              boxShadow: '0 2px 5px rgba(0,0,0,0.05)'
            }}
          />
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <ThemeToggle />

        <button
          className="p-2 rounded-md relative transition-colors theme-bg-secondary theme-text-primary theme-transition hover:bg-blue-50"
          aria-label="Notifications"
        >
          <Bell className="w-5 h-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full animate-pulse" />
        </button>

        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setDropdownOpen(!dropdownOpen)}
            className="flex items-center gap-2 p-1 rounded-md transition-colors theme-bg-secondary theme-text-primary theme-transition hover:bg-blue-50"
          >
            <div className="h-9 w-9 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-medium shadow-sm">
              <User className="w-4 h-4" />
            </div>
            <div className="hidden md:block text-left">
              <p className="text-sm font-medium theme-text-primary theme-transition">
                {user?.firstName} {user?.lastName}
              </p>
              <p className="text-xs theme-text-muted theme-transition">{user?.role}</p>
            </div>
            <ChevronDown className={`w-4 h-4 theme-text-muted theme-transition ${dropdownOpen ? 'transform rotate-180' : ''}`} />
          </button>

          {dropdownOpen && (
            <div
              className="absolute right-0 mt-2 w-56 rounded-md shadow-lg py-1 z-50 theme-bg-card theme-transition theme-shadow animate-fadeIn"
              style={{
                border: '1px solid var(--color-border)',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}
            >
              <div
                className="px-4 py-2 theme-transition"
                style={{ borderBottom: '1px solid var(--color-border)' }}
              >
                <p className="text-sm font-medium theme-text-primary theme-transition">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs theme-text-muted theme-transition">{user?.email}</p>
              </div>

              <Link
                to="/profile"
                className="flex items-center gap-3 px-4 py-2.5 text-sm w-full text-left transition-colors theme-text-primary theme-transition hover:bg-blue-50"
                onClick={() => setDropdownOpen(false)}
              >
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                  <UserCircle className="w-4 h-4" />
                </div>
                <span>My Profile</span>
              </Link>

              <Link
                to="/change-password"
                className="flex items-center gap-3 px-4 py-2.5 text-sm w-full text-left transition-colors theme-text-primary theme-transition hover:bg-blue-50"
                onClick={() => setDropdownOpen(false)}
              >
                <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                  <Key className="w-4 h-4" />
                </div>
                <span>Change Password</span>
              </Link>

              <Link
                to="/settings"
                className="flex items-center gap-3 px-4 py-2.5 text-sm w-full text-left transition-colors theme-text-primary theme-transition hover:bg-blue-50"
                onClick={() => setDropdownOpen(false)}
              >
                <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                  <Settings className="w-4 h-4" />
                </div>
                <span>Settings</span>
              </Link>

              <div className="mt-1 pt-1" style={{ borderTop: '1px solid var(--color-border)' }}>
                <button
                  onClick={handleLogout}
                  className="flex items-center gap-3 px-4 py-2.5 text-sm w-full text-left transition-colors"
                  style={{ color: 'var(--color-error)' }}
                  onMouseOver={(e) => e.currentTarget.style.backgroundColor = 'var(--color-error-light)'}
                  onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                >
                  <div className="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center text-red-600">
                    <LogOut className="w-4 h-4" />
                  </div>
                  <span>Logout</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
