require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

// Copy of the updated calculateDateRange function from the dashboard controller
function calculateDateRange(dateRange) {
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date
    startDate = new Date(dateRange + 'T00:00:00.000Z');
    endDate = new Date(dateRange + 'T23:59:59.999Z');
    
    console.log(`Using specific date: ${dateRange}`);
  } else {
    // It's a predefined range
    switch(dateRange) {
      case 'today':
        // For today, use current date
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        // For yesterday, use current date - 1 day
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setDate(endDate.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        // For week, use the last 7 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 6);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        // For month, use the last 30 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 29);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'year':
        // For year, use the last 365 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 364);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        // Default to today
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
    }
  }
  
  console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
  return { startDate, endDate };
}

async function testDashboardDynamic() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // First, let's check what dates have data in the database
    console.log('Checking available dates in the database...');
    const checkDatesQuery = `
      SELECT 
        CONVERT(DATE, ExitDateTime) as ExitDate,
        COUNT(*) as RecordCount
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime IS NOT NULL
      GROUP BY CONVERT(DATE, ExitDateTime)
      ORDER BY ExitDate DESC
    `;
    
    const datesResult = await sql.query(checkDatesQuery);
    
    console.log('\nAvailable dates in the database:');
    const availableDates = [];
    datesResult.recordset.slice(0, 10).forEach(date => {
      console.log(`- ${date.ExitDate.toISOString().split('T')[0]}: ${date.RecordCount} records`);
      availableDates.push(date.ExitDate.toISOString().split('T')[0]);
    });
    
    // Test with specific dates that have data
    console.log('\nTesting with specific dates that have data:');
    for (const date of availableDates.slice(0, 3)) {
      const { startDate, endDate } = calculateDateRange(date);
      
      console.log(`\n=== Testing dashboard with specific date: ${date} ===`);
      
      const request = new sql.Request();
      request.input('startDate', sql.DateTime, startDate);
      request.input('endDate', sql.DateTime, endDate);
      
      const summaryResult = await request.query(`
        SELECT 
          ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
          COUNT(*) as TransactionCount
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @startDate AND @endDate
      `);
      
      console.log('Dashboard Summary:');
      console.log('- Total Revenue:', summaryResult.recordset[0].TotalRevenue);
      console.log('- Transaction Count:', summaryResult.recordset[0].TransactionCount);
    }

    // Test with predefined date ranges
    console.log('\nTesting with predefined date ranges:');
    const dateRanges = ['today', 'yesterday', 'week', 'month'];
    
    for (const range of dateRanges) {
      const { startDate, endDate } = calculateDateRange(range);
      
      console.log(`\n=== Testing dashboard with date range: ${range} ===`);
      
      const request = new sql.Request();
      request.input('startDate', sql.DateTime, startDate);
      request.input('endDate', sql.DateTime, endDate);
      
      const summaryResult = await request.query(`
        SELECT 
          ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
          COUNT(*) as TransactionCount
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @startDate AND @endDate
      `);
      
      console.log('Dashboard Summary:');
      console.log('- Total Revenue:', summaryResult.recordset[0].TotalRevenue);
      console.log('- Transaction Count:', summaryResult.recordset[0].TransactionCount);
    }

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

testDashboardDynamic();