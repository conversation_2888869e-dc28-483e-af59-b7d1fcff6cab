-- Migration script to transfer permissions and modules data from PWVMS to ParkwizOps
-- Created: 2024-07-01

-- Step 1: Check and update Modules
-- First, let's check what modules are missing in ParkwizOps
PRINT 'Checking and updating Modules...';

-- Add missing modules from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[Modules] (
    [Name], 
    [Description], 
    [Icon], 
    [DisplayOrder], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    p.[Name], 
    'Migrated from PWVMS', -- Default description
    p.[Icon], 
    ROW_NUMBER() OVER (ORDER BY p.[Id]), -- Generate display order
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[Modules] p
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[Modules] o 
    WHERE o.[Name] = p.[Name]
);

-- Step 2: Check and update SubModules
PRINT 'Checking and updating SubModules...';

-- Add missing submodules from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[SubModules] (
    [ModuleId], 
    [Name], 
    [Icon], 
    [Path], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    om.[Id], -- Map to the corresponding module ID in ParkwizOps
    p.[Name], 
    p.[Icon], 
    p.[Path], 
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[SubModules] p
JOIN [pwvms].[dbo].[Modules] pm ON p.[ModuleId] = pm.[Id]
JOIN [ParkwizOps].[dbo].[Modules] om ON om.[Name] = pm.[Name]
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[SubModules] o 
    WHERE o.[Name] = p.[Name] AND o.[Path] = p.[Path]
);

-- Step 3: Check and update Permissions
PRINT 'Checking and updating Permissions...';

-- Add missing permissions from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[Permissions] (
    [Name], 
    [Description], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    p.[Name], 
    p.[Description], 
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[Permissions] p
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[Permissions] o 
    WHERE o.[Name] = p.[Name]
);

-- Step 4: Check and update SubModulePermissions
PRINT 'Checking and updating SubModulePermissions...';

-- Add missing submodule permissions from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[SubModulePermissions] (
    [SubModuleId], 
    [PermissionId], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    osm.[Id], -- Map to the corresponding submodule ID in ParkwizOps
    op.[Id], -- Map to the corresponding permission ID in ParkwizOps
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[SubModulePermissions] p
JOIN [pwvms].[dbo].[SubModules] psm ON p.[SubModuleId] = psm.[Id]
JOIN [pwvms].[dbo].[Permissions] pp ON p.[PermissionId] = pp.[Id]
JOIN [ParkwizOps].[dbo].[SubModules] osm ON osm.[Name] = psm.[Name]
JOIN [ParkwizOps].[dbo].[Permissions] op ON op.[Name] = pp.[Name]
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[SubModulePermissions] o 
    WHERE o.[SubModuleId] = osm.[Id] AND o.[PermissionId] = op.[Id]
);

-- Step 5: Check and update Roles
PRINT 'Checking and updating Roles...';

-- Add missing roles from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[Roles] (
    [Name], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    p.[Name], 
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[Roles] p
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[Roles] o 
    WHERE o.[Name] = p.[Name]
);

-- Step 6: Check and update RolePermissions
PRINT 'Checking and updating RolePermissions...';

-- Add missing role permissions from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[RolePermissions] (
    [RoleId], 
    [SubModulePermissionId], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    or.[Id], -- Map to the corresponding role ID in ParkwizOps
    osmp.[Id], -- Map to the corresponding submodule permission ID in ParkwizOps
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[RolePermissions] p
JOIN [pwvms].[dbo].[Roles] pr ON p.[RoleId] = pr.[Id]
JOIN [pwvms].[dbo].[SubModulePermissions] psmp ON p.[SubModulePermissionId] = psmp.[Id]
JOIN [pwvms].[dbo].[SubModules] psm ON psmp.[SubModuleId] = psm.[Id]
JOIN [pwvms].[dbo].[Permissions] pp ON psmp.[PermissionId] = pp.[Id]
JOIN [ParkwizOps].[dbo].[Roles] or ON or.[Name] = pr.[Name]
JOIN [ParkwizOps].[dbo].[SubModules] osm ON osm.[Name] = psm.[Name]
JOIN [ParkwizOps].[dbo].[Permissions] op ON op.[Name] = pp.[Name]
JOIN [ParkwizOps].[dbo].[SubModulePermissions] osmp ON osmp.[SubModuleId] = osm.[Id] AND osmp.[PermissionId] = op.[Id]
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[RolePermissions] o 
    WHERE o.[RoleId] = or.[Id] AND o.[SubModulePermissionId] = osmp.[Id]
);

-- Step 7: Check and update RoleModules (if needed)
PRINT 'Checking and updating RoleModules...';

-- Add missing role modules from PWVMS to ParkwizOps
INSERT INTO [ParkwizOps].[dbo].[RoleModules] (
    [SubModuleId], 
    [RoleId], 
    [IsActive], 
    [CreatedBy], 
    [CreatedOn]
)
SELECT 
    osm.[Id], -- Map to the corresponding submodule ID in ParkwizOps
    or.[Id], -- Map to the corresponding role ID in ParkwizOps
    p.[IsActive], 
    p.[CreatedBy], 
    GETDATE()
FROM [pwvms].[dbo].[RoleModules] p
JOIN [pwvms].[dbo].[SubModules] psm ON p.[SubModuleId] = psm.[Id]
JOIN [pwvms].[dbo].[Roles] pr ON p.[RoleId] = pr.[Id]
JOIN [ParkwizOps].[dbo].[SubModules] osm ON osm.[Name] = psm.[Name]
JOIN [ParkwizOps].[dbo].[Roles] or ON or.[Name] = pr.[Name]
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[RoleModules] o 
    WHERE o.[SubModuleId] = osm.[Id] AND o.[RoleId] = or.[Id]
);

-- Step 8: Add missing Modules from the error log
-- Based on the error you provided, it seems you're missing the "Transactions" and "Monitoring" modules
-- Let's check if they exist in ParkwizOps and add them if not

IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[Modules] WHERE [Name] = 'Transactions')
BEGIN
    PRINT 'Adding Transactions module...';
    INSERT INTO [ParkwizOps].[dbo].[Modules] (
        [Name], 
        [Description], 
        [Icon], 
        [DisplayOrder], 
        [IsActive], 
        [CreatedBy], 
        [CreatedOn]
    )
    VALUES (
        'Transactions',
        'Manage transaction data',
        'receipt',
        8,
        1,
        1,
        GETDATE()
    );
END

IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[Modules] WHERE [Name] = 'Monitoring')
BEGIN
    PRINT 'Adding Monitoring module...';
    INSERT INTO [ParkwizOps].[dbo].[Modules] (
        [Name], 
        [Description], 
        [Icon], 
        [DisplayOrder], 
        [IsActive], 
        [CreatedBy], 
        [CreatedOn]
    )
    VALUES (
        'Monitoring',
        'System monitoring and alerts',
        'monitoring',
        9,
        1,
        1,
        GETDATE()
    );
END

-- Step 9: Add missing SubModules for Transactions and Monitoring
-- Let's add the SubModules for these modules if they don't exist

-- For Transactions module
DECLARE @TransactionsModuleId INT;
SELECT @TransactionsModuleId = [Id] FROM [ParkwizOps].[dbo].[Modules] WHERE [Name] = 'Transactions';

IF @TransactionsModuleId IS NOT NULL
BEGIN
    -- Add Payment Transactions submodule
    IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[SubModules] WHERE [Name] = 'Payment Transactions' AND [ModuleId] = @TransactionsModuleId)
    BEGIN
        PRINT 'Adding Payment Transactions submodule...';
        INSERT INTO [ParkwizOps].[dbo].[SubModules] (
            [ModuleId], 
            [Name], 
            [Icon], 
            [Path], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @TransactionsModuleId,
            'Payment Transactions',
            'credit-card',
            '/payment-transactions',
            1,
            1,
            GETDATE()
        );
    END

    -- Add Vehicle Entries submodule
    IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[SubModules] WHERE [Name] = 'Vehicle Entries' AND [ModuleId] = @TransactionsModuleId)
    BEGIN
        PRINT 'Adding Vehicle Entries submodule...';
        INSERT INTO [ParkwizOps].[dbo].[SubModules] (
            [ModuleId], 
            [Name], 
            [Icon], 
            [Path], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @TransactionsModuleId,
            'Vehicle Entries',
            'car',
            '/vehicle-entries',
            1,
            1,
            GETDATE()
        );
    END

    -- Add Transaction History submodule
    IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[SubModules] WHERE [Name] = 'Transaction History' AND [ModuleId] = @TransactionsModuleId)
    BEGIN
        PRINT 'Adding Transaction History submodule...';
        INSERT INTO [ParkwizOps].[dbo].[SubModules] (
            [ModuleId], 
            [Name], 
            [Icon], 
            [Path], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @TransactionsModuleId,
            'Transaction History',
            'history',
            '/transaction-history',
            1,
            1,
            GETDATE()
        );
    END
END

-- For Monitoring module
DECLARE @MonitoringModuleId INT;
SELECT @MonitoringModuleId = [Id] FROM [ParkwizOps].[dbo].[Modules] WHERE [Name] = 'Monitoring';

IF @MonitoringModuleId IS NOT NULL
BEGIN
    -- Add Lane Status submodule
    IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[SubModules] WHERE [Name] = 'Lane Status' AND [ModuleId] = @MonitoringModuleId)
    BEGIN
        PRINT 'Adding Lane Status submodule...';
        INSERT INTO [ParkwizOps].[dbo].[SubModules] (
            [ModuleId], 
            [Name], 
            [Icon], 
            [Path], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @MonitoringModuleId,
            'Lane Status',
            'activity',
            '/lane-status',
            1,
            1,
            GETDATE()
        );
    END

    -- Add System Health submodule
    IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[SubModules] WHERE [Name] = 'System Health' AND [ModuleId] = @MonitoringModuleId)
    BEGIN
        PRINT 'Adding System Health submodule...';
        INSERT INTO [ParkwizOps].[dbo].[SubModules] (
            [ModuleId], 
            [Name], 
            [Icon], 
            [Path], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @MonitoringModuleId,
            'System Health',
            'heart-pulse',
            '/system-health',
            1,
            1,
            GETDATE()
        );
    END

    -- Add Alerts submodule
    IF NOT EXISTS (SELECT 1 FROM [ParkwizOps].[dbo].[SubModules] WHERE [Name] = 'Alerts' AND [ModuleId] = @MonitoringModuleId)
    BEGIN
        PRINT 'Adding Alerts submodule...';
        INSERT INTO [ParkwizOps].[dbo].[SubModules] (
            [ModuleId], 
            [Name], 
            [Icon], 
            [Path], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @MonitoringModuleId,
            'Alerts',
            'bell',
            '/alerts',
            1,
            1,
            GETDATE()
        );
    END
END

-- Step 10: Add permissions for the new submodules
PRINT 'Adding permissions for new submodules...';

-- Get all submodules that don't have permissions assigned
DECLARE @SubModulesWithoutPermissions TABLE (SubModuleId INT);

INSERT INTO @SubModulesWithoutPermissions
SELECT sm.[Id]
FROM [ParkwizOps].[dbo].[SubModules] sm
WHERE NOT EXISTS (
    SELECT 1 
    FROM [ParkwizOps].[dbo].[SubModulePermissions] smp 
    WHERE smp.[SubModuleId] = sm.[Id]
);

-- For each submodule without permissions, add standard permissions (View, Create, Edit, Delete)
DECLARE @SubModuleId INT;
DECLARE @ViewPermissionId INT;
DECLARE @CreatePermissionId INT;
DECLARE @EditPermissionId INT;
DECLARE @DeletePermissionId INT;

-- Get permission IDs
SELECT @ViewPermissionId = [Id] FROM [ParkwizOps].[dbo].[Permissions] WHERE [Name] = 'View';
SELECT @CreatePermissionId = [Id] FROM [ParkwizOps].[dbo].[Permissions] WHERE [Name] = 'Create';
SELECT @EditPermissionId = [Id] FROM [ParkwizOps].[dbo].[Permissions] WHERE [Name] = 'Edit';
SELECT @DeletePermissionId = [Id] FROM [ParkwizOps].[dbo].[Permissions] WHERE [Name] = 'Delete';

-- Add permissions for each submodule
DECLARE SubModuleCursor CURSOR FOR 
SELECT SubModuleId FROM @SubModulesWithoutPermissions;

OPEN SubModuleCursor;
FETCH NEXT FROM SubModuleCursor INTO @SubModuleId;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- Add View permission
    IF @ViewPermissionId IS NOT NULL
    BEGIN
        INSERT INTO [ParkwizOps].[dbo].[SubModulePermissions] (
            [SubModuleId], 
            [PermissionId], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @SubModuleId,
            @ViewPermissionId,
            1,
            1,
            GETDATE()
        );
    END

    -- Add Create permission
    IF @CreatePermissionId IS NOT NULL
    BEGIN
        INSERT INTO [ParkwizOps].[dbo].[SubModulePermissions] (
            [SubModuleId], 
            [PermissionId], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @SubModuleId,
            @CreatePermissionId,
            1,
            1,
            GETDATE()
        );
    END

    -- Add Edit permission
    IF @EditPermissionId IS NOT NULL
    BEGIN
        INSERT INTO [ParkwizOps].[dbo].[SubModulePermissions] (
            [SubModuleId], 
            [PermissionId], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @SubModuleId,
            @EditPermissionId,
            1,
            1,
            GETDATE()
        );
    END

    -- Add Delete permission
    IF @DeletePermissionId IS NOT NULL
    BEGIN
        INSERT INTO [ParkwizOps].[dbo].[SubModulePermissions] (
            [SubModuleId], 
            [PermissionId], 
            [IsActive], 
            [CreatedBy], 
            [CreatedOn]
        )
        VALUES (
            @SubModuleId,
            @DeletePermissionId,
            1,
            1,
            GETDATE()
        );
    END

    FETCH NEXT FROM SubModuleCursor INTO @SubModuleId;
END

CLOSE SubModuleCursor;
DEALLOCATE SubModuleCursor;

-- Step 11: Assign permissions to SuperAdmin role
PRINT 'Assigning permissions to SuperAdmin role...';

-- Get SuperAdmin role ID
DECLARE @SuperAdminRoleId INT;
SELECT @SuperAdminRoleId = [Id] FROM [ParkwizOps].[dbo].[Roles] WHERE [Name] = 'SuperAdmin';

-- If SuperAdmin role exists, assign all permissions to it
IF @SuperAdminRoleId IS NOT NULL
BEGIN
    -- Get all SubModulePermissions that aren't already assigned to SuperAdmin
    INSERT INTO [ParkwizOps].[dbo].[RolePermissions] (
        [RoleId], 
        [SubModulePermissionId], 
        [IsActive], 
        [CreatedBy], 
        [CreatedOn]
    )
    SELECT 
        @SuperAdminRoleId,
        smp.[Id],
        1,
        1,
        GETDATE()
    FROM [ParkwizOps].[dbo].[SubModulePermissions] smp
    WHERE NOT EXISTS (
        SELECT 1 
        FROM [ParkwizOps].[dbo].[RolePermissions] rp 
        WHERE rp.[RoleId] = @SuperAdminRoleId AND rp.[SubModulePermissionId] = smp.[Id]
    );
END

PRINT 'Migration completed successfully!';