-- Create Valet Management Module with SubModules and Permissions
-- This script creates a complete Valet Management module structure

USE ParkwizOps;
GO

PRINT 'Starting Valet Management Module Setup...';

-- Step 1: Create Valet Management Module
IF NOT EXISTS (SELECT 1 FROM Modules WHERE Name = 'Valet Management')
BEGIN
    PRINT 'Creating Valet Management module...';
    
    INSERT INTO Modules (Name, IsActive, CreatedBy, CreatedOn)
    VALUES ('Valet Management', 1, 1, GETDATE());
    
    PRINT 'Valet Management module created successfully.';
END
ELSE
BEGIN
    PRINT 'Valet Management module already exists.';
END

-- Get the Module ID
DECLARE @ModuleId INT;
SELECT @ModuleId = Id FROM Modules WHERE Name = 'Valet Management';
PRINT 'Valet Management Module ID: ' + CAST(@ModuleId AS VARCHAR(10));

-- Step 2: Create SubModules for Valet Management
PRINT 'Creating SubModules for Valet Management...';

-- Check what columns exist in SubModules table first
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SubModules' AND COLUMN_NAME = 'Description')
BEGIN
    -- Table has Description column
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Points' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Points', 1, 1, GETDATE());
        PRINT 'Valet Points submodule created.';
    END
    
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Services' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Services', 1, 1, GETDATE());
        PRINT 'Valet Services submodule created.';
    END
    
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Staff' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Staff', 1, 1, GETDATE());
        PRINT 'Valet Staff submodule created.';
    END
    
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Reports' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Reports', 1, 1, GETDATE());
        PRINT 'Valet Reports submodule created.';
    END
END
ELSE
BEGIN
    -- Table doesn't have Description column, use basic structure
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Points' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Points', 1, 1, GETDATE());
        PRINT 'Valet Points submodule created.';
    END
    
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Services' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Services', 1, 1, GETDATE());
        PRINT 'Valet Services submodule created.';
    END
    
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Staff' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Staff', 1, 1, GETDATE());
        PRINT 'Valet Staff submodule created.';
    END
    
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Valet Reports' AND ModuleId = @ModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Valet Reports', 1, 1, GETDATE());
        PRINT 'Valet Reports submodule created.';
    END
END

-- Step 3: Get SubModule IDs
DECLARE @ValetPointsSubModuleId INT, @ValetServicesSubModuleId INT, @ValetStaffSubModuleId INT, @ValetReportsSubModuleId INT;

SELECT @ValetPointsSubModuleId = Id FROM SubModules WHERE Name = 'Valet Points' AND ModuleId = @ModuleId;
SELECT @ValetServicesSubModuleId = Id FROM SubModules WHERE Name = 'Valet Services' AND ModuleId = @ModuleId;
SELECT @ValetStaffSubModuleId = Id FROM SubModules WHERE Name = 'Valet Staff' AND ModuleId = @ModuleId;
SELECT @ValetReportsSubModuleId = Id FROM SubModules WHERE Name = 'Valet Reports' AND ModuleId = @ModuleId;

PRINT 'SubModule IDs:';
PRINT 'Valet Points: ' + CAST(@ValetPointsSubModuleId AS VARCHAR(10));
PRINT 'Valet Services: ' + CAST(@ValetServicesSubModuleId AS VARCHAR(10));
PRINT 'Valet Staff: ' + CAST(@ValetStaffSubModuleId AS VARCHAR(10));
PRINT 'Valet Reports: ' + CAST(@ValetReportsSubModuleId AS VARCHAR(10));

-- Step 4: Create SubModule Permissions
PRINT 'Creating SubModule Permissions...';

-- Get Permission IDs
DECLARE @ViewPermissionId INT, @CreatePermissionId INT, @EditPermissionId INT, @DeletePermissionId INT;
SELECT @ViewPermissionId = Id FROM Permissions WHERE Name = 'View';
SELECT @CreatePermissionId = Id FROM Permissions WHERE Name = 'Create';
SELECT @EditPermissionId = Id FROM Permissions WHERE Name = 'Edit';
SELECT @DeletePermissionId = Id FROM Permissions WHERE Name = 'Delete';

-- Create permissions for each submodule
DECLARE @SubModuleIds TABLE (Id INT);
INSERT INTO @SubModuleIds VALUES (@ValetPointsSubModuleId), (@ValetServicesSubModuleId), (@ValetStaffSubModuleId), (@ValetReportsSubModuleId);

DECLARE @PermissionIds TABLE (Id INT);
INSERT INTO @PermissionIds VALUES (@ViewPermissionId), (@CreatePermissionId), (@EditPermissionId), (@DeletePermissionId);

-- Insert SubModule Permissions
INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
SELECT sm.Id, p.Id, 1, 1, GETDATE()
FROM @SubModuleIds sm
CROSS JOIN @PermissionIds p
WHERE NOT EXISTS (
    SELECT 1 FROM SubModulePermissions smp 
    WHERE smp.SubModuleId = sm.Id AND smp.PermissionId = p.Id
);

PRINT 'SubModule Permissions created.';

-- Step 5: Assign Permissions to Roles
PRINT 'Assigning permissions to roles...';

-- Get Role IDs
DECLARE @SuperAdminRoleId INT, @CompanyAdminRoleId INT, @PlazaManagerRoleId INT;
SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
SELECT @PlazaManagerRoleId = Id FROM Roles WHERE Name = 'PlazaManager';

-- SuperAdmin gets all permissions for all valet submodules
IF @SuperAdminRoleId IS NOT NULL
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @SuperAdminRoleId,
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE sm.ModuleId = @ModuleId
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp 
        WHERE rp.RoleId = @SuperAdminRoleId 
        AND rp.SubModulePermissionId = smp.Id
    );
    
    PRINT 'SuperAdmin permissions assigned for Valet Management.';
END

-- CompanyAdmin gets all permissions for all valet submodules
IF @CompanyAdminRoleId IS NOT NULL
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @CompanyAdminRoleId,
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE sm.ModuleId = @ModuleId
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp 
        WHERE rp.RoleId = @CompanyAdminRoleId 
        AND rp.SubModulePermissionId = smp.Id
    );
    
    PRINT 'CompanyAdmin permissions assigned for Valet Management.';
END

-- PlazaManager gets all permissions for all valet submodules
IF @PlazaManagerRoleId IS NOT NULL
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @PlazaManagerRoleId,
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE sm.ModuleId = @ModuleId
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp 
        WHERE rp.RoleId = @PlazaManagerRoleId 
        AND rp.SubModulePermissionId = smp.Id
    );
    
    PRINT 'PlazaManager permissions assigned for Valet Management.';
END

-- Step 6: Verification
PRINT '';
PRINT '=== VERIFICATION ===';
PRINT 'Valet Management Module Structure:';

SELECT 
    m.Name as ModuleName,
    sm.Name as SubModuleName,
    sm.Id as SubModuleId,
    sm.IsActive
FROM Modules m
JOIN SubModules sm ON m.Id = sm.ModuleId
WHERE m.Name = 'Valet Management'
ORDER BY sm.Id;

PRINT '';
PRINT 'Role Permissions for Valet Management:';

SELECT 
    r.Name as RoleName,
    sm.Name as SubModuleName,
    p.Name as PermissionName,
    rp.IsActive
FROM RolePermissions rp
JOIN Roles r ON rp.RoleId = r.Id
JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
JOIN Modules m ON sm.ModuleId = m.Id
WHERE m.Name = 'Valet Management'
ORDER BY r.Name, sm.Name, p.Name;

PRINT '';
PRINT '✅ Valet Management Module setup completed successfully!';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Update your frontend navigation to include Valet Management';
PRINT '2. Use "Valet Points" as the requiredModule in PermissionGuard components';
PRINT '3. Restart your backend server to refresh permissions';
