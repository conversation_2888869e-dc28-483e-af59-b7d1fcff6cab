import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { countryApi } from '../api/countryApi';
import CountryList from '../components/Country/CountryList';
import CountryDialog from '../components/Country/CountryDialog';
// import CountryStats from '../components/Country/CountryStats';

export default function CountryManagement() {
  const queryClient = useQueryClient();
  const toast = useToast();
  const [selectedCountry, setSelectedCountry] = React.useState(null);
  const [dialogOpen, setDialogOpen] = React.useState(false);
  const [editingCountry, setEditingCountry] = React.useState(null);

  const { data: countries, isLoading: countriesLoading } = useQuery({
    queryKey: ['countries'],
    queryFn: countryApi.getCountries,
  });

  // const { data: countryStats } = useQuery({
  //   queryKey: ['countryStats', selectedCountry],
  //   queryFn: () => (selectedCountry ? countryApi.getCountryStats(selectedCountry) : null),
  //   enabled: !!selectedCountry,
  // });

  const createMutation = useMutation({
    mutationFn: countryApi.createCountry,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['countries'] });
      toast.showCrudSuccess('create', 'Country');
      setDialogOpen(false);
    },
    onError: (error) => {
      console.error("Create Error:", error);
      toast.showCrudError('create', 'Country', error.response?.data?.message);
    }
  });

  const updateMutation = useMutation({
    // mutationFn: ({ id, data }) => countryApi.updateCountry(id, data),
    // mutationFn: (newData) => {
    //   console.log("API Create Payload:", newData); 
    //   return countryApi.createCountry(newData);
    // },
 // Log the data being sent to the API function
 mutationFn: ({ id, data }) => {
  console.log("API Update ID:", id);
  console.log("API Update Payload:", data);
  return countryApi.updateCountry(id, data);
},

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['countries'] });
      toast.showCrudSuccess('update', 'Country');
      setDialogOpen(false);
      setEditingCountry(null);
    },
    onError: (error) => {
      console.error("Update Error:", error);
      toast.showCrudError('update', 'Country', error.response?.data?.message);
    }
    
  });

  const deleteMutation = useMutation({
    mutationFn: countryApi.deleteCountry,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['countries'] });
      toast.showCrudSuccess('delete', 'Country');
      if (selectedCountry) setSelectedCountry(null);
    },
    onError: (error) => {
      toast.showCrudError('delete', 'Country', error.response?.data?.message);
    },
  });

  const handleSubmit = (data) => {
    if (editingCountry && data.Id) {
      updateMutation.mutate({ id: data.Id, data });
    } else if (!editingCountry) {
      createMutation.mutate(data);
    }
    else {
      console.error("Attempting to update but ID is missing in submitted data", data);
      toast.showError("Failed to update country: Missing ID.");
    }
  };

  const handleEdit = (country) => {
    console.log("Editing country:", country);    
    setEditingCountry(country);
    setDialogOpen(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this country?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingCountry(null);
  };

  if (countriesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Country Management</h1>
          <button
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Country
          </button>
        </div>

        <div className="bg-white rounded-lg shadow">
          <CountryList
            countries={countries || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={setSelectedCountry}
          />
        </div>

        {/* {selectedCountry && countryStats && (
          <CountryStats stats={countryStats} />
        )} */}

        <CountryDialog
          isOpen={dialogOpen}
          onClose={handleCloseDialog}
          onSubmit={handleSubmit}
          country = {editingCountry}
          // initialData={
          //   editingCountry
          //     ? {
          //         name: editingCountry.Name,
          //         code: editingCountry.Id,
          //         status: editingCountry.status,
          //       }
          //     : undefined
          // }
          title={editingCountry ? 'Edit Country' : 'Add Country'}
        />
      </div>
    </div>
  );
}


