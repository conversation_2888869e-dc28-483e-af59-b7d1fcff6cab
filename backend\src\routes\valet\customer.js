const express = require('express');
const router = express.Router();
const customerController = require('../../controllers/valet/CustomerController');
const { auth, authorizeRoles } = require('../../middleware/auth');

/**
 * Valet Customer Routes
 * Handles customer registration, management, and vehicle operations
 */

// Public routes (for customer app/QR code scanning)
router.post('/register', customerController.registerCustomer);
router.get('/mobile/:mobileNumber', customerController.getCustomerByMobile);

// Customer vehicle management
router.get('/:customerId/vehicles', customerController.getCustomerVehicles);
router.post('/:customerId/vehicles', customerController.addCustomerVehicle);
router.put('/:customerId', customerController.updateCustomerDetails);

// Admin routes (require authentication)
router.get('/',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  customerController.getAllCustomers
);

module.exports = router;
