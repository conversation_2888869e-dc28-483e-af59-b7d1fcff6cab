-- Update sp_Valet_Transaction_Create stored procedure to add PaymentType parameter
USE ParkwizOps;
GO

-- Drop and recreate the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Transaction_Create]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Transaction_Create];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Transaction_Create]
    @PNRNumber UNIQUEIDENTIFIER,
    @ParkingPin DECIMAL(18,0),
    @CompanyId DECIMAL(18,0),
    @PlazaId DECIMAL(18,0),
    @CustomerVehicleNumber NVARCHAR(50),
    @CustomerMobileNumber NVARCHAR(15) = NULL,
    @CustomerName NVARCHAR(255) = NULL,
    @IsAnyValuableItem BIT = 0,
    @AnyValuableItem NVARCHAR(500) = NULL,
    @ValetFee DECIMAL(18,2) = 0,
    @ParkingFee DECIMAL(18,2) = 0,
    @TotalFee DECIMAL(18,2) = 0,
    @Source INT = 1, -- 1=Mobile App, 2=Web, 3=Controller
    @PayAt INT = 1, -- 1=Entry, 2=Exit
    @PaymentType INT = NULL, -- Added parameter
    @VehicleType INT = 1, -- 1=Car, 2=Bike, etc.
    @PlazaValetPointId DECIMAL(18,0) = NULL,
    @EntryBy DECIMAL(18,0),
    @NewId DECIMAL(18,0) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PNRNumber IS NULL
        BEGIN
            RAISERROR('PNR Number is required', 16, 1);
            RETURN -1;
        END
        
        IF @ParkingPin IS NULL OR @ParkingPin <= 0
        BEGIN
            RAISERROR('Parking Pin is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CompanyId IS NULL OR @CompanyId <= 0
        BEGIN
            RAISERROR('Company ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('Plaza ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CustomerVehicleNumber IS NULL OR LTRIM(RTRIM(@CustomerVehicleNumber)) = ''
        BEGIN
            RAISERROR('Customer vehicle number is required', 16, 1);
            RETURN -1;
        END
        
        IF @EntryBy IS NULL OR @EntryBy <= 0
        BEGIN
            RAISERROR('EntryBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate PNR
        IF EXISTS(SELECT 1 FROM [dbo].[ParkingTransactions] WHERE [PNRNumber] = @PNRNumber)
        BEGIN
            RAISERROR('Transaction with this PNR already exists', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate Parking Pin
        IF EXISTS(SELECT 1 FROM [dbo].[ParkingTransactions] WHERE [ParkingPin] = @ParkingPin)
        BEGIN
            RAISERROR('Transaction with this Parking Pin already exists', 16, 1);
            RETURN -1;
        END
        
        -- Insert new transaction
        INSERT INTO [dbo].[ParkingTransactions]
        (
            [PNRNumber],
            [ParkingPin],
            [CompanyId],
            [PlazaId],
            [CustomerVehicleNumber],
            [CustomerMobileNumber],
            [CustomerName],
            [IsAnyValuableItem],
            [AnyValuableItem],
            [ValetFee],
            [ParkingFee],
            [TotalFee],
            [Source],
            [PayAt],
            [PaymentType],
            [IsPaymentCompleted],
            [IsPromoCodeTransaction],
            [VehicleType],
            [EntryBy],
            [EntryDateTime],
            [PlazaValetPointId],
            [IsRequestMyVehicleLater],
            [RequestMyVehicleLaterTimeInterval],
            [IsVoid],
            [IsTransactionCanceled],
            [TransactionStatus],
            [IsKeyHandover]
        )
        VALUES
        (
            @PNRNumber,
            @ParkingPin,
            @CompanyId,
            @PlazaId,
            UPPER(LTRIM(RTRIM(@CustomerVehicleNumber))),
            @CustomerMobileNumber,
            @CustomerName,
            @IsAnyValuableItem,
            @AnyValuableItem,
            @ValetFee,
            @ParkingFee,
            @TotalFee,
            @Source,
            @PayAt,
            @PaymentType, -- Now using the parameter
            0, -- IsPaymentCompleted
            0, -- IsPromoCodeTransaction
            @VehicleType,
            @EntryBy,
            GETDATE(),
            @PlazaValetPointId,
            0, -- IsRequestMyVehicleLater
            0, -- RequestMyVehicleLaterTimeInterval
            0, -- IsVoid
            0, -- IsTransactionCanceled
            1, -- TransactionStatus: 1=Active, 2=Completed, 3=Cancelled
            0  -- IsKeyHandover
        );
        
        SET @NewId = SCOPE_IDENTITY();
        
        -- Return success with transaction details
        SELECT 
            @NewId AS Id,
            @PNRNumber AS PNRNumber,
            @ParkingPin AS ParkingPin,
            @CustomerVehicleNumber AS VehicleNumber,
            @TotalFee AS TotalFee,
            'Valet transaction created successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @NewId = -1;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_Transaction_Create';