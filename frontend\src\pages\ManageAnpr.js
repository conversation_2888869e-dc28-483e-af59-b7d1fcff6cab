import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { anprApi } from '../api/anprApi'; // Use the new ANPR API
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import { laneApi } from '../api/laneApi';
import AnprList from '../components/AnprConfig/AnprList'; // Use the new ANPR List component
import AnprDialog from '../components/AnprConfig/AnprDialog'; // Use the new ANPR Dialog component
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';
import ModuleFilters from '../components/filters/ModuleFilters';

export default function ManageAnpr() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();

  // Fetch ANPR configurations data
  const { data: configurations, isLoading: configurationsLoading, refetch: refetchConfigurations } = useQuery({
    queryKey: ['anprConfigs'], // Updated query key
    queryFn: async () => {
      // Add a timestamp to force a fresh request
      const timestamp = new Date().getTime();
      const data = await anprApi.getAllConfigurations(timestamp);
      console.log('Fetched ANPR configurations:', data);
      // Assuming the API returns an array directly or within a property
      return Array.isArray(data) ? data : (data?.configurations || []);
    },
    // Disable caching to ensure fresh data
    staleTime: 0,
    cacheTime: 0,
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });

  // Fetch plazas for dropdown
  const { data: plazas, isLoading: plazasLoading } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas,
    select: (data) => {
      if (data && data.success === true && Array.isArray(data.data)) return data.data;
      if (data && Array.isArray(data)) return data;
      if (data && data.plazas && Array.isArray(data.plazas)) return data.plazas;
      return [];
    }
  });

  // Fetch companies for dropdown
  const { data: companies, isLoading: companiesLoading } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => {
      // Handle potential object structure from companyApi
      if (data && data.success === true && Array.isArray(data.companies)) return data.companies;
      return Array.isArray(data) ? data : [];
    }
  });

  // Fetch lanes for dropdown
  const { data: lanes, isLoading: lanesLoading } = useQuery({
    queryKey: ['lanes'],
    queryFn: laneApi.getAllLanes,
    select: (data) => {
       // Assuming laneApi.getAllLanes returns { success: true, lanes: [...] }
       if (data && data.success === true && Array.isArray(data.lanes)) return data.lanes;
       return Array.isArray(data) ? data : []; // Fallback if structure is different
    }
  });

  // Process raw data - ensure we have arrays
  const rawConfigurations = Array.isArray(configurations) ? configurations : [];
  const processedPlazas = Array.isArray(plazas) ? plazas : [];
  const processedCompanies = Array.isArray(companies) ? companies : [];
  const processedLanes = Array.isArray(lanes) ? lanes : [];

  // Apply filtering to ANPR configurations data using the new module filter hook
  const {
    filteredData: filteredConfigurations,
    filters,
    setFilters,
    canCreate,
    canEdit,
    canDelete
  } = useModuleFilter({
    data: rawConfigurations,
    companies: processedCompanies,
    plazas: processedPlazas,
    lanes: processedLanes,
    companyIdField: 'CompanyID',
    plazaIdField: 'PlazaID',
    laneIdField: 'LaneID',
    module: 'ANPR'
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: anprApi.createConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['anprConfigs'] });
      toast.showCrudSuccess('create', 'ANPR configuration');
      setDialogOpen(false);
    },
    onError: (error) => toast.showError(`Failed to create ANPR configuration: ${error.response?.data?.message || error.message}`),
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => anprApi.updateConfiguration(id, data),
    onSuccess: () => {
      // Force a refetch to ensure we get the latest data
      queryClient.invalidateQueries({ queryKey: ['anprConfigs'] });
      queryClient.refetchQueries({ queryKey: ['anprConfigs'] });

      // Also use the direct refetch function for immediate refresh
      setTimeout(() => {
        refetchConfigurations();
        console.log('Manually triggered refetch after update');
      }, 500); // Small delay to ensure the backend has processed the update

      toast.showCrudSuccess('update', 'ANPR configuration');
      setDialogOpen(false);
      setEditingConfig(null);
    },
    onError: (error) => toast.showError(`Failed to update ANPR configuration: ${error.response?.data?.message || error.message}`),
  });

  // Delete mutation (Soft Delete)
  const deleteMutation = useMutation({
    // ANPR delete requires UpdatedBy for soft delete
    mutationFn: ({ id, updatedBy }) => anprApi.deleteConfiguration(id, updatedBy),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['anprConfigs'] });
      toast.success('ANPR configuration marked as inactive');
    },
    onError: (error) => toast.showError(`Failed to delete ANPR configuration: ${error.response?.data?.message || error.message}`),
  });

  const handleSubmit = (data) => {
    console.log('Handle ANPR submit with data:', data);
    // Make sure boolean values are explicitly set to '1' or '0' strings
    const processedData = {
      ...data,
      // Ensure boolean-like fields are '1' or '0'
      flgEnableANPR: data.flgEnableANPR === '1' ? '1' : '0',
      ActiveStatus: data.ActiveStatus === '1' ? '1' : '0',
      AllowBlacklistedVehicle: data.AllowBlacklistedVehicle === '1' ? '1' : '0',
      // Add user tracking
      UpdatedBy: user?.id || data.UpdatedBy || 'admin'
    };

    console.log('Processed ANPR data for submission:', processedData);

    if (editingConfig) {
      // Check if user has permission to edit this configuration
      if (canEdit(editingConfig, 'ANPR')) {
        console.log('Updating ANPR config with ID:', editingConfig.ANPRID);
        updateMutation.mutate({
          id: editingConfig.ANPRID,
          data: processedData
        });
      } else {
        toast.showError('You do not have permission to edit this ANPR configuration');
      }
    } else {
      // Check if user has permission to create configurations
      if (canCreate('ANPR')) {
        console.log('Creating new ANPR config');
        createMutation.mutate(processedData);
      } else {
        toast.showError('You do not have permission to create ANPR configurations');
      }
    }
  };

  const handleEdit = (config) => {
    // Check if user has permission to edit this configuration
    if (!canEdit(config, 'ANPR')) {
      toast.showError('You do not have permission to edit this ANPR configuration');
      return;
    }

    console.log('Editing ANPR config:', config);
    console.log('Original boolean values:', {
      flgEnableANPR: config.flgEnableANPR,
      ActiveStatus: config.ActiveStatus,
      AllowBlacklistedVehicle: config.AllowBlacklistedVehicle,
      types: {
        flgEnableANPR: typeof config.flgEnableANPR,
        ActiveStatus: typeof config.ActiveStatus,
        AllowBlacklistedVehicle: typeof config.AllowBlacklistedVehicle
      }
    });

    // Normalize boolean fields from '1'/'0' or Y/N to '1'/'0' for the dialog state
    const normalizeBool = (value) => {
      console.log(`Normalizing value in handleEdit: ${value}, type: ${typeof value}, stringified: ${JSON.stringify(value)}`);

      // Handle various true values
      if (value === 'Y' || value === 1 || value === true || value === '1' || value === 'true' || value === 'yes') {
        console.log(`Value ${value} normalized to '1'`);
        return '1';
      }

      // Handle various false values
      if (value === 'N' || value === 0 || value === false || value === '0' || value === 'false' || value === 'no' || value === null || value === undefined) {
        console.log(`Value ${value} normalized to '0'`);
        return '0';
      }

      // For any other case, try to parse the value
      try {
        // If it's a string that can be parsed as a number
        const numValue = Number(value);
        if (!isNaN(numValue)) {
          const result = numValue > 0 ? '1' : '0';
          console.log(`Parsed numeric value ${value} to ${result}`);
          return result;
        }
      } catch (e) {
        console.error(`Error parsing value ${value}:`, e);
      }

      console.log(`Defaulting value ${value} to '0'`);
      return '0'; // Default to '0' for any other value
    };

    const formattedConfig = {
      ...config,
      flgEnableANPR: normalizeBool(config.flgEnableANPR),
      ActiveStatus: normalizeBool(config.ActiveStatus),
      AllowBlacklistedVehicle: normalizeBool(config.AllowBlacklistedVehicle),
    };

    console.log('Formatted ANPR config for editing:', formattedConfig);
    console.log('Normalized boolean values:', {
      flgEnableANPR: formattedConfig.flgEnableANPR,
      ActiveStatus: formattedConfig.ActiveStatus,
      AllowBlacklistedVehicle: formattedConfig.AllowBlacklistedVehicle,
      types: {
        flgEnableANPR: typeof formattedConfig.flgEnableANPR,
        ActiveStatus: typeof formattedConfig.ActiveStatus,
        AllowBlacklistedVehicle: typeof formattedConfig.AllowBlacklistedVehicle
      }
    });

    setEditingConfig(formattedConfig);
    setDialogOpen(true);
  };

  const handleDelete = (id) => {
    // Find the configuration by ID
    const config = rawConfigurations.find(c => c.ANPRID === id);

    // Check if user has permission to delete this configuration
    if (config && canDelete(config, 'ANPR')) {
      if (window.confirm('Are you sure you want to mark this ANPR configuration as inactive? This is a soft delete.')) {
        // Pass UpdatedBy for soft delete, get from context
        const updatedBy = user?.id || 'admin_delete';
        deleteMutation.mutate({ id, updatedBy });
      }
    } else {
      toast.showError('You do not have permission to delete this ANPR configuration');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingConfig(null);
  };

  if (configurationsLoading || plazasLoading || companiesLoading || lanesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span>Loading ANPR Data...</span>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center mb-6 pb-4 border-b">
          <div className="flex items-center">
            <h1 className="text-3xl font-bold text-gray-800 mr-4">Manage ANPR Configuration</h1>
            <button
              onClick={() => {
                console.log('Manual refresh triggered');
                refetchConfigurations();
                toast.success('Refreshing ANPR data...');
              }}
              className="px-3 py-1 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition duration-150 ease-in-out"
            >
              Refresh Data
            </button>
          </div>
          <PermissionButton
            requiredModule="ANPR"
            requiredPermissions={["Create"]}
            onClick={() => {
              setEditingConfig(null); // Ensure we are adding new
              setDialogOpen(true);
            }}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow hover:bg-blue-700 transition duration-150 ease-in-out"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add ANPR Config
          </PermissionButton>
        </div>

        {/* Filter Section */}
        <div className="bg-white rounded-lg shadow p-4 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <ModuleFilters
              companies={processedCompanies || []}
              plazas={processedPlazas || []}
              lanes={processedLanes || []}
              filters={filters}
              onFilterChange={setFilters}
              showCompanyFilter={true}
              showPlazaFilter={true}
              showLaneFilter={true}
              loading={configurationsLoading || plazasLoading || companiesLoading || lanesLoading}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <AnprList
            configurations={filteredConfigurations || []}
            onEdit={handleEdit}
            onDelete={handleDelete} // Pass the handleDelete function
            // onSelect is not used in the list component provided, but kept for potential future use
            onSelect={(config) => console.log('Selected ANPR Config:', config)}
          />
        </div>

        {/* Ensure all required props are passed to the dialog */}
        {dialogOpen && plazas && companies && lanes && (
          <AnprDialog
            isOpen={dialogOpen}
            onClose={handleCloseDialog}
            onSubmit={handleSubmit}
            initialData={editingConfig}
            title={editingConfig ? 'Edit ANPR Configuration' : 'Add ANPR Configuration'}
            plazas={plazas} // Pass plazas data
            companies={companies} // Pass companies data
            lanes={lanes} // Pass lanes data
          />
        )}
      </div>
    </div>
  );
}