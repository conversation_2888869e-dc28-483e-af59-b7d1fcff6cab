const db = require('./src/config/database');

async function checkParkingTables() {
  try {
    console.log('=== CHECKING PARKING DATA TABLES ===');
    
    // Check if both tables exist
    const tableCheckQuery = `
      SELECT
        t.name AS TableName,
        p.rows AS [RowCount]
      FROM sys.tables t
      INNER JOIN sys.indexes i ON t.object_id = i.object_id
      INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
      WHERE i.index_id < 2
      AND (
        t.name = 'tblParkwiz_Parking_Data' OR
        t.name = 'tblParkwiz_Parking_Data_OLD' OR
        t.name = 'parkwizparkingdata'
      )
      ORDER BY t.name
    `;
    
    const tableResult = await db.query(tableCheckQuery);
    console.log('\n1. TABLE EXISTENCE AND ROW COUNTS:');
    tableResult.recordset.forEach(table => {
      console.log(`  ✓ ${table.TableName}: ${table.RowCount} rows`);
    });
    
    // Check column structure of parkwizparkingdata
    const columnCheckQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        CHARACTER_MAXIMUM_LENGTH
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'parkwizparkingdata'
      ORDER BY ORDINAL_POSITION
    `;
    
    const columnResult = await db.query(columnCheckQuery);
    console.log('\n2. PARKWIZPARKINGDATA TABLE STRUCTURE:');
    if (columnResult.recordset.length > 0) {
      columnResult.recordset.forEach(col => {
        console.log(`  ✓ ${col.COLUMN_NAME} (${col.DATA_TYPE}${col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : ''}) - ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
    } else {
      console.log('  ❌ Table parkwizparkingdata does not exist');
    }
    
    // Check column structure of old table for comparison
    const oldColumnCheckQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        CHARACTER_MAXIMUM_LENGTH
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'tblParkwiz_Parking_Data_OLD'
      ORDER BY ORDINAL_POSITION
    `;
    
    const oldColumnResult = await db.query(oldColumnCheckQuery);
    console.log('\n3. TBLPARKWIZ_PARKING_DATA_OLD TABLE STRUCTURE:');
    if (oldColumnResult.recordset.length > 0) {
      oldColumnResult.recordset.forEach(col => {
        console.log(`  ✓ ${col.COLUMN_NAME} (${col.DATA_TYPE}${col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : ''}) - ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
    } else {
      console.log('  ❌ Table tblParkwiz_Parking_Data_OLD does not exist');
    }
    
    // Sample data from parkwizparkingdata
    const sampleNewQuery = `
      SELECT TOP 5 
        *
      FROM parkwizparkingdata
      WHERE ExitDateTime IS NOT NULL
      ORDER BY ExitDateTime DESC
    `;
    
    try {
      const sampleNewResult = await db.query(sampleNewQuery);
      console.log('\n4. SAMPLE DATA FROM PARKWIZPARKINGDATA:');
      if (sampleNewResult.recordset.length > 0) {
        console.log('  ✓ Sample record:');
        const sample = sampleNewResult.recordset[0];
        Object.keys(sample).forEach(key => {
          console.log(`    ${key}: ${sample[key]}`);
        });
      } else {
        console.log('  ⚠️ No data found in parkwizparkingdata');
      }
    } catch (error) {
      console.log('  ❌ Error accessing parkwizparkingdata:', error.message);
    }
    
    // Sample data from old table
    const sampleOldQuery = `
      SELECT TOP 5 
        *
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime IS NOT NULL
      ORDER BY ExitDateTime DESC
    `;
    
    try {
      const sampleOldResult = await db.query(sampleOldQuery);
      console.log('\n5. SAMPLE DATA FROM TBLPARKWIZ_PARKING_DATA_OLD:');
      if (sampleOldResult.recordset.length > 0) {
        console.log('  ✓ Sample record:');
        const sample = sampleOldResult.recordset[0];
        Object.keys(sample).forEach(key => {
          console.log(`    ${key}: ${sample[key]}`);
        });
      } else {
        console.log('  ⚠️ No data found in tblParkwiz_Parking_Data_OLD');
      }
    } catch (error) {
      console.log('  ❌ Error accessing tblParkwiz_Parking_Data_OLD:', error.message);
    }
    
    console.log('\n=== ANALYSIS COMPLETE ===');
    console.log('Ready to update DashboardController.js to use parkwizparkingdata instead of tblParkwiz_Parking_Data_OLD');
    
    process.exit(0);
  } catch (error) {
    console.error('Analysis failed:', error);
    process.exit(1);
  }
}

checkParkingTables();
