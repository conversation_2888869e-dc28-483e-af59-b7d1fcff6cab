const sql = require("mssql");
const db = require("../config/database"); // adjust path if needed

const countryController = {
  // ✅ Create a new country
  createCountry: async (req, res) => {
    try {
      const { name, isActive = true } = req.body;

      if (!name) {
        return res.status(400).json({ error: "Country name is required." });
      }

      const query = `
        INSERT INTO Country (Name, IsActive, CreatedBy, CreatedOn)
        VALUES (@name, @isActive, @createdBy, @createdOn);
      `;

      const result = await db.query(query, {
        name,
        isActive,
        createdBy: null,
        createdOn: new Date(),
      });

      res.status(201).json({ success: true, message: "✅ Country created successfully", result });
    } catch (error) {
      console.error("Create country failed:", error.message);
      res.status(500).json({ error: "Failed to create country" });
    }
  },

  // ✅ Get all countries
  getCountries: async (req, res) => {
    try {
      const result = await db.query("SELECT * FROM Country");
      res.json({ countries: result.recordset });
    } catch (error) {
      console.error("Get countries failed:", error.message);
      res.status(500).json({ error: "Failed to fetch countries" });
    }
  },

  // ✅ Get a country by ID
  getCountryById: async (req, res) => {
    try {
      const { id } = req.params;
      const result = await db.query("SELECT * FROM Country WHERE Id = @id", { id });

      if (result.recordset.length === 0) {
        return res.status(404).json({ error: "Country not found" });
      }

      res.json({ country: result.recordset[0] });
    } catch (error) {
      console.error("Get country by ID failed:", error.message);
      res.status(500).json({ error: "Failed to fetch country" });
    }
  },

  // ✅ Update a country
  updateCountry: async (req, res) => {
    try {
      const { id } = req.params;
      const { name, isActive } = req.body;

      const result = await db.query(`
        UPDATE Country
        SET Name = @name, IsActive = @isActive,
            ModifiedBy = @modifiedBy, ModifiedOn = @modifiedOn
        WHERE Id = @id
      `, {
        id,
        name,
        isActive,
        modifiedBy: null,
        modifiedOn: new Date(),
      });

      if (result.rowsAffected[0] === 0) {
        return res.status(404).json({ error: "Country not found or nothing changed" });
      }

      res.json({ message: "✅ Country updated successfully" });
    } catch (error) {
      console.error("Update country failed:", error.message);
      res.status(500).json({ error: "Failed to update country" });
    }
  },

  // ✅ Delete a country
  deleteCountry: async (req, res) => {
    try {
      const { id } = req.params;
      await db.query("DELETE FROM Country WHERE Id = @id", { id });
      res.json({ message: "🗑️ Country deleted successfully" });
    } catch (error) {
      console.error("Delete country failed:", error.message);
      res.status(500).json({ error: "Failed to delete country" });
    }
  }
};
module.exports = countryController;
