const sql = require('mssql');

async function checkPermissionModule() {
  try {
    const config = {
      user: 'hparkwiz',
      password: 'Parkwiz@2020',
      server: 'parkwizvms.database.windows.net',
      database: 'ParkwizOps',
      port: 1433,
      options: {
        encrypt: true,
        trustServerCertificate: true
      },
      requestTimeout: 30000,
      connectionTimeout: 30000
    };

    console.log('🔍 Connecting to database...');
    const pool = await sql.connect(config);
    
    console.log('✅ Connected! Checking permission management module...\n');
    
    // Check if Permission Management module exists
    const permissionModuleQuery = `
      SELECT * FROM Modules 
      WHERE Name LIKE '%Permission%' OR Name LIKE '%Role%' OR Name LIKE '%Access%'
      ORDER BY Name
    `;
    
    const permissionModules = await pool.request().query(permissionModuleQuery);
    console.log('🔍 Permission-related modules:');
    if (permissionModules.recordset.length > 0) {
      permissionModules.recordset.forEach(module => {
        console.log(`  - ID: ${module.Id}, Name: ${module.Name}, Active: ${module.IsActive}, DisplayOrder: ${module.DisplayOrder}`);
      });
    } else {
      console.log('  ❌ No permission-related modules found');
    }
    
    // Check all modules to see what's available
    console.log('\n🔍 All modules in database:');
    const allModules = await pool.request().query('SELECT * FROM Modules WHERE IsActive = 1 ORDER BY DisplayOrder, Name');
    allModules.recordset.forEach(module => {
      console.log(`  - ID: ${module.Id}, Name: ${module.Name}, DisplayOrder: ${module.DisplayOrder}`);
    });
    
    // Check if there are submodules for permission management
    console.log('\n🔍 Checking submodules for permission management:');
    const subModulesQuery = `
      SELECT sm.*, m.Name as ModuleName 
      FROM SubModules sm
      INNER JOIN Modules m ON sm.ModuleId = m.Id
      WHERE sm.Name LIKE '%Permission%' OR sm.Name LIKE '%Role%' OR m.Name LIKE '%Permission%' OR m.Name LIKE '%Role%'
      ORDER BY m.Name, sm.Name
    `;
    
    const subModules = await pool.request().query(subModulesQuery);
    if (subModules.recordset.length > 0) {
      subModules.recordset.forEach(subModule => {
        console.log(`  - Module: ${subModule.ModuleName}, SubModule: ${subModule.Name}, Path: ${subModule.Path}, Active: ${subModule.IsActive}`);
      });
    } else {
      console.log('  ❌ No permission-related submodules found');
    }
    
    // Check if we need to create the Permission Management module
    const userManagementModule = await pool.request().query(`
      SELECT * FROM Modules WHERE Name = 'User Management'
    `);
    
    if (userManagementModule.recordset.length > 0) {
      console.log('\n🔍 User Management module found. Checking its submodules:');
      const userSubModules = await pool.request().query(`
        SELECT * FROM SubModules WHERE ModuleId = ${userManagementModule.recordset[0].Id} ORDER BY Name
      `);
      
      userSubModules.recordset.forEach(subModule => {
        console.log(`  - ${subModule.Name} (Path: ${subModule.Path}, Active: ${subModule.IsActive})`);
      });
      
      // Check if Permissions submodule exists
      const permissionsSubModule = userSubModules.recordset.find(sm => sm.Name === 'Permissions');
      if (permissionsSubModule) {
        console.log('\n🔍 Permissions submodule found! Checking its permissions:');
        const permissionsQuery = `
          SELECT smp.*, p.Name as PermissionName, p.Description as PermissionDescription
          FROM SubModulePermissions smp
          INNER JOIN Permissions p ON smp.PermissionId = p.Id
          WHERE smp.SubModuleId = ${permissionsSubModule.Id} AND smp.IsActive = 1
          ORDER BY p.Name
        `;
        
        const permissions = await pool.request().query(permissionsQuery);
        permissions.recordset.forEach(perm => {
          console.log(`    - ${perm.PermissionName}: ${perm.PermissionDescription}`);
        });
      } else {
        console.log('\n❌ Permissions submodule not found in User Management');
      }
    }
    
    // Check what roles exist and their permissions
    console.log('\n🔍 Checking existing roles:');
    const roles = await pool.request().query('SELECT * FROM Roles WHERE IsActive = 1 ORDER BY Name');
    roles.recordset.forEach(role => {
      console.log(`  - ID: ${role.Id}, Name: ${role.Name}`);
    });
    
    if (roles.recordset.length > 0) {
      console.log('\n🔍 Checking permissions for first role (SuperAdmin):');
      const superAdminRole = roles.recordset.find(r => r.Name === 'SuperAdmin') || roles.recordset[0];
      
      const rolePermissionsQuery = `
        SELECT 
          rp.Id as RolePermissionId,
          m.Name as ModuleName,
          sm.Name as SubModuleName,
          sm.Path as SubModulePath,
          p.Name as PermissionName
        FROM RolePermissions rp
        INNER JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
        INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
        INNER JOIN Modules m ON sm.ModuleId = m.Id
        INNER JOIN Permissions p ON smp.PermissionId = p.Id
        WHERE rp.RoleId = ${superAdminRole.Id} AND rp.IsActive = 1
        ORDER BY m.DisplayOrder, sm.Name, p.Name
      `;
      
      const rolePermissions = await pool.request().query(rolePermissionsQuery);
      console.log(`  Found ${rolePermissions.recordset.length} permissions for ${superAdminRole.Name}:`);
      
      const groupedPermissions = {};
      rolePermissions.recordset.forEach(perm => {
        const key = `${perm.ModuleName} > ${perm.SubModuleName}`;
        if (!groupedPermissions[key]) {
          groupedPermissions[key] = [];
        }
        groupedPermissions[key].push(perm.PermissionName);
      });
      
      Object.keys(groupedPermissions).forEach(key => {
        console.log(`    ${key}: ${groupedPermissions[key].join(', ')}`);
      });
    }
    
    await pool.close();
    console.log('\n✅ Permission module check completed');
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  }
}

checkPermissionModule().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('❌ Script error:', error.message);
  process.exit(1);
});