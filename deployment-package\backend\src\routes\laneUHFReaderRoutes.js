const express = require('express');
const router = express.Router();
const laneUHFReaderController = require('../controllers/LaneUHFReaderController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/uhf-readers
 * @desc    Get all UHF reader configurations
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), laneUHFReaderController.getAllUHFReaderConfigurations);

/**
 * @route   GET /api/uhf-readers/:id
 * @desc    Get UHF reader configuration by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), laneUHFReaderController.getUHFReaderConfigurationById);

/**
 * @route   GET /api/uhf-readers/lane/:laneId
 * @desc    Get UHF reader configurations by lane ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/lane/:laneId', auth(['View']), laneUHFReaderController.getUHFReaderConfigurationsByLane);

/**
 * @route   POST /api/uhf-readers
 * @desc    Create a new UHF reader configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), laneUHFReaderController.createUHFReaderConfiguration);

/**
 * @route   PUT /api/uhf-readers/:id
 * @desc    Update an existing UHF reader configuration
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.put('/:id', auth(['Edit']), laneUHFReaderController.updateUHFReaderConfiguration);

/**
 * @route   DELETE /api/uhf-readers/:id
 * @desc    Delete a UHF reader configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), laneUHFReaderController.deleteUHFReaderConfiguration);

/**
 * @route   PATCH /api/uhf-readers/:id/toggle
 * @desc    Toggle UHF reader enable/disable status
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.patch('/:id/toggle', auth(['Edit']), laneUHFReaderController.toggleUHFReaderStatus);

module.exports = router;
