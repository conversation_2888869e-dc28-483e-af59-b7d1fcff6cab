const sql = require('mssql');
const fs = require('fs');
require('dotenv').config({path: './backend/.env'});

async function executeSQLScript(scriptPath, scriptName) {
  try {
    console.log(`📄 Executing ${scriptName}...`);
    
    // Read the SQL script
    const script = fs.readFileSync(scriptPath, 'utf8');
    
    // Split by GO statements and execute each batch
    const batches = script.split(/^\s*GO\s*$/gim).filter(batch => batch.trim());
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i].trim();
      if (batch) {
        try {
          await sql.query(batch);
          console.log(`   ✅ Batch ${i + 1}/${batches.length} completed`);
        } catch (batchError) {
          console.error(`   ❌ Batch ${i + 1} failed:`, batchError.message);
          // Continue with next batch for non-critical errors
          if (batchError.message.includes('already exists')) {
            console.log(`   ⚠️ Skipping - object already exists`);
          } else {
            throw batchError;
          }
        }
      }
    }
    
    console.log(`✅ ${scriptName} completed successfully`);
    return true;
    
  } catch (error) {
    console.error(`❌ ${scriptName} failed:`, error.message);
    return false;
  }
}

async function executeValetMigration() {
  try {
    console.log('🚀 Executing Valet Database Migration Scripts...');
    console.log('================================================');
    
    // Connect to ParkwizOps database
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: 'ParkwizOps',
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    
    console.log('🔗 Connected to ParkwizOps database');
    console.log('');

    // Execute migration scripts in order
    const scripts = [
      { path: './valet_database_migration.sql', name: 'Core Tables Migration' },
      { path: './valet_database_migration_part2.sql', name: 'Additional Tables Migration' },
      { path: './valet_module_setup.sql', name: 'Module Setup' }
    ];

    let allSuccess = true;
    
    for (const script of scripts) {
      const success = await executeSQLScript(script.path, script.name);
      if (!success) {
        allSuccess = false;
      }
      console.log('');
    }

    if (allSuccess) {
      console.log('🎉 All migration scripts executed successfully!');
      console.log('');
      
      // Verify tables were created
      console.log('🔍 Verifying created tables...');
      const verifyQuery = `
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME IN (
          'Customer', 'CustomerVehicle', 'OTP', 'ValetDrivers', 
          'ValetControllers', 'VehicleStatusTracking', 'ValetQRCodes', 
          'SMSNotifications', 'PlazaRazorPayConfiguration', 'PlazaPhonePeConfiguration'
        )
        ORDER BY TABLE_NAME
      `;
      
      const result = await sql.query(verifyQuery);
      console.log('📋 Created tables:');
      result.recordset.forEach(row => {
        console.log(`   ✅ ${row.TABLE_NAME}`);
      });
      
    } else {
      console.log('⚠️ Some migration scripts had issues. Please check the logs above.');
    }

    await sql.close();
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Migration execution failed:', error.message);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  executeValetMigration();
}

module.exports = { executeValetMigration };
