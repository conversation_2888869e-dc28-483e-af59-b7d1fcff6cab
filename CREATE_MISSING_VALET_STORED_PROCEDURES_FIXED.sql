-- =============================================
-- CREATE MISSING VALET STORED PROCEDURES - FIXED VERSION
-- Fixed to match actual database table structures
-- Database: ParkwizOps
-- =============================================

USE ParkwizOps;
GO

PRINT 'Creating missing Valet stored procedures (FIXED VERSION)...';
PRINT '============================================================';

-- =============================================
-- 1. sp_Valet_Payment_GetOptions - FIXED
-- =============================================
PRINT 'Creating sp_Valet_Payment_GetOptions (FIXED)...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Payment_GetOptions]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Payment_GetOptions];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Payment_GetOptions]
    @PlazaId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        SELECT 
            p.Id as PlazaId,
            p.PlazaName,
            p.ValetFee,
            p.FixParkingFee as ParkingFee,
            (p.ValetFee + p.FixParkingFee) as TotalFee,
            CASE WHEN rp.Id IS NOT NULL THEN 1 ELSE 0 END as HasRazorPay,
            CASE WHEN pp.Id IS NOT NULL THEN 1 ELSE 0 END as HasPhonePe,
            p.IsCashActive as HasCash,
            0 as RazorPayTestMode, -- Default to production mode
            0 as PhonePeTestMode   -- Default to production mode
        FROM Plaza p
        LEFT JOIN PlazaRazorPayConfiguration rp ON p.Id = rp.PlazaId AND rp.IsActive = 1
        LEFT JOIN PlazaPhonePeConfiguration pp ON p.Id = pp.PlazaId AND pp.IsActive = 1
        WHERE p.Id = @PlazaId AND p.IsActive = 1;
        
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 2. sp_Valet_Payment_Initiate - UNCHANGED (Already correct)
-- =============================================
PRINT 'Creating sp_Valet_Payment_Initiate...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Payment_Initiate]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Payment_Initiate];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Payment_Initiate]
    @TransactionId INT,
    @GatewayType NVARCHAR(50),
    @GatewayOrderId NVARCHAR(100),
    @Amount DECIMAL(10,2),
    @PaymentMethod NVARCHAR(50),
    @CustomerName NVARCHAR(100) = NULL,
    @CustomerEmail NVARCHAR(100) = NULL,
    @CustomerMobile NVARCHAR(15) = NULL,
    @CreatedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO PaymentGatewayTransactions (
            TransactionId, GatewayType, GatewayOrderId, Amount, 
            PaymentMethod, CustomerName, CustomerEmail, CustomerMobile, CreatedBy
        )
        VALUES (
            @TransactionId, @GatewayType, @GatewayOrderId, @Amount,
            @PaymentMethod, @CustomerName, @CustomerEmail, @CustomerMobile, @CreatedBy
        );
        
        SELECT SCOPE_IDENTITY() as PaymentId;
        
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 3. sp_Valet_Payment_UpdateStatus - UNCHANGED (Already correct)
-- =============================================
PRINT 'Creating sp_Valet_Payment_UpdateStatus...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Payment_UpdateStatus]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Payment_UpdateStatus];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Payment_UpdateStatus]
    @PaymentId INT,
    @Status NVARCHAR(50),
    @GatewayTransactionId NVARCHAR(100) = NULL,
    @GatewayResponse NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        UPDATE PaymentGatewayTransactions 
        SET 
            Status = @Status,
            GatewayTransactionId = COALESCE(@GatewayTransactionId, GatewayTransactionId),
            GatewayResponse = COALESCE(@GatewayResponse, GatewayResponse),
            UpdatedOn = GETDATE()
        WHERE Id = @PaymentId;
        
        SELECT @@ROWCOUNT as RowsAffected;
        
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- 4. sp_Valet_SMS_Send - FIXED
-- =============================================
PRINT 'Creating sp_Valet_SMS_Send (FIXED)...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_SMS_Send]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_SMS_Send];
GO

CREATE PROCEDURE [dbo].[sp_Valet_SMS_Send]
    @MobileNumber NVARCHAR(15),
    @Message NVARCHAR(MAX),
    @SMSType NVARCHAR(50),
    @TransactionId DECIMAL = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO SMSNotifications (
            MobileNumber, Message, SMSType, TransactionId, Status, CreatedOn
        )
        VALUES (
            @MobileNumber, @Message, @SMSType, @TransactionId, 'PENDING', GETDATE()
        );
        
        SELECT SCOPE_IDENTITY() as SMSId;
        
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- VERIFICATION - Check stored procedures created
-- =============================================
PRINT '';
PRINT 'Verifying FIXED stored procedures created...';
PRINT '============================================';

SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME IN (
    'sp_Valet_Payment_GetOptions',
    'sp_Valet_Payment_Initiate', 
    'sp_Valet_Payment_UpdateStatus',
    'sp_Valet_SMS_Send'
)
ORDER BY ROUTINE_NAME;

PRINT '';
PRINT 'FIXED Valet stored procedures created successfully!';
PRINT 'Backend server should now work without column errors!';
PRINT '====================================================';
