const db = require('./backend/src/config/database');

async function checkTableStructure() {
  try {
    console.log('🔍 Checking table structures...\n');

    // Check Modules table structure
    console.log('📋 Modules table structure:');
    const modulesColumns = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Modules' 
      ORDER BY ORDINAL_POSITION
    `);
    modulesColumns.recordset.forEach(col => {
      console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE})`);
    });

    // Check SubModules table structure
    console.log('\n📋 SubModules table structure:');
    const subModulesColumns = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'SubModules' 
      ORDER BY ORDINAL_POSITION
    `);
    subModulesColumns.recordset.forEach(col => {
      console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE})`);
    });

    // Check Permissions table structure
    console.log('\n📋 Permissions table structure:');
    const permissionsColumns = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Permissions' 
      ORDER BY ORDINAL_POSITION
    `);
    permissionsColumns.recordset.forEach(col => {
      console.log(`  - ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE})`);
    });

    // Sample data from Modules
    console.log('\n🏗️ Sample Modules data:');
    const modules = await db.query('SELECT TOP 3 * FROM Modules');
    modules.recordset.forEach(m => {
      console.log(`  Module ${m.Id}: ${m.Name} - Active: ${m.IsActive}`);
      console.log(`    Description: ${m.Description || 'N/A'}`);
      console.log(`    Icon: ${m.Icon || 'N/A'}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await db.closePool();
    process.exit(0);
  }
}

checkTableStructure();