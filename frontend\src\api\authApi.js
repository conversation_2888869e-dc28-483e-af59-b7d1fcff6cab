// // frontend/src/api/authApi.js
// import api from '../services/api';

// // Register an owner
// export const registerOwner = async (ownerData) => {
//   try {
//     const response = await api.post('/auth/register/owner', ownerData);
//     return response.data; // This could include user data or a success message
//   } catch (error) {
//     console.error('Error registering owner:', error.response?.data || error.message);
//     throw error;
//   }
// };

// // Register a branch head
// export const registerBranchHead = async (branchHeadData) => {
//   try {
//     const response = await api.post('/auth/register/branchhead', branchHeadData);
//     return response.data; // This could include user data or a success message
//   } catch (error) {
//     console.error('Error registering branch head:', error.response?.data || error.message);
//     throw error;
//   }
// };

// // Login a user (owner or branch head)
// export const login = async (email, password) => {
//   try {
//     const response = await api.post('/auth/login', { email, password });
//     return response.data; // Contains token and user data
//   } catch (error) {
//     console.error('Login error:', error.response?.data || error.message);
//     throw error;
//   }
// };

// // Fetch the current user's data
// export const getCurrentUser = async () => {
//   try {
//     const response = await api.get('/auth/me');
//     return response.data; // Contains user information
//   } catch (error) {
//     console.error('Error fetching user data:', error.response?.data || error.message);
//     throw error;
//   }
// };

// // Logout a user
// export const logout = async () => {
//   try {
//     const response = await api.post('/auth/logout');
//     return response.data; // Contains success message or confirmation
//   } catch (error) {
//     console.error('Logout error:', error.response?.data || error.message);
//     throw error;
//   }
// };

