const express = require('express');
const router = express.Router();
const passRegistrationController = require('../controllers/PassRegistrationController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/pass-registrations
 * @desc    Get all pass registrations with pagination and filtering
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), passRegistrationController.getAllPassRegistrations);

/**
 * @route   GET /api/pass-registrations/:id
 * @desc    Get pass registration by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), passRegistrationController.getPassRegistrationById);

/**
 * @route   GET /api/pass-registrations/search/vehicle/:vehicleNo
 * @desc    Search pass registrations by vehicle number
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/search/vehicle/:vehicleNo', auth(['View']), passRegistrationController.searchByVehicleNumber);

/**
 * @route   POST /api/pass-registrations
 * @desc    Create a new pass registration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), passRegistrationController.createPassRegistration);

/**
 * @route   PUT /api/pass-registrations/:id
 * @desc    Update an existing pass registration
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.put('/:id', auth(['Edit']), passRegistrationController.updatePassRegistration);

/**
 * @route   DELETE /api/pass-registrations/:id
 * @desc    Delete a pass registration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), passRegistrationController.deletePassRegistration);

module.exports = router;
