const db = require('../config/database');
const sql = require('mssql');

exports.createAddress = async (req, res) => {
  const {
    CountryId,
    StateId,
    FirstName,
    LastName,
    Mobile,
    Email,
    Pincode,
    AddressLine1,
    AddressLine2,
    Landmark,
    City,
    Latitude,
    Longitude,
    IsLatLongRequired,
    CreatedBy
  } = req.body;

  try {
    // Validate required fields
    if (!AddressLine1 || !City || !Pincode) {
      return res.status(400).json({ error: 'AddressLine1, City, and Pincode are required' });
    }

    // Validate latitude/longitude if required
    if (IsLatLongRequired && (!Latitude || !Longitude)) {
      return res.status(400).json({ error: 'Latitude and Longitude are required when IsLatLongRequired is true' });
    }

    const result = await db.query(
      `INSERT INTO Address (
        CountryId, StateId, FirstName, LastName, Mobile, Email, 
        Pincode, AddressLine1, AddressLine2, Landmark, City, 
        Latitude, Longitude, IsLatLongRequired, IsActive, 
        CreatedBy, CreatedOn
      ) 
      OUTPUT INSERTED.*
      VALUES (
        @CountryId, @StateId, @FirstName, @LastName, @Mobile, @Email,
        @Pincode, @AddressLine1, @AddressLine2, @Landmark, @City,
        @Latitude, @Longitude, @IsLatLongRequired, 1,
        @CreatedBy, GETDATE()
      )`,
      {
        CountryId: CountryId || null,
        StateId: StateId || null,
        FirstName: FirstName || null,
        LastName: LastName || null,
        Mobile: Mobile || null,
        Email: Email || null,
        Pincode,
        AddressLine1,
        AddressLine2: AddressLine2 || null,
        Landmark: Landmark || null,
        City,
        Latitude: Latitude || null,
        Longitude: Longitude || null,
        IsLatLongRequired: IsLatLongRequired ? 1 : 0,
        CreatedBy
      }
    );

    res.status(201).json(result.recordset[0]);
  } catch (error) {
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

exports.getAllAddresses = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT 
        Id, CountryId, StateId, FirstName, LastName, Mobile, Email,
        Pincode, AddressLine1, AddressLine2, Landmark, City,
        Latitude, Longitude, IsLatLongRequired, IsActive,
        CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
      FROM Address
      WHERE IsActive = 1
    `);
    res.json(result.recordset);
  } catch (error) {
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

exports.getAddressById = async (req, res) => {
  const id = req.params.id;
  try {
    const result = await db.query(
      'SELECT * FROM Address WHERE Id = @Id AND IsActive = 1',
      { Id: id }
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

exports.updateAddress = async (req, res) => {
  const id = req.params.id;
  const {
    CountryId,
    StateId,
    FirstName,
    LastName,
    Mobile,
    Email,
    Pincode,
    AddressLine1,
    AddressLine2,
    Landmark,
    City,
    Latitude,
    Longitude,
    IsLatLongRequired,
    ModifiedBy
  } = req.body;

  try {
    // Get existing address
    const existingAddress = await db.query(
      'SELECT * FROM Address WHERE Id = @Id',
      { Id: id }
    );

    if (!existingAddress.recordset[0]) {
      return res.status(404).json({ error: 'Address not found' });
    }

    // Validate latitude/longitude if required
    if (IsLatLongRequired && (!Latitude || !Longitude)) {
      return res.status(400).json({ error: 'Latitude and Longitude are required when IsLatLongRequired is true' });
    }

    const result = await db.query(
      `UPDATE Address SET
        CountryId = @CountryId,
        StateId = @StateId,
        FirstName = @FirstName,
        LastName = @LastName,
        Mobile = @Mobile,
        Email = @Email,
        Pincode = @Pincode,
        AddressLine1 = @AddressLine1,
        AddressLine2 = @AddressLine2,
        Landmark = @Landmark,
        City = @City,
        Latitude = @Latitude,
        Longitude = @Longitude,
        IsLatLongRequired = @IsLatLongRequired,
        ModifiedBy = @ModifiedBy,
        ModifiedOn = GETDATE()
      WHERE Id = @Id`,
      {
        Id: id,
        CountryId: CountryId || null,
        StateId: StateId || null,
        FirstName: FirstName || null,
        LastName: LastName || null,
        Mobile: Mobile || null,
        Email: Email || null,
        Pincode: Pincode || existingAddress.recordset[0].Pincode,
        AddressLine1: AddressLine1 || existingAddress.recordset[0].AddressLine1,
        AddressLine2: AddressLine2 || null,
        Landmark: Landmark || null,
        City: City || existingAddress.recordset[0].City,
        Latitude: Latitude || null,
        Longitude: Longitude || null,
        IsLatLongRequired: IsLatLongRequired ? 1 : 0,
        ModifiedBy
      }
    );

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    res.json({ message: 'Address updated successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};

exports.deleteAddress = async (req, res) => {
  const id = req.params.id;
  try {
    const result = await db.query(
      `UPDATE Address SET
        IsActive = 0,
        ModifiedBy = @ModifiedBy,
        ModifiedOn = GETDATE()
      WHERE Id = @Id`,
      {
        Id: id,
        ModifiedBy: req.body.ModifiedBy
      }
    );

    if (result.rowsAffected[0] === 0) {
      return res.status(404).json({ error: 'Address not found' });
    }

    res.json({ message: 'Address soft-deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: 'Database error', details: error.message });
  }
};