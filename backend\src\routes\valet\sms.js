const express = require('express');
const router = express.Router();
const SMSController = require('../../controllers/valet/SMSController');
const { auth } = require('../../middleware/auth');

/**
 * Valet SMS Routes
 * Handles SMS notifications for OTP, VRN, pickup alerts, and other customer notifications
 */

// Send OTP SMS
router.post('/send-otp', auth(), SMSController.sendOTP);

// Send VRN notification SMS
router.post('/send-vrn-notification', auth(), SMSController.sendVRNNotification);

// Send pickup ready notification SMS
router.post('/send-pickup-ready', auth(), SMSController.sendPickupReadyNotification);

// Send payment success notification SMS
router.post('/send-payment-success', auth(), SMSController.sendPaymentSuccessNotification);

// Get SMS history for a customer or transaction
router.get('/history', auth(), SMSController.getSMSHistory);

module.exports = router;
