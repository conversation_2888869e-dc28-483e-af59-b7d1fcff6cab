import { useState } from 'react';
import { Search, Edit, Trash2, ToggleLeft, ToggleRight } from 'lucide-react';
import { PermissionButton } from '../auth/PermissionButton';

export default function PlazaList({ plazas = [], onEdit, onDelete, onSelect, onToggleStatus }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'PlazaName', direction: 'ascending' });

  // Filter plazas based on search term
  const filteredPlazas = plazas.filter(plaza =>
    (plaza.PlazaName && plaza.PlazaName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (plaza.PlazaCode && plaza.PlazaCode.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (plaza.PlazaDisplayName && plaza.PlazaDisplayName.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort plazas based on sortConfig
  const sortedPlazas = [...filteredPlazas].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const getSortIndicator = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? '↑' : '↓';
    }
    return '';
  };

  return (
    <div className="overflow-hidden">
      <div className="p-4 border-b">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search plazas..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('PlazaCode')}
              >
                Plaza Code {getSortIndicator('PlazaCode')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('PlazaName')}
              >
                Plaza Name {getSortIndicator('PlazaName')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('PlazaDisplayName')}
              >
                Display Name {getSortIndicator('PlazaDisplayName')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('ContactPerson')}
              >
                Contact Person {getSortIndicator('ContactPerson')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('ContactNumber')}
              >
                Contact Number {getSortIndicator('ContactNumber')}
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('IsActive')}
              >
                Status {getSortIndicator('IsActive')}
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedPlazas.length === 0 ? (
              <tr>
                <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                  No plazas found. Please check your filters or try creating a new plaza.
                </td>
              </tr>
            ) : (
              sortedPlazas.map((plaza) => (
                <tr
                  key={plaza.Id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onSelect(plaza.Id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{plaza.PlazaCode}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{plaza.PlazaName}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{plaza.PlazaDisplayName}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{plaza.ContactPerson}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{plaza.ContactNumber}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        plaza.IsActive === true || plaza.IsActive === 1 || plaza.IsActive === '1' || plaza.IsActive === 'Y'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {plaza.IsActive === true || plaza.IsActive === 1 || plaza.IsActive === '1' || plaza.IsActive === 'Y'
                        ? 'Active'
                        : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <PermissionButton
                        requiredModule="Plazas"
                        requiredPermissions={["Edit"]}
                        companyId={plaza.CompanyId}
                        plazaId={plaza.Id}
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(plaza);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        aria-label={`Edit ${plaza.PlazaName}`}
                      >
                        <Edit size={18} />
                      </PermissionButton>
                      <PermissionButton
                        requiredModule="Plazas"
                        requiredPermissions={["Delete"]}
                        companyId={plaza.CompanyId}
                        plazaId={plaza.Id}
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(plaza.Id);
                        }}
                        className="text-red-600 hover:text-red-900"
                        aria-label={`Delete ${plaza.PlazaName}`}
                      >
                        <Trash2 size={18} />
                      </PermissionButton>

                      {onToggleStatus && (
                        <PermissionButton
                          requiredModule="Plazas"
                          requiredPermissions={["Edit"]}
                          companyId={plaza.CompanyId}
                          plazaId={plaza.Id}
                          onClick={(e) => {
                            e.stopPropagation();
                            onToggleStatus(plaza);
                          }}
                          className={`${
                            plaza.IsActive === true || plaza.IsActive === 1 || plaza.IsActive === '1' || plaza.IsActive === 'Y'
                              ? 'text-green-600 hover:text-green-900'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          aria-label={`${
                            plaza.IsActive === true || plaza.IsActive === 1 || plaza.IsActive === '1' || plaza.IsActive === 'Y'
                              ? 'Deactivate'
                              : 'Activate'
                          } ${plaza.PlazaName}`}
                        >
                          {plaza.IsActive === true || plaza.IsActive === 1 || plaza.IsActive === '1' || plaza.IsActive === 'Y'
                            ? <ToggleRight size={18} />
                            : <ToggleLeft size={18} />}
                        </PermissionButton>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}