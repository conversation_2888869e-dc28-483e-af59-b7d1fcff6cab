-- Create sp_Valet_CustomerSession_Complete stored procedure
USE ParkwizOps;
GO

-- Create the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_CustomerSession_Complete]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_CustomerSession_Complete];
GO

CREATE PROCEDURE [dbo].[sp_Valet_CustomerSession_Complete]
    @SessionId INT,
    @CustomerId DECIMAL(18,0),
    @TransactionId DECIMAL(18,0)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @SessionId IS NULL OR @SessionId <= 0
        BEGIN
            RAISERROR('Session ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CustomerId IS NULL OR @CustomerId <= 0
        BEGIN
            RAISERROR('Customer ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @TransactionId IS NULL OR @TransactionId <= 0
        BEGIN
            RAISERROR('Transaction ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if session exists and belongs to customer
        IF NOT EXISTS(SELECT 1 FROM [dbo].[ValetCustomerSessions] WHERE [Id] = @SessionId AND [CustomerId] = @CustomerId AND [IsActive] = 1)
        BEGIN
            RAISERROR('Session not found or inactive', 16, 1);
            RETURN -1;
        END
        
        -- Get current session data
        DECLARE @SessionData NVARCHAR(MAX);
        SELECT @SessionData = [SessionData] FROM [dbo].[ValetCustomerSessions] WHERE [Id] = @SessionId;
        
        -- Update session data to include transaction ID and mark as completed
        DECLARE @UpdatedSessionData NVARCHAR(MAX);
        SET @UpdatedSessionData = JSON_MODIFY(@SessionData, '$.transactionId', @TransactionId);
        SET @UpdatedSessionData = JSON_MODIFY(@UpdatedSessionData, '$.status', 'COMPLETED');
        SET @UpdatedSessionData = JSON_MODIFY(@UpdatedSessionData, '$.completedAt', CONVERT(NVARCHAR(30), GETDATE(), 127));
        
        -- Update session
        UPDATE [dbo].[ValetCustomerSessions]
        SET
            [SessionData] = @UpdatedSessionData,
            [IsActive] = 0,
            [UpdatedOn] = GETDATE()
        WHERE [Id] = @SessionId AND [CustomerId] = @CustomerId;
        
        -- Return completed session details
        SELECT 
            s.[Id] AS SessionId,
            s.[CustomerId],
            s.[PlazaValetPointId],
            s.[SessionData],
            s.[CreatedOn],
            s.[UpdatedOn],
            s.[IsActive],
            c.[Name] AS CustomerName,
            c.[MobileNumber] AS CustomerMobileNumber,
            pvp.[ValetPointName],
            p.[PlazaName],
            cm.[CompanyName],
            @TransactionId AS TransactionId
        FROM [dbo].[ValetCustomerSessions] s
        JOIN [dbo].[ValetCustomers] c ON s.[CustomerId] = c.[Id]
        JOIN [dbo].[PlazaValetPoint] pvp ON s.[PlazaValetPointId] = pvp.[Id]
        JOIN [dbo].[Plaza] p ON pvp.[PlazaId] = p.[Id]
        JOIN [dbo].[tblCompanyMaster] cm ON pvp.[CompanyId] = cm.[Id]
        WHERE s.[Id] = @SessionId;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_CustomerSession_Complete';