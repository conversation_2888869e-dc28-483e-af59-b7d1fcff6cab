const db = require('./src/config/database');

async function analyzePermissions() {
  try {
    console.log('=== ANALYZING PERMISSION SYSTEM ===');
    
    // Step 1: Check Permissions table
    console.log('\n1. CHECKING PERMISSIONS TABLE...');
    
    const permissions = await db.query('SELECT * FROM Permissions ORDER BY Id');
    console.log('Available Permissions:', permissions.recordset);
    
    // Step 2: Check SubModulePermissions mapping
    console.log('\n2. CHECKING SUBMODULE-PERMISSION MAPPING...');
    
    const subModulePermissions = await db.query(`
      SELECT 
        sm.Name as SubModuleName,
        sm.Path,
        p.Name as PermissionName,
        smp.Id as SubModulePermissionId
      FROM SubModulePermissions smp
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE smp.IsActive = 1
      ORDER BY sm.Id, p.Id
    `);
    
    console.log(`Total SubModule-Permission mappings: ${subModulePermissions.recordset.length}`);
    console.log('Sample mappings:', subModulePermissions.recordset.slice(0, 10));
    
    // Step 3: Check Role Permissions for each role
    console.log('\n3. CHECKING ROLE PERMISSIONS...');
    
    const rolePermissionDetails = await db.query(`
      SELECT 
        r.Name as RoleName,
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        sm.Path,
        p.Name as PermissionName,
        rp.IsActive
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE rp.IsActive = 1
      ORDER BY r.Id, m.DisplayOrder, sm.Id, p.Id
    `);
    
    // Group by role
    const permissionsByRole = {};
    rolePermissionDetails.recordset.forEach(perm => {
      if (!permissionsByRole[perm.RoleName]) {
        permissionsByRole[perm.RoleName] = [];
      }
      permissionsByRole[perm.RoleName].push(perm);
    });
    
    console.log('\nPermissions by Role:');
    Object.keys(permissionsByRole).forEach(roleName => {
      console.log(`\n${roleName}: ${permissionsByRole[roleName].length} permissions`);
      
      // Group by module
      const moduleGroups = {};
      permissionsByRole[roleName].forEach(perm => {
        if (!moduleGroups[perm.ModuleName]) {
          moduleGroups[perm.ModuleName] = {};
        }
        if (!moduleGroups[perm.ModuleName][perm.SubModuleName]) {
          moduleGroups[perm.ModuleName][perm.SubModuleName] = [];
        }
        moduleGroups[perm.ModuleName][perm.SubModuleName].push(perm.PermissionName);
      });
      
      Object.keys(moduleGroups).forEach(moduleName => {
        console.log(`  ${moduleName}:`);
        Object.keys(moduleGroups[moduleName]).forEach(subModuleName => {
          console.log(`    ${subModuleName} (${moduleGroups[moduleName][subModuleName].join(', ')})`);
        });
      });
    });
    
    // Step 4: Check what's missing for CompanyAdmin and PlazaManager
    console.log('\n4. ANALYZING MISSING PERMISSIONS...');
    
    const superAdminPermissions = permissionsByRole['SuperAdmin'] || [];
    const companyAdminPermissions = permissionsByRole['CompanyAdmin'] || [];
    const plazaManagerPermissions = permissionsByRole['PlazaManager'] || [];
    
    console.log(`SuperAdmin has ${superAdminPermissions.length} permissions`);
    console.log(`CompanyAdmin has ${companyAdminPermissions.length} permissions`);
    console.log(`PlazaManager has ${plazaManagerPermissions.length} permissions`);
    
    // Step 5: Check specific modules that should be accessible
    console.log('\n5. CHECKING MODULE ACCESS...');
    
    const moduleAccess = await db.query(`
      SELECT 
        r.Name as RoleName,
        m.Name as ModuleName,
        COUNT(DISTINCT sm.Id) as SubModulesWithPermissions
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      WHERE rp.IsActive = 1
      GROUP BY r.Id, r.Name, m.Id, m.Name
      ORDER BY r.Id, m.DisplayOrder
    `);
    
    console.log('Module access by role:');
    moduleAccess.recordset.forEach(access => {
      console.log(`${access.RoleName} -> ${access.ModuleName}: ${access.SubModulesWithPermissions} submodules`);
    });
    
    // Step 6: Check if CompanyAdmin and PlazaManager need more permissions
    console.log('\n6. PERMISSION GAPS ANALYSIS...');
    
    // Check what modules CompanyAdmin should have access to but doesn't
    const expectedCompanyAdminModules = [
      'User Management', 'Company Management', 'Plaza Management', 
      'Lane Management', 'Configuration', 'Reports'
    ];
    
    const expectedPlazaManagerModules = [
      'Plaza Management', 'Lane Management', 'Configuration'
    ];
    
    const companyAdminModules = moduleAccess.recordset
      .filter(m => m.RoleName === 'CompanyAdmin')
      .map(m => m.ModuleName);
    
    const plazaManagerModules = moduleAccess.recordset
      .filter(m => m.RoleName === 'PlazaManager')
      .map(m => m.ModuleName);
    
    console.log('CompanyAdmin missing modules:', 
      expectedCompanyAdminModules.filter(m => !companyAdminModules.includes(m)));
    
    console.log('PlazaManager missing modules:', 
      expectedPlazaManagerModules.filter(m => !plazaManagerModules.includes(m)));
    
    process.exit(0);
  } catch (error) {
    console.error('Permission analysis failed:', error);
    process.exit(1);
  }
}

analyzePermissions();
