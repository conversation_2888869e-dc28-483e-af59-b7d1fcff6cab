import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter, Search } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { passRegistrationApi } from '../api/passRegistrationApi';
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import PassRegistrationList from '../components/PassRegistration/PassRegistrationList';
import PassRegistrationDialog from '../components/PassRegistration/PassRegistrationDialog';
import { useAuth } from '../contexts/authContext';
import { PermissionButton } from '../components/auth/PermissionButton';

const ManagePassRegistration = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingPass, setEditingPass] = useState(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [passTypeFilter, setPassTypeFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();

  // Fetch companies for dropdown
  const { data: companies = [] } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => Array.isArray(data) ? data : []
  });

  // Fetch plazas for dropdown
  const { data: plazas = [] } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas,
    select: (data) => {
      if (data && data.success === true && Array.isArray(data.data)) {
        return data.data;
      }
      return Array.isArray(data) ? data : [];
    }
  });

  // State for selected plaza
  const [selectedPlaza, setSelectedPlaza] = useState('');

  // Fetch pass registrations with pagination and filters
  const { data: passData, isLoading, error } = useQuery({
    queryKey: ['passRegistrations', currentPage, pageSize, searchTerm, selectedPlaza, passTypeFilter],
    queryFn: () => passRegistrationApi.getAll({
      page: currentPage,
      limit: pageSize,
      search: searchTerm,
      plazaCode: selectedPlaza || '',
      passType: passTypeFilter
    }),
    onError: (error) => {
      console.error('Error fetching pass registrations:', error);
      toast.showError('Failed to fetch pass registrations');
    }
  });

  // Extract data and pagination from the API response
  // Handle different API response formats
  let passRegistrations = [];
  let paginationData = {};
  
  if (passData) {
    console.log('Raw API response:', passData);
    
    // The API response has a nested data structure
    if (passData.data) {
      // Check if data.data is an array (nested data structure)
      if (passData.data.data && Array.isArray(passData.data.data)) {
        passRegistrations = passData.data.data;
        paginationData = passData.data.pagination || {};
      } 
      // Check if data is an array directly
      else if (Array.isArray(passData.data)) {
        passRegistrations = passData.data;
        paginationData = passData.pagination || {};
      }
    } else if (Array.isArray(passData)) {
      // If the API returns an array directly
      passRegistrations = passData;
    }
  }
  
  console.log('Extracted pass registrations:', passRegistrations);
  
  // Create a proper pagination object with all required properties
  const pagination = {
    currentPage: parseInt(paginationData.currentPage || 1, 10),
    totalPages: parseInt(paginationData.totalPages || 1, 10),
    totalRecords: parseInt(paginationData.totalRecords || passRegistrations.length, 10),
    pageSize: pageSize
  };
  
  // Debug log
  console.log('Pagination in component:', pagination);
  console.log('Pass registrations count:', passRegistrations.length);

  // Create mutation
  const createMutation = useMutation({
    mutationFn: passRegistrationApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['passRegistrations']);
      toast.showCrudSuccess('create', 'Pass registration');
      setIsDialogOpen(false);
      setEditingPass(null);
    },
    onError: (error) => {
      console.error('Error creating pass registration:', error);
      toast.showError(error.response?.data?.message || 'Failed to create pass registration');
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => passRegistrationApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['passRegistrations']);
      toast.showCrudSuccess('update', 'Pass registration');
      setIsDialogOpen(false);
      setEditingPass(null);
    },
    onError: (error) => {
      console.error('Error updating pass registration:', error);
      toast.showError(error.response?.data?.message || 'Failed to update pass registration');
    }
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: passRegistrationApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries(['passRegistrations']);
      toast.showCrudSuccess('delete', 'Pass registration');
    },
    onError: (error) => {
      console.error('Error deleting pass registration:', error);
      toast.showError(error.response?.data?.message || 'Failed to delete pass registration');
    }
  });

  const handleCreate = () => {
    setEditingPass(null);
    setIsDialogOpen(true);
  };

  const handleEdit = (pass) => {
    setEditingPass(pass);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this pass registration?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleSubmit = (data) => {
    if (editingPass) {
      updateMutation.mutate({ id: editingPass.PassRegID, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    console.log('Searching with term:', searchTerm);
    console.log('Current filters - Plaza:', selectedPlaza, 'Pass Type:', passTypeFilter);
    setCurrentPage(1); // Reset to first page when searching
  };

  const clearFilters = () => {
    setSelectedPlaza('');
    setPassTypeFilter('');
    setSearchTerm('');
    setCurrentPage(1);
    console.log('Filters cleared');
    // Force a refetch with cleared filters
    queryClient.invalidateQueries(['passRegistrations']);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  
  const handleItemsPerPageChange = (value) => {
    setPageSize(value);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  // Pass types for filter dropdown
  const passTypes = [
    'Monthly',
    'Quarterly',
    'Half Yearly',
    'Yearly',
    'Daily',
    'Weekly'
  ];

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error Loading Pass Registrations</h3>
          <p className="text-red-600 mt-1">
            {error.response?.data?.message || 'Failed to load pass registrations. Please try again.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pass Registration Management</h1>
            <p className="text-gray-600 mt-1">
              Manage vehicle pass registrations for toll plazas
            </p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </button>

            <PermissionButton
              requiredModule="Pass Registration"
              requiredPermissions={["Create"]}
              onClick={handleCreate}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add Pass Registration</span>
            </PermissionButton>
          </div>
        </div>

        {/* Search Bar */}
        <div className="mt-4">
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by holder name, vehicle number, GIN, contact, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Search
            </button>
          </form>
        </div>

        {/* Filters */}
        {isFilterOpen && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Plaza Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plaza
                </label>
                <select
                  value={selectedPlaza}
                  onChange={(e) => setSelectedPlaza(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Plazas</option>
                  {plazas.map((plaza) => (
                    <option key={plaza.Id} value={plaza.PlazaCode}>
                      {plaza.PlazaName}
                    </option>
                  ))}
                </select>
              </div>

              {/* Pass Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pass Type
                </label>
                <select
                  value={passTypeFilter}
                  onChange={(e) => setPassTypeFilter(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Pass Types</option>
                  {passTypes.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4 flex justify-end space-x-2">
              <button
                onClick={() => {
                  // Apply filters
                  console.log('Applying filters - Plaza:', selectedPlaza, 'Pass Type:', passTypeFilter);
                  setCurrentPage(1); // Reset to first page when applying filters
                  queryClient.invalidateQueries(['passRegistrations']);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Apply Filters
              </button>
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Pass Registration List */}
      <PassRegistrationList
        passRegistrations={passRegistrations}
        isLoading={isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        pagination={pagination}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
      />

      {/* Pass Registration Dialog */}
      {isDialogOpen && (
        <PassRegistrationDialog
          isOpen={isDialogOpen}
          onClose={() => {
            setIsDialogOpen(false);
            setEditingPass(null);
          }}
          onSubmit={handleSubmit}
          passRegistration={editingPass}
          isLoading={createMutation.isLoading || updateMutation.isLoading}
        />
      )}
    </div>
  );
};

export default ManagePassRegistration;
