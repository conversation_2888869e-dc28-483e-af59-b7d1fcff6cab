const axios = require('axios');

/**
 * Valet API Endpoints Test Script
 * Tests all valet endpoints to ensure stored procedures integration works correctly
 */

const BASE_URL = 'http://localhost:3000/api/valet';
const TEST_MOBILE = '9876543210';
const TEST_VEHICLE = 'KA01AB1234';

// Test configuration
const config = {
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// Test results tracking
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Helper function to log test results
function logTest(testName, success, error = null) {
  if (success) {
    console.log(`✅ ${testName} - PASSED`);
    testResults.passed++;
  } else {
    console.log(`❌ ${testName} - FAILED`);
    if (error) {
      console.log(`   Error: ${error.message}`);
      testResults.errors.push({ test: testName, error: error.message });
    }
    testResults.failed++;
  }
}

// Test Customer Endpoints
async function testCustomerEndpoints() {
  console.log('\n🧪 Testing Customer Endpoints...');
  
  try {
    // Test 1: Register Customer
    const registerResponse = await axios.post(`${BASE_URL}/customers/register`, {
      mobileNumber: TEST_MOBILE,
      vehicleNumber: TEST_VEHICLE
    }, config);
    
    logTest('Customer Registration', registerResponse.status === 201);
    
    // Test 2: Get Customer by Mobile
    const getCustomerResponse = await axios.get(`${BASE_URL}/customers/mobile/${TEST_MOBILE}`, config);
    logTest('Get Customer by Mobile', getCustomerResponse.status === 200 && getCustomerResponse.data.success);
    
    const customerId = getCustomerResponse.data.customer?.Id;
    
    if (customerId) {
      // Test 3: Update Customer Details
      const updateResponse = await axios.put(`${BASE_URL}/customers/${customerId}`, {
        name: 'Test Customer',
        addressId: null
      }, config);
      
      logTest('Update Customer Details', updateResponse.status === 200);
      
      // Test 4: Add Customer Vehicle
      const addVehicleResponse = await axios.post(`${BASE_URL}/customers/${customerId}/vehicles`, {
        vehicleNumber: 'KA02CD5678'
      }, config);
      
      logTest('Add Customer Vehicle', addVehicleResponse.status === 201);
      
      // Test 5: Get Customer Vehicles
      const getVehiclesResponse = await axios.get(`${BASE_URL}/customers/${customerId}/vehicles`, config);
      logTest('Get Customer Vehicles', getVehiclesResponse.status === 200);
    }
    
  } catch (error) {
    logTest('Customer Endpoints', false, error);
  }
}

// Test OTP Endpoints
async function testOTPEndpoints() {
  console.log('\n🧪 Testing OTP Endpoints...');
  
  try {
    // Test 1: Generate OTP
    const generateResponse = await axios.post(`${BASE_URL}/otp/generate`, {
      mobileNumber: TEST_MOBILE,
      expiryMinutes: 5
    }, config);
    
    logTest('Generate OTP', generateResponse.status === 201);
    
    // Test 2: Get OTP Status
    const statusResponse = await axios.get(`${BASE_URL}/otp/status/${TEST_MOBILE}`, config);
    logTest('Get OTP Status', statusResponse.status === 200);
    
    // Test 3: Verify OTP (with dummy OTP - will fail but tests endpoint)
    try {
      const verifyResponse = await axios.post(`${BASE_URL}/otp/verify`, {
        mobileNumber: TEST_MOBILE,
        otp: '123456'
      }, config);
      logTest('Verify OTP (Invalid)', verifyResponse.status === 400);
    } catch (error) {
      if (error.response && error.response.status === 400) {
        logTest('Verify OTP (Invalid)', true);
      } else {
        logTest('Verify OTP (Invalid)', false, error);
      }
    }
    
    // Test 4: Resend OTP
    const resendResponse = await axios.post(`${BASE_URL}/otp/resend`, {
      mobileNumber: TEST_MOBILE
    }, config);
    
    logTest('Resend OTP', resendResponse.status === 200);
    
  } catch (error) {
    logTest('OTP Endpoints', false, error);
  }
}

// Test Transaction Endpoints
async function testTransactionEndpoints() {
  console.log('\n🧪 Testing Transaction Endpoints...');
  
  try {
    // Test 1: Create Valet Transaction
    const createResponse = await axios.post(`${BASE_URL}/transactions/create`, {
      customerVehicleNumber: TEST_VEHICLE,
      customerMobileNumber: TEST_MOBILE,
      customerName: 'Test Customer',
      plazaValetPointId: 1, // Assuming valet point exists
      valetFee: 50,
      parkingFee: 20,
      paymentType: 1,
      payAt: 1
    }, config);
    
    logTest('Create Valet Transaction', createResponse.status === 201);
    
    const transactionData = createResponse.data.transaction;
    
    if (transactionData) {
      // Test 2: Get Transaction by PNR
      const getPNRResponse = await axios.get(`${BASE_URL}/transactions/search/${transactionData.pnrNumber}?type=pnr`, config);
      logTest('Get Transaction by PNR', getPNRResponse.status === 200);
      
      // Test 3: Get Transaction by Pin
      const getPinResponse = await axios.get(`${BASE_URL}/transactions/search/${transactionData.parkingPin}?type=pin`, config);
      logTest('Get Transaction by Pin', getPinResponse.status === 200);
      
      // Test 4: Update Transaction Status
      const updateStatusResponse = await axios.put(`${BASE_URL}/transactions/${transactionData.id}/status`, {
        status: 'DRIVER_ASSIGNED',
        driverId: 1,
        remarks: 'Test driver assignment'
      }, config);
      
      logTest('Update Transaction Status', updateStatusResponse.status === 200);
    }
    
  } catch (error) {
    logTest('Transaction Endpoints', false, error);
  }
}

// Test QR Code Endpoints
async function testQRCodeEndpoints() {
  console.log('\n🧪 Testing QR Code Endpoints...');
  
  try {
    // Test 1: Generate QR Code
    const generateResponse = await axios.post(`${BASE_URL}/qrcode/generate`, {
      plazaValetPointId: 1, // Assuming valet point exists
      qrType: 'VALET_POINT'
    }, config);
    
    logTest('Generate QR Code', generateResponse.status === 201);
    
    const qrData = generateResponse.data.data?.qrData;
    
    if (qrData) {
      // Test 2: Get QR Code by Data (Scan)
      const scanResponse = await axios.get(`${BASE_URL}/qrcode/scan/${encodeURIComponent(qrData)}`, config);
      logTest('Scan QR Code', scanResponse.status === 200);
    }
    
    // Test 3: Get QR Codes by Plaza
    const getByPlazaResponse = await axios.get(`${BASE_URL}/qrcode/plaza/1?pageNumber=1&pageSize=10`, config);
    logTest('Get QR Codes by Plaza', getByPlazaResponse.status === 200);
    
  } catch (error) {
    logTest('QR Code Endpoints', false, error);
  }
}

// Test Health Check
async function testHealthCheck() {
  console.log('\n🧪 Testing Health Check...');
  
  try {
    const healthResponse = await axios.get(`${BASE_URL}/health`, config);
    logTest('Health Check', healthResponse.status === 200 && healthResponse.data.success);
  } catch (error) {
    logTest('Health Check', false, error);
  }
}

// Main test runner
async function runAllTests() {
  console.log('🚀 Starting Valet API Endpoints Test Suite...');
  console.log(`📍 Base URL: ${BASE_URL}`);
  console.log(`📱 Test Mobile: ${TEST_MOBILE}`);
  console.log(`🚗 Test Vehicle: ${TEST_VEHICLE}`);
  
  // Run all test suites
  await testHealthCheck();
  await testCustomerEndpoints();
  await testOTPEndpoints();
  await testTransactionEndpoints();
  await testQRCodeEndpoints();
  
  // Print summary
  console.log('\n📊 Test Results Summary:');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🔍 Error Details:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  console.log('\n✨ Test suite completed!');
}

// Handle command line execution
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test suite failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testCustomerEndpoints,
  testOTPEndpoints,
  testTransactionEndpoints,
  testQRCodeEndpoints,
  testHealthCheck
};
