const express = require('express');
const router = express.Router();
const countryController = require('../controllers/countryController');
const auth = require('../middleware/auth');

/**
 * @route   POST /api/countries
 * @desc    Create a new country
 * @access  Private (SuperAdmin only)
 */
router.post('/', auth(['Create']), countryController.createCountry);

/**
 * @route   GET /api/countries
 * @desc    Get all countries
 * @access  Private (All authenticated users)
 */
router.get('/', auth(['View']), countryController.getCountries);

/**
 * @route   GET /api/countries/:id
 * @desc    Get a country by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', auth(['View']), countryController.getCountryById);

/**
 * @route   PUT /api/countries/:id
 * @desc    Update a country
 * @access  Private (SuperAdmin only)
 */
router.put('/:id', auth(['Edit']), countryController.updateCountry);

/**
 * @route   DELETE /api/countries/:id
 * @desc    Delete a country
 * @access  Private (SuperAdmin only)
 */
router.delete('/:id', auth(['Delete']), countryController.deleteCountry);

module.exports = router;
