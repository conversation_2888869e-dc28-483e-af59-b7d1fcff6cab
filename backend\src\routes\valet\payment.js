const express = require('express');
const router = express.Router();
const PaymentController = require('../../controllers/valet/PaymentController');
const { auth } = require('../../middleware/auth');

/**
 * Valet Payment Routes
 * Handles payment processing, gateway integration, and payment status management
 */

// =============================================
// PUBLIC ROUTES (Customer App)
// =============================================

// Get payment options for a plaza (PUBLIC)
router.get('/options/:plazaId', PaymentController.getPaymentOptions);

// Initiate payment process (PUBLIC)
router.post('/initiate', PaymentController.initiatePayment);

// Get payment status (PUBLIC)
router.get('/status/:paymentId', PaymentController.getPaymentStatus);

// Update payment status (for gateway callbacks) (PUBLIC)
router.put('/status/:paymentId', PaymentController.updatePaymentStatus);

// =============================================
// ADMIN ROUTES (Require Authentication)
// =============================================

// Accept cash payment (for valet controllers) (REQUIRES AUTH)
router.post('/cash/accept/:paymentId', auth(), PaymentController.acceptCashPayment);

// Get pending cash payments (for valet controllers) (REQUIRES AUTH)
router.get('/cash/pending/:plazaId', auth(), PaymentController.getPendingCashPayments);

module.exports = router;
