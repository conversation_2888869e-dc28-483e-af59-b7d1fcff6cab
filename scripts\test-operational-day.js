// scripts/test-operational-day.js
/**
 * ===============================================================================
 * # Test Operational Day Logic (6:00 AM to 5:59 AM)
 * ===============================================================================
 * 
 * This script tests the new operational day date range calculation
 * to ensure it works correctly for different times and scenarios.
 */

// Copy the calculateDateRange function for testing
function calculateDateRange(dateRange, testTime = null) {
  // Use test time if provided, otherwise current time
  const referenceDate = testTime ? new Date(testTime) : new Date();
  let startDate, endDate;
  
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date - operational day from 6:00 AM to 5:59 AM next day
    startDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate = new Date(dateRange + 'T06:00:00.000Z');
    endDate.setDate(endDate.getDate() + 1); // Next day
    endDate.setSeconds(endDate.getSeconds() - 1); // 5:59:59 AM
    
    console.log(`Using specific operational date: ${dateRange} (6:00 AM to 5:59 AM next day)`);
  } else {
    // It's a predefined range - operational day logic (6:00 AM to 5:59 AM)
    switch(dateRange) {
      case 'today':
        // Current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        break;
      case 'yesterday':
        // Previous operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - day before yesterday's operational day
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 2);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() - 1);
          endDate.setHours(5, 59, 59, 999);
        }
        break;
      case 'week':
        // Last 7 operational days
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - include current operational day
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - end at current operational day
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
        startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7);
        startDate.setHours(6, 0, 0, 0);
        break;
      default:
        // Default to current operational day
        if (referenceDate.getHours() >= 6) {
          // After 6 AM - current operational day started today at 6 AM
          startDate = new Date(referenceDate);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setDate(endDate.getDate() + 1);
          endDate.setHours(5, 59, 59, 999);
        } else {
          // Before 6 AM - current operational day started yesterday at 6 AM
          startDate = new Date(referenceDate);
          startDate.setDate(startDate.getDate() - 1);
          startDate.setHours(6, 0, 0, 0);
          endDate = new Date(referenceDate);
          endDate.setHours(5, 59, 59, 999);
        }
    }
  }
  
  return { startDate, endDate };
}

function formatDateTime(date) {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

function testScenario(description, dateRange, testTime) {
  console.log(`\n🧪 ${description}`);
  console.log(`   Test Time: ${formatDateTime(new Date(testTime))}`);
  
  const result = calculateDateRange(dateRange, testTime);
  
  console.log(`   📅 ${dateRange.toUpperCase()}:`);
  console.log(`      Start: ${formatDateTime(result.startDate)}`);
  console.log(`      End:   ${formatDateTime(result.endDate)}`);
  
  // Calculate duration
  const durationHours = (result.endDate - result.startDate) / (1000 * 60 * 60);
  console.log(`      Duration: ${durationHours.toFixed(1)} hours`);
  
  return result;
}

console.log('🕐 OPERATIONAL DAY TESTING (6:00 AM to 5:59 AM)');
console.log('='.repeat(60));

// Test scenarios for different times of day
const testDate = '2024-01-15'; // Monday

// Test 1: Current time is 10:00 AM (after 6 AM)
testScenario(
  'Current time: 10:00 AM (after operational day start)',
  'today',
  `${testDate}T10:00:00.000Z`
);

// Test 2: Current time is 3:00 AM (before 6 AM)
testScenario(
  'Current time: 3:00 AM (before operational day start)',
  'today',
  `${testDate}T03:00:00.000Z`
);

// Test 3: Yesterday at 10:00 AM
testScenario(
  'Yesterday at 10:00 AM',
  'yesterday',
  `${testDate}T10:00:00.000Z`
);

// Test 4: Yesterday at 3:00 AM
testScenario(
  'Yesterday at 3:00 AM',
  'yesterday',
  `${testDate}T03:00:00.000Z`
);

// Test 5: Week range at 10:00 AM
testScenario(
  'Week range at 10:00 AM',
  'week',
  `${testDate}T10:00:00.000Z`
);

// Test 6: Week range at 3:00 AM
testScenario(
  'Week range at 3:00 AM',
  'week',
  `${testDate}T03:00:00.000Z`
);

// Test 7: Specific date
testScenario(
  'Specific date: 2024-01-10',
  '2024-01-10',
  `${testDate}T10:00:00.000Z`
);

console.log('\n' + '='.repeat(60));
console.log('✅ OPERATIONAL DAY LOGIC SUMMARY:');
console.log('   • Operational day runs from 6:00 AM to 5:59 AM (next day)');
console.log('   • "Today" depends on current time:');
console.log('     - If current time >= 6:00 AM: Today 6:00 AM to Tomorrow 5:59 AM');
console.log('     - If current time < 6:00 AM: Yesterday 6:00 AM to Today 5:59 AM');
console.log('   • "Yesterday" is always the previous operational day');
console.log('   • Week/Month ranges adjust based on current operational day');
console.log('   • Specific dates always start at 6:00 AM of that date');

console.log('\n🎯 PRACTICAL EXAMPLES:');
console.log('   • At 2:00 AM on Jan 15: "Today" = Jan 14 6:00 AM to Jan 15 5:59 AM');
console.log('   • At 8:00 AM on Jan 15: "Today" = Jan 15 6:00 AM to Jan 16 5:59 AM');
console.log('   • At 11:00 PM on Jan 15: "Today" = Jan 15 6:00 AM to Jan 16 5:59 AM');

console.log('\n🚀 Ready to test with your dashboard!');