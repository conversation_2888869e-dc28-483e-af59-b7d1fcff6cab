const db = require('./src/config/database');

async function checkTableNames() {
  try {
    console.log('=== CHECKING DATABASE TABLE NAMES ===');
    
    // Get all table names
    const tables = await db.query(`
      SELECT TABLE_NAME, TABLE_TYPE
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    
    console.log('\nAll tables in database:');
    tables.recordset.forEach(table => {
      console.log(`  ${table.TABLE_NAME}`);
    });
    
    // Check for lane-related tables
    console.log('\nLane-related tables:');
    const laneTables = tables.recordset.filter(t => 
      t.TABLE_NAME.toLowerCase().includes('lane')
    );
    
    if (laneTables.length === 0) {
      console.log('  ❌ No lane tables found');
    } else {
      laneTables.forEach(table => {
        console.log(`  ✓ ${table.TABLE_NAME}`);
      });
    }
    
    // Check for plaza-related tables
    console.log('\nPlaza-related tables:');
    const plazaTables = tables.recordset.filter(t => 
      t.TABLE_NAME.toLowerCase().includes('plaza')
    );
    
    if (plazaTables.length === 0) {
      console.log('  ❌ No plaza tables found');
    } else {
      plazaTables.forEach(table => {
        console.log(`  ✓ ${table.TABLE_NAME}`);
      });
    }
    
    // Check views
    console.log('\nViews in database:');
    const views = await db.query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.VIEWS
      ORDER BY TABLE_NAME
    `);
    
    views.recordset.forEach(view => {
      console.log(`  ${view.TABLE_NAME}`);
    });
    
    // Check if Plaza table exists and get sample data
    const plazaExists = tables.recordset.some(t => t.TABLE_NAME === 'Plaza');
    if (plazaExists) {
      console.log('\n=== PLAZA TABLE DATA ===');
      const plazaData = await db.query('SELECT TOP 5 * FROM Plaza');
      console.log(`Plaza records: ${plazaData.recordset.length}`);
      plazaData.recordset.forEach(plaza => {
        console.log(`  ID: ${plaza.Id}, Name: ${plaza.PlazaName}, CompanyId: ${plaza.CompanyId}`);
      });
    }
    
    // Check company assignments again with correct table names
    console.log('\n=== COMPANY ADMIN ACCESS CHECK ===');
    
    const companyAccess = await db.query(`
      SELECT c.Id, c.CompanyName, uc.IsActive
      FROM UserCompany uc
      JOIN Company c ON uc.CompanyId = c.Id
      WHERE uc.UserId = 4
    `);
    
    console.log('Company assignments for CompanyAdmin (User ID: 4):');
    companyAccess.recordset.forEach(assignment => {
      console.log(`  ${assignment.IsActive ? '✓' : '✗'} ${assignment.CompanyName} (ID: ${assignment.Id})`);
    });
    
    if (plazaExists) {
      const plazaAccess = await db.query(`
        SELECT p.Id, p.PlazaName, c.CompanyName
        FROM Plaza p
        JOIN Company c ON p.CompanyId = c.Id
        JOIN UserCompany uc ON c.Id = uc.CompanyId
        WHERE uc.UserId = 4 AND uc.IsActive = 1 AND p.IsActive = 1
      `);
      
      console.log(`\nAccessible plazas: ${plazaAccess.recordset.length}`);
      plazaAccess.recordset.forEach(plaza => {
        console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName})`);
      });
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Check failed:', error);
    process.exit(1);
  }
}

checkTableNames();
