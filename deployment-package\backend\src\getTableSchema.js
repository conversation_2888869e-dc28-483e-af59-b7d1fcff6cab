// backend/src/getTableSchema.js
require('dotenv').config();
const db = require('./config/database');

async function getTableSchema(tableName) {
  try {
    // Query to get the table schema
    const query = `
      SELECT 
        c.name AS ColumnName,
        t.name AS DataType,
        c.max_length AS MaxLength,
        c.precision AS Precision,
        c.scale AS Scale,
        c.is_nullable AS IsNullable,
        CASE WHEN pk.column_id IS NOT NULL THEN 1 ELSE 0 END AS IsPrimaryKey
      FROM 
        sys.columns c
      INNER JOIN 
        sys.types t ON c.user_type_id = t.user_type_id
      LEFT JOIN 
        (
          SELECT 
            ic.column_id,
            ic.object_id
          FROM 
            sys.indexes i
          INNER JOIN 
            sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
          WHERE 
            i.is_primary_key = 1
        ) pk ON c.object_id = pk.object_id AND c.column_id = pk.column_id
      WHERE 
        c.object_id = OBJECT_ID(@tableName)
      ORDER BY 
        c.column_id;
    `;
    
    const result = await db.query(query, { tableName });
    
    console.log(`Schema for table ${tableName}:`);
    console.log('======================');
    
    if (result.recordset.length === 0) {
      console.log(`No columns found for table ${tableName}.`);
    } else {
      console.log('Column Name | Data Type | Max Length | Precision | Scale | Nullable | Primary Key');
      console.log('-----------|-----------|------------|-----------|-------|----------|------------');
      
      result.recordset.forEach(column => {
        console.log(`${column.ColumnName} | ${column.DataType} | ${column.MaxLength} | ${column.Precision} | ${column.Scale} | ${column.IsNullable ? 'Yes' : 'No'} | ${column.IsPrimaryKey ? 'Yes' : 'No'}`);
      });
      
      console.log(`\nTotal columns: ${result.recordset.length}`);
    }
    
    // Close the connection
    await db.closePool();
  } catch (error) {
    console.error('Error getting table schema:', error.message);
  }
}

// Execute the function with the table name
getTableSchema('dbo.tblParkwiz_Parking_Data');