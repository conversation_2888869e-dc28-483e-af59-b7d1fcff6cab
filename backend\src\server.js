require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path'); //  for serving static files
const fs = require('fs'); // for file system operations

// Redis services
const { initializeRedis, closeRedisConnections } = require('./config/redis');
const redisService = require('./services/RedisService');
const sessionService = require('./services/SessionService');
const realtimeService = require('./services/RealtimeService');

// Routes
const countryRoutes = require('./routes/country');
const stateRoutes = require('./routes/state');
const companyRoutes = require('./routes/company');
const addressRoutes = require('./routes/Address');
const userRoutes = require('./routes/UserRoutes');
const plazaRoutes = require('./routes/plaza');
const laneRoutes = require('./routes/lane');
const laneDigitalPayConfigRoutes = require('./routes/laneDigitalPayConfig');
const laneFastagConfig = require('./routes/laneFastagConfig');
const laneANPRConfigurationRoutes = require('./routes/laneANPRConfigurationRoutes'); // Import the new routes
const laneUHFReaderRoutes = require('./routes/laneUHFReaderRoutes');
const passRegistrationRoutes = require('./routes/passRegistrationRoutes');
const plazaValetPointRoutes = require('./routes/plazavaletpoint');
const permissionManagementRoutes = require('./routes/permissionManagement');
const authRoutes = require('./routes/authRoutes');
const dashboardRoutes = require('./routes/dashboardRoutes');
const enhancedDashboardRoutes = require('./routes/enhancedDashboardRoutes');
const valetRoutes = require('./routes/valet');
// const branchRoutes = require('./routes/branch');
// const productRoutes = require('./routes/product');
// const cityRoutes = require('./routes/city');
// const clientsRoutes = require('./routes/clients');
// const countryViewRoutes = require('./routes/ViewRoutes/countryView');
// const stateViewRoutes = require('./routes/ViewRoutes/stateView');
// const cityViewRoutes = require('./routes/ViewRoutes/cityView');

const db = require('./config/database'); // Database connection

const app = express(); // Initialize the app

// Initialize Redis and services
async function initializeServices() {
  try {
    console.log('🚀 Initializing Redis and services...');
    
    // Initialize Redis connections
    await initializeRedis();
    
    // Initialize Redis service
    await redisService.initialize();
    
    // Initialize real-time service
    await realtimeService.initialize();
    
    console.log('✅ All services initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    process.exit(1);
  }
}

// Initialize services
initializeServices();

// Middleware
app.use(cors());
app.use(express.json());

// Ensure upload directories exist
const companiesUploadDir = path.join(__dirname, '../Uploads/Companies');
const plazasUploadDir = path.join(__dirname, '../Uploads/Plazas');

// Create directories if they don't exist
if (!fs.existsSync(companiesUploadDir)) {
  console.log(`Creating companies upload directory: ${companiesUploadDir}`);
  fs.mkdirSync(companiesUploadDir, { recursive: true });
}

if (!fs.existsSync(plazasUploadDir)) {
  console.log(`Creating plazas upload directory: ${plazasUploadDir}`);
  fs.mkdirSync(plazasUploadDir, { recursive: true });
}

// Serve static files from Uploads directories
app.use('/Uploads/Companies', express.static(companiesUploadDir));
app.use('/Uploads/Plazas', express.static(plazasUploadDir));

// Direct access to company logos
app.get('/company-logos/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(companiesUploadDir, filename);

  if (fs.existsSync(filePath)) {
    res.sendFile(filePath);
  } else {
    res.status(404).send('File not found');
  }
});

// Database connection is handled automatically when the database module is imported
// No need to call db.connect() as it's initialized in the module

// Gracefully handle shutdown
process.on('SIGINT', async () => {
  try {
    await db.closePool();
    console.log('Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('Error closing database connection:', error);
    process.exit(1);
  }
});

// Also handle SIGTERM for container environments
process.on('SIGTERM', async () => {
  try {
    await db.closePool();
    console.log('Database connection closed due to SIGTERM');
    process.exit(0);
  } catch (error) {
    console.error('Error closing database connection on SIGTERM:', error);
    process.exit(1);
  }
});

// Enhanced health check endpoint with Redis status
app.get('/health-check', async (req, res) => {
  try {
    // Check if we can query the database
    const dbResult = await db.query('SELECT DB_NAME() AS CurrentDatabase, GETDATE() AS CurrentTime');

    // Check if Plaza table exists and has records
    const plazaCountResult = await db.query('SELECT COUNT(*) AS PlazaCount FROM Plaza');
    const plazaCount = plazaCountResult.recordset[0].PlazaCount;

    // Check Redis status
    const redisInfo = await redisService.getRedisInfo();

    // Return health check information
    return res.status(200).json({
      success: true,
      message: 'Health check successful',
      timestamp: new Date().toISOString(),
      database: {
        name: dbResult.recordset[0].CurrentDatabase,
        time: dbResult.recordset[0].CurrentTime,
        connected: true
      },
      redis: {
        connected: redisInfo.connected,
        status: redisInfo.connected ? 'healthy' : 'error'
      },
      tables: {
        plaza: {
          exists: true,
          recordCount: plazaCount
        }
      },
      services: {
        redisService: redisService ? 'initialized' : 'not initialized',
        realtimeService: realtimeService ? 'initialized' : 'not initialized'
      }
    });
  } catch (error) {
    console.error('Health check failed:', error);
    return res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/countries', countryRoutes);
app.use('/api/states', stateRoutes);
app.use('/api/addresses', addressRoutes);
// app.use('/api/cities', cityRoutes);
app.use('/api/companies', companyRoutes);
app.use('/api/plazas', plazaRoutes);
app.use('/api/plazavaletpoints', plazaValetPointRoutes);
app.use('/api/permission-management', permissionManagementRoutes);
app.use('/api/lanes', laneRoutes);
app.use('/api/lane-digital-pay-configs', laneDigitalPayConfigRoutes);
app.use('/api/lane-fastag', laneFastagConfig);
app.use('/api/anpr-configs', laneANPRConfigurationRoutes); // Corrected route path
app.use('/api/uhf-readers', laneUHFReaderRoutes);
app.use('/api/pass-registrations', passRegistrationRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/dashboard/enhanced', enhancedDashboardRoutes);
app.use('/api/valet', valetRoutes);
// app.use('/api/branches', branchRoutes);
// app.use('/api/products', productRoutes);
// app.use('/api/clients', clientsRoutes);
// app.use('/api/view/countries', countryViewRoutes);
// app.use('/api/view/states', stateViewRoutes);
// app.use('/api/view/cities', cityViewRoutes);

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

module.exports = app;
