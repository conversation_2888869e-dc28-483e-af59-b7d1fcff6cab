// import React from "react"
// import { useState, useEffect, useMemo } from "react"
// import {
//   flexRender,
//   getCoreRowModel,
//   getFilteredRowModel,
//   getPaginationRowModel,
//   getSortedRowModel,
//   useReactTable,
// } from "@tanstack/react-table"
// import {
//   ChevronLeft,
//   ChevronRight,
//   ChevronsLeft,
//   ChevronsRight,
//   Download,
//   Edit,
//   Image as ImageIcon,
//   MoreHorizontal,
//   Plus,
//   Search,
//   SlidersHorizontal,
//   Trash2,
// } from "lucide-react"
// import { companyApi } from "../../api/companyApi"

// import { Button } from "../../components/ui/button"
// import { Checkbox } from "../../components/ui/checkbox"
// import {
//   DropdownMenu,
//   DropdownMenuCheckboxItem,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "../../components/ui/dropdown-menu"
// import { Input } from "../../components/ui/input"
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../components/ui/table"
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
// import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
// import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../../components/ui/tooltip"
// import { Badge } from "../../components/ui/badge"
// import { Skeleton } from "../../components/ui/skeleton"
// import  {CompanyDialog}  from "../../components/Company/CompanyDialog"
// import { toast } from "../../components/ui/use-toast"
// import { Avatar, AvatarFallback, AvatarImage } from "../../components/ui/avatar"

// // Function to format date
// const formatDate = (dateString) => {
//   const date = new Date(dateString)
//   return new Intl.DateTimeFormat("en-US", {
//     year: "numeric",
//     month: "short",
//     day: "numeric",
//     hour: "2-digit",
//     minute: "2-digit",
//   }).format(date)
// }

// export function CompanyList() {
//   // State
//   const [sorting, setSorting] = useState([])
//   const [columnFilters, setColumnFilters] = useState([])
//   const [columnVisibility, setColumnVisibility] = useState({})
//   const [rowSelection, setRowSelection] = useState({})
//   const [globalFilter, setGlobalFilter] = useState("")
//   const [pagination, setPagination] = useState({
//     pageIndex: 0,
//     pageSize: 10,
//   })
//   const [isLoading, setIsLoading] = useState(true)
//   const [companies, setCompanies] = useState([])
//   const [isDialogOpen, setIsDialogOpen] = useState(false)
//   const [selectedCompany, setSelectedCompany] = useState(undefined)
//   const [statusFilter, setStatusFilter] = useState("all")

//   // Fetch companies from API
//   const fetchCompanies = async () => {
//     setIsLoading(true)
//     try {
//       const companiesData = await companyApi.getCompanies()
//       // Transform data to ensure consistent property naming
//       const formattedData = companiesData.map(company => ({
//         id: company.id || company.Id,
//         companyName: company.companyName || company.CompanyName,
//         companyCode: company.companyCode || company.CompanyCode,
//         companyLogo: company.companyLogo || company.CompanyLogo,
//         contactPerson: company.contactPerson || company.ContactPerson,
//         contactEmail: company.contactEmail || company.ContactEmail,
//         contactNumber: company.contactNumber || company.ContactNumber,
//         address: company.address || company.Address,
//         isActive: company.isActive !== undefined ? company.isActive : (company.IsActive !== undefined ? company.IsActive : true),
//         createdOn: company.createdOn || company.CreatedOn || new Date().toISOString(),
//         modifiedOn: company.modifiedOn || company.ModifiedOn || new Date().toISOString()
//       }))
//       setCompanies(formattedData)
//     } catch (error) {
//       console.error("Error fetching companies:", error)
//       toast({
//         title: "Error fetching companies",
//         description: "There was a problem retrieving company data.",
//         variant: "destructive",
//       })
//     } finally {
//       setIsLoading(false)
//     }
//   }

//   // Initial data load
//   useEffect(() => {
//     fetchCompanies()
//   }, [])

//   // Delete company
//   const handleDeleteCompany = async (id) => {
//     try {
//       await companyApi.deleteCompany(id)
//       toast({
//         title: "Company deleted",
//         description: "The company has been successfully deleted.",
//       })
//       fetchCompanies() // Refresh the list
//     } catch (error) {
//       console.error("Error deleting company:", error)
//       toast({
//         title: "Error deleting company",
//         description: "There was a problem deleting the company.",
//         variant: "destructive",
//       })
//     }
//   }

//   // Toggle active status
//   const handleToggleStatus = async (company) => {
//     try {
//       await companyApi.updateCompany(company.id, {
//         ...company,
//         isActive: !company.isActive
//       })
//       toast({
//         title: "Status updated",
//         description: `Company status set to ${!company.isActive ? "active" : "inactive"}.`,
//       })
//       fetchCompanies() // Refresh the list
//     } catch (error) {
//       console.error("Error updating company status:", error)
//       toast({
//         title: "Error updating status",
//         description: "There was a problem updating the company status.",
//         variant: "destructive",
//       })
//     }
//   }

//   // Table columns
//   const columns = useMemo(
//     () => [
//       {
//         id: "select",
//         header: ({ table }) => (
//           <Checkbox
//             checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
//             onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
//             aria-label="Select all"
//           />
//         ),
//         cell: ({ row }) => (
//           <Checkbox
//             checked={row.getIsSelected()}
//             onCheckedChange={(value) => row.toggleSelected(!!value)}
//             aria-label="Select row"
//           />
//         ),
//         enableSorting: false,
//         enableHiding: false,
//       },
//       {
//         accessorKey: "companyName",
//         header: "Company",
//         cell: ({ row }) => {
//           const company = row.original
//           return (
//             <div className="flex items-center gap-3">
//               <Avatar className="h-10 w-10">
//                 <AvatarImage src={company.companyLogo} alt={company.companyName} />
//                 <AvatarFallback>{company.companyName?.charAt(0)}</AvatarFallback>
//               </Avatar>
//               <div>
//                 <div className="font-medium">{company.companyName}</div>
//                 <div className="text-xs text-muted-foreground">Code: {company.companyCode}</div>
//               </div>
//             </div>
//           )
//         },
//       },
//       {
//         accessorKey: "contactPerson",
//         header: "Contact",
//         cell: ({ row }) => {
//           const company = row.original
//           return (
//             <div className="space-y-1">
//               <div className="text-sm font-medium">{company.contactPerson}</div>
//               <div className="text-xs">{company.contactEmail}</div>
//               <div className="text-xs text-muted-foreground">{company.contactNumber}</div>
//             </div>
//           )
//         },
//       },
//       {
//         accessorKey: "address",
//         header: "Address",
//         cell: ({ row }) => {
//           const company = row.original
//           return (
//             <div className="text-sm">
//               {company.address || "Address not found"}
//             </div>
//           )
//         },
//       },
//       {
//         accessorKey: "isActive",
//         header: "Status",
//         cell: ({ row }) => {
//           const isActive = row.original.isActive
//           return <Badge variant={isActive ? "default" : "secondary"}>{isActive ? "Active" : "Inactive"}</Badge>
//         },
//       },
//       {
//         accessorKey: "createdOn",
//         header: "Created",
//         cell: ({ row }) => {
//           return <div className="text-sm">{formatDate(row.original.createdOn)}</div>
//         },
//       },
//       {
//         accessorKey: "modifiedOn",
//         header: "Last Modified",
//         cell: ({ row }) => {
//           return <div className="text-sm">{formatDate(row.original.modifiedOn)}</div>
//         },
//       },
//       {
//         id: "actions",
//         cell: ({ row }) => {
//           const company = row.original

//           return (
//             <DropdownMenu>
//               <DropdownMenuTrigger asChild>
//                 <Button variant="ghost" className="h-8 w-8 p-0">
//                   <span className="sr-only">Open menu</span>
//                   <MoreHorizontal className="h-4 w-4" />
//                 </Button>
//               </DropdownMenuTrigger>
//               <DropdownMenuContent align="end">
//                 <DropdownMenuLabel>Actions</DropdownMenuLabel>
//                 <DropdownMenuItem
//                   onClick={() => {
//                     setSelectedCompany(company)
//                     setIsDialogOpen(true)
//                   }}
//                 >
//                   <Edit className="mr-2 h-4 w-4" />
//                   Edit
//                 </DropdownMenuItem>
//                 <DropdownMenuItem
//                   onClick={() => {
//                     handleDeleteCompany(company.id)
//                   }}
//                   className="text-destructive focus:text-destructive"
//                 >
//                   <Trash2 className="mr-2 h-4 w-4" />
//                   Delete
//                 </DropdownMenuItem>
//                 <DropdownMenuSeparator />
//                 <DropdownMenuItem
//                   onClick={() => {
//                     handleToggleStatus(company)
//                   }}
//                 >
//                   <Checkbox checked={company.isActive} className="mr-2" onCheckedChange={() => {}} />
//                   {company.isActive ? "Set Inactive" : "Set Active"}
//                 </DropdownMenuItem>
//               </DropdownMenuContent>
//             </DropdownMenu>
//           )
//         },
//       },
//     ],
//     [],
//   )

//   // Filter functions
//   const handleGlobalFilterChange = (e) => {
//     setGlobalFilter(e.target.value)
//   }

//   const handleStatusFilterChange = (value) => {
//     setStatusFilter(value)
//     if (value === "active") {
//       table.getColumn("isActive")?.setFilterValue(true)
//     } else if (value === "inactive") {
//       table.getColumn("isActive")?.setFilterValue(false)
//     } else if (value === "all") {
//       table.getColumn("isActive")?.setFilterValue(undefined)
//     }
//   }

//   // Initialize table
//   const table = useReactTable({
//     data: companies,
//     columns,
//     onSortingChange: setSorting,
//     onColumnFiltersChange: setColumnFilters,
//     onColumnVisibilityChange: setColumnVisibility,
//     onRowSelectionChange: setRowSelection,
//     onPaginationChange: setPagination,
//     getCoreRowModel: getCoreRowModel(),
//     getSortedRowModel: getSortedRowModel(),
//     getFilteredRowModel: getFilteredRowModel(),
//     getPaginationRowModel: getPaginationRowModel(),
//     state: {
//       sorting,
//       columnFilters,
//       columnVisibility,
//       rowSelection,
//       pagination,
//       globalFilter,
//     },
//   })

//   // Handle dialog close and form submission
//   const handleDialogClose = () => {
//     setIsDialogOpen(false);
//     setSelectedCompany(undefined);
//     fetchCompanies(); // Refresh data when saved
//   }

//   // Handle add new company
//   const handleAddNew = () => {
//     setSelectedCompany(undefined)
//     setIsDialogOpen(true)
//   }

//   // Export selected companies to CSV
//   const handleExport = () => {
//     const selectedRows = table.getFilteredSelectedRowModel().rows
//     if (selectedRows.length === 0) {
//       toast({
//         title: "No companies selected",
//         description: "Please select at least one company to export.",
//         variant: "destructive",
//       })
//       return
//     }

//     const selectedData = selectedRows.map(row => row.original)
//     const csvContent = convertToCSV(selectedData)

//     // Create download link
//     const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
//     const url = URL.createObjectURL(blob)
//     const link = document.createElement('a')
//     link.setAttribute('href', url)
//     link.setAttribute('download', 'companies.csv')
//     document.body.appendChild(link)
//     link.click()
//     document.body.removeChild(link)

//     toast({
//       title: "Export complete",
//       description: `Successfully exported ${selectedRows.length} companies.`
//     })
//   }

//   // Convert data to CSV format
//   const convertToCSV = (data) => {
//     const headers = ["Company Name", "Company Code", "Contact Person", "Contact Number", "Contact Email", "Address", "Status", "Created On", "Modified On"]
//     const rows = data.map(item => [
//       item.companyName,
//       item.companyCode,
//       item.contactPerson,
//       item.contactNumber,
//       item.contactEmail,
//       item.address || "Address not found",
//       item.isActive ? "Active" : "Inactive",
//       formatDate(item.createdOn),
//       formatDate(item.modifiedOn)
//     ])

//     return [
//       headers.join(","),
//       ...rows.map(row => row.join(","))
//     ].join("\n")
//   }

//   // Render loading state
//   if (isLoading) {
//     return (
//       <Card>
//         <CardHeader>
//           <CardTitle>Company Management</CardTitle>
//           <CardDescription>Loading company records...</CardDescription>
//         </CardHeader>
//         <CardContent>
//           <div className="space-y-4">
//             <div className="flex items-center justify-between">
//               <Skeleton className="h-10 w-[250px]" />
//               <Skeleton className="h-10 w-[100px]" />
//             </div>
//             <div className="space-y-2">
//               {Array.from({ length: 5 }).map((_, index) => (
//                 <Skeleton key={index} className="h-16 w-full" />
//               ))}
//             </div>
//           </div>
//         </CardContent>
//       </Card>
//     )
//   }

//   return (
//     <Card className="w-full">
//       <CardHeader>
//         <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
//           <div>
//             <CardTitle>Company Management</CardTitle>
//             <CardDescription>Manage company records with advanced filtering and sorting.</CardDescription>
//           </div>
//           <Button onClick={handleAddNew}>
//             <Plus className="mr-2 h-4 w-4" /> Add Company
//           </Button>
//         </div>
//       </CardHeader>
//       <CardContent className="w-full">
//         <div className="space-y-4 w-full">
//           <div className="flex flex-col gap-4 sm:flex-row">
//             <div className="flex items-center w-full sm:w-auto">
//               <Input
//                 placeholder="Search companies..."
//                 value={globalFilter}
//                 onChange={handleGlobalFilterChange}
//                 className="max-w-sm"
//               />
//               <div className="ml-2">
//                 <TooltipProvider>
//                   <Tooltip>
//                     <TooltipTrigger asChild>
//                       <Button variant="outline" size="icon">
//                         <Search className="h-4 w-4" />
//                       </Button>
//                     </TooltipTrigger>
//                     <TooltipContent>
//                       <p>Search by name, contact, code, etc.</p>
//                     </TooltipContent>
//                   </Tooltip>
//                 </TooltipProvider>
//               </div>
//             </div>
//             <div className="flex flex-1 items-center gap-2">
//               <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
//                 <SelectTrigger className="w-[180px]">
//                   <SelectValue placeholder="Filter by status" />
//                 </SelectTrigger>
//                 <SelectContent>
//                   <SelectItem value="all">All Statuses</SelectItem>
//                   <SelectItem value="active">Active</SelectItem>
//                   <SelectItem value="inactive">Inactive</SelectItem>
//                 </SelectContent>
//               </Select>
//               <DropdownMenu>
//                 <DropdownMenuTrigger asChild>
//                   <Button variant="outline" className="ml-auto">
//                     <SlidersHorizontal className="mr-2 h-4 w-4" />
//                     View
//                   </Button>
//                 </DropdownMenuTrigger>
//                 <DropdownMenuContent align="end">
//                   <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
//                   <DropdownMenuSeparator />
//                   {table
//                     .getAllColumns()
//                     .filter((column) => column.getCanHide())
//                     .map((column) => {
//                       return (
//                         <DropdownMenuCheckboxItem
//                           key={column.id}
//                           className="capitalize"
//                           checked={column.getIsVisible()}
//                           onCheckedChange={(value) => column.toggleVisibility(!!value)}
//                         >
//                           {column.id}
//                         </DropdownMenuCheckboxItem>
//                       )
//                     })}
//                 </DropdownMenuContent>
//               </DropdownMenu>
//               <Button variant="outline" onClick={handleExport}>
//                 <Download className="mr-2 h-4 w-4" />
//                 Export
//               </Button>
//             </div>
//           </div>
//           <div className="rounded-md border">
//             <Table>
//               <TableHeader>
//                 {table.getHeaderGroups().map((headerGroup) => (
//                   <TableRow key={headerGroup.id}>
//                     {headerGroup.headers.map((header) => {
//                       return (
//                         <TableHead key={header.id}>
//                           {header.isPlaceholder
//                             ? null
//                             : flexRender(header.column.columnDef.header, header.getContext())}
//                         </TableHead>
//                       )
//                     })}
//                   </TableRow>
//                 ))}
//               </TableHeader>
//               <TableBody>
//                 {table.getRowModel().rows?.length ? (
//                   table.getRowModel().rows.map((row) => (
//                     <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
//                       {row.getVisibleCells().map((cell) => (
//                         <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
//                       ))}
//                     </TableRow>
//                   ))
//                 ) : (
//                   <TableRow>
//                     <TableCell colSpan={columns.length} className="h-24 text-center">
//                       No results found.
//                     </TableCell>
//                   </TableRow>
//                 )}
//               </TableBody>
//             </Table>
//           </div>
//           <div className="flex items-center justify-between">
//             <div className="flex-1 text-sm text-muted-foreground">
//               {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s)
//               selected.
//             </div>
//             <div className="flex items-center space-x-2">
//               <Button
//                 variant="outline"
//                 size="sm"
//                 onClick={() => table.setPageIndex(0)}
//                 disabled={!table.getCanPreviousPage()}
//               >
//                 <ChevronsLeft className="h-4 w-4" />
//               </Button>
//               <Button
//                 variant="outline"
//                 size="sm"
//                 onClick={() => table.previousPage()}
//                 disabled={!table.getCanPreviousPage()}
//               >
//                 <ChevronLeft className="h-4 w-4" />
//               </Button>
//               <span className="flex items-center gap-1">
//                 <div>Page</div>
//                 <strong>
//                   {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
//                 </strong>
//               </span>
//               <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
//                 <ChevronRight className="h-4 w-4" />
//               </Button>
//               <Button
//                 variant="outline"
//                 size="sm"
//                 onClick={() => table.setPageIndex(table.getPageCount() - 1)}
//                 disabled={!table.getCanNextPage()}
//               >
//                 <ChevronsRight className="h-4 w-4" />
//               </Button>
//               <Select
//                 value={`${table.getState().pagination.pageSize}`}
//                 onValueChange={(value) => {
//                   table.setPageSize(Number(value))
//                 }}
//               >
//                 <SelectTrigger className="h-8 w-[70px]">
//                   <SelectValue placeholder={table.getState().pagination.pageSize} />
//                 </SelectTrigger>
//                 <SelectContent side="top">
//                   {[10, 20, 30, 40, 50].map((pageSize) => (
//                     <SelectItem key={pageSize} value={`${pageSize}`}>
//                       {pageSize}
//                     </SelectItem>
//                   ))}
//                 </SelectContent>
//               </Select>
//             </div>
//           </div>
//         </div>
//       </CardContent>
//       <CompanyDialog
//         open={isDialogOpen}
//         onOpenChange={(open) => {
//           setIsDialogOpen(open);
//           if (!open) {
//             handleDialogClose();
//           }
//         }}
//         initialData={selectedCompany}
//         onSave={() => {
//           fetchCompanies();
//           setIsDialogOpen(false);
//           setSelectedCompany(undefined);
//         }}
//       />
//     </Card>
//   )
// }


// components/Company/CompanyList.js
import React, { useMemo } from 'react';
import {
  ArrowDown, ArrowUp, Edit2, Trash2,
  Mail, Phone, MapPin, Calendar, ChevronRight, ChevronLeft,
  Search, Filter, X
} from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';

export default function CompanyList({
  companies = [],
  totalCompanies = 0,
  onEdit,
  onDelete,
  onSelect,
  loading = false,
  page = 1,
  pageSize = 10,
  onPageChange,
  onPageSizeChange,
  sortConfig = { key: 'CompanyName', direction: 'asc' },
  onSort,
  filters = {},
  onFilter,
  onClearFilters
}) {
  // Generate pagination range
  const totalPages = Math.ceil(totalCompanies / pageSize);
  const pageRange = useMemo(() => {
    const delta = 2; // Number of pages to show before and after current page
    const range = [];
    const rangeWithDots = [];
    let l;

    for (let i = 1; i <= totalPages; i++) {
      if (i === 1 || i === totalPages || (i >= page - delta && i <= page + delta)) {
        range.push(i);
      }
    }

    for (let i of range) {
      if (l) {
        if (i - l === 2) {
          rangeWithDots.push(l + 1);
        } else if (i - l !== 1) {
          rangeWithDots.push('...');
        }
      }
      rangeWithDots.push(i);
      l = i;
    }

    return rangeWithDots;
  }, [page, totalPages, pageSize]);

  // Handle sort request
  const requestSort = (key) => {
    const direction = sortConfig.key === key && sortConfig.direction === 'asc' ? 'desc' : 'asc';
    onSort({ key, direction });
  };

  // Format dates for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  // Generate empty state content when no records
  const EmptyState = () => (
    <tr>
      <td colSpan="12" className="px-6 py-12 text-center">
        <div className="flex flex-col items-center justify-center">
          <MapPin className="w-12 h-12 text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-500">No Companies Found</h3>
          <p className="text-gray-400 mt-1">Try adjusting your search or filter criteria</p>
          {Object.keys(filters).length > 0 && (
            <button
              onClick={onClearFilters}
              className="mt-4 px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-md flex items-center"
            >
              <X className="w-4 h-4 mr-2" />
              Clear All Filters
            </button>
          )}
        </div>
      </td>
    </tr>
  );

  // Loading skeleton
  const LoadingSkeleton = () => (
    <>
      {Array(5).fill(0).map((_, index) => (
        <tr key={`loading-${index}`} className="animate-pulse">
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </td>
          <td className="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
          </td>
          <td className="hidden md:table-cell px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-1/3"></div>
          </td>
          <td className="hidden lg:table-cell px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </td>
          <td className="px-6 py-4 whitespace-nowrap">
            <div className="h-4 bg-gray-200 rounded w-16"></div>
          </td>
        </tr>
      ))}
    </>
  );

  // Column render helpers
  const renderSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) return null;
    return sortConfig.direction === 'asc' ?
      <ArrowUp className="w-4 h-4 inline ml-1" /> :
      <ArrowDown className="w-4 h-4 inline ml-1" />;
  };

  return (

    <div className="bg-white rounded-lg shadow overflow-hidden">
      {/* Search & Filter Bar */}
      <div className="p-4 border-b flex flex-col sm:flex-row gap-3 justify-between">
        <div className="relative flex-grow max-w-md">
          <input
            type="text"
            placeholder="Search companies..."
            value={filters.search || ''}
            onChange={(e) => onFilter({ ...filters, search: e.target.value })}
            className="pl-10 pr-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Search companies"
          />
          <Search className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>

        <div className="flex items-center gap-2">
          <select
            value={filters.IsActive === undefined ? '' : filters.IsActive}
            onChange={(e) => {
              const value = e.target.value === '' ? undefined : e.target.value === 'true';
              onFilter({ ...filters, IsActive: value });
            }}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Filter by status"
          >
            <option value="">All Status</option>
            <option value="true">Active</option>
            <option value="false">Inactive</option>
          </select>

          <button
            onClick={() => onFilter({})}
            className={`p-2 rounded-md ${Object.keys(filters).length > 0 ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'}`}
            aria-label="Filter records"
            disabled={Object.keys(filters).length === 0}
          >
            <Filter className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto" role="region" aria-label="Companies list" tabIndex="0">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('CompanyName')}
                aria-sort={sortConfig.key === 'CompanyName' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Company
                  {renderSortIcon('CompanyName')}
                </span>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('CompanyCode')}
                aria-sort={sortConfig.key === 'CompanyCode' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Code
                  {renderSortIcon('CompanyCode')}
                </span>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('ContactPerson')}
                aria-sort={sortConfig.key === 'ContactPerson' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Contact Person
                  {renderSortIcon('ContactPerson')}
                </span>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('ContactNumber')}
                aria-sort={sortConfig.key === 'ContactNumber' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Phone
                  {renderSortIcon('ContactNumber')}
                </span>
              </th>
              <th
                scope="col"
                className="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('ContactEmail')}
                aria-sort={sortConfig.key === 'ContactEmail' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Email
                  {renderSortIcon('ContactEmail')}
                </span>
              </th>
              <th
                scope="col"
                className="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('CreatedOn')}
                aria-sort={sortConfig.key === 'CreatedOn' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Created
                  {renderSortIcon('CreatedOn')}
                </span>
              </th>
              <th
                scope="col"
                className="hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                onClick={() => requestSort('IsActive')}
                aria-sort={sortConfig.key === 'IsActive' ? sortConfig.direction : 'none'}
              >
                <span className="flex items-center">
                  Status
                  {renderSortIcon('IsActive')}
                </span>
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <LoadingSkeleton />
            ) : companies.length === 0 ? (
              <EmptyState />
            ) : (
              companies.map((company) => (
                <tr
                  key={company.Id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onSelect(company.Id)}
                  tabIndex="0"
                  role="row"
                  aria-label={`Company: ${company.CompanyName}`}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {/* Always show the company initials in a colored circle */}
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3 text-blue-700 font-medium">
                        {company.CompanyName.substring(0, 2).toUpperCase()}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{company.CompanyName}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{company.CompanyCode}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{company.ContactPerson}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center">
                      <Phone className="w-4 h-4 text-gray-400 mr-1" />
                      {company.ContactNumber}
                    </div>
                  </td>
                  <td className="hidden sm:table-cell px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 flex items-center">
                      <Mail className="w-4 h-4 text-gray-400 mr-1" />
                      {company.ContactEmail}
                    </div>
                  </td>
                  <td className="hidden md:table-cell px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 flex items-center">
                      <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                      {formatDate(company.CreatedOn)}
                    </div>
                  </td>
                  <td className="hidden lg:table-cell px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        company.IsActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {company.IsActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex space-x-2 justify-end">
                      <PermissionButton
                        requiredModule="Companies"
                        requiredPermissions={["Edit"]}
                        companyId={company.Id}
                        onClick={(e) => {
                          e.stopPropagation();
                          onEdit(company);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                        aria-label={`Edit ${company.CompanyName}`}
                      >
                        <Edit2 className="w-5 h-5" />
                      </PermissionButton>
                      <PermissionButton
                        requiredModule="Companies"
                        requiredPermissions={["Delete"]}
                        companyId={company.Id}
                        onClick={(e) => {
                          e.stopPropagation();
                          onDelete(company.Id);
                        }}
                        className="text-red-600 hover:text-red-900"
                        aria-label={`Delete ${company.CompanyName}`}
                      >
                        <Trash2 className="w-5 h-5" />
                      </PermissionButton>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {!loading && companies.length > 0 && (
        <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => onPageChange(page - 1)}
              disabled={page === 1}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                page === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange(page + 1)}
              disabled={page === totalPages}
              className={`ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                page === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{Math.min((page - 1) * pageSize + 1, totalCompanies)}</span> to{' '}
                <span className="font-medium">{Math.min(page * pageSize, totalCompanies)}</span> of{' '}
                <span className="font-medium">{totalCompanies}</span> results
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div>
                <select
                  value={pageSize}
                  onChange={(e) => onPageSizeChange(Number(e.target.value))}
                  className="px-2 py-1 border border-gray-300 rounded-md text-sm"
                  aria-label="Rows per page"
                >
                  {[10, 20, 50, 100].map((size) => (
                    <option key={size} value={size}>
                      {size} per page
                    </option>
                  ))}
                </select>
              </div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => onPageChange(page - 1)}
                  disabled={page === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                    page === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                  }`}
                  aria-label="Previous page"
                >
                  <ChevronLeft className="h-5 w-5" />
                </button>

                {pageRange.map((pageNumber, i) => (
                  pageNumber === '...' ? (
                    <span
                      key={`ellipsis-${i}`}
                      className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                    >
                      ...
                    </span>
                    // This continues the CompanyList.js component from where we left off

                  ) : (
                    <button
                      key={pageNumber}
                      onClick={() => onPageChange(pageNumber)}
                      aria-current={page === pageNumber ? 'page' : undefined}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        page === pageNumber
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                      aria-label={`Page ${pageNumber}`}
                    >
                      {pageNumber}
                    </button>
                  )
                ))}

                <button
                  onClick={() => onPageChange(page + 1)}
                  disabled={page === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                    page === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                  }`}
                  aria-label="Next page"
                >
                  <ChevronRight className="h-5 w-5" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}