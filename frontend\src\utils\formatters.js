/**
 * Utility functions for formatting numbers and values
 */

/**
 * Format large numbers with appropriate suffixes (K, M, B)
 * @param {number} num - The number to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} Formatted number string
 */
export const formatCompactNumber = (num, decimals = 1) => {
  if (num === null || num === undefined || isNaN(num)) return '0';
  
  const absNum = Math.abs(num);
  
  if (absNum >= 1e9) {
    return (num / 1e9).toFixed(decimals) + 'B';
  } else if (absNum >= 1e6) {
    return (num / 1e6).toFixed(decimals) + 'M';
  } else if (absNum >= 1e3) {
    return (num / 1e3).toFixed(decimals) + 'K';
  } else {
    return num.toString();
  }
};

/**
 * Format currency with compact notation for large amounts
 * @param {number} amount - The amount to format
 * @param {string} currency - Currency symbol (default: '₹')
 * @param {boolean} compact - Whether to use compact notation (default: true for amounts > 100K)
 * @returns {string} Formatted currency string
 */
export const formatCurrency = (amount, currency = '₹', compact = null) => {
  if (amount === null || amount === undefined || isNaN(amount)) return `${currency}0`;
  
  const absAmount = Math.abs(amount);
  
  // Auto-determine compact mode if not specified
  if (compact === null) {
    compact = absAmount >= 100000; // Use compact for amounts >= 100K
  }
  
  if (compact) {
    return `${currency}${formatCompactNumber(amount)}`;
  } else {
    return `${currency}${amount.toLocaleString()}`;
  }
};

/**
 * Format duration in hours and minutes
 * @param {number} minutes - Duration in minutes
 * @returns {string} Formatted duration string
 */
export const formatDuration = (minutes) => {
  if (!minutes || isNaN(minutes)) return '0h 0m';
  
  const hours = Math.floor(minutes / 60);
  const mins = Math.round(minutes % 60);
  
  if (hours === 0) {
    return `${mins}m`;
  } else if (mins === 0) {
    return `${hours}h`;
  } else {
    return `${hours}h ${mins}m`;
  }
};

/**
 * Format percentage with proper sign and decimals
 * @param {number} percentage - The percentage value
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} Formatted percentage string
 */
export const formatPercentage = (percentage, decimals = 1) => {
  if (percentage === null || percentage === undefined || isNaN(percentage)) return '0%';
  
  const sign = percentage >= 0 ? '+' : '';
  return `${sign}${percentage.toFixed(decimals)}%`;
};

/**
 * Format numbers with appropriate locale formatting
 * @param {number} num - The number to format
 * @param {number} decimals - Number of decimal places (default: 0)
 * @returns {string} Formatted number string
 */
export const formatNumber = (num, decimals = 0) => {
  if (num === null || num === undefined || isNaN(num)) return '0';
  
  return num.toLocaleString('en-IN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

/**
 * Get appropriate text size class based on content length
 * @param {string} content - The content to measure
 * @param {object} breakpoints - Custom breakpoints for text sizes
 * @returns {string} Tailwind CSS class for text size
 */
export const getResponsiveTextSize = (content, breakpoints = {}) => {
  const defaultBreakpoints = {
    small: 10,    // Less than 10 characters
    medium: 20,   // Less than 20 characters
    large: 30,    // Less than 30 characters
    ...breakpoints
  };
  
  const length = content ? content.toString().length : 0;
  
  if (length <= defaultBreakpoints.small) {
    return 'text-lg sm:text-xl lg:text-2xl xl:text-3xl';
  } else if (length <= defaultBreakpoints.medium) {
    return 'text-base sm:text-lg lg:text-xl xl:text-2xl';
  } else if (length <= defaultBreakpoints.large) {
    return 'text-sm sm:text-base lg:text-lg xl:text-xl';
  } else {
    return 'text-xs sm:text-sm lg:text-base xl:text-lg';
  }
};