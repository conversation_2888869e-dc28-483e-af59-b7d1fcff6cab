// Script to update permissions in ParkwizOps database
require('dotenv').config({ path: './backend/.env' });
const fs = require('fs');
const sql = require('mssql');
const path = require('path');

async function updatePermissions() {
  console.log('Starting permission update process...');
  
  try {
    // Read the SQL script
    const sqlScript = fs.readFileSync(path.join(__dirname, 'update_permissions.sql'), 'utf8');
    
    // Connect to the database
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    
    console.log('Connected to database successfully.');
    
    // Execute the SQL script
    console.log('Executing SQL script to update permissions...');
    
    // Split the script into individual statements
    const statements = sqlScript.split(';').filter(stmt => stmt.trim() !== '');
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i];
      if (stmt.trim()) {
        try {
          await sql.query(stmt);
          console.log(`Executed statement ${i + 1}/${statements.length}`);
        } catch (err) {
          console.error(`Error executing statement ${i + 1}:`, err);
          console.error('Statement:', stmt);
          throw err;
        }
      }
    }
    
    console.log('Permissions updated successfully!');
    
    // Verify the update
    const companyAdminCount = await sql.query('SELECT COUNT(*) as count FROM RolePermissions WHERE RoleId = 7');
    const plazaManagerCount = await sql.query('SELECT COUNT(*) as count FROM RolePermissions WHERE RoleId = 8');
    
    console.log(`CompanyAdmin permissions count: ${companyAdminCount.recordset[0].count}`);
    console.log(`PlazaManager permissions count: ${plazaManagerCount.recordset[0].count}`);
    
    await sql.close();
    console.log('Database connection closed.');
    
  } catch (err) {
    console.error('Error updating permissions:', err);
    if (sql.connected) {
      await sql.close();
      console.log('Database connection closed due to error.');
    }
    process.exit(1);
  }
}

updatePermissions().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});