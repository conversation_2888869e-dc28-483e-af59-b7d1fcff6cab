import React from 'react';

export function Chart({ data, labels, height = "h-64" }) {
  const max = Math.max(...data);

  return (
    <div className={`${height} flex items-end gap-2`}>
      {data.map((value, index) => (
        <div key={index} className="flex-1 flex flex-col items-center">
          <div
            className="w-full bg-blue-500 rounded-t hover:bg-blue-600 transition-colors"
            style={{ height: `${(value / max) * 100}%` }}
          />
          <span className="text-xs text-gray-600 mt-2">{labels[index]}</span>
        </div>
      ))}
    </div>
  );
}
