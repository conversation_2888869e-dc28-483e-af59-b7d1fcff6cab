// <PERSON>ript to fix Security module permissions
require('dotenv').config({ path: './backend/.env' });
const fs = require('fs');
const sql = require('mssql');
const path = require('path');

async function fixSecurityModule() {
  console.log('Starting Security module fix...');
  
  try {
    // Read the SQL script
    const sqlScript = fs.readFileSync(path.join(__dirname, 'fix_security_module_simple.sql'), 'utf8');
    
    // Connect to the database
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    
    console.log('Connected to database successfully.');
    
    // Execute the SQL script
    console.log('Executing SQL script to fix Security module...');
    const result = await sql.query(sqlScript);
    
    console.log('Security module fix completed successfully!');
    
    // Check if Security module exists
    const moduleCheck = await sql.query('SELECT * FROM Modules WHERE Name = \'Security\'');
    if (moduleCheck.recordset.length > 0) {
      console.log('Security module exists in the database.');
      
      // Get the module ID
      const moduleId = moduleCheck.recordset[0].Id;
      
      // Check if Security submodule exists
      const submoduleCheck = await sql.query('SELECT * FROM SubModules WHERE ModuleId = @moduleId AND Name = \'Security\'', {
        moduleId
      });
      
      if (submoduleCheck.recordset.length > 0) {
        console.log('Security submodule exists in the database.');
        
        // Get the submodule ID
        const submoduleId = submoduleCheck.recordset[0].Id;
        
        // Check if CompanyAdmin has permission
        const permissionCheck = await sql.query(`
          SELECT * FROM RolePermissions rp
          JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
          JOIN SubModules sm ON smp.SubModuleId = sm.Id
          JOIN Permissions p ON smp.PermissionId = p.Id
          JOIN Roles r ON rp.RoleId = r.Id
          WHERE sm.Id = @submoduleId AND r.Name = 'CompanyAdmin'
        `, {
          submoduleId
        });
        
        if (permissionCheck.recordset.length > 0) {
          console.log('CompanyAdmin has permission for Security module.');
        } else {
          console.log('CompanyAdmin does NOT have permission for Security module.');
        }
      } else {
        console.log('Security submodule does NOT exist in the database.');
      }
    } else {
      console.log('Security module does NOT exist in the database.');
    }
    
    await sql.close();
    console.log('Database connection closed.');
    
  } catch (err) {
    console.error('Error fixing Security module:', err);
    if (sql.connected) {
      await sql.close();
      console.log('Database connection closed due to error.');
    }
    process.exit(1);
  }
}

fixSecurityModule().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});