<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Refresh Session</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            margin-top: 20px;
        }
        h1 {
            color: #333;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .steps {
            margin-top: 20px;
        }
        .step {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>Session Refresh Required</h1>
    
    <div class="container">
        <p>The permissions for the CompanyAdmin and PlazaManager roles have been updated in the database. To apply these changes, you need to refresh your session by logging out and logging back in.</p>
        
        <div class="steps">
            <h2>Steps to refresh your session:</h2>
            <div class="step">1. Click the "Logout" button in the application</div>
            <div class="step">2. Log back in with your username and password</div>
            <div class="step">3. Your permissions will be updated and you should now have access to all the modules</div>
        </div>
        
        <p>If you continue to experience issues after logging out and logging back in, please contact the system administrator.</p>
        
        <a href="javascript:void(0)" class="button" onclick="logoutUser()">Logout Now</a>
    </div>
    
    <script>
        function logoutUser() {
            // Clear local storage
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            
            // Redirect to login page
            window.location.href = '/login';
        }
    </script>
</body>
</html>