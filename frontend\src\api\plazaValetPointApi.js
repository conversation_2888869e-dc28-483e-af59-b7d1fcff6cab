import api from '../services/api';

export const plazaValetPointApi = {
  /**
   * Gets all plaza valet points with optional filtering.
   * GET /plazavaletpoints
   */
  getAllPlazaValetPoints: async (params = {}) => {
    try {
      console.log('Fetching all plaza valet points with params:', params);
      const response = await api.get('/plazavaletpoints', { params });
      return response.data;
    } catch (error) {
      console.error('Error in getAllPlazaValetPoints:', error);
      throw error;
    }
  },

  /**
   * Gets a plaza valet point by ID.
   * GET /plazavaletpoints/:id
   */
  getPlazaValetPointById: async (id) => {
    try {
      console.log('Fetching plaza valet point with ID:', id);
      const response = await api.get(`/plazavaletpoints/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in getPlazaValetPointById:', error);
      throw error;
    }
  },

  /**
   * Gets valet points by plaza ID.
   * GET /plazavaletpoints/plaza/:plazaId
   */
  getValetPointsByPlaza: async (plazaId, params = {}) => {
    try {
      console.log('Fetching valet points for plaza ID:', plazaId);
      const response = await api.get(`/plazavaletpoints/plaza/${plazaId}`, { params });
      return response.data;
    } catch (error) {
      console.error('Error in getValetPointsByPlaza:', error);
      throw error;
    }
  },

  /**
   * Creates a new plaza valet point.
   * POST /plazavaletpoints
   */
  createPlazaValetPoint: async (valetPointData) => {
    try {
      console.log('Creating plaza valet point:', valetPointData);
      const response = await api.post('/plazavaletpoints', valetPointData);
      return response.data;
    } catch (error) {
      console.error('Error in createPlazaValetPoint:', error);
      throw error;
    }
  },

  /**
   * Updates an existing plaza valet point.
   * PUT /plazavaletpoints/:id
   */
  updatePlazaValetPoint: async (id, valetPointData) => {
    try {
      console.log('Updating plaza valet point with ID:', id);
      const response = await api.put(`/plazavaletpoints/${id}`, valetPointData);
      return response.data;
    } catch (error) {
      console.error('Error in updatePlazaValetPoint:', error);
      throw error;
    }
  },

  /**
   * Deletes a plaza valet point by ID (soft delete).
   * DELETE /plazavaletpoints/:id
   */
  deletePlazaValetPoint: async (id) => {
    try {
      console.log('Deleting plaza valet point with ID:', id);
      const response = await api.delete(`/plazavaletpoints/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in deletePlazaValetPoint:', error);
      throw error;
    }
  },

  /**
   * Toggles the active status of a plaza valet point.
   * PATCH /plazavaletpoints/toggle-status/:id
   */
  toggleActiveStatus: async (id) => {
    try {
      console.log('Toggling active status for plaza valet point with ID:', id);
      const response = await api.patch(`/plazavaletpoints/toggle-status/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in toggleActiveStatus:', error);
      throw error;
    }
  }
};
