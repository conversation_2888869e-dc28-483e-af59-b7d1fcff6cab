const axios = require('axios');

/**
 * Test script for Valet System API endpoints
 * Tests the basic functionality of customer registration, OTP, and transactions
 */

const BASE_URL = 'http://localhost:5000/api/valet';

async function testValetSystem() {
  console.log('🚀 Testing Valet System APIs...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Valet System Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data.message);
    console.log('   Version:', healthResponse.data.version);
    console.log('');

    // Test 2: Generate OTP
    console.log('2. Testing OTP Generation...');
    const otpResponse = await axios.post(`${BASE_URL}/otp/generate`, {
      mobileNumber: '**********',
      otpType: 'REGISTRATION'
    });
    console.log('✅ OTP Generated:', otpResponse.data.message);
    if (process.env.NODE_ENV === 'development') {
      console.log('   OTP:', otpResponse.data.data.otp);
    }
    console.log('   Expiry:', otpResponse.data.data.expiryTime);
    console.log('');

    // Test 3: Verify OTP (using development OTP if available)
    if (process.env.NODE_ENV === 'development' && otpResponse.data.data.otp) {
      console.log('3. Testing OTP Verification...');
      const verifyResponse = await axios.post(`${BASE_URL}/otp/verify`, {
        mobileNumber: '**********',
        otp: otpResponse.data.data.otp
      });
      console.log('✅ OTP Verified:', verifyResponse.data.message);
      console.log('');
    }

    // Test 4: Register Customer
    console.log('4. Testing Customer Registration...');
    const customerResponse = await axios.post(`${BASE_URL}/customers/register`, {
      mobileNumber: '**********',
      plazaValetPointId: 1 // Assuming valet point ID 1 exists
    });
    console.log('✅ Customer Registered:', customerResponse.data.message);
    console.log('   Customer ID:', customerResponse.data.data.customerId);
    console.log('   Valet Point:', customerResponse.data.data.valetPoint.name);
    console.log('');

    const customerId = customerResponse.data.data.customerId;

    // Test 5: Add Customer Vehicle
    console.log('5. Testing Add Customer Vehicle...');
    const vehicleResponse = await axios.post(`${BASE_URL}/customers/${customerId}/vehicles`, {
      vehicleNumber: 'MH12AB1234'
    });
    console.log('✅ Vehicle Added:', vehicleResponse.data.message);
    console.log('   Vehicle ID:', vehicleResponse.data.vehicleId);
    console.log('');

    // Test 6: Get Customer by Mobile
    console.log('6. Testing Get Customer by Mobile...');
    const getCustomerResponse = await axios.get(`${BASE_URL}/customers/mobile/**********`);
    console.log('✅ Customer Retrieved:', getCustomerResponse.data.message);
    console.log('   Customer Name:', getCustomerResponse.data.customer.Name || 'Not set');
    console.log('   Vehicle Count:', getCustomerResponse.data.customer.VehicleCount);
    console.log('');

    // Test 7: Create Valet Transaction
    console.log('7. Testing Create Valet Transaction...');
    const transactionResponse = await axios.post(`${BASE_URL}/transactions/create`, {
      customerId: customerId,
      customerVehicleNumber: 'MH12AB1234',
      customerMobileNumber: '**********',
      customerName: 'Test Customer',
      plazaValetPointId: 1,
      isAnyValuableItem: false,
      valetFee: 50,
      parkingFee: 20,
      paymentType: 1, // Cash
      payAt: 1 // Entry
    });
    console.log('✅ Transaction Created:', transactionResponse.data.message);
    console.log('   Transaction ID:', transactionResponse.data.data.transactionId);
    console.log('   PNR:', transactionResponse.data.data.pnrNumber.substring(0, 8) + '...');
    console.log('   Parking Pin:', transactionResponse.data.data.parkingPin);
    console.log('   Total Fee:', transactionResponse.data.data.totalFee);
    console.log('');

    const transactionId = transactionResponse.data.data.transactionId;
    const parkingPin = transactionResponse.data.data.parkingPin;

    // Test 8: Get Transaction by Pin
    console.log('8. Testing Get Transaction by Pin...');
    const getTransactionResponse = await axios.get(`${BASE_URL}/transactions/search/${parkingPin}?type=pin`);
    console.log('✅ Transaction Retrieved:', getTransactionResponse.data.message);
    console.log('   Vehicle Number:', getTransactionResponse.data.transaction.CustomerVehicleNumber);
    console.log('   Current Status:', getTransactionResponse.data.transaction.CurrentStatus || 'REGISTERED');
    console.log('   Entry Time:', new Date(getTransactionResponse.data.transaction.EntryDateTime).toLocaleString());
    console.log('');

    // Test 9: Update Transaction Status
    console.log('9. Testing Update Transaction Status...');
    const statusResponse = await axios.put(`${BASE_URL}/transactions/${transactionId}/status`, {
      status: 'PAYMENT_COMPLETED',
      remarks: 'Payment completed via cash'
    });
    console.log('✅ Status Updated:', statusResponse.data.message);
    console.log('   New Status:', statusResponse.data.data.status);
    console.log('');

    console.log('🎉 All Valet System tests completed successfully!');
    console.log('');
    console.log('📋 Test Summary:');
    console.log('   ✅ Health Check');
    console.log('   ✅ OTP Generation');
    console.log('   ✅ OTP Verification (dev mode)');
    console.log('   ✅ Customer Registration');
    console.log('   ✅ Vehicle Addition');
    console.log('   ✅ Customer Retrieval');
    console.log('   ✅ Transaction Creation');
    console.log('   ✅ Transaction Retrieval');
    console.log('   ✅ Status Update');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
    if (error.response?.data?.error) {
      console.error('   Error details:', error.response.data.error);
    }
    console.error('   Status:', error.response?.status);
    console.error('   URL:', error.config?.url);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  testValetSystem();
}

module.exports = { testValetSystem };
