require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

/**
 * <PERSON><PERSON><PERSON> to check the time ranges used by the dashboard data fetching
 * This script simulates the calculateDateRange function from DashboardController
 * and shows what actual date ranges are being used for different selections
 */

/**
 * Helper function to calculate date range based on selection
 * (Copied from DashboardController.js)
 */
function calculateDateRange(dateRange) {
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date
    startDate = new Date(dateRange + 'T00:00:00.000Z');
    endDate = new Date(dateRange + 'T23:59:59.999Z');
    
    console.log(`Using specific date: ${dateRange}`);
  } else {
    // It's a predefined range
    switch(dateRange) {
      case 'today':
        // For today, use current date
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        // For yesterday, use current date - 1 day
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setDate(endDate.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        // For week, use the last 7 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 6);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        // For month, use the last 30 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 29);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'year':
        // For year, use the last 365 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 364);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        // Default to today
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
    }
  }
  
  return { startDate, endDate };
}

async function checkDashboardTimeRanges() {
  try {
    console.log('=== DASHBOARD TIME RANGES ANALYSIS ===');
    console.log(`Current system time: ${new Date().toISOString()}`);
    console.log(`Current local time: ${new Date().toString()}`);
    console.log();

    // Test all available date range options
    const dateRangeOptions = ['today', 'yesterday', 'week', 'month', 'year', '2025-06-21'];
    
    console.log('DATE RANGE CALCULATIONS:');
    console.log('========================');
    
    for (const option of dateRangeOptions) {
      const { startDate, endDate } = calculateDateRange(option);
      const durationDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      
      console.log(`\n${option.toUpperCase()}:`);
      console.log(`  Start: ${startDate.toISOString()} (${startDate.toString()})`);
      console.log(`  End:   ${endDate.toISOString()} (${endDate.toString()})`);
      console.log(`  Duration: ${durationDays} day(s)`);
    }

    // Connect to database to check actual data availability
    console.log('\n\nCONNECTING TO DATABASE...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected successfully!');

    // Check data availability for each date range
    console.log('\n\nDATA AVAILABILITY CHECK:');
    console.log('========================');
    
    for (const option of dateRangeOptions) {
      const { startDate, endDate } = calculateDateRange(option);
      
      const request = new sql.Request();
      request.input('startDate', sql.DateTime, startDate);
      request.input('endDate', sql.DateTime, endDate);
      
      // Check both tables
      const oldTableResult = await request.query(`
        SELECT COUNT(*) as Count
        FROM tblParkwiz_Parking_Data_OLD 
        WHERE ExitDateTime BETWEEN @startDate AND @endDate
      `);
      
      const newTableResult = await request.query(`
        SELECT COUNT(*) as Count
        FROM tblParkwiz_Parking_Data 
        WHERE ExitDateTime BETWEEN @startDate AND @endDate
      `);
      
      console.log(`\n${option.toUpperCase()}:`);
      console.log(`  OLD Table: ${oldTableResult.recordset[0].Count} transactions`);
      console.log(`  NEW Table: ${newTableResult.recordset[0].Count} transactions`);
      console.log(`  Total: ${oldTableResult.recordset[0].Count + newTableResult.recordset[0].Count} transactions`);
    }

    // Check the overall data range in both tables
    console.log('\n\nOVERALL DATA RANGE:');
    console.log('==================');
    
    const dataRangeResult = await sql.query(`
      SELECT 
        'OLD_TABLE' as TableName,
        MIN(ExitDateTime) as EarliestDate,
        MAX(ExitDateTime) as LatestDate,
        COUNT(*) as TotalRecords
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime IS NOT NULL
      
      UNION ALL
      
      SELECT 
        'NEW_TABLE' as TableName,
        MIN(ExitDateTime) as EarliestDate,
        MAX(ExitDateTime) as LatestDate,
        COUNT(*) as TotalRecords
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime IS NOT NULL
    `);
    
    dataRangeResult.recordset.forEach(row => {
      console.log(`\n${row.TableName}:`);
      console.log(`  Earliest: ${row.EarliestDate ? row.EarliestDate.toISOString() : 'No data'}`);
      console.log(`  Latest:   ${row.LatestDate ? row.LatestDate.toISOString() : 'No data'}`);
      console.log(`  Records:  ${row.TotalRecords}`);
    });

    // Check recent data (last 7 days)
    console.log('\n\nRECENT DATA ANALYSIS (Last 7 days):');
    console.log('===================================');
    
    const recentDataResult = await sql.query(`
      SELECT 
        CONVERT(date, ExitDateTime) as Date,
        COUNT(*) as TransactionCount,
        MIN(ExitDateTime) as FirstTransaction,
        MAX(ExitDateTime) as LastTransaction
      FROM (
        SELECT ExitDateTime FROM tblParkwiz_Parking_Data_OLD
        WHERE ExitDateTime >= DATEADD(day, -7, GETDATE())
        UNION ALL
        SELECT ExitDateTime FROM tblParkwiz_Parking_Data
        WHERE ExitDateTime >= DATEADD(day, -7, GETDATE())
      ) combined
      GROUP BY CONVERT(date, ExitDateTime)
      ORDER BY Date DESC
    `);
    
    if (recentDataResult.recordset.length === 0) {
      console.log('No recent data found in the last 7 days.');
    } else {
      recentDataResult.recordset.forEach(day => {
        console.log(`${day.Date.toISOString().split('T')[0]}: ${day.TransactionCount} transactions`);
        console.log(`  First: ${day.FirstTransaction.toISOString()}`);
        console.log(`  Last:  ${day.LastTransaction.toISOString()}`);
      });
    }

    await sql.close();
    console.log('\nDatabase connection closed.');
    
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

checkDashboardTimeRanges();