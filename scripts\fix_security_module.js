// <PERSON>ript to fix Security module permissions
require('dotenv').config({ path: './backend/.env' });
const fs = require('fs');
const sql = require('mssql');
const path = require('path');

async function fixSecurityModule() {
  console.log('Starting Security module fix...');
  
  try {
    // Read the SQL script
    const sqlScript = fs.readFileSync(path.join(__dirname, 'fix_security_module.sql'), 'utf8');
    
    // Connect to the database
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    
    console.log('Connected to database successfully.');
    
    // Execute the SQL script
    console.log('Executing SQL script to fix Security module...');
    const result = await sql.query(sqlScript);
    
    console.log('Security module fix completed successfully!');
    console.log('Result:', result);
    
    await sql.close();
    console.log('Database connection closed.');
    
  } catch (err) {
    console.error('Error fixing Security module:', err);
    if (sql.connected) {
      await sql.close();
      console.log('Database connection closed due to error.');
    }
    process.exit(1);
  }
}

fixSecurityModule().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});