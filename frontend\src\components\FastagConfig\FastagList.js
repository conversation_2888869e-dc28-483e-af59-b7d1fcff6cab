import React, { useState, useRef } from 'react';
import {
  Search,
  Edit,
  Trash2,
  Download,
  Columns,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';

export default function FastagList({ configurations, onEdit, onDelete, onSelect }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'LaneNumber', direction: 'ascending' });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [visibleColumns, setVisibleColumns] = useState({
    LaneNumber: true,
    PlazaName: true,
    CompanyName: true,
    FastagOrgID: true,
    FastagAgencyCode: true,
    FastagAPIAddress: true,
    LaneStatus: true,
    Actions: true
  });
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const tableRef = useRef(null);

  // Filter configurations based on search term
  const filteredConfigurations = configurations.filter(config =>
    String(config.LaneNumber).toLowerCase().includes(searchTerm.toLowerCase()) ||
    (config.PlazaName && config.PlazaName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (config.FastagOrgID && config.FastagOrgID.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (config.FastagAgencyCode && config.FastagAgencyCode.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Sort configurations based on sortConfig
  const sortedConfigurations = [...filteredConfigurations].sort((a, b) => {
    if (!a[sortConfig.key] && !b[sortConfig.key]) return 0;
    if (!a[sortConfig.key]) return 1;
    if (!b[sortConfig.key]) return -1;

    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0;
  });

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedConfigurations.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(sortedConfigurations.length / itemsPerPage);

  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const requestSort = (key) => {
    let direction = 'ascending';
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const getSortIndicator = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? '↑' : '↓';
    }
    return '';
  };

  const toggleColumnVisibility = (column) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column]
    }));
  };

  const exportToCSV = () => {
    // Get visible columns
    const headers = Object.keys(visibleColumns)
      .filter(col => visibleColumns[col] && col !== 'Actions')
      .map(col => col);

    // Create CSV header row
    let csvContent = headers.join(',') + '\n';

    // Add data rows
    sortedConfigurations.forEach(config => {
      const row = headers.map(header => {
        // Handle special cases
        if (header === 'LaneStatus') {
          return config[header] === 'Active' ? 'Active' : 'Inactive';
        }
        return config[header] || '';
      }).join(',');
      csvContent += row + '\n';
    });

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `fastag_configs_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="overflow-hidden">
      <div className="p-4 border-b">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="relative w-full md:w-1/2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search configurations..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <div className="flex items-center space-x-2 w-full md:w-auto">
            {/* Items per page selector */}
            <select
              className="border border-gray-300 rounded-md px-2 py-1 text-sm"
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
            </select>

            {/* Column visibility toggle */}
            <div className="relative">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="flex items-center space-x-1 bg-white border border-gray-300 rounded-md px-3 py-1 text-sm hover:bg-gray-50"
                title="Toggle columns"
              >
                <Columns size={16} />
                <span className="hidden md:inline">Columns</span>
              </button>

              {/* Column selector modal */}
              {showColumnSelector && (
                <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 max-w-md w-full max-h-[80vh] overflow-auto">
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Toggle Columns</h3>
                      <button
                        onClick={() => setShowColumnSelector(false)}
                        className="text-gray-400 hover:text-gray-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                    <div className="space-y-3">
                      {Object.keys(visibleColumns).map(column => (
                        <div key={column} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`column-${column}`}
                            checked={visibleColumns[column]}
                            onChange={() => toggleColumnVisibility(column)}
                            className="h-5 w-5 text-blue-600 border-gray-300 rounded"
                          />
                          <label htmlFor={`column-${column}`} className="ml-3 text-sm font-medium text-gray-700">
                            {column}
                          </label>
                        </div>
                      ))}
                    </div>
                    <div className="mt-6 flex justify-end">
                      <button
                        onClick={() => setShowColumnSelector(false)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Done
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Export button */}
            <button
              onClick={exportToCSV}
              className="flex items-center space-x-1 bg-white border border-gray-300 rounded-md px-3 py-1 text-sm hover:bg-gray-50"
              title="Export to CSV"
            >
              <Download size={16} />
              <span className="hidden md:inline">Export</span>
            </button>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table ref={tableRef} className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {visibleColumns.LaneNumber && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('LaneNumber')}
                >
                  Lane Number {getSortIndicator('LaneNumber')}
                </th>
              )}
              {visibleColumns.PlazaName && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('PlazaName')}
                >
                  Plaza Name {getSortIndicator('PlazaName')}
                </th>
              )}
              {visibleColumns.CompanyName && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('CompanyName')}
                >
                  Company Name {getSortIndicator('CompanyName')}
                </th>
              )}
              {visibleColumns.FastagOrgID && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('FastagOrgID')}
                >
                  Fastag Org ID {getSortIndicator('FastagOrgID')}
                </th>
              )}
              {visibleColumns.FastagAgencyCode && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('FastagAgencyCode')}
                >
                  Agency Code {getSortIndicator('FastagAgencyCode')}
                </th>
              )}
              {visibleColumns.FastagAPIAddress && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('FastagAPIAddress')}
                >
                  API Address {getSortIndicator('FastagAPIAddress')}
                </th>
              )}
              {visibleColumns.LaneStatus && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                  onClick={() => requestSort('LaneStatus')}
                >
                  Status {getSortIndicator('LaneStatus')}
                </th>
              )}
              {visibleColumns.Actions && (
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currentItems.length > 0 ? (
              currentItems.map((config) => (
                <tr
                  key={config.RecordID}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onSelect && onSelect(config)}
                >
                  {visibleColumns.LaneNumber && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{config.LaneNumber}</td>
                  )}
                  {visibleColumns.PlazaName && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{config.PlazaName}</td>
                  )}
                  {visibleColumns.CompanyName && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {config.CompanyName || `Company ID: ${config.CompanyID}`}
                    </td>
                  )}
                  {visibleColumns.FastagOrgID && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{config.FastagOrgID}</td>
                  )}
                  {visibleColumns.FastagAgencyCode && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{config.FastagAgencyCode}</td>
                  )}
                  {visibleColumns.FastagAPIAddress && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{config.FastagAPIAddress}</td>
                  )}
                  {visibleColumns.LaneStatus && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${config.LaneStatus === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                      >
                        {config.LaneStatus || 'Unknown'}
                      </span>
                    </td>
                  )}
                  {visibleColumns.Actions && (
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2 justify-end">
                        <PermissionButton
                          requiredModule="Fastag"
                          requiredPermissions={["Edit"]}
                          companyId={config.CompanyID}
                          plazaId={config.PlazaID}
                          onClick={(e) => {
                            e.stopPropagation();
                            onEdit(config);
                          }}
                          className="text-indigo-600 hover:text-indigo-900"
                          title="Edit"
                        >
                          <Edit size={18} />
                        </PermissionButton>
                        <PermissionButton
                          requiredModule="Fastag"
                          requiredPermissions={["Delete"]}
                          companyId={config.CompanyID}
                          plazaId={config.PlazaID}
                          onClick={(e) => {
                            e.stopPropagation();
                            onDelete(config.RecordID);
                          }}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <Trash2 size={18} />
                        </PermissionButton>
                      </div>
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={Object.values(visibleColumns).filter(Boolean).length}
                  className="px-6 py-4 text-center text-sm text-gray-500"
                >
                  No configurations found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                <span className="font-medium">
                  {indexOfLastItem > sortedConfigurations.length ? sortedConfigurations.length : indexOfLastItem}
                </span>{' '}
                of <span className="font-medium">{sortedConfigurations.length}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => paginate(1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`}
                >
                  <span className="sr-only">First</span>
                  <ChevronsLeft className="h-5 w-5" aria-hidden="true" />
                </button>
                <button
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`}
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                </button>
                {/* Page numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Calculate page numbers to show (centered around current page)
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  return (
                    <button
                      key={pageNum}
                      onClick={() => paginate(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === pageNum ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'text-gray-500 hover:bg-gray-50'}`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
                <button
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`}
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-5 w-5" aria-hidden="true" />
                </button>
                <button
                  onClick={() => paginate(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'}`}
                >
                  <span className="sr-only">Last</span>
                  <ChevronsRight className="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}