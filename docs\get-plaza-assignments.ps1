# PowerShell script to get all plazas assigned to users
# This script queries the database to show user-plaza assignments

param(
    [string]$ConnectionString = "Server=parkwizvms.database.windows.net;Database=ParkwizOps;User Id=hparkwiz;Password=************;Encrypt=true;TrustServerCertificate=true;"
)

# Import SQL Server module if available
try {
    Import-Module SqlServer -ErrorAction Stop
    Write-Host "Using SqlServer module" -ForegroundColor Green
} catch {
    try {
        Import-Module SQLPS -ErrorAction Stop
        Write-Host "Using SQLPS module" -ForegroundColor Yellow
    } catch {
        Write-Host "SQL Server PowerShell modules not found. Using Invoke-Sqlcmd directly." -ForegroundColor Red
    }
}

Write-Host "=== PLAZA ASSIGNMENTS REPORT ===" -ForegroundColor Cyan
Write-Host "Connecting to database: ParkwizOps" -ForegroundColor Yellow

try {
    # First, let's check the UserPlaza table structure
    $userPlazaStructureQuery = @"
    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'UserPlaza'
    ORDER BY ORDINAL_POSITION
"@

    Write-Host "`nChecking UserPlaza table structure..." -ForegroundColor Yellow
    $userPlazaStructure = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $userPlazaStructureQuery
    
    if ($userPlazaStructure) {
        Write-Host "UserPlaza table structure:" -ForegroundColor Green
        $userPlazaStructure | Format-Table -AutoSize
    } else {
        Write-Host "UserPlaza table not found or empty" -ForegroundColor Red
    }

    # Main query to get all plaza assignments
    $mainQuery = @"
    SELECT 
        u.Id as UserId,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName,
        p.Id as PlazaId,
        p.PlazaName,
        p.PlazaCode,
        p.ContactPerson as PlazaContactPerson,
        p.ContactNumber as PlazaContactNumber,
        c.CompanyName,
        up.IsActive as AssignmentActive,
        up.CreatedOn as AssignedOn
    FROM Users u
    INNER JOIN UserPlaza up ON u.Id = up.UserId
    INNER JOIN Plaza p ON up.PlazaId = p.Id
    INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE u.IsActive = 1 AND p.IsActive = 1
    ORDER BY u.Username, p.PlazaName
"@

    Write-Host "`nExecuting main query for plaza assignments..." -ForegroundColor Yellow
    $results = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $mainQuery

    if ($results) {
        Write-Host "`nPLAZA ASSIGNMENTS FOUND:" -ForegroundColor Green
        Write-Host "Total assignments: $($results.Count)" -ForegroundColor Cyan
        
        # Group by user for better readability
        $groupedResults = $results | Group-Object Username
        
        foreach ($userGroup in $groupedResults) {
            $user = $userGroup.Group[0]
            Write-Host "`n--- USER: $($user.Username) ($($user.FirstName) $($user.LastName)) ---" -ForegroundColor Magenta
            Write-Host "Role: $($user.RoleName)" -ForegroundColor White
            Write-Host "Email: $($user.Email)" -ForegroundColor White
            Write-Host "Assigned Plazas:" -ForegroundColor Yellow
            
            foreach ($assignment in $userGroup.Group) {
                Write-Host "  • $($assignment.PlazaName) (Code: $($assignment.PlazaCode))" -ForegroundColor White
                Write-Host "    Company: $($assignment.CompanyName)" -ForegroundColor Gray
                Write-Host "    Contact: $($assignment.PlazaContactPerson) - $($assignment.PlazaContactNumber)" -ForegroundColor Gray
                Write-Host "    Assigned On: $($assignment.AssignedOn)" -ForegroundColor Gray
                Write-Host ""
            }
        }
        
        # Summary by role
        Write-Host "`n=== SUMMARY BY ROLE ===" -ForegroundColor Cyan
        $rolesSummary = $results | Group-Object RoleName | Sort-Object Name
        foreach ($roleGroup in $rolesSummary) {
            $uniqueUsers = ($roleGroup.Group | Select-Object -Unique UserId).Count
            $totalAssignments = $roleGroup.Count
            Write-Host "$($roleGroup.Name): $uniqueUsers users with $totalAssignments total plaza assignments" -ForegroundColor White
        }
        
        # Export to CSV
        $csvPath = "d:/PWVMS/plaza-assignments-$(Get-Date -Format 'yyyyMMdd-HHmmss').csv"
        $results | Export-Csv -Path $csvPath -NoTypeInformation
        Write-Host "`nResults exported to: $csvPath" -ForegroundColor Green
        
    } else {
        Write-Host "No plaza assignments found." -ForegroundColor Red
        
        # Let's check if there are any users and plazas at all
        Write-Host "`nChecking for users..." -ForegroundColor Yellow
        $userCount = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query "SELECT COUNT(*) as UserCount FROM Users WHERE IsActive = 1"
        Write-Host "Active users: $($userCount.UserCount)" -ForegroundColor White
        
        Write-Host "Checking for plazas..." -ForegroundColor Yellow
        $plazaCount = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query "SELECT COUNT(*) as PlazaCount FROM Plaza WHERE IsActive = 1"
        Write-Host "Active plazas: $($plazaCount.PlazaCount)" -ForegroundColor White
        
        Write-Host "Checking UserPlaza table..." -ForegroundColor Yellow
        $userPlazaCount = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query "SELECT COUNT(*) as AssignmentCount FROM UserPlaza"
        Write-Host "Total UserPlaza records: $($userPlazaCount.AssignmentCount)" -ForegroundColor White
    }

    # Additional query to show users without plaza assignments
    $usersWithoutPlazasQuery = @"
    SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName
    FROM Users u
    LEFT JOIN UserPlaza up ON u.Id = up.UserId
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE u.IsActive = 1 AND up.UserId IS NULL
    ORDER BY u.Username
"@

    Write-Host "`n=== USERS WITHOUT PLAZA ASSIGNMENTS ===" -ForegroundColor Cyan
    $usersWithoutPlazas = Invoke-Sqlcmd -ConnectionString $ConnectionString -Query $usersWithoutPlazasQuery
    
    if ($usersWithoutPlazas) {
        Write-Host "Found $($usersWithoutPlazas.Count) users without plaza assignments:" -ForegroundColor Yellow
        $usersWithoutPlazas | Format-Table Username, FirstName, LastName, RoleName, Email -AutoSize
    } else {
        Write-Host "All active users have plaza assignments." -ForegroundColor Green
    }

} catch {
    Write-Host "Error executing query: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.Exception.StackTrace)" -ForegroundColor Red
}

Write-Host "`n=== REPORT COMPLETE ===" -ForegroundColor Cyan