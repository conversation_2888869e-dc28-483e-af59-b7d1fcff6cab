const sql = require('mssql');
const fs = require('fs');
require('dotenv').config({path: './backend/.env'});

async function executeDataMigration() {
  try {
    console.log('🚀 Executing Valet Data Migration from pwvms to ParkwizOps...');
    console.log('================================================================');
    
    // Connect to ParkwizOps database (target)
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: 'ParkwizOps',
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    
    console.log('🔗 Connected to ParkwizOps database');
    console.log('');

    // Read and execute data migration script
    console.log('📄 Executing valet_data_migration.sql...');
    
    const script = fs.readFileSync('./valet_data_migration.sql', 'utf8');
    
    // Split by GO statements and execute each batch
    const batches = script.split(/^\s*GO\s*$/gim).filter(batch => batch.trim());
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i].trim();
      if (batch) {
        try {
          console.log(`   🔄 Executing batch ${i + 1}/${batches.length}...`);
          const result = await sql.query(batch);
          
          // If the batch returns data, show row count
          if (result.recordset && result.recordset.length > 0) {
            console.log(`   ✅ Batch ${i + 1} completed - ${result.recordset.length} records processed`);
          } else if (result.rowsAffected && result.rowsAffected[0] > 0) {
            console.log(`   ✅ Batch ${i + 1} completed - ${result.rowsAffected[0]} rows affected`);
          } else {
            console.log(`   ✅ Batch ${i + 1} completed`);
          }
          
        } catch (batchError) {
          console.error(`   ❌ Batch ${i + 1} failed:`, batchError.message);
          
          // Continue with next batch for non-critical errors
          if (batchError.message.includes('already exists') || 
              batchError.message.includes('Cannot insert duplicate key') ||
              batchError.message.includes('Invalid object name')) {
            console.log(`   ⚠️ Skipping batch ${i + 1} - non-critical error`);
          } else {
            throw batchError;
          }
        }
      }
    }
    
    console.log('✅ Data migration completed successfully');
    console.log('');
    
    // Verify migrated data
    console.log('🔍 Verifying migrated data...');
    
    const verificationQueries = [
      { name: 'PlazaValetPoint', query: 'SELECT COUNT(*) as count FROM PlazaValetPoint' },
      { name: 'Customer', query: 'SELECT COUNT(*) as count FROM Customer' },
      { name: 'CustomerVehicle', query: 'SELECT COUNT(*) as count FROM CustomerVehicle' },
      { name: 'ParkingTransactions (Valet)', query: 'SELECT COUNT(*) as count FROM ParkingTransactions WHERE PlazaValetPointId IS NOT NULL' },
      { name: 'ValetDrivers', query: 'SELECT COUNT(*) as count FROM ValetDrivers' }
    ];
    
    for (const verification of verificationQueries) {
      try {
        const result = await sql.query(verification.query);
        const count = result.recordset[0].count;
        console.log(`   📊 ${verification.name}: ${count} records`);
      } catch (error) {
        console.log(`   ⚠️ ${verification.name}: Could not verify (${error.message})`);
      }
    }

    await sql.close();
    console.log('');
    console.log('🎉 Valet data migration completed successfully!');
    console.log('🔌 Database connection closed');
    
  } catch (error) {
    console.error('💥 Data migration failed:', error.message);
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  executeDataMigration();
}

module.exports = { executeDataMigration };
