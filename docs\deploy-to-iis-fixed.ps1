# IIS Deployment Script for PWVMS (Fixed Version)
# Run this script as administrator

# Configuration
$siteName = "PWVMS"
$appPoolName = "PWVMS_AppPool"
$sitePath = "C:\inetpub\wwwroot\PWVMS"
$port = 80
$sourceDir = $PSScriptRoot  # Current directory where the script is located

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Please run this script as Administrator!"
    exit
}

# Check if IIS is installed
if ((Get-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole).State -ne "Enabled") {
    Write-Host "Installing IIS..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-ManagementConsole, IIS-HttpErrors, IIS-HttpRedirect, IIS-StaticContent, IIS-DefaultDocument, IIS-ApplicationDevelopment, IIS-NetFxExtensibility45, IIS-ASPNET45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-WebSockets, IIS-ApplicationInit, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic -All
}

# Check if URL Rewrite module is installed
$rewriteModule = Get-WebGlobalModule | Where-Object { $_.Name -eq "RewriteModule" }
if ($null -eq $rewriteModule) {
    Write-Host "URL Rewrite Module is not installed. Please install it from: https://www.iis.net/downloads/microsoft/url-rewrite" -ForegroundColor Red
    Write-Host "After installing, restart this script." -ForegroundColor Red
    exit
}

# Check if iisnode is installed
$iisnodePath = "C:\Program Files\iisnode\iisnode.dll"
if (-not (Test-Path $iisnodePath)) {
    Write-Host "iisnode is not installed. Please install it from: https://github.com/Azure/iisnode/releases" -ForegroundColor Red
    Write-Host "After installing, restart this script." -ForegroundColor Red
    exit
}

# Create site directory if it doesn't exist
if (-not (Test-Path $sitePath)) {
    Write-Host "Creating site directory at $sitePath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $sitePath -Force
}

# Create deployment directories
$deploymentDirs = @(
    "$sitePath\backend",
    "$sitePath\backend\src",
    "$sitePath\backend\Uploads",
    "$sitePath\backend\Uploads\Companies",
    "$sitePath\backend\Uploads\Plazas",
    "$sitePath\frontend\build"
)

foreach ($dir in $deploymentDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating directory: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# Build the React frontend
Write-Host "Building React frontend..." -ForegroundColor Yellow

# Check if frontend directory exists
if (Test-Path "$sourceDir\frontend") {
    Set-Location -Path "$sourceDir\frontend"
    
    # Check if package.json exists in frontend directory
    if (Test-Path "package.json") {
        Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
        npm install
        Write-Host "Building frontend..." -ForegroundColor Yellow
        npm run build
    } else {
        Write-Host "Warning: package.json not found in frontend directory." -ForegroundColor Yellow
        Write-Host "Skipping frontend build. Will use pre-built files if available." -ForegroundColor Yellow
    }
    
    Set-Location -Path $sourceDir
} else {
    Write-Host "Warning: Frontend directory not found at $sourceDir\frontend" -ForegroundColor Yellow
    Write-Host "Skipping frontend build. Will use pre-built files if available." -ForegroundColor Yellow
}

# Copy backend files
Write-Host "Copying backend files..." -ForegroundColor Yellow
if (Test-Path "$sourceDir\backend\src") {
    Copy-Item -Path "$sourceDir\backend\src\*" -Destination "$sitePath\backend\src" -Recurse -Force
} else {
    Write-Host "Warning: Backend source directory not found at $sourceDir\backend\src" -ForegroundColor Yellow
    Write-Host "Checking for backend files in the current directory..." -ForegroundColor Yellow
    if (Test-Path "src") {
        Write-Host "Found src directory in current location, copying..." -ForegroundColor Yellow
        Copy-Item -Path "src\*" -Destination "$sitePath\backend\src" -Recurse -Force
    } else {
        Write-Host "Error: Backend source files not found. The application will not function correctly." -ForegroundColor Red
    }
}

# Check and copy package.json
Write-Host "Checking for package.json..." -ForegroundColor Yellow
$packageJsonFound = $false

# Check in backend directory
if (Test-Path "$sourceDir\backend\package.json") {
    Write-Host "Found package.json in backend directory, copying..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\package.json" -Destination "$sitePath\backend\package.json" -Force
    $packageJsonFound = $true
}
# Check in current directory
elseif (Test-Path "package.json") {
    Write-Host "Found package.json in current directory, copying..." -ForegroundColor Yellow
    Copy-Item -Path "package.json" -Destination "$sitePath\backend\package.json" -Force
    $packageJsonFound = $true
}
# Check in deployment package root
elseif (Test-Path "$sourceDir\package.json") {
    Write-Host "Found package.json in deployment package root, copying..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\package.json" -Destination "$sitePath\backend\package.json" -Force
    $packageJsonFound = $true
}

if (-not $packageJsonFound) {
    Write-Host "Error: package.json not found. npm install may fail." -ForegroundColor Red
}

# Check and copy package-lock.json
Write-Host "Checking for package-lock.json..." -ForegroundColor Yellow
$packageLockFound = $false

# Check in backend directory
if (Test-Path "$sourceDir\backend\package-lock.json") {
    Write-Host "Found package-lock.json in backend directory, copying..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\package-lock.json" -Destination "$sitePath\backend\package-lock.json" -Force
    $packageLockFound = $true
}
# Check in current directory
elseif (Test-Path "package-lock.json") {
    Write-Host "Found package-lock.json in current directory, copying..." -ForegroundColor Yellow
    Copy-Item -Path "package-lock.json" -Destination "$sitePath\backend\package-lock.json" -Force
    $packageLockFound = $true
}
# Check in deployment package root
elseif (Test-Path "$sourceDir\package-lock.json") {
    Write-Host "Found package-lock.json in deployment package root, copying..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\package-lock.json" -Destination "$sitePath\backend\package-lock.json" -Force
    $packageLockFound = $true
}

if (-not $packageLockFound) {
    Write-Host "Note: package-lock.json not found. This is not critical." -ForegroundColor Yellow
}

# Copy .env file or create from template if it doesn't exist
if (Test-Path "$sourceDir\backend\.env.production") {
    Write-Host "Copying production environment file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env.production" -Destination "$sitePath\backend\.env" -Force
} elseif (Test-Path "$sourceDir\backend\.env") {
    Write-Host "Copying development environment file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env" -Destination "$sitePath\backend\.env" -Force
} elseif (Test-Path "$sourceDir\backend\.env.template") {
    Write-Host "Copying environment template file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env.template" -Destination "$sitePath\backend\.env" -Force
    Write-Host "Warning: Using template environment file. Please update with production values." -ForegroundColor Yellow
} elseif (Test-Path ".env") {
    Write-Host "Copying environment file from current directory..." -ForegroundColor Yellow
    Copy-Item -Path ".env" -Destination "$sitePath\backend\.env" -Force
} else {
    Write-Host "No environment file found. Please create one manually." -ForegroundColor Red
}

# Copy frontend build files
Write-Host "Copying frontend build files..." -ForegroundColor Yellow
$frontendBuildFound = $false

# Check in expected location
if (Test-Path "$sourceDir\frontend\build") {
    Write-Host "Found frontend build in expected location, copying..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\frontend\build\*" -Destination "$sitePath\frontend\build" -Recurse -Force
    $frontendBuildFound = $true
}
# Check in current directory
elseif (Test-Path "build") {
    Write-Host "Found build directory in current location, copying..." -ForegroundColor Yellow
    Copy-Item -Path "build\*" -Destination "$sitePath\frontend\build" -Recurse -Force
    $frontendBuildFound = $true
}
# Check in deployment package root
elseif (Test-Path "$sourceDir\build") {
    Write-Host "Found build directory in deployment package root, copying..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\build\*" -Destination "$sitePath\frontend\build" -Recurse -Force
    $frontendBuildFound = $true
}

if (-not $frontendBuildFound) {
    Write-Host "Warning: Frontend build directory not found." -ForegroundColor Yellow
    Write-Host "The application may not function correctly without frontend files." -ForegroundColor Red
}

# Copy web.config
Write-Host "Copying web.config..." -ForegroundColor Yellow
if (Test-Path "$sourceDir\web.config") {
    Copy-Item -Path "$sourceDir\web.config" -Destination "$sitePath\web.config" -Force
} elseif (Test-Path "web.config") {
    Copy-Item -Path "web.config" -Destination "$sitePath\web.config" -Force
} else {
    Write-Host "Error: web.config not found. The application will not function correctly in IIS." -ForegroundColor Red
}

# Install backend dependencies
Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
Set-Location -Path "$sitePath\backend"

# Check if package.json exists before running npm install
if (Test-Path "package.json") {
    Write-Host "Installing production dependencies..." -ForegroundColor Yellow
    npm install --production
} else {
    Write-Host "Error: package.json not found in $sitePath\backend" -ForegroundColor Red
    Write-Host "Attempting to continue deployment, but the application may not function correctly." -ForegroundColor Red
}

Set-Location -Path $sourceDir

# Create application pool if it doesn't exist
if (-not (Get-IISAppPool -Name $appPoolName -ErrorAction SilentlyContinue)) {
    Write-Host "Creating application pool $appPoolName" -ForegroundColor Yellow
    New-WebAppPool -Name $appPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
}

# Create website if it doesn't exist
if (-not (Get-Website -Name $siteName -ErrorAction SilentlyContinue)) {
    Write-Host "Creating website $siteName" -ForegroundColor Yellow
    New-Website -Name $siteName -PhysicalPath $sitePath -ApplicationPool $appPoolName -Port $port -Force
} else {
    Write-Host "Updating website $siteName" -ForegroundColor Yellow
    Set-ItemProperty -Path "IIS:\Sites\$siteName" -Name "physicalPath" -Value $sitePath
    Set-ItemProperty -Path "IIS:\Sites\$siteName" -Name "applicationPool" -Value $appPoolName
}

# Set permissions
Write-Host "Setting folder permissions..." -ForegroundColor Yellow
$acl = Get-Acl $sitePath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$appPoolName", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl $sitePath $acl

# Create uploads directories if they don't exist and set permissions
$uploadDirs = @(
    "$sitePath\backend\Uploads",
    "$sitePath\backend\Uploads\Companies",
    "$sitePath\backend\Uploads\Plazas"
)

foreach ($dir in $uploadDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating upload directory: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
    
    # Set permissions for upload directories
    $acl = Get-Acl $dir
    $acl.SetAccessRule($accessRule)
    Set-Acl $dir $acl
}

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Your application should be accessible at http://localhost:$port" -ForegroundColor Green
Write-Host "Note: You may need to update the .env file in $sitePath\backend with your production database credentials." -ForegroundColor Yellow