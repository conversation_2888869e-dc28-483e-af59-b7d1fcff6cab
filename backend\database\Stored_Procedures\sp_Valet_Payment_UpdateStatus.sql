-- =============================================
-- Author: Valet System
-- Create date: 2024-12-07
-- Description: Update payment status for gateway callbacks
-- =============================================
CREATE OR ALTER PROCEDURE sp_Valet_Payment_UpdateStatus
    @PaymentId INT,
    @Status NVARCHAR(50),
    @GatewayTransactionId NVARCHAR(100) = NULL,
    @GatewayResponse NVARCHAR(MAX) = NULL,
    @UpdatedBy INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate payment exists
        IF NOT EXISTS (SELECT 1 FROM PaymentGatewayTransactions WHERE Id = @PaymentId)
        BEGIN
            RAISERROR('Payment not found', 16, 1);
            RETURN;
        END
        
        -- Get transaction ID for status update
        DECLARE @TransactionId INT;
        SELECT @TransactionId = TransactionId 
        FROM PaymentGatewayTransactions 
        WHERE Id = @PaymentId;
        
        -- Update payment status
        UPDATE PaymentGatewayTransactions
        SET Status = @Status,
            GatewayTransactionId = @GatewayTransactionId,
            GatewayResponse = @GatewayResponse,
            UpdatedOn = GETDATE()
        WHERE Id = @PaymentId;
        
        -- Update main transaction status based on payment status
        IF @Status = 'SUCCESS'
        BEGIN
            UPDATE ParkingTransactions
            SET Status = 'PAYMENT_COMPLETED',
                ModifiedBy = ISNULL(@UpdatedBy, 1),
                ModifiedOn = GETDATE()
            WHERE Id = @TransactionId;
        END
        ELSE IF @Status = 'FAILED'
        BEGIN
            UPDATE ParkingTransactions
            SET Status = 'PAYMENT_FAILED',
                ModifiedBy = ISNULL(@UpdatedBy, 1),
                ModifiedOn = GETDATE()
            WHERE Id = @TransactionId;
        END
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
