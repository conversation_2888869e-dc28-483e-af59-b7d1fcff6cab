const db = require('../config/database');
const sql = require('mssql');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure the upload directory exists
const uploadDir = path.join(__dirname, '../../Uploads/Plazas');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// File upload setup
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

// Handle file uploads
exports.uploadPlazaFiles = () => {
  return upload.fields([
    { name: 'Logo', maxCount: 1 }
  ]);
};


// Get all plazas
exports.getAllPlazas = async (req, res) => {
  try {
    // Removed the IsActive = 1 filter to show all plazas including inactive ones
    let query = 'SELECT p.*, c.CompanyName FROM Plaza p JOIN tblCompanyMaster c ON p.CompanyId = c.Id';
    const queryParams = {};

    // If not SuperAdmin, filter by user's access
    if (req.user && req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only see plazas from companies they have access to
        query += ` WHERE p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        // PlazaManager can only see plazas they are assigned to
        query += ` WHERE p.Id IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      }
    }

    // Add ordering
    query += ' ORDER BY c.CompanyName, p.PlazaName';

    // First, check if the Plaza table has any records
    const countResult = await db.query('SELECT COUNT(*) AS count FROM Plaza', {});

    // Execute the main query
    const result = await db.query(query, queryParams);

    return res.status(200).json({
      success: true,
      data: result.recordset,
      message: 'Plazas retrieved successfully',
      totalCount: countResult.recordset[0].count
    });
  } catch (error) {
    console.error('Error in getAllPlazas controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch plazas',
      error: error.message
    });
  }
};


// Get plaza by ID
exports.getPlazaById = async (req, res) => {
  try {
    const { id } = req.params;

    let query = `
      SELECT p.*, c.CompanyName
      FROM Plaza p
      JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE p.Id = @Id AND p.IsActive = 1
    `;
    const queryParams = { Id: id };

    // If not SuperAdmin, filter by user's access
    if (req.user && req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only see plazas from companies they have access to
        query += ` AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        // PlazaManager can only see plazas they are assigned to
        query += ` AND p.Id IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      }
    }

    const result = await db.query(query, queryParams);
    const plaza = result.recordset[0];

    if (!plaza) {
      return res.status(404).json({
        success: false,
        message: 'Plaza not found or you do not have access to this plaza'
      });
    }

    return res.status(200).json({
      success: true,
      data: plaza,
      message: 'Plaza retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getPlazaById controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch plaza',
      error: error.message
    });
  }
};

// Create plaza
// exports.createPlaza = async (req, res) => {
//   const {
//     CompanyId,
//     PlazaName,
//     AddressId,
//     ContactPerson,
//     ContactNumber,
//     ContactEmail,
//     PlazaDisplayName,
//     PlazaCode,
//     GstNumber,
//     SmsSenderHeader,
//     MessageSenderKey,
//     PayAtEntryOrExit,
//     IsValetFee,
//     ValetFee,
//     IsParkingFee,
//     ParkingFeeType,
//     FixParkingFee,
//     PerHourParkingFee,
//     HelplineNumber,
//     OpenTime,
//     CloseTime,
//     IsActive,
//     CreatedBy,
//     IsCashActive,
//     IsPhonePe,
//     IsAutoExitPreviousDayTransaction,
//     isPaymentReceivedMessageEnabled,
//     isHandoverMessageEnabled,
//     isParkedSafelyEnabled,
//     isVRNEnabled
//   } = req.body;

//   // Handle file upload
//   let logoFilename = null;

//   if (req.files && req.files.Logo && req.files.Logo.length > 0) {
//     logoFilename = req.files.Logo[0].filename;
//   }

//   try {
//     await db.query(
//       `INSERT INTO Plaza
//        (CompanyId, PlazaName, AddressId, ContactPerson, ContactNumber, ContactEmail, PlazaDisplayName, PlazaCode,
//         GstNumber, SmsSenderHeader, MessageSenderKey, PayAtEntryOrExit, IsValetFee, ValetFee, IsParkingFee, ParkingFeeType,
//         FixParkingFee, PerHourParkingFee, HelplineNumber, OpenTime, CloseTime, IsActive, CreatedBy, CreatedOn,
//         IsCashActive, IsPhonePe, IsAutoExitPreviousDayTransaction, isPaymentReceivedMessageEnabled, isHandoverMessageEnabled,
//         isParkedSafelyEnabled, isVRNEnabled, CONVERT(varbinary(max), @Logo))
//        VALUES
//        (@CompanyId, @PlazaName, @AddressId, @ContactPerson, @ContactNumber, @ContactEmail, @PlazaDisplayName, @PlazaCode,
//         @GstNumber, @SmsSenderHeader, @MessageSenderKey, @PayAtEntryOrExit, @IsValetFee, @ValetFee, @IsParkingFee,
//         @ParkingFeeType, @FixParkingFee, @PerHourParkingFee, @HelplineNumber, @OpenTime, @CloseTime, @IsActive, @CreatedBy,
//         GETDATE(), @IsCashActive, @IsPhonePe, @IsAutoExitPreviousDayTransaction, @isPaymentReceivedMessageEnabled,
//         @isHandoverMessageEnabled, @isParkedSafelyEnabled, @isVRNEnabled, @Logo)`,
//       {
//         CompanyId,
//         PlazaName,
//         AddressId,
//         ContactPerson,
//         ContactNumber,
//         ContactEmail,
//         PlazaDisplayName,
//         PlazaCode,
//         GstNumber,
//         SmsSenderHeader,
//         MessageSenderKey,
//         PayAtEntryOrExit,
//         IsValetFee,
//         ValetFee,
//         IsParkingFee,
//         ParkingFeeType,
//         FixParkingFee,
//         PerHourParkingFee,
//         HelplineNumber,
//         OpenTime,
//         CloseTime,
//         IsActive,
//         CreatedBy,
//         IsCashActive,
//         IsPhonePe,
//         IsAutoExitPreviousDayTransaction,
//         isPaymentReceivedMessageEnabled,
//         isHandoverMessageEnabled,
//         isParkedSafelyEnabled,
//         isVRNEnabled,
//         Logo: logoFilename
//       }
//     );

//     res.status(201).json({
//       message: 'Plaza created successfully',
//       Logo: logoFilename ? `../../Uploads/Plazas/${logoFilename}` : null
//     });
//   } catch (error) {
//     res.status(500).json({ error: 'Database error', details: error.message });
//   }
// };

exports.createPlaza = async (req, res) => {
  try {
    const {
      CompanyId,
      PlazaName,
      AddressId,
      ContactPerson,
      ContactNumber,
      ContactEmail,
      PlazaDisplayName,
      PlazaCode,
      GstNumber,
      SmsSenderHeader,
      MessageSenderKey,
      PayAtEntryOrExit,
      IsValetFee,
      ValetFee,
      IsParkingFee,
      ParkingFeeType,
      FixParkingFee,
      PerHourParkingFee,
      HelplineNumber,
      OpenTime,
      CloseTime,
      IsActive,
      IsCashActive,
      IsPhonePe,
      IsAutoExitPreviousDayTransaction,
      isPaymentReceivedMessageEnabled,
      isHandoverMessageEnabled,
      isParkedSafelyEnabled,
      isVRNEnabled
    } = req.body;

    // Validate required fields
    if (!CompanyId || !PlazaName || !ContactPerson || !ContactEmail) {
      return res.status(400).json({
        success: false,
        message: 'Required fields missing: CompanyId, PlazaName, ContactPerson, and ContactEmail are required'
      });
    }

    // Check if user has access to the company
    if (req.user.role !== 'SuperAdmin') {
      // CompanyAdmin can only create plazas for companies they have access to
      const companyAccessQuery = `
        SELECT COUNT(*) as count
        FROM UserCompany
        WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
      `;

      const companyAccessResult = await db.query(companyAccessQuery, {
        userId: req.user.id,
        companyId: CompanyId
      });

      if (companyAccessResult.recordset[0].count === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to create a plaza for this company'
        });
      }
    }

    // Get current user ID for CreatedBy
    const createdBy = req.user.id;

    // Handle file upload
    let logoFilename = null;

    if (req.files && req.files.Logo && req.files.Logo.length > 0) {
      logoFilename = req.files.Logo[0].filename;
    }

    // Insert plaza
    const result = await db.query(
      `INSERT INTO Plaza
       (CompanyId, PlazaName, AddressId, ContactPerson, ContactNumber, ContactEmail, PlazaDisplayName, PlazaCode,
        GstNumber, SmsSenderHeader, MessageSenderKey, PayAtEntryOrExit, IsValetFee, ValetFee, IsParkingFee, ParkingFeeType,
        FixParkingFee, PerHourParkingFee, HelplineNumber, OpenTime, CloseTime, IsActive, CreatedBy, CreatedOn,
        IsCashActive, IsPhonePe, IsAutoExitPreviousDayTransaction, isPaymentReceivedMessageEnabled, isHandoverMessageEnabled,
        isParkedSafelyEnabled, isVRNEnabled, Logo)
       VALUES
       (@CompanyId, @PlazaName, @AddressId, @ContactPerson, @ContactNumber, @ContactEmail, @PlazaDisplayName, @PlazaCode,
        @GstNumber, @SmsSenderHeader, @MessageSenderKey, @PayAtEntryOrExit, @IsValetFee, @ValetFee, @IsParkingFee,
        @ParkingFeeType, @FixParkingFee, @PerHourParkingFee, @HelplineNumber, @OpenTime, @CloseTime, @IsActive, @CreatedBy,
        GETDATE(), @IsCashActive, @IsPhonePe, @IsAutoExitPreviousDayTransaction, @isPaymentReceivedMessageEnabled,
        @isHandoverMessageEnabled, @isParkedSafelyEnabled, @isVRNEnabled, Convert(varbinary(max), @Logo));
       SELECT SCOPE_IDENTITY() AS Id;`,
      {
        CompanyId,
        PlazaName,
        AddressId: AddressId || null,
        ContactPerson,
        ContactNumber: ContactNumber || null,
        ContactEmail,
        PlazaDisplayName: PlazaDisplayName || PlazaName,
        PlazaCode: PlazaCode || null,
        GstNumber: GstNumber || null,
        SmsSenderHeader: SmsSenderHeader || null,
        MessageSenderKey: MessageSenderKey || null,
        PayAtEntryOrExit: PayAtEntryOrExit || 'Exit',
        IsValetFee: IsValetFee !== undefined ? IsValetFee : 0,
        ValetFee: ValetFee || 0,
        IsParkingFee: IsParkingFee !== undefined ? IsParkingFee : 0,
        ParkingFeeType: ParkingFeeType === 'Fixed' ? 1 : (ParkingFeeType === 'Hourly' ? 2 : 1), // Convert string to int
        FixParkingFee: FixParkingFee || 0,
        PerHourParkingFee: PerHourParkingFee || 0,
        HelplineNumber: HelplineNumber || null,
        OpenTime: OpenTime || null,
        CloseTime: CloseTime || null,
        IsActive: IsActive !== undefined ? IsActive : 1,
        CreatedBy: createdBy,
        IsCashActive: IsCashActive !== undefined ? IsCashActive : 1,
        IsPhonePe: IsPhonePe !== undefined ? IsPhonePe : 0,
        IsAutoExitPreviousDayTransaction: IsAutoExitPreviousDayTransaction !== undefined ? IsAutoExitPreviousDayTransaction : 0,
        isPaymentReceivedMessageEnabled: isPaymentReceivedMessageEnabled !== undefined ? isPaymentReceivedMessageEnabled : 0,
        isHandoverMessageEnabled: isHandoverMessageEnabled !== undefined ? isHandoverMessageEnabled : 0,
        isParkedSafelyEnabled: isParkedSafelyEnabled !== undefined ? isParkedSafelyEnabled : 0,
        isVRNEnabled: isVRNEnabled !== undefined ? isVRNEnabled : 0,
        Logo: logoFilename
      }
    );

    const plazaId = result.recordset[0].Id;

    // If user is a CompanyAdmin, assign them to this plaza
    if (req.user.role === 'CompanyAdmin') {
      await db.query(`
        INSERT INTO UserPlaza (UserId, PlazaId, IsActive, CreatedBy, CreatedOn)
        VALUES (@userId, @plazaId, 1, @createdBy, GETDATE())
      `, {
        userId: req.user.id,
        plazaId,
        createdBy
      });
    }

    res.status(201).json({
      success: true,
      message: 'Plaza created successfully',
      plazaId,
      Logo: logoFilename ? `../../Uploads/Plazas/${logoFilename}` : null
    });
  } catch (error) {
    console.error('Error in createPlaza controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};



// Update plaza
exports.updatePlaza = async (req, res) => {
  try {
    const id = req.params.id;
    const {
      CompanyId,
      PlazaName,
      AddressId,
      ContactPerson,
      ContactNumber,
      ContactEmail,
      PlazaDisplayName,
      PlazaCode,
      GstNumber,
      SmsSenderHeader,
      MessageSenderKey,
      PayAtEntryOrExit,
      IsValetFee,
      ValetFee,
      IsParkingFee,
      ParkingFeeType,
      FixParkingFee,
      PerHourParkingFee,
      HelplineNumber,
      OpenTime,
      CloseTime,
      IsActive,
      IsCashActive,
      IsPhonePe,
      IsAutoExitPreviousDayTransaction,
      isPaymentReceivedMessageEnabled,
      isHandoverMessageEnabled,
      isParkedSafelyEnabled,
      isVRNEnabled
    } = req.body;

    // Validate required fields
    if (!CompanyId || !PlazaName || !ContactPerson || !ContactEmail) {
      return res.status(400).json({
        success: false,
        message: 'Required fields missing: CompanyId, PlazaName, ContactPerson, and ContactEmail are required'
      });
    }

    // Check if plaza exists and user has access to it
    let query = `
      SELECT p.*, c.CompanyName
      FROM Plaza p
      JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE p.Id = @Id AND p.IsActive = 1
    `;
    const queryParams = { Id: id };

    // If not SuperAdmin, filter by user's access
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only update plazas from companies they have access to
        query += ` AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else {
        // PlazaManager can't update plazas
        return res.status(403).json({
          success: false,
          message: 'Plaza Managers cannot update plaza details'
        });
      }
    }

    const result = await db.query(query, queryParams);

    const currentPlaza = result.recordset[0];
    if (!currentPlaza) {
      return res.status(404).json({
        success: false,
        message: 'Plaza not found or you do not have permission to update it'
      });
    }

    // Check if user has access to the new company if it's being changed
    if (CompanyId !== currentPlaza.CompanyId && req.user.role !== 'SuperAdmin') {
      const companyAccessQuery = `
        SELECT COUNT(*) as count
        FROM UserCompany
        WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
      `;

      const companyAccessResult = await db.query(companyAccessQuery, {
        userId: req.user.id,
        companyId: CompanyId
      });

      if (companyAccessResult.recordset[0].count === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to move this plaza to the specified company'
        });
      }
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user.id;

    // Handle file upload
    let logoFilename = null;

    if (req.files && req.files.Logo && req.files.Logo.length > 0) {
      logoFilename = req.files.Logo[0].filename;
    }

    await db.query(
      `UPDATE Plaza
       SET CompanyId = @CompanyId,
           PlazaName = @PlazaName,
           AddressId = @AddressId,
           ContactPerson = @ContactPerson,
           ContactNumber = @ContactNumber,
           ContactEmail = @ContactEmail,
           PlazaDisplayName = @PlazaDisplayName,
           PlazaCode = @PlazaCode,
           GstNumber = @GstNumber,
           SmsSenderHeader = @SmsSenderHeader,
           MessageSenderKey = @MessageSenderKey,
           PayAtEntryOrExit = @PayAtEntryOrExit,
           IsValetFee = @IsValetFee,
           ValetFee = @ValetFee,
           IsParkingFee = @IsParkingFee,
           ParkingFeeType = @ParkingFeeType,
           FixParkingFee = @FixParkingFee,
           PerHourParkingFee = @PerHourParkingFee,
           HelplineNumber = @HelplineNumber,
           OpenTime = @OpenTime,
           CloseTime = @CloseTime,
           IsActive = @IsActive,
           ModifiedBy = @ModifiedBy,
           ModifiedOn = GETDATE(),
           IsCashActive = @IsCashActive,
           IsPhonePe = @IsPhonePe,
           IsAutoExitPreviousDayTransaction = @IsAutoExitPreviousDayTransaction,
           isPaymentReceivedMessageEnabled = @isPaymentReceivedMessageEnabled,
           isHandoverMessageEnabled = @isHandoverMessageEnabled,
           isParkedSafelyEnabled = @isParkedSafelyEnabled,
           isVRNEnabled = @isVRNEnabled
           ${logoFilename ? ', Logo = CONVERT(Varbinary(max), @Logo)' : ''}
       WHERE Id = @Id`,
      {
        Id: id,
        CompanyId,
        PlazaName,
        AddressId: AddressId || currentPlaza.AddressId,
        ContactPerson,
        ContactNumber: ContactNumber || currentPlaza.ContactNumber,
        ContactEmail,
        PlazaDisplayName: PlazaDisplayName || PlazaName,
        PlazaCode: PlazaCode || currentPlaza.PlazaCode,
        GstNumber: GstNumber || currentPlaza.GstNumber,
        SmsSenderHeader: SmsSenderHeader || currentPlaza.SmsSenderHeader,
        MessageSenderKey: MessageSenderKey || currentPlaza.MessageSenderKey,
        PayAtEntryOrExit: PayAtEntryOrExit || currentPlaza.PayAtEntryOrExit,
        IsValetFee: IsValetFee !== undefined ? IsValetFee : currentPlaza.IsValetFee,
        ValetFee: ValetFee !== undefined ? ValetFee : currentPlaza.ValetFee,
        IsParkingFee: IsParkingFee !== undefined ? IsParkingFee : currentPlaza.IsParkingFee,
        ParkingFeeType: ParkingFeeType === 'Fixed' ? 1 : (ParkingFeeType === 'Hourly' ? 2 : (currentPlaza.ParkingFeeType || 1)), // Convert string to int
        FixParkingFee: FixParkingFee !== undefined ? FixParkingFee : currentPlaza.FixParkingFee,
        PerHourParkingFee: PerHourParkingFee !== undefined ? PerHourParkingFee : currentPlaza.PerHourParkingFee,
        HelplineNumber: HelplineNumber || currentPlaza.HelplineNumber,
        OpenTime: OpenTime || currentPlaza.OpenTime,
        CloseTime: CloseTime || currentPlaza.CloseTime,
        IsActive: IsActive !== undefined ? IsActive : currentPlaza.IsActive,
        ModifiedBy: modifiedBy,
        IsCashActive: IsCashActive !== undefined ? IsCashActive : currentPlaza.IsCashActive,
        IsPhonePe: IsPhonePe !== undefined ? IsPhonePe : currentPlaza.IsPhonePe,
        IsAutoExitPreviousDayTransaction: IsAutoExitPreviousDayTransaction !== undefined ? IsAutoExitPreviousDayTransaction : currentPlaza.IsAutoExitPreviousDayTransaction,
        isPaymentReceivedMessageEnabled: isPaymentReceivedMessageEnabled !== undefined ? isPaymentReceivedMessageEnabled : currentPlaza.isPaymentReceivedMessageEnabled,
        isHandoverMessageEnabled: isHandoverMessageEnabled !== undefined ? isHandoverMessageEnabled : currentPlaza.isHandoverMessageEnabled,
        isParkedSafelyEnabled: isParkedSafelyEnabled !== undefined ? isParkedSafelyEnabled : currentPlaza.isParkedSafelyEnabled,
        isVRNEnabled: isVRNEnabled !== undefined ? isVRNEnabled : currentPlaza.isVRNEnabled,
        Logo: logoFilename
      }
    );

    res.json({
      success: true,
      message: 'Plaza updated successfully',
      Logo: logoFilename ? `../../Uploads/Plazas/${logoFilename}` : null
    });
  } catch (error) {
    console.error('Error in updatePlaza controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};
// DELETE function for deleting a plaza (soft delete)
exports.deletePlaza = async (req, res) => {
  try {
    const id = req.params.id;

    // Check if plaza exists and user has access to it
    let query = `
      SELECT p.*, c.CompanyName
      FROM Plaza p
      JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE p.Id = @Id
    `;
    const queryParams = { Id: id };

    // If not SuperAdmin, filter by user's access
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only delete plazas from companies they have access to
        query += ` AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else {
        // PlazaManager can't delete plazas
        return res.status(403).json({
          success: false,
          message: 'Plaza Managers cannot delete plazas'
        });
      }
    }

    const result = await db.query(query, queryParams);

    const plaza = result.recordset[0];
    if (!plaza) {
      return res.status(404).json({
        success: false,
        message: 'Plaza not found or you do not have permission to delete it'
      });
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user.id;

    try {
      // 1. Get all lanes associated with this plaza
      const lanesQuery = await db.query('SELECT LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaId', { PlazaId: id });
      const laneIds = lanesQuery.recordset.map(lane => lane.LaneID);
      console.log(`Found ${laneIds.length} lanes for plaza ${id}`);

      // 2. Delete all associated configurations for each lane
      if (laneIds.length > 0) {
        // Delete ANPR configurations if table exists
        try {
          await db.query(`DELETE FROM tblLaneANPRConfiguration WHERE LaneID IN (SELECT LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaId)`, { PlazaId: id });
          console.log(`Deleted ANPR configurations for lanes`);
        } catch (err) {
          console.log(`No ANPR configurations to delete or table doesn't exist`);
        }
        
        // Delete Fastag configurations if table exists
        try {
          await db.query(`DELETE FROM tblLaneFastagConfiguration WHERE LaneID IN (SELECT LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaId)`, { PlazaId: id });
          console.log(`Deleted Fastag configurations for lanes`);
        } catch (err) {
          console.log(`No Fastag configurations to delete or table doesn't exist`);
        }
        
        // Delete Digital Payment configurations if table exists
        try {
          await db.query(`DELETE FROM tblLaneDigitalPayConfiguration WHERE LaneID IN (SELECT LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaId)`, { PlazaId: id });
          console.log(`Deleted Digital Payment configurations for lanes`);
        } catch (err) {
          console.log(`No Digital Payment configurations to delete or table doesn't exist`);
        }
        
        // Delete UHF Reader details if table exists
        try {
          await db.query(`DELETE FROM tblLaneUHFReaderDetails WHERE LaneID IN (SELECT LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaId)`, { PlazaId: id });
          console.log(`Deleted UHF Reader details for lanes`);
        } catch (err) {
          console.log(`No UHF Reader details to delete or table doesn't exist`);
        }
      }
      
      // 3. Delete Pass Registrations associated with this plaza if table exists
      try {
        // First get the PlazaCode for this plaza
        const plazaCodeResult = await db.query(`SELECT PlazaCode, PlazaName FROM Plaza WHERE Id = @PlazaId`, { PlazaId: id });
        
        if (plazaCodeResult.recordset.length > 0) {
          const plazaCode = plazaCodeResult.recordset[0].PlazaCode;
          const plazaName = plazaCodeResult.recordset[0].PlazaName;
          
          // First check if there are any pass registrations for this plaza
          const passRegCountResult = await db.query(`
            SELECT COUNT(*) as PassRegCount 
            FROM tbl_Parkwiz_Pass_Reg 
            WHERE PlazaCode = @PlazaCode OR PlazaName = @PlazaName
          `, { 
            PlazaCode: plazaCode,
            PlazaName: plazaName
          });
          
          const passRegCount = passRegCountResult.recordset[0].PassRegCount;
          
          if (passRegCount > 0) {
            // Option 1: Hard delete pass registrations
            await db.query(`
              DELETE FROM tbl_Parkwiz_Pass_Reg 
              WHERE PlazaCode = @PlazaCode OR PlazaName = @PlazaName
            `, { 
              PlazaCode: plazaCode,
              PlazaName: plazaName
            });
            
            // Option 2: Soft delete by setting MarkDelete = 1
            // await db.query(`
            //   UPDATE tbl_Parkwiz_Pass_Reg 
            //   SET MarkDelete = 1
            //   WHERE PlazaCode = @PlazaCode OR PlazaName = @PlazaName
            // `, { 
            //   PlazaCode: plazaCode,
            //   PlazaName: plazaName
            // });
            
            console.log(`Deleted ${passRegCount} Pass Registrations for plaza ${id} with code ${plazaCode}`);
          } else {
            console.log(`No Pass Registrations found for plaza ${id} with code ${plazaCode}`);
          }
        } else {
          console.log(`No PlazaCode found for plaza ${id}`);
        }
      } catch (err) {
        console.log(`Error deleting Pass Registrations: ${err.message}`);
      }

      // 4. Delete the lanes themselves
      if (laneIds.length > 0) {
        await db.query(`DELETE FROM tblLaneDetails WHERE PlazaID = @PlazaId`, { PlazaId: id });
        console.log(`Deleted ${laneIds.length} lanes`);
      }

      // 5. Delete user-plaza associations
      await db.query(`DELETE FROM UserPlaza WHERE PlazaId = @PlazaId`, { PlazaId: id });
      console.log(`Deleted User-Plaza associations for plaza ${id}`);

      // 6. Finally, delete the plaza itself
      await db.query(`DELETE FROM Plaza WHERE Id = @Id`, { Id: id });
      console.log(`Deleted Plaza ${id}`);

      res.json({
        success: true,
        message: 'Plaza and all associated data deleted successfully'
      });
    } catch (error) {
      console.error('Error during plaza deletion:', error);
      throw error;
    }
  } catch (error) {
    console.error('Error in deletePlaza controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Toggle plaza active status
exports.toggleActiveStatus = async (req, res) => {
  try {
    const id = req.params.id;

    // Check if plaza exists and user has access to it
    let query = `
      SELECT p.*, c.CompanyName
      FROM Plaza p
      JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE p.Id = @Id
    `;
    const queryParams = { Id: id };

    // If not SuperAdmin, filter by user's access
    if (req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only toggle plazas from companies they have access to
        query += ` AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else {
        // PlazaManager can't toggle plaza status
        return res.status(403).json({
          success: false,
          message: 'Plaza Managers cannot change plaza status'
        });
      }
    }

    const result = await db.query(query, queryParams);

    const plaza = result.recordset[0];
    if (!plaza) {
      return res.status(404).json({
        success: false,
        message: 'Plaza not found or you do not have permission to update it'
      });
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user.id;

    // Get current status and toggle it
    const currentStatus = plaza.IsActive;
    const newStatus = currentStatus === 1 || currentStatus === true ? 0 : 1;

    // Update the plaza status
    await db.query(`
      UPDATE Plaza
      SET IsActive = @NewStatus, ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE()
      WHERE Id = @Id
    `, {
      Id: id,
      NewStatus: newStatus,
      ModifiedBy: modifiedBy
    });

    // If deactivating, also deactivate user-plaza associations
    if (newStatus === 0) {
      await db.query(`
        UPDATE UserPlaza
        SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE()
        WHERE PlazaId = @Id
      `, {
        Id: id,
        ModifiedBy: modifiedBy
      });
    }

    return res.status(200).json({
      success: true,
      message: `Plaza ${newStatus === 1 ? 'activated' : 'deactivated'} successfully`,
      data: {
        id: plaza.Id,
        plazaName: plaza.PlazaName,
        isActive: newStatus
      }
    });
  } catch (error) {
    console.error('Error in toggleActiveStatus controller:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to toggle plaza status',
      error: error.message
    });
  }
};