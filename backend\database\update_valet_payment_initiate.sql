-- Update sp_Valet_Payment_Initiate stored procedure to match controller expectations
USE ParkwizOps;
GO

-- Check if the PaymentGatewayTransactions table has the necessary columns
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PaymentGatewayTransactions' AND COLUMN_NAME = 'OrderId')
BEGIN
    ALTER TABLE PaymentGatewayTransactions ADD OrderId NVARCHAR(100) NULL;
END

-- Drop and recreate the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Payment_Initiate]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Payment_Initiate];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Payment_Initiate]
    @TransactionId INT,
    @PaymentMethod NVARCHAR(50),
    @Amount DECIMAL(18,2),
    @CustomerId INT = NULL,
    @CustomerName NVARCHAR(255) = NULL,
    @CustomerEmail NVARCHAR(255) = NULL,
    @CustomerMobile NVARCHAR(15) = NULL,
    @CreatedBy INT = NULL,
    @PaymentId INT OUTPUT,
    @OrderId NVARCHAR(100) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @TransactionId IS NULL OR @TransactionId <= 0
        BEGIN
            RAISERROR('Transaction ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @PaymentMethod IS NULL OR LTRIM(RTRIM(@PaymentMethod)) = ''
        BEGIN
            RAISERROR('Payment method is required', 16, 1);
            RETURN -1;
        END
        
        IF @Amount IS NULL OR @Amount <= 0
        BEGIN
            RAISERROR('Amount is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Generate unique order ID
        SET @OrderId = 'ORD' + CONVERT(NVARCHAR(20), @TransactionId) + CONVERT(NVARCHAR(20), DATEDIFF(SECOND, '2000-01-01', GETDATE()));
        
        -- Determine gateway type based on payment method
        DECLARE @GatewayType NVARCHAR(50);
        IF @PaymentMethod LIKE 'RAZORPAY%'
            SET @GatewayType = 'RAZORPAY';
        ELSE IF @PaymentMethod LIKE 'PHONEPE%'
            SET @GatewayType = 'PHONEPE';
        ELSE
            SET @GatewayType = 'OTHER';
        
        -- Insert payment record
        INSERT INTO [dbo].[PaymentGatewayTransactions] (
            TransactionId,
            GatewayType,
            GatewayOrderId,
            Amount,
            PaymentMethod,
            Status,
            CustomerName,
            CustomerEmail,
            CustomerMobile,
            CreatedBy,
            CreatedOn,
            IsActive,
            OrderId
        )
        VALUES (
            @TransactionId,
            @GatewayType,
            @OrderId,
            @Amount,
            @PaymentMethod,
            'INITIATED',
            @CustomerName,
            @CustomerEmail,
            @CustomerMobile,
            @CreatedBy,
            GETDATE(),
            1,
            @OrderId
        );
        
        SET @PaymentId = SCOPE_IDENTITY();
        
        -- Return success with payment details
        SELECT 
            @PaymentId AS PaymentId,
            @OrderId AS OrderId,
            @TransactionId AS TransactionId,
            @Amount AS Amount,
            'Payment initiated successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @PaymentId = -1;
        SET @OrderId = NULL;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_Payment_Initiate';