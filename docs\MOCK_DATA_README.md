# Mock Data Creation Script

This repository contains scripts to create mock data for the Parkwiz VMS system.

## Issues Fixed

The original script had several issues that were fixed:

1. **Digital Pay Configuration**:
   - Fixed column names to match the actual database schema
   - Added missing required columns
   - Removed non-existent columns

2. **Fastag Configuration**:
   - Fixed the `LaneGate` column value to prevent truncation errors
   - The column has a 5-character limit, so we used shorter values

## Files

- `complete_mock_data.js` - The main script that creates all mock data
- `digital_pay_only.js` - A script that only creates Digital Pay configurations
- `check_fastag_digital_pay_schema.js` - A utility script to check the schema of the tables

## How to Run

```bash
node complete_mock_data.js
```

## Data Created

The script creates the following data:

1. Lane details for 3 plazas
2. ANPR configurations for each lane
3. Fastag configurations for each lane
4. Digital Pay configurations for each lane
5. UHF Reader configurations for each lane
6. Pass registrations for each plaza

## Database Schema

The script is designed to work with the current database schema. If the schema changes, you may need to update the script accordingly.