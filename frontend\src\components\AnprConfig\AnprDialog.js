import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';
import { plazaApi } from '../../api/plazaApi';
import { useQuery } from '@tanstack/react-query';
// Consider creating a shared CSS file if styles are similar or keep specific styles here
// import './AnprDialog.css';

export default function AnprDialog({ isOpen, onClose, onSubmit, initialData, title, plazas: allPlazas, companies, lanes }) {
  const { user } = useAuth();
  
  // State to track the selected company's plazas
  const [companyPlazas, setCompanyPlazas] = useState([]);
  const [formData, setFormData] = useState({
    PlazaID: '',
    CompanyID: '',
    LaneID: '',
    PMSLaneNumber: '', // This might be derived from LaneID or entered manually
    flgEnableANPR: '0', // Default to '0' (No)
    ANPROrgID: '',
    ANPRLaneID: '',
    ANPRPublicKey: '',
    ANPRPrivateKey: '',
    ANPRSource: '',
    ANPRAPIURL: '',
    ANPRAPIURL2: '',
    ActiveStatus: '1', // Default to '1' (Active)
    UpdatedBy: 'admin', // Default or get from auth context
    AllowBlacklistedVehicle: '0', // Default to '0' (No)
    ANPRVendor: ''
  });

  const [errors, setErrors] = useState({});
  const [availableLanes, setAvailableLanes] = useState([]);

  // Helper to normalize boolean-like values to '1' or '0' for internal state
  const normalizeBooleanState = (value) => {
    // More explicit logging to debug the issue
    console.log(`Normalizing boolean value: ${value}, type: ${typeof value}, stringified: ${JSON.stringify(value)}`);

    // Handle various true values
    if (value === 'Y' || value === 1 || value === true || value === '1' || value === 'true' || value === 'yes') {
      console.log(`Value ${value} normalized to '1'`);
      return '1';
    }

    // Handle various false values
    if (value === 'N' || value === 0 || value === false || value === '0' || value === 'false' || value === 'no' || value === null || value === undefined) {
      console.log(`Value ${value} normalized to '0'`);
      return '0';
    }

    // For any other case, try to parse the value
    try {
      // If it's a string that can be parsed as a number
      const numValue = Number(value);
      if (!isNaN(numValue)) {
        const result = numValue > 0 ? '1' : '0';
        console.log(`Parsed numeric value ${value} to ${result}`);
        return result;
      }
    } catch (e) {
      console.error(`Error parsing value ${value}:`, e);
    }

    console.log(`Defaulting value ${value} to '0'`);
    return '0'; // Default to '0' for any other value
  };

  // Initialize form data
  useEffect(() => {
    if (initialData) {
      console.log('Initializing ANPR Dialog with data:', initialData);
      console.log('Boolean values before normalization:', {
        flgEnableANPR: initialData.flgEnableANPR,
        ActiveStatus: initialData.ActiveStatus,
        AllowBlacklistedVehicle: initialData.AllowBlacklistedVehicle
      });

      // Explicitly normalize boolean values
      const normalizedFlgEnableANPR = normalizeBooleanState(initialData.flgEnableANPR);
      const normalizedActiveStatus = normalizeBooleanState(initialData.ActiveStatus);
      const normalizedAllowBlacklistedVehicle = normalizeBooleanState(initialData.AllowBlacklistedVehicle);

      console.log('Normalized boolean values:', {
        flgEnableANPR: normalizedFlgEnableANPR,
        ActiveStatus: normalizedActiveStatus,
        AllowBlacklistedVehicle: normalizedAllowBlacklistedVehicle
      });

      setFormData({
        PlazaID: initialData.PlazaID || '',
        CompanyID: initialData.CompanyID || '',
        LaneID: initialData.LaneID || '',
        PMSLaneNumber: initialData.PMSLaneNumber || '',
        flgEnableANPR: normalizedFlgEnableANPR,
        ANPROrgID: initialData.ANPROrgID || '',
        ANPRLaneID: initialData.ANPRLaneID || '',
        ANPRPublicKey: initialData.ANPRPublicKey || '',
        ANPRPrivateKey: initialData.ANPRPrivateKey || '',
        ANPRSource: initialData.ANPRSource || '',
        ANPRAPIURL: initialData.ANPRAPIURL || '',
        ANPRAPIURL2: initialData.ANPRAPIURL2 || '',
        ActiveStatus: normalizedActiveStatus,
        UpdatedBy: initialData.UpdatedBy || 'admin',
        AllowBlacklistedVehicle: normalizedAllowBlacklistedVehicle,
        ANPRVendor: initialData.ANPRVendor || ''
      });

      // Log the form data after setting it
      console.log('Form data after setting:', {
        flgEnableANPR: normalizedFlgEnableANPR,
        ActiveStatus: normalizedActiveStatus,
        AllowBlacklistedVehicle: normalizedAllowBlacklistedVehicle
      });
    } else {
      resetForm(); // Reset for new entry
    }
  }, [initialData, isOpen]);

  // Fetch plazas for the selected company
  const { data: fetchedCompanyPlazas, isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazasByCompany', formData.CompanyID],
    queryFn: async () => {
      try {
        // Log the API call
        console.log(`Calling API: /companies/${formData.CompanyID}/plazas`);
        
        // Make the API call
        const data = await plazaApi.getPlazasByCompany(formData.CompanyID);
        
        // Log the response
        console.log(`API response for company ${formData.CompanyID} plazas:`, data);
        
        return data;
      } catch (error) {
        console.error(`API call failed for company ${formData.CompanyID} plazas:`, error);
        throw error;
      }
    },
    enabled: !!formData.CompanyID, // Only run the query if a company is selected
    onSuccess: (data) => {
      console.log('Setting company plazas state with:', data);
      setCompanyPlazas(data || []);
    },
    onError: (error) => {
      console.error('Error fetching plazas for company:', error);
    }
  });

  // Filter lanes based on selected plaza
  useEffect(() => {
    if (formData.PlazaID && lanes) {
      const filtered = lanes.filter(lane =>
        String(lane.PlazaID) === String(formData.PlazaID)
      );
      setAvailableLanes(filtered);
      // Reset LaneID if the selected plaza changes and the current LaneID is not valid for the new plaza
      if (initialData?.PlazaID !== formData.PlazaID) { // Only reset if plaza changed
         const currentLaneIsValid = filtered.some(lane => String(lane.LaneID) === String(formData.LaneID));
         if (!currentLaneIsValid) {
            setFormData(prev => ({ ...prev, LaneID: '', PMSLaneNumber: '' }));
         }
      }
    } else {
      setAvailableLanes([]);
    }
  }, [formData.PlazaID, lanes, initialData?.PlazaID, formData.LaneID]); // Added formData.LaneID dependency

  // Update PMSLaneNumber when LaneID changes
  useEffect(() => {
    if (formData.LaneID && availableLanes.length > 0) {
      const selectedLane = availableLanes.find(lane =>
        String(lane.LaneID) === String(formData.LaneID)
      );
      if (selectedLane) {
        setFormData(prev => ({
          ...prev,
          // Assuming PMSLaneNumber should be the same as LaneNumber from tblLaneDetails
          PMSLaneNumber: selectedLane.LaneNumber
        }));
      }
    } else if (!formData.LaneID) {
        // Clear PMSLaneNumber if LaneID is cleared
        setFormData(prev => ({ ...prev, PMSLaneNumber: '' }));
    }
  }, [formData.LaneID, availableLanes]);

  const resetForm = () => {
    setFormData({
      PlazaID: '',
      CompanyID: '',
      LaneID: '',
      PMSLaneNumber: '',
      flgEnableANPR: '0',
      ANPROrgID: '',
      ANPRLaneID: '',
      ANPRPublicKey: '',
      ANPRPrivateKey: '',
      ANPRSource: '',
      ANPRAPIURL: '',
      ANPRAPIURL2: '',
      ActiveStatus: '1',
      UpdatedBy: 'admin',
      AllowBlacklistedVehicle: '0',
      ANPRVendor: ''
    });
    setErrors({});
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? (checked ? '1' : '0') : value;

    // Enhanced logging for checkbox changes
    if (type === 'checkbox') {
      console.log(`Checkbox ${name} changed:`, checked, '→ value:', newValue);
      console.log(`Previous form data for ${name}:`, formData[name]);
    }

    // Create a new form data object with the updated value
    let updatedFormData = {
      ...formData,
      [name]: newValue
    };

    // If company changes, reset plaza and lane
    if (name === 'CompanyID') {
      console.log('Company changed, resetting PlazaID and LaneID');
      updatedFormData = {
        ...updatedFormData,
        PlazaID: '',
        LaneID: '',
        PMSLaneNumber: ''
      };
    }

    // Set the form data with the new value
    setFormData(updatedFormData);

    // Log the updated form data for this field
    if (type === 'checkbox') {
      console.log(`Updated form data for ${name}:`, updatedFormData[name]);
      console.log('Current form data after update:', updatedFormData);
    }

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.PlazaID) newErrors.PlazaID = 'Plaza is required';
    if (!formData.CompanyID) newErrors.CompanyID = 'Company is required';
    if (!formData.LaneID) newErrors.LaneID = 'Lane is required';
    if (!formData.PMSLaneNumber) newErrors.PMSLaneNumber = 'PMS Lane Number is required (auto-filled from Lane)';
    if (!formData.UpdatedBy) newErrors.UpdatedBy = 'Updated By is required';
    // Add other ANPR specific validations if needed
    if (formData.flgEnableANPR === '1' && !formData.ANPRVendor) newErrors.ANPRVendor = 'ANPR Vendor is required when ANPR is enabled';
    if (formData.flgEnableANPR === '1' && !formData.ANPRAPIURL) newErrors.ANPRAPIURL = 'ANPR API URL is required when ANPR is enabled';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      // Ensure boolean-like fields are sent as '1' or '0' as expected by the backend controller
      const processedData = {
        ...formData,
        flgEnableANPR: formData.flgEnableANPR === '1' ? '1' : '0',
        ActiveStatus: formData.ActiveStatus === '1' ? '1' : '0',
        AllowBlacklistedVehicle: formData.AllowBlacklistedVehicle === '1' ? '1' : '0',
        // Add user tracking
        UpdatedBy: user?.id || formData.UpdatedBy || 'admin'
      };

      // Force the boolean values to be strings
      processedData.flgEnableANPR = String(processedData.flgEnableANPR === '1' ? '1' : '0');
      processedData.ActiveStatus = String(processedData.ActiveStatus === '1' ? '1' : '0');
      processedData.AllowBlacklistedVehicle = String(processedData.AllowBlacklistedVehicle === '1' ? '1' : '0');

      onSubmit(processedData);
    }
  };

  if (!isOpen) return null;

  // Helper to render select options safely
  const renderOptions = (items, valueKey, labelKey, defaultOptionLabel, isPlazaSelect = false) => {
    // If this is the plaza select and we have a company selected, use the company-specific plazas
    if (isPlazaSelect && formData.CompanyID) {
      if (plazasLoading) {
        return <option value="">Loading plazas...</option>;
      }
      
      if (plazasError) {
        console.error('Error loading plazas:', plazasError);
        return <option value="">Error loading plazas</option>;
      }
      
      if (fetchedCompanyPlazas) {
        // Handle different response structures
        let plazaItems = [];
        
        if (Array.isArray(fetchedCompanyPlazas)) {
          plazaItems = fetchedCompanyPlazas;
        } else if (fetchedCompanyPlazas.plazas && Array.isArray(fetchedCompanyPlazas.plazas)) {
          plazaItems = fetchedCompanyPlazas.plazas;
        } else if (fetchedCompanyPlazas.data && Array.isArray(fetchedCompanyPlazas.data)) {
          plazaItems = fetchedCompanyPlazas.data;
        }
        
        if (plazaItems.length > 0) {
          console.log('Rendering plaza options:', plazaItems);
          
          return [
            <option key="" value="">{`Select ${defaultOptionLabel}`}</option>,
            ...plazaItems.map((item) => {
              const id = item[valueKey] || item.Id || item.id;
              const name = item[labelKey] || item.PlazaName || item.plazaName || item.name;
              
              if (id !== undefined && name !== undefined) {
                return (
                  <option key={id} value={id}>
                    {name}
                  </option>
                );
              }
              return null;
            })
          ];
        }
      }
      
      return <option value="">No plazas available for this company</option>;
    }
    
    // For non-plaza selects or when no company is selected, use the original logic
    let dataArray = items;
    if (items && !Array.isArray(items) && items.data && Array.isArray(items.data)) {
      dataArray = items.data;
    }
    if (!Array.isArray(dataArray)) {
      return <option value="">{`No ${defaultOptionLabel.toLowerCase()} available`}</option>;
    }
    return [
      <option key="" value="">{`Select ${defaultOptionLabel}`}</option>,
      ...dataArray.map((item) => {
        const id = item[valueKey];
        const name = item[labelKey];
        if (id !== undefined && name !== undefined) {
          return (
            <option key={id} value={id}>
              {name}
            </option>
          );
        }
        return null;
      })
    ];
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-auto shadow-xl">
        <div className="flex justify-between items-center px-6 py-4 border-b sticky top-0 bg-white z-10">
          <h2 className="text-xl font-semibold text-gray-800">{title}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information Section */}
          <div className="border p-4 rounded-md shadow-sm">
            <h3 className="text-lg font-medium text-gray-700 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Company Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Company*</label>
                <select
                  name="CompanyID"
                  value={formData.CompanyID}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.CompanyID ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                >
                  {renderOptions(companies, 'Id', 'CompanyName', 'Company')}
                </select>
                {errors.CompanyID && <p className="mt-1 text-sm text-red-500">{errors.CompanyID}</p>}
              </div>

              {/* Plaza Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Plaza*</label>
                <select
                  name="PlazaID"
                  value={formData.PlazaID}
                  onChange={handleChange}
                  disabled={!formData.CompanyID}
                  className={`w-full px-3 py-2 border rounded-md ${errors.PlazaID ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                >
                  {!formData.CompanyID ? (
                    <option value="">Select a company first</option>
                  ) : (
                    renderOptions(allPlazas, 'Id', 'PlazaName', 'Plaza', true)
                  )}
                </select>
                {errors.PlazaID && <p className="mt-1 text-sm text-red-500">{errors.PlazaID}</p>}
              </div>

              {/* Lane Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Lane*</label>
                <select
                  name="LaneID"
                  value={formData.LaneID}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.LaneID ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  disabled={!formData.PlazaID || availableLanes.length === 0}
                >
                  <option value="">Select Lane</option>
                  {availableLanes.map((lane) => (
                    <option key={lane.LaneID} value={lane.LaneID}>
                      {lane.LaneNumber} - {lane.LaneType}
                    </option>
                  ))}
                </select>
                {errors.LaneID && <p className="mt-1 text-sm text-red-500">{errors.LaneID}</p>}
              </div>

              {/* PMS Lane Number (Readonly/Derived) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">PMS Lane Number*</label>
                <input
                  type="text"
                  name="PMSLaneNumber"
                  value={formData.PMSLaneNumber}
                  readOnly // Make it read-only as it's derived
                  className={`w-full px-3 py-2 border rounded-md bg-gray-100 ${errors.PMSLaneNumber ? 'border-red-500' : 'border-gray-300'}`}
                />
                 {errors.PMSLaneNumber && <p className="mt-1 text-sm text-red-500">{errors.PMSLaneNumber}</p>}
              </div>

               {/* ANPR Vendor */}
               <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">ANPR Vendor</label>
                <input
                  type="text"
                  name="ANPRVendor"
                  value={formData.ANPRVendor}
                  onChange={handleChange}
                  className={`w-full px-3 py-2 border rounded-md ${errors.ANPRVendor ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="e.g., VaaaN, Effcon"
                />
                 {errors.ANPRVendor && <p className="mt-1 text-sm text-red-500">{errors.ANPRVendor}</p>}
              </div>

              {/* Updated By (Consider making read-only or getting from context) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Updated By*</label>
                <input
                  type="text"
                  name="UpdatedBy"
                  value={formData.UpdatedBy}
                  onChange={handleChange} // Or make readOnly and set from context
                  className={`w-full px-3 py-2 border rounded-md ${errors.UpdatedBy ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  readOnly // Example: Make read-only if set automatically
                  // placeholder="Enter updater username"
                />
                 {errors.UpdatedBy && <p className="mt-1 text-sm text-red-500">{errors.UpdatedBy}</p>}
              </div>
            </div>
          </div>

          {/* ANPR Configuration Section */}
          <div className="border p-4 rounded-md shadow-sm">
            <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-700">ANPR Configuration</h3>
                <div className="flex items-center space-x-2">
                    <label htmlFor="flgEnableANPR" className="text-sm font-medium text-gray-700">Enable ANPR</label>
                    <input
                        type="checkbox"
                        id="flgEnableANPR"
                        name="flgEnableANPR"
                        checked={formData.flgEnableANPR === '1'}
                        onChange={handleChange}
                        className="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 cursor-pointer"
                    />
                    {/* Debug info */}
                    <span className="text-xs text-gray-500 ml-2">(Value: {formData.flgEnableANPR})</span>
                </div>
            </div>

            {/* Conditional ANPR Fields */}
            {formData.flgEnableANPR === '1' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR Org ID</label>
                  <input type="text" name="ANPROrgID" value={formData.ANPROrgID} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR Lane ID</label>
                  <input type="text" name="ANPRLaneID" value={formData.ANPRLaneID} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR Public Key</label>
                  <input type="text" name="ANPRPublicKey" value={formData.ANPRPublicKey} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR Private Key</label>
                  <input type="password" name="ANPRPrivateKey" value={formData.ANPRPrivateKey} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR Source</label>
                  <input type="text" name="ANPRSource" value={formData.ANPRSource} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR API URL*</label>
                  <input
                    type="text"
                    name="ANPRAPIURL"
                    value={formData.ANPRAPIURL}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-md ${errors.ANPRAPIURL ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="Primary API endpoint"
                  />
                   {errors.ANPRAPIURL && <p className="mt-1 text-sm text-red-500">{errors.ANPRAPIURL}</p>}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">ANPR API URL 2 (Optional)</label>
                  <input type="text" name="ANPRAPIURL2" value={formData.ANPRAPIURL2} onChange={handleChange} className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Secondary/Backup API endpoint" />
                </div>
              </div>
            )}
          </div>

          {/* Status and Flags Section */}
          <div className="border p-4 rounded-md shadow-sm">
             <h3 className="text-lg font-medium text-gray-700 mb-4">Status and Flags</h3>
             <div className="flex flex-wrap gap-6">
                <div className="flex items-center space-x-2">
                    <label htmlFor="ActiveStatus" className="text-sm font-medium text-gray-700">Active Status</label>
                    <input
                        type="checkbox"
                        id="ActiveStatus"
                        name="ActiveStatus"
                        checked={formData.ActiveStatus === '1'}
                        onChange={handleChange}
                        className="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 cursor-pointer"
                    />
                    {/* Debug info */}
                    <span className="text-xs text-gray-500 ml-2">(Value: {formData.ActiveStatus})</span>
                </div>
                <div className="flex items-center space-x-2">
                    <label htmlFor="AllowBlacklistedVehicle" className="text-sm font-medium text-gray-700">Allow Blacklisted Vehicle</label>
                    <input
                        type="checkbox"
                        id="AllowBlacklistedVehicle"
                        name="AllowBlacklistedVehicle"
                        checked={formData.AllowBlacklistedVehicle === '1'}
                        onChange={handleChange}
                        className="h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 cursor-pointer"
                    />
                    {/* Debug info */}
                    <span className="text-xs text-gray-500 ml-2">(Value: {formData.AllowBlacklistedVehicle})</span>
                </div>
             </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400"
            >
              Cancel
            </button>
            <PermissionButton
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              requiredModule="ANPR"
              requiredPermissions={initialData ? ["Edit"] : ["Create"]}
              companyId={initialData?.CompanyID}
              plazaId={initialData?.PlazaID}
            >
              {initialData ? 'Update Configuration' : 'Create Configuration'}
            </PermissionButton>
          </div>
        </form>
      </div>
    </div>
  );
}