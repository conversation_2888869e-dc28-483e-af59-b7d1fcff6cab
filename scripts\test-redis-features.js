// scripts/test-redis-features.js
const axios = require('axios');

/**
 * ===============================================================================
 * # Redis Features Testing Script for PWVMS
 * ===============================================================================
 * 
 * This script tests all Redis-enabled features in your PWVMS application
 * and provides performance metrics and optimization recommendations.
 */

const BASE_URL = 'http://localhost:5000';

// Test configuration
const TEST_CONFIG = {
  // Test user credentials (you'll need to update these)
  testUser: {
    username: 'admin', // Update with your test user
    password: 'admin123' // Update with your test password
  },
  
  // Test parameters
  testParams: {
    dateRanges: ['today', 'yesterday', 'week', 'month'],
    iterations: 3, // Number of times to test each endpoint
    delayBetweenTests: 1000 // 1 second delay between tests
  }
};

class RedisFeatureTester {
  constructor() {
    this.authToken = null;
    this.testResults = {
      healthCheck: null,
      authentication: null,
      dashboardCaching: [],
      realtimeFeatures: [],
      sessionManagement: null,
      rateLimit: null,
      performance: {
        cacheHits: 0,
        cacheMisses: 0,
        avgResponseTime: 0,
        totalRequests: 0
      }
    };
  }

  /**
   * ===============================================================================
   * ## UTILITY METHODS
   * ===============================================================================
   */

  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async makeRequest(method, endpoint, data = null, headers = {}) {
    const startTime = Date.now();
    
    try {
      const config = {
        method,
        url: `${BASE_URL}${endpoint}`,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      };

      if (this.authToken) {
        config.headers.Authorization = `Bearer ${this.authToken}`;
      }

      if (data) {
        config.data = data;
      }

      const response = await axios(config);
      const responseTime = Date.now() - startTime;
      
      this.testResults.performance.totalRequests++;
      
      // Check if response was cached
      if (response.data.cached) {
        this.testResults.performance.cacheHits++;
      } else {
        this.testResults.performance.cacheMisses++;
      }

      return {
        success: true,
        data: response.data,
        responseTime,
        status: response.status,
        cached: response.data.cached || false
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.testResults.performance.totalRequests++;
      
      return {
        success: false,
        error: error.response?.data || error.message,
        responseTime,
        status: error.response?.status || 0
      };
    }
  }

  /**
   * ===============================================================================
   * ## TEST METHODS
   * ===============================================================================
   */

  /**
   * Test 1: Health Check - Redis Status
   */
  async testHealthCheck() {
    console.log('\n🔍 Testing Health Check & Redis Status...');
    
    const result = await this.makeRequest('GET', '/health-check');
    this.testResults.healthCheck = result;
    
    if (result.success) {
      const { redis, services } = result.data;
      console.log(`✅ Health Check: ${result.status}`);
      console.log(`✅ Redis Connected: ${redis.connected}`);
      console.log(`✅ Redis Status: ${redis.status}`);
      console.log(`✅ RedisService: ${services.redisService}`);
      console.log(`✅ RealtimeService: ${services.realtimeService}`);
      
      return redis.connected;
    } else {
      console.log(`❌ Health Check Failed: ${result.error}`);
      return false;
    }
  }

  /**
   * Test 2: Authentication & Session Management
   */
  async testAuthentication() {
    console.log('\n🔐 Testing Authentication & Session Management...');
    
    const loginResult = await this.makeRequest('POST', '/api/auth/login', {
      username: TEST_CONFIG.testUser.username,
      password: TEST_CONFIG.testUser.password
    });
    
    this.testResults.authentication = loginResult;
    
    if (loginResult.success && loginResult.data.token) {
      this.authToken = loginResult.data.token;
      console.log(`✅ Login Successful: ${loginResult.responseTime}ms`);
      console.log(`✅ Token Received: ${this.authToken.substring(0, 20)}...`);
      
      // Test session validation
      const sessionResult = await this.makeRequest('GET', '/api/auth/validate');
      if (sessionResult.success) {
        console.log(`✅ Session Validation: ${sessionResult.responseTime}ms`);
        return true;
      } else {
        console.log(`❌ Session Validation Failed: ${sessionResult.error}`);
        return false;
      }
    } else {
      console.log(`❌ Login Failed: ${loginResult.error}`);
      return false;
    }
  }

  /**
   * Test 3: Dashboard Caching Performance
   */
  async testDashboardCaching() {
    console.log('\n📊 Testing Dashboard Caching Performance...');
    
    if (!this.authToken) {
      console.log('❌ No auth token available for dashboard testing');
      return false;
    }

    for (const dateRange of TEST_CONFIG.testParams.dateRanges) {
      console.log(`\n  Testing ${dateRange} data...`);
      
      const testResults = [];
      
      for (let i = 0; i < TEST_CONFIG.testParams.iterations; i++) {
        const result = await this.makeRequest('GET', `/api/dashboard/summary?dateRange=${dateRange}`);
        testResults.push(result);
        
        if (result.success) {
          const cached = result.cached ? '🟢 CACHED' : '🔴 DB QUERY';
          console.log(`    Attempt ${i + 1}: ${result.responseTime}ms ${cached}`);
        } else {
          console.log(`    Attempt ${i + 1}: ❌ FAILED - ${result.error}`);
        }
        
        if (i < TEST_CONFIG.testParams.iterations - 1) {
          await this.delay(TEST_CONFIG.testParams.delayBetweenTests);
        }
      }
      
      this.testResults.dashboardCaching.push({
        dateRange,
        results: testResults,
        avgResponseTime: testResults.reduce((sum, r) => sum + r.responseTime, 0) / testResults.length,
        cacheHitRate: testResults.filter(r => r.cached).length / testResults.length * 100
      });
    }
    
    return true;
  }

  /**
   * Test 4: Rate Limiting
   */
  async testRateLimit() {
    console.log('\n🚦 Testing Rate Limiting...');
    
    const rapidRequests = [];
    const requestCount = 10;
    
    console.log(`  Making ${requestCount} rapid requests...`);
    
    for (let i = 0; i < requestCount; i++) {
      const result = await this.makeRequest('GET', '/health-check');
      rapidRequests.push(result);
      
      if (result.status === 429) {
        console.log(`    Request ${i + 1}: ⚠️ RATE LIMITED`);
      } else if (result.success) {
        console.log(`    Request ${i + 1}: ✅ SUCCESS (${result.responseTime}ms)`);
      } else {
        console.log(`    Request ${i + 1}: ❌ FAILED`);
      }
    }
    
    this.testResults.rateLimit = {
      totalRequests: requestCount,
      successfulRequests: rapidRequests.filter(r => r.success).length,
      rateLimitedRequests: rapidRequests.filter(r => r.status === 429).length,
      avgResponseTime: rapidRequests.reduce((sum, r) => sum + r.responseTime, 0) / rapidRequests.length
    };
    
    return true;
  }

  /**
   * Test 5: Real-time Features (if available)
   */
  async testRealtimeFeatures() {
    console.log('\n⚡ Testing Real-time Features...');
    
    // Test live data endpoints if they exist
    const liveDataEndpoints = [
      '/api/dashboard/live-data',
      '/api/parking/live-status',
      '/api/notifications/live'
    ];
    
    for (const endpoint of liveDataEndpoints) {
      const result = await this.makeRequest('GET', endpoint);
      
      if (result.success) {
        console.log(`✅ ${endpoint}: ${result.responseTime}ms`);
        this.testResults.realtimeFeatures.push({
          endpoint,
          success: true,
          responseTime: result.responseTime,
          cached: result.cached
        });
      } else if (result.status === 404) {
        console.log(`⚠️ ${endpoint}: Not implemented`);
      } else {
        console.log(`❌ ${endpoint}: ${result.error}`);
        this.testResults.realtimeFeatures.push({
          endpoint,
          success: false,
          error: result.error
        });
      }
    }
    
    return true;
  }

  /**
   * ===============================================================================
   * ## ANALYSIS & REPORTING
   * ===============================================================================
   */

  generateReport() {
    console.log('\n' + '='.repeat(80));
    console.log('📋 REDIS FEATURES TEST REPORT');
    console.log('='.repeat(80));
    
    // Overall Performance
    const { performance } = this.testResults;
    const cacheHitRate = performance.totalRequests > 0 
      ? (performance.cacheHits / performance.totalRequests * 100).toFixed(2)
      : 0;
    
    console.log('\n🚀 OVERALL PERFORMANCE:');
    console.log(`   Total Requests: ${performance.totalRequests}`);
    console.log(`   Cache Hits: ${performance.cacheHits}`);
    console.log(`   Cache Misses: ${performance.cacheMisses}`);
    console.log(`   Cache Hit Rate: ${cacheHitRate}%`);
    
    // Health Check Results
    console.log('\n🔍 HEALTH CHECK:');
    if (this.testResults.healthCheck?.success) {
      const redis = this.testResults.healthCheck.data.redis;
      console.log(`   ✅ Redis Status: ${redis.connected ? 'Connected' : 'Disconnected'}`);
      console.log(`   ✅ Response Time: ${this.testResults.healthCheck.responseTime}ms`);
    } else {
      console.log(`   ❌ Health Check Failed`);
    }
    
    // Dashboard Caching Results
    console.log('\n📊 DASHBOARD CACHING:');
    this.testResults.dashboardCaching.forEach(test => {
      console.log(`   ${test.dateRange.toUpperCase()}:`);
      console.log(`     Avg Response Time: ${test.avgResponseTime.toFixed(2)}ms`);
      console.log(`     Cache Hit Rate: ${test.cacheHitRate.toFixed(2)}%`);
    });
    
    // Rate Limiting Results
    if (this.testResults.rateLimit) {
      console.log('\n🚦 RATE LIMITING:');
      const rl = this.testResults.rateLimit;
      console.log(`   Total Requests: ${rl.totalRequests}`);
      console.log(`   Successful: ${rl.successfulRequests}`);
      console.log(`   Rate Limited: ${rl.rateLimitedRequests}`);
      console.log(`   Avg Response Time: ${rl.avgResponseTime.toFixed(2)}ms`);
    }
    
    // Optimization Recommendations
    console.log('\n💡 OPTIMIZATION RECOMMENDATIONS:');
    
    if (cacheHitRate < 50) {
      console.log('   ⚠️ Low cache hit rate - consider increasing TTL values');
    } else if (cacheHitRate > 80) {
      console.log('   ✅ Excellent cache hit rate - Redis is working optimally');
    }
    
    const avgDashboardTime = this.testResults.dashboardCaching.reduce((sum, test) => 
      sum + test.avgResponseTime, 0) / this.testResults.dashboardCaching.length;
    
    if (avgDashboardTime > 1000) {
      console.log('   ⚠️ Dashboard response times are high - consider query optimization');
    } else if (avgDashboardTime < 200) {
      console.log('   ✅ Dashboard response times are excellent');
    }
    
    console.log('\n' + '='.repeat(80));
  }

  /**
   * ===============================================================================
   * ## MAIN TEST RUNNER
   * ===============================================================================
   */

  async runAllTests() {
    console.log('🚀 Starting Redis Features Test Suite...');
    console.log('='.repeat(80));
    
    try {
      // Test 1: Health Check
      const healthOk = await this.testHealthCheck();
      if (!healthOk) {
        console.log('\n❌ Redis is not working properly. Please check your Redis installation.');
        return false;
      }
      
      // Test 2: Authentication
      const authOk = await this.testAuthentication();
      if (!authOk) {
        console.log('\n⚠️ Authentication failed. Some tests will be skipped.');
      }
      
      // Test 3: Dashboard Caching
      if (authOk) {
        await this.testDashboardCaching();
      }
      
      // Test 4: Rate Limiting
      await this.testRateLimit();
      
      // Test 5: Real-time Features
      if (authOk) {
        await this.testRealtimeFeatures();
      }
      
      // Generate Report
      this.generateReport();
      
      return true;
    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
      return false;
    }
  }
}

/**
 * ===============================================================================
 * ## SCRIPT EXECUTION
 * ===============================================================================
 */

async function main() {
  const tester = new RedisFeatureTester();
  
  console.log('📋 Redis Features Test Configuration:');
  console.log(`   Base URL: ${BASE_URL}`);
  console.log(`   Test User: ${TEST_CONFIG.testUser.username}`);
  console.log(`   Date Ranges: ${TEST_CONFIG.testParams.dateRanges.join(', ')}`);
  console.log(`   Iterations per test: ${TEST_CONFIG.testParams.iterations}`);
  
  const success = await tester.runAllTests();
  
  if (success) {
    console.log('\n✅ All tests completed successfully!');
    process.exit(0);
  } else {
    console.log('\n❌ Some tests failed. Please check the output above.');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
}

module.exports = RedisFeatureTester;