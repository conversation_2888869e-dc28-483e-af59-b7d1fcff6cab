require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function checkDatabaseSchema() {
  try {
    console.log('=== DATABASE SCHEMA CHECK ===');
    
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected to database successfully!');

    // Check Users table structure
    console.log('\n=== USERS TABLE STRUCTURE ===');
    const usersSchemaQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Users'
      ORDER BY ORDINAL_POSITION
    `;
    
    const usersSchemaResult = await sql.query(usersSchemaQuery);
    console.log('Users table columns:');
    usersSchemaResult.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
    });

    // Check actual Users table data
    console.log('\n=== USERS TABLE DATA ===');
    const usersDataQuery = `SELECT TOP 5 * FROM Users`;
    const usersDataResult = await sql.query(usersDataQuery);
    
    if (usersDataResult.recordset.length > 0) {
      console.log('Sample users data:');
      console.log('Columns:', Object.keys(usersDataResult.recordset[0]).join(', '));
      usersDataResult.recordset.forEach((user, index) => {
        console.log(`  User ${index + 1}:`, JSON.stringify(user, null, 2));
      });
    }

    // Check Company table
    console.log('\n=== COMPANY TABLE STRUCTURE ===');
    const companySchemaQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Company'
      ORDER BY ORDINAL_POSITION
    `;
    
    const companySchemaResult = await sql.query(companySchemaQuery);
    console.log('Company table columns:');
    companySchemaResult.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
    });

    // Check Plaza table
    console.log('\n=== PLAZA TABLE STRUCTURE ===');
    const plazaSchemaQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Plaza'
      ORDER BY ORDINAL_POSITION
    `;
    
    const plazaSchemaResult = await sql.query(plazaSchemaQuery);
    console.log('Plaza table columns:');
    plazaSchemaResult.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
    });

    // Check UserCompany table
    console.log('\n=== USERCOMPANY TABLE STRUCTURE ===');
    const userCompanySchemaQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'UserCompany'
      ORDER BY ORDINAL_POSITION
    `;
    
    const userCompanySchemaResult = await sql.query(userCompanySchemaQuery);
    if (userCompanySchemaResult.recordset.length > 0) {
      console.log('UserCompany table columns:');
      userCompanySchemaResult.recordset.forEach(col => {
        console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
      });
    } else {
      console.log('UserCompany table not found');
    }

    // Check UserPlaza table
    console.log('\n=== USERPLAZA TABLE STRUCTURE ===');
    const userPlazaSchemaQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'UserPlaza'
      ORDER BY ORDINAL_POSITION
    `;
    
    const userPlazaSchemaResult = await sql.query(userPlazaSchemaQuery);
    if (userPlazaSchemaResult.recordset.length > 0) {
      console.log('UserPlaza table columns:');
      userPlazaSchemaResult.recordset.forEach(col => {
        console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
      });
    } else {
      console.log('UserPlaza table not found');
    }

    // Check main parking data table structure
    console.log('\n=== PARKING DATA TABLE STRUCTURE ===');
    const parkingDataSchemaQuery = `
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblParkwiz_Parking_Data'
      ORDER BY ORDINAL_POSITION
    `;
    
    const parkingDataSchemaResult = await sql.query(parkingDataSchemaQuery);
    console.log('tblParkwiz_Parking_Data table columns:');
    parkingDataSchemaResult.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME}: ${col.DATA_TYPE} (${col.IS_NULLABLE === 'YES' ? 'nullable' : 'not null'})`);
    });

    await sql.close();
    console.log('\nDatabase connection closed.');
    
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

checkDatabaseSchema();