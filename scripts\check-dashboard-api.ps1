# check-dashboard-api.ps1 - PowerShell script to check dashboard API endpoints

# First, we need to authenticate to get a token
$loginUrl = "http://localhost:5000/api/auth/login"
$credentials = @{
    email = "<EMAIL>"  # Replace with actual admin credentials
    password = "admin123"        # Replace with actual password
} | ConvertTo-Json

Write-Host "Authenticating to API..." -ForegroundColor Cyan
try {
    $authResponse = Invoke-RestMethod -Uri $loginUrl -Method Post -Body $credentials -ContentType "application/json"
    $token = $authResponse.token
    Write-Host "Authentication successful!" -ForegroundColor Green
}
catch {
    Write-Host "Authentication failed: $_" -ForegroundColor Red
    exit
}

# Set up headers with the token
$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

# Dashboard API endpoints to check
$endpoints = @{
    "Dashboard Summary" = "http://localhost:5000/api/dashboard/summary?dateRange=today"
    "Revenue by Payment Method" = "http://localhost:5000/api/dashboard/revenue/by-payment-method?dateRange=today"
    "Recent Transactions" = "http://localhost:5000/api/dashboard/transactions/recent?limit=5"
    "Peak Hours Data" = "http://localhost:5000/api/dashboard/transactions/peak-hours?dateRange=today"
    "Revenue by Plaza" = "http://localhost:5000/api/dashboard/revenue/by-plaza?dateRange=today"
}

# Check each endpoint
foreach ($endpointName in $endpoints.Keys) {
    $url = $endpoints[$endpointName]
    Write-Host "`n--- Checking $endpointName ---" -ForegroundColor Cyan
    Write-Host "URL: $url"
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Get -Headers $headers
        
        # Display response summary
        Write-Host "Status: Success" -ForegroundColor Green
        
        if ($response.data -is [array]) {
            Write-Host "Data count: $($response.data.Count) items"
            
            # Show sample of data (first item)
            if ($response.data.Count -gt 0) {
                Write-Host "Sample data (first item):" -ForegroundColor Yellow
                $response.data[0] | ConvertTo-Json -Depth 3
            }
        }
        else {
            Write-Host "Data:" -ForegroundColor Yellow
            $response.data | ConvertTo-Json -Depth 3
        }
    }
    catch {
        Write-Host "Error: $_" -ForegroundColor Red
    }
}