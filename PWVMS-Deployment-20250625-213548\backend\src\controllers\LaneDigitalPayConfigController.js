const db = require('../config/database');

/**
 * Lane Digital Pay Configuration Controller - Handles operations for tblLaneDigitalPayConfiguration
 */
const laneDigitalPayConfigController = {
  /**
   * Get all lane digital pay configurations
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getAllConfigurations: async (req, res) => {
    try {
      // Base query with joins - return only active configurations
      let query = `
        SELECT
          c.*,
          p.PlazaName,
          cm.CompanyName,
          l.LaneNumber as LaneNumberDetail
        FROM tblLaneDigitalPayConfiguration c
        LEFT JOIN Plaza p ON c.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster cm ON c.CompanyID = cm.Id
        LEFT JOIN tblLaneDetails l ON c.LaneID = l.LaneID
        WHERE c.ActiveStatus = 'Y'
      `;

      console.log('Digital Pay - Getting only active configurations');

      const queryParams = {};

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see configurations from companies they have access to
          query += ` AND c.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see configurations from plazas they are assigned to
          query += ` AND c.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Add ordering
      query += ` ORDER BY c.ConfigLaneID DESC`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Helper function to normalize boolean values
      const normalizeBoolean = (value) => {
        console.log(`Digital Pay - Normalizing boolean value: ${value}, type: ${typeof value}`);

        // Special handling for string values with spaces (SQL Server char fields can have trailing spaces)
        if (typeof value === 'string') {
          const trimmedValue = value.trim();
          if (trimmedValue === 'Y') {
            console.log(`Digital Pay - Trimmed value '${value}' normalized to '1'`);
            return '1';
          }
          if (trimmedValue === 'N') {
            console.log(`Digital Pay - Trimmed value '${value}' normalized to '0'`);
            return '0';
          }
        }

        // Handle various true values
        if (value === 'Y' || value === 1 || value === true || value === '1' || value === 'true' || value === 'yes') {
          console.log(`Digital Pay - Value ${value} normalized to '1'`);
          return '1';
        }

        // Handle various false values
        if (value === 'N' || value === 0 || value === false || value === '0' || value === 'false' || value === 'no' || value === null || value === undefined) {
          console.log(`Digital Pay - Value ${value} normalized to '0'`);
          return '0';
        }

        // For any other case, try to parse the value
        try {
          // If it's a string that can be parsed as a number
          const numValue = Number(value);
          if (!isNaN(numValue)) {
            const result = numValue > 0 ? '1' : '0';
            console.log(`Digital Pay - Parsed numeric value ${value} to ${result}`);
            return result;
          }
        } catch (e) {
          console.error(`Error parsing value ${value}:`, e);
        }

        console.log(`Digital Pay - Defaulting value ${value} to '0'`);
        return '0'; // Default to '0' for any other value
      };

      // Normalize ActiveStatus and toggle fields to ensure consistent format for frontend
      const normalizedConfigurations = result.recordset.map(config => {
        // Log the raw values from database for debugging
        console.log(`Digital Pay Config ID ${config.ConfigLaneID} - Raw values from DB:`, {
          EnableCardPayment: config.EnableCardPayment,
          EnableUPIPhonePe: config.EnableUPIPhonePe,
          EnableSendSMS: config.EnableSendSMS,
          ActiveStatus: config.ActiveStatus,
          AllowBlacklistedVehicle: config.AllowBlacklistedVehicle
        });

        const normalizedConfig = {
          ...config,
          // Convert 'Y'/'N' to '1'/'0' for consistency with frontend
          ActiveStatus: normalizeBoolean(config.ActiveStatus),
          EnableCardPayment: normalizeBoolean(config.EnableCardPayment),
          EnableUPIPhonePe: normalizeBoolean(config.EnableUPIPhonePe),
          EnableSendSMS: normalizeBoolean(config.EnableSendSMS),
          AllowBlacklistedVehicle: normalizeBoolean(config.AllowBlacklistedVehicle)
        };

        // Log the normalized values
        console.log(`Digital Pay Config ID ${config.ConfigLaneID} - Normalized values:`, {
          EnableCardPayment: normalizedConfig.EnableCardPayment,
          EnableUPIPhonePe: normalizedConfig.EnableUPIPhonePe,
          EnableSendSMS: normalizedConfig.EnableSendSMS,
          ActiveStatus: normalizedConfig.ActiveStatus,
          AllowBlacklistedVehicle: normalizedConfig.AllowBlacklistedVehicle
        });

        return normalizedConfig;
      });

      res.json({ success: true, configurations: normalizedConfigurations });
    } catch (error) {
      console.error('Get all lane digital pay configurations failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lane digital pay configurations',
        details: error.message
      });
    }
  },

  /**
   * Get lane digital pay configuration by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getConfigurationById: async (req, res) => {
    try {
      const { id } = req.params;

      // Base query with joins - only return active configurations
      let query = `
        SELECT
          c.*,
          p.PlazaName,
          cm.CompanyName,
          l.LaneNumber as LaneNumberDetail
        FROM tblLaneDigitalPayConfiguration c
        LEFT JOIN Plaza p ON c.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster cm ON c.CompanyID = cm.Id
        LEFT JOIN tblLaneDetails l ON c.LaneID = l.LaneID
        WHERE c.ConfigLaneID = @id AND c.ActiveStatus = 'Y'
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see configurations from companies they have access to
          query += ` AND c.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see configurations from plazas they are assigned to
          query += ` AND c.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane digital pay configuration not found or you do not have access to it'
        });
      }

      // Normalize ActiveStatus and toggle fields to ensure consistent format for frontend
      const normalizedConfiguration = {
        ...result.recordset[0],
        // Convert 'Y'/'N' to '1'/'0' for consistency with frontend
        ActiveStatus: result.recordset[0].ActiveStatus === 'Y' ? '1' : '0',
        EnableCardPayment: result.recordset[0].EnableCardPayment === 'Y' || result.recordset[0].EnableCardPayment === '1' ? '1' : '0',
        EnableUPIPhonePe: result.recordset[0].EnableUPIPhonePe === 'Y' || result.recordset[0].EnableUPIPhonePe === '1' ? '1' : '0',
        EnableSendSMS: result.recordset[0].EnableSendSMS === 'Y' || result.recordset[0].EnableSendSMS === '1' ? '1' : '0',
        AllowBlacklistedVehicle: result.recordset[0].AllowBlacklistedVehicle === 'Y' || result.recordset[0].AllowBlacklistedVehicle === '1' ? '1' : '0'
      };

      res.json({ success: true, configuration: normalizedConfiguration });
    } catch (error) {
      console.error('Get lane digital pay configuration by ID failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lane digital pay configuration',
        details: error.message
      });
    }
  },

  /**
   * Get lane digital pay configurations by Lane ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getConfigurationsByLane: async (req, res) => {
    try {
      const { laneId } = req.params;

      // Base query with joins - return only active configurations
      let query = `
        SELECT
          c.*,
          p.PlazaName,
          cm.CompanyName,
          l.LaneNumber as LaneNumberDetail
        FROM tblLaneDigitalPayConfiguration c
        LEFT JOIN Plaza p ON c.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster cm ON c.CompanyID = cm.Id
        LEFT JOIN tblLaneDetails l ON c.LaneID = l.LaneID
        WHERE c.LaneID = @laneId AND c.ActiveStatus = 'Y'
      `;

      console.log('Digital Pay - Getting only active configurations for lane');

      const queryParams = { laneId };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only see configurations from companies they have access to
          query += ` AND c.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can only see configurations from plazas they are assigned to
          query += ` AND c.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      // Add ordering
      query += ` ORDER BY c.ConfigLaneID DESC`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Normalize ActiveStatus and toggle fields to ensure consistent format for frontend
      const normalizedConfigurations = result.recordset.map(config => ({
        ...config,
        // Convert 'Y'/'N' to '1'/'0' for consistency with frontend
        ActiveStatus: config.ActiveStatus === 'Y' ? '1' : '0',
        EnableCardPayment: config.EnableCardPayment === 'Y' || config.EnableCardPayment === '1' ? '1' : '0',
        EnableUPIPhonePe: config.EnableUPIPhonePe === 'Y' || config.EnableUPIPhonePe === '1' ? '1' : '0',
        EnableSendSMS: config.EnableSendSMS === 'Y' || config.EnableSendSMS === '1' ? '1' : '0',
        AllowBlacklistedVehicle: config.AllowBlacklistedVehicle === 'Y' || config.AllowBlacklistedVehicle === '1' ? '1' : '0'
      }));

      res.json({ success: true, configurations: normalizedConfigurations });
    } catch (error) {
      console.error('Get lane digital pay configurations by lane failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lane digital pay configurations for lane',
        details: error.message
      });
    }
  },

  /**
   * Get lane digital pay configurations by Plaza ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  getConfigurationsByPlaza: async (req, res) => {
    try {
      const { plazaId } = req.params;

      // Check if user has access to this plaza
      if (req.user && req.user.role !== 'SuperAdmin') {
        let plazaAccessQuery = '';
        const plazaAccessParams = { plazaId, userId: req.user.id };

        if (req.user.role === 'CompanyAdmin') {
          plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1 AND p.IsActive = 1
          `;
        } else if (req.user.role === 'PlazaManager') {
          plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM UserPlaza
            WHERE PlazaId = @plazaId AND UserId = @userId AND IsActive = 1
          `;
        }

        if (plazaAccessQuery) {
          const plazaAccessResult = await db.query(plazaAccessQuery, plazaAccessParams);
          if (plazaAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have access to this plaza'
            });
          }
        }
      }

      // Base query with joins - return only active configurations
      let query = `
        SELECT
          c.*,
          p.PlazaName,
          cm.CompanyName,
          l.LaneNumber as LaneNumberDetail
        FROM tblLaneDigitalPayConfiguration c
        LEFT JOIN Plaza p ON c.PlazaID = p.Id
        LEFT JOIN tblCompanyMaster cm ON c.CompanyID = cm.Id
        LEFT JOIN tblLaneDetails l ON c.LaneID = l.LaneID
        WHERE c.PlazaID = @plazaId AND c.ActiveStatus = 'Y'
      `;

      console.log('Digital Pay - Getting only active configurations for plaza');

      const queryParams = { plazaId };

      // Add ordering
      query += ` ORDER BY c.ConfigLaneID DESC`;

      // Execute the query with parameters
      const result = await db.query(query, queryParams);

      // Normalize ActiveStatus and toggle fields to ensure consistent format for frontend
      const normalizedConfigurations = result.recordset.map(config => ({
        ...config,
        // Convert 'Y'/'N' to '1'/'0' for consistency with frontend
        ActiveStatus: config.ActiveStatus === 'Y' ? '1' : '0',
        EnableCardPayment: config.EnableCardPayment === 'Y' || config.EnableCardPayment === '1' ? '1' : '0',
        EnableUPIPhonePe: config.EnableUPIPhonePe === 'Y' || config.EnableUPIPhonePe === '1' ? '1' : '0',
        EnableSendSMS: config.EnableSendSMS === 'Y' || config.EnableSendSMS === '1' ? '1' : '0',
        AllowBlacklistedVehicle: config.AllowBlacklistedVehicle === 'Y' || config.AllowBlacklistedVehicle === '1' ? '1' : '0'
      }));

      res.json({ success: true, configurations: normalizedConfigurations });
    } catch (error) {
      console.error('Get lane digital pay configurations by plaza failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch lane digital pay configurations for plaza',
        details: error.message
      });
    }
  },

  /**
   * Create a new lane digital pay configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  createConfiguration: async (req, res) => {
    try {
      const {
        PlazaID,
        CompanyID,
        LaneID,
        LaneNumber,
        EnableCardPayment,
        CardReaderPGProvider,
        CardReaderPort,
        CardReaderEnvType,
        CardReaderUserName,
        CardReaderPassword,
        CardReaderAppKey,
        CardPayAPIURL,
        CardReaderDeviceID,
        CardReaderMID,
        EnableUPIPhonePe,
        UPIPGProvider,
        PhonePeMID,
        PhonePeKeyID,
        PhonePeIndexID,
        PhonePeAPI,
        EnableSendSMS,
        SMSSenderName,
        SMSAPI,
        UpdatedBy,
        ActiveStatus,
        CardPaymentDeviceModel,
        AllowBlacklistedVehicle
      } = req.body;

      // Validate required fields
      if (!PlazaID || !CompanyID || !LaneID) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing',
          requiredFields: ['PlazaID', 'CompanyID', 'LaneID']
        });
      }

      // Check user permissions based on role
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // Check if CompanyAdmin has access to this company
          const companyAccessQuery = `
            SELECT COUNT(*) as count
            FROM UserCompany
            WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
          `;

          const companyAccessResult = await db.query(companyAccessQuery, {
            userId: req.user.id,
            companyId: CompanyID
          });

          if (companyAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a configuration for this company'
            });
          }

          // Check if CompanyAdmin has access to this plaza
          const plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE uc.UserId = @userId AND p.Id = @plazaId AND uc.IsActive = 1 AND p.IsActive = 1
          `;

          const plazaAccessResult = await db.query(plazaAccessQuery, {
            userId: req.user.id,
            plazaId: PlazaID
          });

          if (plazaAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a configuration for this plaza'
            });
          }

          // Check if lane belongs to the plaza
          const laneAccessQuery = `
            SELECT COUNT(*) as count
            FROM tblLaneDetails
            WHERE LaneID = @laneId AND PlazaID = @plazaId
          `;

          const laneAccessResult = await db.query(laneAccessQuery, {
            laneId: LaneID,
            plazaId: PlazaID
          });

          if (laneAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'The specified lane does not belong to the specified plaza'
            });
          }
        } else if (req.user.role === 'PlazaManager') {
          // Check if PlazaManager has access to this plaza
          const plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM UserPlaza
            WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
          `;

          const plazaAccessResult = await db.query(plazaAccessQuery, {
            userId: req.user.id,
            plazaId: PlazaID
          });

          if (plazaAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to create a configuration for this plaza'
            });
          }
        }
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : (UpdatedBy || 1);

      const result = await db.query(`
        INSERT INTO tblLaneDigitalPayConfiguration (
          PlazaID,
          CompanyID,
          LaneID,
          LaneNumber,
          EnableCardPayment,
          CardReaderPGProvider,
          CardReaderPort,
          CardReaderEnvType,
          CardReaderUserName,
          CardReaderPassword,
          CardReaderAppKey,
          CardPayAPIURL,
          CardReaderDeviceID,
          CardReaderMID,
          EnableUPIPhonePe,
          UPIPGProvider,
          PhonePeMID,
          PhonePeKeyID,
          PhonePeIndexID,
          PhonePeAPI,
          EnableSendSMS,
          SMSSenderName,
          SMSAPI,
          UpdatedBy,
          UpdatedDateTime,
          ActiveStatus,
          CardPaymentDeviceModel,
          AllowBlacklistedVehicle
        ) VALUES (
          @PlazaID,
          @CompanyID,
          @LaneID,
          @LaneNumber,
          @EnableCardPayment,
          @CardReaderPGProvider,
          @CardReaderPort,
          @CardReaderEnvType,
          @CardReaderUserName,
          @CardReaderPassword,
          @CardReaderAppKey,
          @CardPayAPIURL,
          @CardReaderDeviceID,
          @CardReaderMID,
          @EnableUPIPhonePe,
          @UPIPGProvider,
          @PhonePeMID,
          @PhonePeKeyID,
          @PhonePeIndexID,
          @PhonePeAPI,
          @EnableSendSMS,
          @SMSSenderName,
          @SMSAPI,
          @UpdatedBy,
          GETDATE(),
          @ActiveStatus,
          @CardPaymentDeviceModel,
          @AllowBlacklistedVehicle
        )
      `, {
        PlazaID,
        CompanyID,
        LaneID,
        LaneNumber: LaneNumber || null,
        EnableCardPayment: EnableCardPayment === '0' || EnableCardPayment === 0 || EnableCardPayment === false || EnableCardPayment === 'false' || EnableCardPayment === 'N' ? 'N' : 'Y',
        CardReaderPGProvider: CardReaderPGProvider || null,
        CardReaderPort: CardReaderPort || null,
        CardReaderEnvType: CardReaderEnvType || null,
        CardReaderUserName: CardReaderUserName || null,
        CardReaderPassword: CardReaderPassword || null,
        CardReaderAppKey: CardReaderAppKey || null,
        CardPayAPIURL: CardPayAPIURL || null,
        CardReaderDeviceID: CardReaderDeviceID || null,
        CardReaderMID: CardReaderMID || null,
        EnableUPIPhonePe: EnableUPIPhonePe === '0' || EnableUPIPhonePe === 0 || EnableUPIPhonePe === false || EnableUPIPhonePe === 'false' || EnableUPIPhonePe === 'N' ? 'N' : 'Y',
        UPIPGProvider: UPIPGProvider || null,
        PhonePeMID: PhonePeMID || null,
        PhonePeKeyID: PhonePeKeyID || null,
        PhonePeIndexID: PhonePeIndexID || null,
        PhonePeAPI: PhonePeAPI || null,
        EnableSendSMS: EnableSendSMS === '0' || EnableSendSMS === 0 || EnableSendSMS === false || EnableSendSMS === 'false' || EnableSendSMS === 'N' ? 'N' : 'Y',
        SMSSenderName: SMSSenderName || null,
        SMSAPI: SMSAPI || null,
        updatedBy,
        ActiveStatus: ActiveStatus !== undefined ? (ActiveStatus === '1' || ActiveStatus === 1 || ActiveStatus === true || ActiveStatus === 'true' ? 'Y' : 'N') : 'Y',
        CardPaymentDeviceModel: CardPaymentDeviceModel || null,
        AllowBlacklistedVehicle: AllowBlacklistedVehicle === '0' || AllowBlacklistedVehicle === 0 || AllowBlacklistedVehicle === false || AllowBlacklistedVehicle === 'false' || AllowBlacklistedVehicle === 'N' ? 'N' : 'Y'
      });

      res.status(201).json({
        success: true,
        message: 'Lane digital pay configuration created successfully',
        result
      });
    } catch (error) {
      console.error('Create lane digital pay configuration failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to create lane digital pay configuration',
        details: error.message
      });
    }
  },

  /**
   * Update an existing lane digital pay configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  updateConfiguration: async (req, res) => {
    try {
      const { id } = req.params;
      const {
        PlazaID,
        CompanyID,
        LaneID,
        LaneNumber,
        EnableCardPayment,
        CardReaderPGProvider,
        CardReaderPort,
        CardReaderEnvType,
        CardReaderUserName,
        CardReaderPassword,
        CardReaderAppKey,
        CardPayAPIURL,
        CardReaderDeviceID,
        CardReaderMID,
        EnableUPIPhonePe,
        UPIPGProvider,
        PhonePeMID,
        PhonePeKeyID,
        PhonePeIndexID,
        PhonePeAPI,
        EnableSendSMS,
        SMSSenderName,
        SMSAPI,
        UpdatedBy,
        ActiveStatus,
        CardPaymentDeviceModel,
        AllowBlacklistedVehicle
      } = req.body;

      // Validate required fields
      if (!PlazaID || !CompanyID || !LaneID) {
        return res.status(400).json({
          success: false,
          error: 'Required fields missing',
          requiredFields: ['PlazaID', 'CompanyID', 'LaneID']
        });
      }

      // Check if configuration exists and user has access to it
      let query = `
        SELECT c.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDigitalPayConfiguration c
        JOIN Plaza p ON c.PlazaID = p.Id
        WHERE c.ConfigLaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only update configurations from companies they have access to
          query += ` AND c.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager cannot update configurations
          return res.status(403).json({
            success: false,
            message: 'Plaza Managers cannot update digital pay configurations'
          });
        }
      }

      const checkResult = await db.query(query, queryParams);

      if (checkResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane digital pay configuration not found or you do not have permission to update it'
        });
      }

      const existingConfig = checkResult.recordset[0];

      // Check user permissions for the new company/plaza if they're being changed
      if (req.user && req.user.role !== 'SuperAdmin') {
        // If company is being changed, check if user has access to the new company
        if (CompanyID !== existingConfig.CompanyID) {
          const companyAccessQuery = `
            SELECT COUNT(*) as count
            FROM UserCompany
            WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
          `;

          const companyAccessResult = await db.query(companyAccessQuery, {
            userId: req.user.id,
            companyId: CompanyID
          });

          if (companyAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to move this configuration to the specified company'
            });
          }
        }

        // If plaza is being changed, check if user has access to the new plaza
        if (PlazaID !== existingConfig.PlazaID) {
          const plazaAccessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE uc.UserId = @userId AND p.Id = @plazaId AND uc.IsActive = 1 AND p.IsActive = 1
          `;

          const plazaAccessResult = await db.query(plazaAccessQuery, {
            userId: req.user.id,
            plazaId: PlazaID
          });

          if (plazaAccessResult.recordset[0].count === 0) {
            return res.status(403).json({
              success: false,
              message: 'You do not have permission to move this configuration to the specified plaza'
            });
          }
        }

        // Check if lane belongs to the plaza
        const laneAccessQuery = `
          SELECT COUNT(*) as count
          FROM tblLaneDetails
          WHERE LaneID = @laneId AND PlazaID = @plazaId
        `;

        const laneAccessResult = await db.query(laneAccessQuery, {
          laneId: LaneID,
          plazaId: PlazaID
        });

        if (laneAccessResult.recordset[0].count === 0) {
          return res.status(403).json({
            success: false,
            message: 'The specified lane does not belong to the specified plaza'
          });
        }
      }

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : (UpdatedBy || 1);

      // Log the incoming boolean values for debugging
      console.log(`Digital Pay Config ID ${id} - Update request values:`, {
        EnableCardPayment,
        EnableUPIPhonePe,
        EnableSendSMS,
        ActiveStatus,
        AllowBlacklistedVehicle
      });

      // Helper function to convert boolean values to 'Y'/'N' for database
      const toDbBoolean = (value) => {
        // Log the raw value for debugging
        console.log(`Converting value to DB boolean: ${value}, type: ${typeof value}`);

        // Handle various true values
        if (value === '1' || value === 1 || value === true || value === 'true' || value === 'Y' || value === 'y' || value === 'yes') {
          console.log(`Value ${value} converted to 'Y'`);
          return 'Y';
        }

        // Handle various false values
        console.log(`Value ${value} converted to 'N'`);
        return 'N';
      };

      // Convert boolean values to database format
      const dbEnableCardPayment = toDbBoolean(EnableCardPayment);
      const dbEnableUPIPhonePe = toDbBoolean(EnableUPIPhonePe);
      const dbEnableSendSMS = toDbBoolean(EnableSendSMS);
      const dbActiveStatus = toDbBoolean(ActiveStatus);
      const dbAllowBlacklistedVehicle = toDbBoolean(AllowBlacklistedVehicle);

      // Log the converted values
      console.log(`Digital Pay Config ID ${id} - Converted DB values:`, {
        dbEnableCardPayment,
        dbEnableUPIPhonePe,
        dbEnableSendSMS,
        dbActiveStatus,
        dbAllowBlacklistedVehicle
      });

      const result = await db.query(`
        UPDATE tblLaneDigitalPayConfiguration SET
          PlazaID = @PlazaID,
          CompanyID = @CompanyID,
          LaneID = @LaneID,
          LaneNumber = @LaneNumber,
          EnableCardPayment = @EnableCardPayment,
          CardReaderPGProvider = @CardReaderPGProvider,
          CardReaderPort = @CardReaderPort,
          CardReaderEnvType = @CardReaderEnvType,
          CardReaderUserName = @CardReaderUserName,
          CardReaderPassword = @CardReaderPassword,
          CardReaderAppKey = @CardReaderAppKey,
          CardPayAPIURL = @CardPayAPIURL,
          CardReaderDeviceID = @CardReaderDeviceID,
          CardReaderMID = @CardReaderMID,
          EnableUPIPhonePe = @EnableUPIPhonePe,
          UPIPGProvider = @UPIPGProvider,
          PhonePeMID = @PhonePeMID,
          PhonePeKeyID = @PhonePeKeyID,
          PhonePeIndexID = @PhonePeIndexID,
          PhonePeAPI = @PhonePeAPI,
          EnableSendSMS = @EnableSendSMS,
          SMSSenderName = @SMSSenderName,
          SMSAPI = @SMSAPI,
          UpdatedBy = @updatedBy,
          UpdatedDateTime = GETDATE(),
          ActiveStatus = @ActiveStatus,
          CardPaymentDeviceModel = @CardPaymentDeviceModel,
          AllowBlacklistedVehicle = @AllowBlacklistedVehicle
        WHERE ConfigLaneID = @id
      `, {
        id,
        PlazaID,
        CompanyID,
        LaneID,
        LaneNumber: LaneNumber || null,
        EnableCardPayment: dbEnableCardPayment,
        CardReaderPGProvider: CardReaderPGProvider || null,
        CardReaderPort: CardReaderPort || null,
        CardReaderEnvType: CardReaderEnvType || null,
        CardReaderUserName: CardReaderUserName || null,
        CardReaderPassword: CardReaderPassword || null,
        CardReaderAppKey: CardReaderAppKey || null,
        CardPayAPIURL: CardPayAPIURL || null,
        CardReaderDeviceID: CardReaderDeviceID || null,
        CardReaderMID: CardReaderMID || null,
        EnableUPIPhonePe: dbEnableUPIPhonePe,
        UPIPGProvider: UPIPGProvider || null,
        PhonePeMID: PhonePeMID || null,
        PhonePeKeyID: PhonePeKeyID || null,
        PhonePeIndexID: PhonePeIndexID || null,
        PhonePeAPI: PhonePeAPI || null,
        EnableSendSMS: dbEnableSendSMS,
        SMSSenderName: SMSSenderName || null,
        SMSAPI: SMSAPI || null,
        UpdatedBy: updatedBy,
        ActiveStatus: dbActiveStatus,
        CardPaymentDeviceModel: CardPaymentDeviceModel || null,
        AllowBlacklistedVehicle: dbAllowBlacklistedVehicle
      });

      // Execute direct SQL queries to update all boolean fields
      // This is a workaround to ensure the values are properly updated
      const directUpdateQueries = [
        `UPDATE tblLaneDigitalPayConfiguration SET EnableCardPayment = '${dbEnableCardPayment}' WHERE ConfigLaneID = ${id}`,
        `UPDATE tblLaneDigitalPayConfiguration SET EnableUPIPhonePe = '${dbEnableUPIPhonePe}' WHERE ConfigLaneID = ${id}`,
        `UPDATE tblLaneDigitalPayConfiguration SET EnableSendSMS = '${dbEnableSendSMS}' WHERE ConfigLaneID = ${id}`,
        `UPDATE tblLaneDigitalPayConfiguration SET ActiveStatus = '${dbActiveStatus}' WHERE ConfigLaneID = ${id}`,
        `UPDATE tblLaneDigitalPayConfiguration SET AllowBlacklistedVehicle = '${dbAllowBlacklistedVehicle}' WHERE ConfigLaneID = ${id}`
      ];

      // Execute each query
      for (const query of directUpdateQueries) {
        console.log(`Digital Pay - Executing direct SQL update: ${query}`);
        await db.query(query);
      }

      // Verify the update by directly querying the database
      const verifyResult = await db.query(`
        SELECT ConfigLaneID, EnableCardPayment, EnableUPIPhonePe, EnableSendSMS, ActiveStatus, AllowBlacklistedVehicle
        FROM tblLaneDigitalPayConfiguration
        WHERE ConfigLaneID = @id
      `, { id });

      if (verifyResult.recordset.length > 0) {
        console.log(`Digital Pay Config ID ${id} - Values after update:`, {
          EnableCardPayment: verifyResult.recordset[0].EnableCardPayment,
          EnableUPIPhonePe: verifyResult.recordset[0].EnableUPIPhonePe,
          EnableSendSMS: verifyResult.recordset[0].EnableSendSMS,
          ActiveStatus: verifyResult.recordset[0].ActiveStatus,
          AllowBlacklistedVehicle: verifyResult.recordset[0].AllowBlacklistedVehicle
        });
      }

      res.json({
        success: true,
        message: 'Lane digital pay configuration updated successfully',
        result
      });
    } catch (error) {
      console.error('Update lane digital pay configuration failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to update lane digital pay configuration',
        details: error.message
      });
    }
  },

  /**
   * Delete a lane digital pay configuration
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  deleteConfiguration: async (req, res) => {
    try {
      const { id } = req.params;

      // Check if configuration exists and user has access to it
      let query = `
        SELECT c.*, p.CompanyId as PlazaCompanyId
        FROM tblLaneDigitalPayConfiguration c
        JOIN Plaza p ON c.PlazaID = p.Id
        WHERE c.ConfigLaneID = @id
      `;

      const queryParams = { id };

      // Apply role-based access control filters
      if (req.user && req.user.role !== 'SuperAdmin') {
        if (req.user.role === 'CompanyAdmin') {
          // CompanyAdmin can only delete configurations from companies they have access to
          query += ` AND c.CompanyID IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        } else if (req.user.role === 'PlazaManager') {
          // PlazaManager can delete configurations for their plazas
          query += ` AND c.PlazaID IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
          queryParams.userId = req.user.id;
        }
      }

      const checkResult = await db.query(query, queryParams);

      if (checkResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane digital pay configuration not found or you do not have permission to delete it'
        });
      }

      // Set UpdatedBy to current user ID for audit trail
      const updatedBy = req.user ? req.user.id : 1;

      // Soft delete by setting ActiveStatus to 'N' instead of hard delete
      await db.query(`
        UPDATE tblLaneDigitalPayConfiguration
        SET ActiveStatus = 'N',
            UpdatedBy = @updatedBy,
            UpdatedDateTime = GETDATE()
        WHERE ConfigLaneID = @id
      `, {
        id,
        updatedBy
      });

      // Verify the update by directly querying the database
      const verifyResult = await db.query(`
        SELECT ConfigLaneID, ActiveStatus
        FROM tblLaneDigitalPayConfiguration
        WHERE ConfigLaneID = @id
      `, { id });

      if (verifyResult.recordset.length > 0) {
        console.log(`Digital Pay Config ID ${id} - ActiveStatus after delete:`, verifyResult.recordset[0].ActiveStatus);
      }

      res.json({
        success: true,
        message: 'Lane digital pay configuration deleted successfully',
        deleted: true // Indicate that the record has been deleted
      });
    } catch (error) {
      console.error('Delete lane digital pay configuration failed:', error.message);
      res.status(500).json({
        success: false,
        error: 'Failed to delete lane digital pay configuration',
        details: error.message
      });
    }
  },

  /**
   * Toggle lane digital pay configuration active status
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  toggleConfigurationStatus: async (req, res) => {
    try {
      const { id } = req.params;

      if (!id) {
        return res.status(400).json({
          success: false,
          message: 'Configuration ID is required'
        });
      }

      // Get the current status with a query that also converts the 'Y'/'N' to 1/0 for easier handling
      const statusQuery = `
        SELECT
          ConfigLaneID,
          CASE
            WHEN ActiveStatus = 'Y' THEN 1
            ELSE 0
          END AS ActiveStatusInt,
          ActiveStatus
        FROM tblLaneDigitalPayConfiguration
        WHERE ConfigLaneID = @id
      `;

      const statusResult = await db.query(statusQuery, { id });

      if (statusResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Lane digital pay configuration not found'
        });
      }

      // Get current status (could be string '0'/'1' or integer 0/1)
      const currentStatus = statusResult.recordset[0].ActiveStatusInt;

      // Toggle between 'Y' and 'N' (active/inactive)
      const newStatus = currentStatus === 1 || currentStatus === '1' ? 'N' : 'Y';

      // Set UpdatedBy to current user ID
      const updatedBy = req.user ? req.user.id : 1;

      // Update configuration status in database using direct SQL query with explicit values
      const updateQuery = `
        UPDATE tblLaneDigitalPayConfiguration
        SET ActiveStatus = '${newStatus}',
            UpdatedBy = ${updatedBy},
            UpdatedDateTime = GETDATE()
        WHERE ConfigLaneID = ${id}
      `;

      await db.query(updateQuery);

      // Verify the update using direct SQL query
      const verifyQuery = `
        SELECT ConfigLaneID, ActiveStatus
        FROM tblLaneDigitalPayConfiguration
        WHERE ConfigLaneID = ${id}
      `;

      const verifyResult = await db.query(verifyQuery);

      // Convert 'Y'/'N' to '1'/'0' for frontend
      const normalizedStatus = newStatus === 'Y' ? '1' : '0';

      // Return success response with data needed by frontend
      res.json({
        success: true,
        message: `Lane digital pay configuration ${normalizedStatus === '1' ? 'activated' : 'deactivated'} successfully`,
        newStatus: normalizedStatus,
        newStatusValue: newStatus,
        configId: id
      });
    } catch (error) {
      console.error('Toggle lane digital pay configuration status failed:', error);

      // Send a more detailed error response
      res.status(500).json({
        success: false,
        error: 'Failed to toggle lane digital pay configuration status',
        details: error.message
      });
    }
  }
};

module.exports = laneDigitalPayConfigController;