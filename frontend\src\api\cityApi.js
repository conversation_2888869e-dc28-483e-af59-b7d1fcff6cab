import api from '../services/api'; // Shared Axios instance

export const cityApi = {
  /**
   * Fetches the list of all cities.
   * GET /cities
   */
  getCities: async () => {
    const response = await api.get('/cities'); // Matches router.get('/')
    return response.data.cities;
  },

  /**
   * Fetches the details of a single city by its ID.
   * GET /cities/:id
   */
  getCityById: async (id) => {
    const response = await api.get(`/cities/${id}`); // Matches router.get('/:id')
    return response.data;
  },

  /**
   * Creates a new city.
   * POST /cities
   */
  createCity: async (data) => {
    const response = await api.post('/cities', data); // Matches router.post('/')
    return response.data.city;
  },

  /**
   * Updates a city by ID.
   * PUT /cities/:id
   */
  updateCity: async (id, data) => {
    const response = await api.put(`/cities/${id}`, data); // Matches router.put('/:id')
    return response.data.city;
  },

  /**
   * Deletes a city by ID.
   * DELETE /cities/:id
   */
  deleteCity: async (id) => {
    await api.delete(`/cities/${id}`); // Matches router.delete('/:id')
  },

  /**
   * Fetches statistics for a specific city by ID.
   * GET /cities/:cityId/stats
   */
  getCityStats: async (cityId) => {
    const response = await api.get(`/cities/${cityId}/stats`); // Matches router.get('/:cityId/stats')
    return response.data;
  },
};
