// backend/src/services/RedisService.js
const { getRedisClient, RedisKeys, CacheTTL } = require('../config/redis');

/**
 * ===============================================================================
 * # Redis Service Layer
 * ===============================================================================
 * 
 * Centralized service for all Redis operations in PWVMS.
 * Provides high-level methods for caching, session management,
 * and real-time data operations.
 */

class RedisService {
  constructor() {
    this.redis = null;
  }

  /**
   * Initialize Redis service
   */
  async initialize() {
    try {
      this.redis = getRedisClient();
      console.log('✅ RedisService initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize RedisService:', error.message);
      throw error;
    }
  }

  /**
   * ===============================================================================
   * ## BASIC CACHE OPERATIONS
   * ===============================================================================
   */

  /**
   * Set cache with TTL
   */
  async set(key, value, ttl = CacheTTL.STATIC_DATA) {
    try {
      const serializedValue = JSON.stringify(value);
      await this.redis.setex(key, ttl, serializedValue);
      return true;
    } catch (error) {
      console.error(`Redis SET error for key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Get cache value
   */
  async get(key) {
    try {
      const value = await this.redis.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Redis GET error for key ${key}:`, error.message);
      return null;
    }
  }

  /**
   * Delete cache key
   */
  async del(key) {
    try {
      await this.redis.del(key);
      return true;
    } catch (error) {
      console.error(`Redis DEL error for key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Check if key exists
   */
  async exists(key) {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error(`Redis EXISTS error for key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * Set TTL for existing key
   */
  async expire(key, ttl) {
    try {
      await this.redis.expire(key, ttl);
      return true;
    } catch (error) {
      console.error(`Redis EXPIRE error for key ${key}:`, error.message);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## DASHBOARD CACHE OPERATIONS
   * ===============================================================================
   */

  /**
   * Cache dashboard summary data
   */
  async cacheDashboardSummary(userId, role, filters, data) {
    const key = RedisKeys.DASHBOARD_SUMMARY(userId, role, filters);
    return await this.set(key, data, CacheTTL.DASHBOARD_SUMMARY);
  }

  /**
   * Get cached dashboard summary
   */
  async getDashboardSummary(userId, role, filters) {
    const key = RedisKeys.DASHBOARD_SUMMARY(userId, role, filters);
    return await this.get(key);
  }

  /**
   * Cache revenue by payment method data
   */
  async cacheRevenueByPayment(userId, role, filters, data) {
    const key = RedisKeys.DASHBOARD_REVENUE_BY_PAYMENT(userId, role, filters);
    return await this.set(key, data, CacheTTL.DASHBOARD_CHARTS);
  }

  /**
   * Get cached revenue by payment method
   */
  async getRevenueByPayment(userId, role, filters) {
    const key = RedisKeys.DASHBOARD_REVENUE_BY_PAYMENT(userId, role, filters);
    return await this.get(key);
  }

  /**
   * Cache peak hours data
   */
  async cachePeakHours(userId, role, filters, data) {
    const key = RedisKeys.DASHBOARD_PEAK_HOURS(userId, role, filters);
    return await this.set(key, data, CacheTTL.DASHBOARD_CHARTS);
  }

  /**
   * Get cached peak hours data
   */
  async getPeakHours(userId, role, filters) {
    const key = RedisKeys.DASHBOARD_PEAK_HOURS(userId, role, filters);
    return await this.get(key);
  }

  /**
   * Cache recent transactions
   */
  async cacheRecentTransactions(userId, role, filters, data) {
    const key = RedisKeys.DASHBOARD_RECENT_TRANSACTIONS(userId, role, filters);
    return await this.set(key, data, CacheTTL.DASHBOARD_SUMMARY);
  }

  /**
   * Get cached recent transactions
   */
  async getRecentTransactions(userId, role, filters) {
    const key = RedisKeys.DASHBOARD_RECENT_TRANSACTIONS(userId, role, filters);
    return await this.get(key);
  }

  /**
   * ===============================================================================
   * ## USER SESSION OPERATIONS
   * ===============================================================================
   */

  /**
   * Store user session data
   */
  async setUserSession(userId, sessionData) {
    const key = RedisKeys.USER_SESSION(userId);
    return await this.set(key, sessionData, CacheTTL.USER_SESSION);
  }

  /**
   * Get user session data
   */
  async getUserSession(userId) {
    const key = RedisKeys.USER_SESSION(userId);
    return await this.get(key);
  }

  /**
   * Delete user session
   */
  async deleteUserSession(userId) {
    const key = RedisKeys.USER_SESSION(userId);
    return await this.del(key);
  }

  /**
   * Store user permissions
   */
  async setUserPermissions(userId, permissions) {
    const key = RedisKeys.USER_PERMISSIONS(userId);
    return await this.set(key, permissions, CacheTTL.USER_PERMISSIONS);
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(userId) {
    const key = RedisKeys.USER_PERMISSIONS(userId);
    return await this.get(key);
  }

  /**
   * Store user companies
   */
  async setUserCompanies(userId, companies) {
    const key = RedisKeys.USER_COMPANIES(userId);
    return await this.set(key, companies, CacheTTL.USER_PERMISSIONS);
  }

  /**
   * Get user companies
   */
  async getUserCompanies(userId) {
    const key = RedisKeys.USER_COMPANIES(userId);
    return await this.get(key);
  }

  /**
   * Store user plazas
   */
  async setUserPlazas(userId, plazas) {
    const key = RedisKeys.USER_PLAZAS(userId);
    return await this.set(key, plazas, CacheTTL.USER_PERMISSIONS);
  }

  /**
   * Get user plazas
   */
  async getUserPlazas(userId) {
    const key = RedisKeys.USER_PLAZAS(userId);
    return await this.get(key);
  }

  /**
   * ===============================================================================
   * ## REAL-TIME DATA OPERATIONS
   * ===============================================================================
   */

  /**
   * Set live parking data
   */
  async setLiveParkingData(plazaCode, data) {
    const key = RedisKeys.LIVE_PARKING_DATA(plazaCode);
    return await this.set(key, data, CacheTTL.LIVE_DATA);
  }

  /**
   * Get live parking data
   */
  async getLiveParkingData(plazaCode) {
    const key = RedisKeys.LIVE_PARKING_DATA(plazaCode);
    return await this.get(key);
  }

  /**
   * Set lane status
   */
  async setLaneStatus(plazaId, status) {
    const key = RedisKeys.LANE_STATUS(plazaId);
    return await this.set(key, status, CacheTTL.LIVE_DATA);
  }

  /**
   * Get lane status
   */
  async getLaneStatus(plazaId) {
    const key = RedisKeys.LANE_STATUS(plazaId);
    return await this.get(key);
  }

  /**
   * Set plaza occupancy
   */
  async setPlazaOccupancy(plazaCode, occupancy) {
    const key = RedisKeys.PLAZA_OCCUPANCY(plazaCode);
    return await this.set(key, occupancy, CacheTTL.LIVE_DATA);
  }

  /**
   * Get plaza occupancy
   */
  async getPlazaOccupancy(plazaCode) {
    const key = RedisKeys.PLAZA_OCCUPANCY(plazaCode);
    return await this.get(key);
  }

  /**
   * ===============================================================================
   * ## RATE LIMITING OPERATIONS
   * ===============================================================================
   */

  /**
   * Check and increment rate limit
   */
  async checkRateLimit(ip, endpoint, limit = 100, window = 3600) {
    try {
      const key = RedisKeys.RATE_LIMIT(ip, endpoint);
      const current = await this.redis.incr(key);
      
      if (current === 1) {
        await this.redis.expire(key, window);
      }
      
      return {
        count: current,
        remaining: Math.max(0, limit - current),
        exceeded: current > limit
      };
    } catch (error) {
      console.error(`Rate limit check error:`, error.message);
      return { count: 0, remaining: limit, exceeded: false };
    }
  }

  /**
   * ===============================================================================
   * ## CACHE INVALIDATION OPERATIONS
   * ===============================================================================
   */

  /**
   * Invalidate dashboard cache for user
   */
  async invalidateDashboardCache(userId, role) {
    try {
      const patterns = [
        `*dashboard:summary:${role}:${userId}:*`,
        `*dashboard:revenue:payment:${role}:${userId}:*`,
        `*dashboard:peak:${role}:${userId}:*`,
        `*dashboard:transactions:${role}:${userId}:*`
      ];

      for (const pattern of patterns) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
      
      return true;
    } catch (error) {
      console.error(`Cache invalidation error:`, error.message);
      return false;
    }
  }

  /**
   * Invalidate all dashboard cache
   */
  async invalidateAllDashboardCache() {
    try {
      const keys = await this.redis.keys('*dashboard:*');
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
      return true;
    } catch (error) {
      console.error(`All dashboard cache invalidation error:`, error.message);
      return false;
    }
  }

  /**
   * Invalidate user-related cache
   */
  async invalidateUserCache(userId) {
    try {
      const patterns = [
        `*session:user:${userId}*`,
        `*permissions:user:${userId}*`,
        `*companies:user:${userId}*`,
        `*plazas:user:${userId}*`
      ];

      for (const pattern of patterns) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
      
      return true;
    } catch (error) {
      console.error(`User cache invalidation error:`, error.message);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## UTILITY OPERATIONS
   * ===============================================================================
   */

  /**
   * Get Redis info
   */
  async getRedisInfo() {
    try {
      const info = await this.redis.info();
      const memory = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      
      return {
        info,
        memory,
        keyspace,
        connected: true
      };
    } catch (error) {
      console.error(`Redis info error:`, error.message);
      return { connected: false, error: error.message };
    }
  }

  /**
   * Flush all cache (use with caution)
   */
  async flushAll() {
    try {
      await this.redis.flushall();
      return true;
    } catch (error) {
      console.error(`Redis flush error:`, error.message);
      return false;
    }
  }
}

// Create singleton instance
const redisService = new RedisService();

module.exports = redisService;