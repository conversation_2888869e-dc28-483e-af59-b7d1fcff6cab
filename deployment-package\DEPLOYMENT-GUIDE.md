# PWVMS Deployment Guide

This package contains a production-ready version of the PWVMS application.

## Prerequisites

1. Windows Server with IIS installed
2. URL Rewrite Module for IIS
3. iisnode v0.2.26
4. Node.js (LTS version recommended)
5. SQL Server database

## Deployment Steps

1. Extract this package to a temporary location (e.g., D:\PWVMS-Deployment)

2. Install prerequisites:
   - Run PowerShell as Administrator
   - Execute: .\install-iisnode.ps1

3. Configure the environment:
   - Navigate to the backend directory
   - Rename .env.template to .env
   - Edit .env and update the following:
     - DB_USER, DB_PASSWORD, DB_SERVER, DB_NAME (database credentials)
     - JWT_SECRET (set a strong, unique secret key)
     - FRONTEND_URL (set to your production URL)

4. Deploy to IIS:
   - Run PowerShell as Administrator
   - Execute: .\deploy-to-iis.ps1

5. Verify the deployment:
   - Open a browser and navigate to your server's IP or hostname
   - Test the application functionality

## Troubleshooting

- Check IIS logs: C:\inetpub\logs\LogFiles\W3SVC1\
- Check iisnode logs: C:\inetpub\wwwroot\PWVMS\iisnode\
- Verify database connection settings in .env file
- Ensure all IIS components are properly installed

## Support

For additional support, please contact the development team.
