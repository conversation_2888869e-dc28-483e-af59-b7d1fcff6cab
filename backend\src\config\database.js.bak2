// backend/src/config/database.js
const sql = require('mssql');
require('dotenv').config();

// Create a connection string that explicitly disables dedicated admin connection
const connectionString = `Server=${process.env.DB_SERVER},${process.env.DB_PORT};Database=${process.env.DB_NAME};User Id=${process.env.DB_USER};Password=${process.env.DB_PASSWORD};Encrypt=${process.env.DB_ENCRYPT === 'true'};TrustServerCertificate=${process.env.DB_TRUST_CERT === 'true'};Connection Timeout=${process.env.DB_TIMEOUT || '30000'};ApplicationIntent=ReadWrite;MultipleActiveResultSets=true;Admin Connection=False;`;

// Create a pool configuration with minimal settings
const poolConfig = {
  max: 3, // Keep pool size small
  min: 0,
  idleTimeoutMillis: 30000
};

// Create a connection pool
let pool = null;
let isConnected = false;

// Database module
const db = {
  /**
   * Initializes the database connection pool
   * @returns {Promise<void>}
   */
  init: async () => {
    try {
      // Create a new pool if it doesn't exist
      if (!pool) {
        pool = new sql.ConnectionPool({
          user: process.env.DB_USER,
          password: process.env.DB_PASSWORD,
          server: process.env.DB_SERVER,
          database: process.env.DB_NAME,
          port: parseInt(process.env.DB_PORT),
          options: {
            encrypt: process.env.DB_ENCRYPT === 'true',
            trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
            enableArithAbort: true,
            appName: 'ParkwizOps-App',
            connectTimeout: 30000,
            requestTimeout: 30000,
            pool: poolConfig,
            // Add this to avoid dedicated admin connection
            connectionIsolationLevel: sql.ISOLATION_LEVEL.READ_COMMITTED
          }
        });
        
        // Set up error handler
        pool.on('error', err => {
          console.error('SQL Server connection pool error:', err);
          isConnected = false;
        });
      }
      
      // Connect to the database
      if (!isConnected) {
        await pool.connect();
        isConnected = true;
        console.log('Database connection pool established');
        
        // Log database info
        const result = await pool.request().query(`
          SELECT 
            DB_NAME() AS CurrentDatabase,
            @@CONNECTIONS AS TotalConnections,
            @@MAX_CONNECTIONS AS MaxConnections
        `);
        
        console.log(`Connected to database: ${result.recordset[0].CurrentDatabase}`);
        console.log(`Total connections: ${result.recordset[0].TotalConnections}`);
        console.log(`Max connections: ${result.recordset[0].MaxConnections}`);
      }
    } catch (error) {
      console.error('Failed to initialize database connection:', error.message);
      isConnected = false;
      throw error;
    }
  },
  
  /**
   * Executes a SQL query
   * @param {string} query - The SQL query to execute
   * @param {object} params - Parameters for the query
   * @returns {Promise<any>} Query result
   */
  query: async (query, params = {}) => {
    try {
      // Make sure we're connected
      if (!isConnected) {
        await db.init();
      }
      
      // Create a new request
      const request = pool.request();
      
      // Add parameters
      Object.entries(params).forEach(([key, value]) => {
        request.input(key, value);
      });
      
      // Execute the query
      return await request.query(query);
    } catch (error) {
      console.error('Query execution error:', error.message);
      
      // If it's a connection error, try to reconnect
      if (error.code === 'ELOGIN' || error.code === 'ETIMEOUT' || error.code === 'ECONNCLOSED') {
        isConnected = false;
        
        // Try one more time
        try {
          await db.init();
          
          const request = pool.request();
          
          // Add parameters
          Object.entries(params).forEach(([key, value]) => {
            request.input(key, value);
          });
          
          return await request.query(query);
        } catch (retryError) {
          console.error('Query retry failed:', retryError.message);
          throw retryError;
        }
      }
      
      throw error;
    }
  },
  
  /**
   * Executes a stored procedure
   * @param {string} procedureName - The name of the stored procedure
   * @param {object} params - Parameters for the stored procedure
   * @returns {Promise<any>} Stored procedure result
   */
  executeProcedure: async (procedureName, params = {}) => {
    try {
      // Make sure we're connected
      if (!isConnected) {
        await db.init();
      }
      
      // Create a new request
      const request = pool.request();
      
      // Add parameters
      Object.entries(params).forEach(([key, value]) => {
        request.input(key, value);
      });
      
      // Execute the stored procedure
      return await request.execute(procedureName);
    } catch (error) {
      console.error(`Error executing stored procedure ${procedureName}:`, error.message);
      
      // If it's a connection error, try to reconnect
      if (error.code === 'ELOGIN' || error.code === 'ETIMEOUT' || error.code === 'ECONNCLOSED') {
        isConnected = false;
        
        // Try one more time
        try {
          await db.init();
          
          const request = pool.request();
          
          // Add parameters
          Object.entries(params).forEach(([key, value]) => {
            request.input(key, value);
          });
          
          return await request.execute(procedureName);
        } catch (retryError) {
          console.error('Stored procedure retry failed:', retryError.message);
          throw retryError;
        }
      }
      
      throw error;
    }
  },
  
  /**
   * Closes the database connection pool
   * @returns {Promise<void>}
   */
  close: async () => {
    if (pool && isConnected) {
      try {
        await pool.close();
        isConnected = false;
        console.log('Database connection pool closed');
      } catch (error) {
        console.error('Error closing database connection pool:', error.message);
      }
    }
  }
};

module.exports = db;