import React, { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Users, 
  ChevronDown, 
  ChevronRight, 
  CheckSquare, 
  Square,
  Eye,
  Plus,
  Edit,
  Trash2,
  Download,
  Upload,
  Settings,
  Copy,
  RotateCcw,
  Save
} from 'lucide-react';
import permissionManagementApi from '../../api/permissionManagementApi';

const RolePermissionEditor = ({
  roles,
  modules,
  selectedRole,
  onRoleSelect,
  onPermissionUpdate,
  onBulkUpdate,
  searchTerm,
  filterModule,
  isLoading
}) => {
  const [expandedModules, setExpandedModules] = useState(new Set());
  const [localPermissions, setLocalPermissions] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // Permission icons mapping
  const permissionIcons = {
    'View': Eye,
    'Create': Plus,
    'Edit': Edit,
    'Delete': Trash2,
    'Export': Download,
    'Import': Upload,
    'Approve': CheckSquare
  };

  // Fetch role permissions when role is selected
  const { 
    data: rolePermissionsData, 
    isLoading: rolePermissionsLoading 
  } = useQuery({
    queryKey: ['role-permissions', selectedRole?.Id],
    queryFn: () => permissionManagementApi.getRolePermissions(selectedRole.Id),
    enabled: !!selectedRole
  });

  // Initialize local permissions when role permissions are loaded
  useEffect(() => {
    if (rolePermissionsData?.data) {
      const permissions = {};
      rolePermissionsData.data.forEach(perm => {
        permissions[perm.SubModulePermissionId] = true;
      });
      setLocalPermissions(permissions);
      setHasChanges(false);
    }
  }, [rolePermissionsData]);

  // Filter modules based on search and filter
  const filteredModules = useMemo(() => {
    let filtered = modules;

    if (filterModule) {
      filtered = filtered.filter(module => module.id.toString() === filterModule);
    }

    if (searchTerm) {
      filtered = filtered.filter(module => 
        module.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        module.subModules.some(sub => 
          sub.name.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    return filtered;
  }, [modules, filterModule, searchTerm]);

  // Toggle module expansion
  const toggleModule = (moduleId) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(moduleId)) {
      newExpanded.delete(moduleId);
    } else {
      newExpanded.add(moduleId);
    }
    setExpandedModules(newExpanded);
  };

  // Handle permission toggle
  const handlePermissionToggle = (subModulePermissionId) => {
    const newPermissions = {
      ...localPermissions,
      [subModulePermissionId]: !localPermissions[subModulePermissionId]
    };
    setLocalPermissions(newPermissions);
    setHasChanges(true);
  };

  // Handle bulk operations
  const handleBulkToggle = (moduleId, subModuleId, newValue) => {
    const module = filteredModules.find(m => m.id === moduleId);
    if (!module) return;

    const newPermissions = { ...localPermissions };

    if (subModuleId) {
      // Toggle all permissions for a specific submodule
      const subModule = module.subModules.find(sm => sm.id === subModuleId);
      if (subModule) {
        subModule.permissions.forEach(perm => {
          newPermissions[perm.subModulePermissionId] = newValue;
        });
      }
    } else {
      // Toggle all permissions for entire module
      module.subModules.forEach(subModule => {
        subModule.permissions.forEach(perm => {
          newPermissions[perm.subModulePermissionId] = newValue;
        });
      });
    }

    setLocalPermissions(newPermissions);
    setHasChanges(true);
  };

  // Save changes
  const handleSave = () => {
    if (!selectedRole || !hasChanges) return;

    const permissions = Object.entries(localPermissions).map(([subModulePermissionId, isActive]) => ({
      subModulePermissionId: parseInt(subModulePermissionId),
      isActive: Boolean(isActive)
    }));

    onPermissionUpdate(selectedRole.Id, permissions);
    setHasChanges(false);
  };

  // Reset changes
  const handleReset = () => {
    if (rolePermissionsData?.data) {
      const permissions = {};
      rolePermissionsData.data.forEach(perm => {
        permissions[perm.SubModulePermissionId] = true;
      });
      setLocalPermissions(permissions);
      setHasChanges(false);
    }
  };

  // Copy permissions from another role
  const handleCopyFromRole = async (fromRoleId) => {
    try {
      const fromRolePermissions = await permissionManagementApi.getRolePermissions(fromRoleId);
      const permissions = {};
      fromRolePermissions.data.forEach(perm => {
        permissions[perm.SubModulePermissionId] = true;
      });
      setLocalPermissions(permissions);
      setHasChanges(true);
    } catch (error) {
      console.error('Error copying permissions:', error);
    }
  };

  return (
    <div className="flex h-[calc(100vh-300px)]">
      {/* Role Selector Sidebar */}
      <div className="w-80 border-r bg-gray-50 overflow-y-auto">
        <div className="p-4 border-b bg-white">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <Users className="w-5 h-5 text-blue-600" />
            Select Role
          </h3>
        </div>
        
        <div className="p-4 space-y-2">
          {roles.map(role => (
            <button
              key={role.Id}
              onClick={() => onRoleSelect(role)}
              className={`w-full text-left p-3 rounded-lg border transition-colors ${
                selectedRole?.Id === role.Id
                  ? 'bg-blue-100 border-blue-300 text-blue-900'
                  : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className="font-medium">{role.Name}</div>
              <div className="text-sm text-gray-500">
                Click to manage permissions
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Permission Editor */}
      <div className="flex-1 flex flex-col">
        {selectedRole ? (
          <>
            {/* Header */}
            <div className="p-4 border-b bg-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {selectedRole.Name} Permissions
                  </h3>
                  <p className="text-sm text-gray-600">
                    Manage permissions for this role
                  </p>
                </div>
                
                <div className="flex items-center gap-2">
                  {/* Copy from role dropdown */}
                  <select
                    onChange={(e) => e.target.value && handleCopyFromRole(e.target.value)}
                    className="text-sm border border-gray-300 rounded px-3 py-1"
                    value=""
                  >
                    <option value="">Copy from role...</option>
                    {roles.filter(r => r.Id !== selectedRole.Id).map(role => (
                      <option key={role.Id} value={role.Id}>
                        {role.Name}
                      </option>
                    ))}
                  </select>

                  <button
                    onClick={handleReset}
                    disabled={!hasChanges}
                    className="flex items-center gap-1 px-3 py-1 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50"
                  >
                    <RotateCcw className="w-4 h-4" />
                    Reset
                  </button>

                  <button
                    onClick={handleSave}
                    disabled={!hasChanges || isLoading}
                    className="flex items-center gap-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    <Save className="w-4 h-4" />
                    Save Changes
                  </button>
                </div>
              </div>
              
              {hasChanges && (
                <div className="mt-2 text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded">
                  You have unsaved changes. Click "Save Changes" to apply them.
                </div>
              )}
            </div>

            {/* Permissions List */}
            <div className="flex-1 overflow-y-auto">
              {rolePermissionsLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading permissions...</p>
                  </div>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {filteredModules.map(module => (
                    <div key={module.id} className="border rounded-lg">
                      {/* Module Header */}
                      <div className="bg-gray-50 p-4 border-b">
                        <button
                          onClick={() => toggleModule(module.id)}
                          className="flex items-center justify-between w-full text-left"
                        >
                          <div className="flex items-center gap-2">
                            {expandedModules.has(module.id) ? (
                              <ChevronDown className="w-4 h-4" />
                            ) : (
                              <ChevronRight className="w-4 h-4" />
                            )}
                            <span className="font-semibold text-gray-900">{module.name}</span>
                            <span className="text-sm text-gray-500">
                              ({module.subModules.length} features)
                            </span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleBulkToggle(module.id, null, true);
                              }}
                              className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                            >
                              Grant All
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleBulkToggle(module.id, null, false);
                              }}
                              className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                            >
                              Revoke All
                            </button>
                          </div>
                        </button>
                      </div>

                      {/* SubModules */}
                      {expandedModules.has(module.id) && (
                        <div className="p-4 space-y-4">
                          {module.subModules.map(subModule => (
                            <div key={subModule.id} className="border rounded">
                              {/* SubModule Header */}
                              <div className="bg-blue-50 p-3 border-b">
                                <div className="flex items-center justify-between">
                                  <div>
                                    <div className="font-medium text-gray-900">{subModule.name}</div>
                                    <div className="text-sm text-gray-600">
                                      {subModule.permissions.length} permissions
                                    </div>
                                  </div>
                                  
                                  <div className="flex items-center gap-2">
                                    <button
                                      onClick={() => handleBulkToggle(module.id, subModule.id, true)}
                                      className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                                    >
                                      Grant All
                                    </button>
                                    <button
                                      onClick={() => handleBulkToggle(module.id, subModule.id, false)}
                                      className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                                    >
                                      Revoke All
                                    </button>
                                  </div>
                                </div>
                              </div>

                              {/* Permissions */}
                              <div className="p-3 space-y-2">
                                {subModule.permissions.map(permission => {
                                  const PermissionIcon = permissionIcons[permission.name] || Settings;
                                  const isGranted = localPermissions[permission.subModulePermissionId];
                                  
                                  return (
                                    <div
                                      key={permission.id}
                                      className="flex items-center justify-between p-2 rounded hover:bg-gray-50"
                                    >
                                      <div className="flex items-center gap-3">
                                        <PermissionIcon className="w-4 h-4 text-gray-500" />
                                        <div>
                                          <div className="font-medium text-gray-900">
                                            {permission.name}
                                          </div>
                                          {permission.description && (
                                            <div className="text-sm text-gray-500">
                                              {permission.description}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                      
                                      <button
                                        onClick={() => handlePermissionToggle(permission.subModulePermissionId)}
                                        className={`p-1 rounded transition-colors ${
                                          isGranted
                                            ? 'text-green-600 hover:text-green-700'
                                            : 'text-gray-400 hover:text-gray-600'
                                        }`}
                                      >
                                        {isGranted ? (
                                          <CheckSquare className="w-5 h-5" />
                                        ) : (
                                          <Square className="w-5 h-5" />
                                        )}
                                      </button>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Select a Role</h3>
              <p className="text-gray-600">Choose a role from the sidebar to manage its permissions</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RolePermissionEditor;
