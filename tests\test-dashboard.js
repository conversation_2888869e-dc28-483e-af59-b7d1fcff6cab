require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function testDashboard() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    const today = new Date('2025-06-25T12:00:00.000Z');
    const startDate = new Date(today);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(today);
    endDate.setHours(23, 59, 59, 999);

    console.log('Testing dashboard with date range:');
    console.log('- Start date:', startDate.toISOString());
    console.log('- End date:', endDate.toISOString());

    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);
    
    const summaryResult = await request.query(`
      SELECT 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
        COUNT(*) as TransactionCount 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
    `);

    console.log('\nDashboard Summary:');
    console.log('- Total Revenue:', summaryResult.recordset[0].TotalRevenue);
    console.log('- Transaction Count:', summaryResult.recordset[0].TransactionCount);

    const paymentMethodResult = await request.query(`
      SELECT 
        PaymentMode, 
        COUNT(*) as TransactionCount, 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate 
      GROUP BY PaymentMode 
      ORDER BY Revenue DESC
    `);

    console.log('\nRevenue by Payment Method:');
    paymentMethodResult.recordset.forEach(method => {
      console.log(`- ${method.PaymentMode}: ${method.TransactionCount} transactions, ₹${method.Revenue.toFixed(2)}`);
    });

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

testDashboard();