const sql = require('mssql');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT) || 30000,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT) || 30000,
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 30000,
  }
};

async function createValetManagementModule() {
  let pool;
  
  try {
    console.log('🚀 Starting Valet Management Module Setup...');
    console.log('This will create a complete Valet Management module with:');
    console.log('- Valet Points (for managing valet parking locations)');
    console.log('- Valet Services (for managing valet service types)');
    console.log('- Valet Staff (for managing valet personnel)');
    console.log('- Valet Reports (for valet-related reporting)');
    console.log('');
    
    console.log('Connecting to database...');
    console.log(`Server: ${dbConfig.server}`);
    console.log(`Database: ${dbConfig.database}`);
    
    // Create connection pool
    pool = await sql.connect(dbConfig);
    console.log('✅ Connected to database successfully!');
    
    // Read the SQL script
    const sqlScriptPath = path.join(__dirname, '../database/create_valet_management_module.sql');
    console.log(`Reading SQL script from: ${sqlScriptPath}`);
    
    if (!fs.existsSync(sqlScriptPath)) {
      throw new Error(`SQL script not found at: ${sqlScriptPath}`);
    }
    
    const sqlScript = fs.readFileSync(sqlScriptPath, 'utf8');
    console.log('✅ SQL script loaded successfully');
    
    // Split the script by GO statements and execute each batch
    const batches = sqlScript.split(/^\s*GO\s*$/gim).filter(batch => batch.trim());
    
    console.log(`\nExecuting ${batches.length} SQL batches...`);
    console.log('');
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i].trim();
      if (batch) {
        console.log(`📝 Executing batch ${i + 1}/${batches.length}...`);
        try {
          const result = await pool.request().query(batch);
          
          // Log any print statements from SQL Server
          if (result.recordset && result.recordset.length > 0) {
            result.recordset.forEach(row => {
              Object.values(row).forEach(value => {
                if (value && typeof value === 'string') {
                  console.log(`   ${value}`);
                }
              });
            });
          }
          
          console.log(`✅ Batch ${i + 1} completed successfully`);
        } catch (batchError) {
          console.error(`❌ Error in batch ${i + 1}:`, batchError.message);
          throw batchError;
        }
      }
    }
    
    console.log('\n🎉 Valet Management Module created successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Valet Management module created');
    console.log('✅ 4 submodules created (Valet Points, Services, Staff, Reports)');
    console.log('✅ Permissions assigned to all roles (SuperAdmin, CompanyAdmin, PlazaManager)');
    console.log('✅ Database structure is ready');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Update your frontend components to use "Valet Points" as the module name');
    console.log('2. Add Valet Management to your navigation sidebar');
    console.log('3. Restart your backend server');
    console.log('4. Test the valet points functionality');
    
  } catch (error) {
    console.error('❌ Error creating Valet Management module:', error);
    
    if (error.code === 'ELOGIN') {
      console.error('💡 Database login failed. Please check your credentials in .env file');
    } else if (error.code === 'ETIMEOUT') {
      console.error('💡 Database connection timeout. Please check your network connection');
    } else if (error.code === 'ENOTFOUND') {
      console.error('💡 Database server not found. Please check the server address');
    }
    
    process.exit(1);
  } finally {
    if (pool) {
      try {
        await pool.close();
        console.log('🔌 Database connection closed');
      } catch (closeError) {
        console.error('Error closing database connection:', closeError);
      }
    }
  }
}

// Run the script
createValetManagementModule();
