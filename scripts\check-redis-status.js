// scripts/check-redis-status.js
const axios = require('axios');

/**
 * ===============================================================================
 * # Simple Redis Status Checker for PWVMS
 * ===============================================================================
 * 
 * Quick script to check if Redis is working properly with your PWVMS application.
 */

const BASE_URL = 'http://localhost:5000';

async function checkRedisStatus() {
  console.log('🔍 Checking Redis Status in PWVMS...\n');
  
  try {
    // Test 1: Health Check Endpoint
    console.log('1️⃣ Testing Health Check Endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health-check`);
    
    if (healthResponse.data.success) {
      const { redis, services, database } = healthResponse.data;
      
      console.log('✅ Health Check Successful!');
      console.log(`   Database: ${database.connected ? '✅ Connected' : '❌ Disconnected'} (${database.name})`);
      console.log(`   Redis: ${redis.connected ? '✅ Connected' : '❌ Disconnected'} (${redis.status})`);
      console.log(`   RedisService: ${services.redisService}`);
      console.log(`   RealtimeService: ${services.realtimeService}`);
      
      if (!redis.connected) {
        console.log('\n❌ Redis is not connected! Please check:');
        console.log('   - Is Redis/Memurai service running?');
        console.log('   - Check Redis configuration in your .env file');
        console.log('   - Verify Redis connection settings');
        return false;
      }
    } else {
      console.log('❌ Health check failed:', healthResponse.data.message);
      return false;
    }
    
    // Test 2: Basic Redis Operations via CLI
    console.log('\n2️⃣ Testing Redis CLI Operations...');
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    
    try {
      // Test Redis ping
      const pingResult = await execAsync('"C:\\Program Files\\Memurai\\memurai-cli.exe" ping');
      if (pingResult.stdout.trim() === 'PONG') {
        console.log('✅ Redis CLI Ping: PONG');
      } else {
        console.log('❌ Redis CLI Ping failed');
      }
      
      // Test Redis info
      const infoResult = await execAsync('"C:\\Program Files\\Memurai\\memurai-cli.exe" info memory');
      if (infoResult.stdout.includes('used_memory')) {
        console.log('✅ Redis Memory Info: Available');
        
        // Extract memory usage
        const memoryLines = infoResult.stdout.split('\n');
        const usedMemory = memoryLines.find(line => line.startsWith('used_memory_human:'));
        const maxMemory = memoryLines.find(line => line.startsWith('maxmemory_human:'));
        
        if (usedMemory) {
          console.log(`   Used Memory: ${usedMemory.split(':')[1]?.trim() || 'Unknown'}`);
        }
        if (maxMemory) {
          console.log(`   Max Memory: ${maxMemory.split(':')[1]?.trim() || 'No limit'}`);
        }
      }
      
      // Test key count
      const keysResult = await execAsync('"C:\\Program Files\\Memurai\\memurai-cli.exe" dbsize');
      const keyCount = parseInt(keysResult.stdout.trim());
      console.log(`✅ Redis Key Count: ${keyCount} keys`);
      
    } catch (cliError) {
      console.log('⚠️ Redis CLI tests failed (this is OK if Redis is working via app)');
      console.log('   Error:', cliError.message);
    }
    
    // Test 3: Application-level Redis Features
    console.log('\n3️⃣ Testing Application Redis Features...');
    
    // Test dashboard endpoint (without auth for now)
    try {
      const dashboardResponse = await axios.get(`${BASE_URL}/api/dashboard/summary?dateRange=today`);
      
      if (dashboardResponse.status === 401) {
        console.log('⚠️ Dashboard endpoint requires authentication (this is expected)');
        console.log('   Redis caching is likely working but needs login to test fully');
      } else if (dashboardResponse.data.success) {
        const cached = dashboardResponse.data.cached ? 'from cache' : 'from database';
        console.log(`✅ Dashboard endpoint working (data ${cached})`);
      }
    } catch (dashError) {
      if (dashError.response?.status === 401) {
        console.log('⚠️ Dashboard endpoint requires authentication (this is expected)');
      } else {
        console.log('❌ Dashboard endpoint error:', dashError.message);
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📋 REDIS STATUS SUMMARY');
    console.log('='.repeat(60));
    console.log('✅ Redis is installed and running');
    console.log('✅ PWVMS application can connect to Redis');
    console.log('✅ Health check endpoint is working');
    console.log('✅ Redis services are initialized');
    
    console.log('\n💡 WHAT REDIS IS DOING IN YOUR PWVMS:');
    console.log('   🔄 Caching dashboard data for faster loading');
    console.log('   👤 Managing user sessions and permissions');
    console.log('   ⚡ Enabling real-time features and notifications');
    console.log('   🚦 Rate limiting API requests');
    console.log('   📊 Storing live parking data and metrics');
    
    console.log('\n🚀 TO TEST REDIS FEATURES FULLY:');
    console.log('   1. Start your backend: npm start');
    console.log('   2. Login to your dashboard');
    console.log('   3. Navigate between different date ranges');
    console.log('   4. Notice faster loading on repeated requests (cached data)');
    console.log('   5. Check browser network tab for response times');
    
    return true;
    
  } catch (error) {
    console.log('\n❌ Redis Status Check Failed!');
    console.log('Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n🔧 TROUBLESHOOTING STEPS:');
      console.log('   1. Make sure your backend is running (npm start)');
      console.log('   2. Check if Redis/Memurai service is running:');
      console.log('      Get-Service Memurai');
      console.log('   3. Verify Redis connection in your .env file');
      console.log('   4. Test Redis directly: "C:\\Program Files\\Memurai\\memurai-cli.exe" ping');
    }
    
    return false;
  }
}

// Run the check
checkRedisStatus().then(success => {
  if (success) {
    console.log('\n🎉 Redis is working perfectly with your PWVMS!');
    process.exit(0);
  } else {
    console.log('\n⚠️ Redis needs attention. Please follow the troubleshooting steps above.');
    process.exit(1);
  }
}).catch(error => {
  console.error('Script failed:', error.message);
  process.exit(1);
});