-- =============================================
-- PlazaValetPoint CRUD Stored Procedures
-- Created for comprehensive valet parking management
-- =============================================

-- =============================================
-- 1. CREATE - Insert new valet point
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_Create]
    @PlazaId INT,
    @ValetPointName NVARCHAR(500),
    @IsActive BIT = 1,
    @CreatedBy INT,
    @CompanyId DECIMAL(18,0),
    @Latitude DECIMAL(8,6) = NULL,
    @Longitude DECIMAL(9,6) = NULL,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('PlazaId is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @ValetPointName IS NULL OR LTRIM(RTRIM(@ValetPointName)) = ''
        BEGIN
            RAISERROR('ValetPointName is required', 16, 1);
            RETURN -1;
        END
        
        IF @CreatedBy IS NULL OR @CreatedBy <= 0
        BEGIN
            RAISERROR('CreatedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CompanyId IS NULL OR @CompanyId <= 0
        BEGIN
            RAISERROR('CompanyId is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate valet point name within the same plaza
        IF EXISTS(SELECT 1 FROM [dbo].[PlazaValetPoint] 
                  WHERE [PlazaId] = @PlazaId 
                  AND [ValetPointName] = @ValetPointName 
                  AND [CompanyId] = @CompanyId
                  AND [IsActive] = 1)
        BEGIN
            RAISERROR('Valet point with the same name already exists in this plaza', 16, 1);
            RETURN -1;
        END
        
        -- Insert new record
        INSERT INTO [dbo].[PlazaValetPoint]
        (
            [PlazaId],
            [ValetPointName],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [CompanyId],
            [Latitude],
            [Longitude]
        )
        VALUES
        (
            @PlazaId,
            LTRIM(RTRIM(@ValetPointName)),
            @IsActive,
            @CreatedBy,
            GETDATE(),
            @CompanyId,
            @Latitude,
            @Longitude
        );
        
        SET @NewId = SCOPE_IDENTITY();
        
        -- Return success message
        SELECT 
            @NewId AS Id,
            'Valet point created successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @NewId = -1;
    END CATCH
END
GO

-- =============================================
-- 2. READ - Get valet point by ID
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_GetById]
    @Id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Id is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Get record by ID
        SELECT 
            [Id],
            [PlazaId],
            [ValetPointName],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            [CompanyId],
            [Latitude],
            [Longitude]
        FROM [dbo].[PlazaValetPoint]
        WHERE [Id] = @Id;
        
        -- Check if record exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'Valet point not found' AS Message, 0 AS Success;
        END
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 3. READ - Get all valet points with filtering
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_GetAll]
    @PlazaId INT = NULL,
    @CompanyId DECIMAL(18,0) = NULL,
    @IsActive BIT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SearchTerm NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PageNumber < 1 SET @PageNumber = 1;
        IF @PageSize < 1 OR @PageSize > 1000 SET @PageSize = 50;
        
        -- Calculate offset
        DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
        
        -- Build dynamic WHERE clause
        DECLARE @WhereClause NVARCHAR(MAX) = N'WHERE 1=1';
        
        IF @PlazaId IS NOT NULL
            SET @WhereClause = @WhereClause + N' AND [PlazaId] = @PlazaId';
            
        IF @CompanyId IS NOT NULL
            SET @WhereClause = @WhereClause + N' AND [CompanyId] = @CompanyId';
            
        IF @IsActive IS NOT NULL
            SET @WhereClause = @WhereClause + N' AND [IsActive] = @IsActive';
            
        IF @SearchTerm IS NOT NULL AND LTRIM(RTRIM(@SearchTerm)) != ''
            SET @WhereClause = @WhereClause + N' AND [ValetPointName] LIKE ''%'' + @SearchTerm + ''%''';
        
        -- Get total count
        DECLARE @TotalRecords INT;
        DECLARE @CountSql NVARCHAR(MAX) = N'SELECT @TotalRecords = COUNT(*) FROM [dbo].[PlazaValetPoint] ' + @WhereClause;
        
        EXEC sp_executesql @CountSql, 
            N'@PlazaId INT, @CompanyId DECIMAL(18,0), @IsActive BIT, @SearchTerm NVARCHAR(500), @TotalRecords INT OUTPUT',
            @PlazaId, @CompanyId, @IsActive, @SearchTerm, @TotalRecords OUTPUT;
        
        -- Get paginated results
        SELECT 
            [Id],
            [PlazaId],
            [ValetPointName],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            [CompanyId],
            [Latitude],
            [Longitude],
            @TotalRecords AS TotalRecords,
            @PageNumber AS CurrentPage,
            @PageSize AS PageSize,
            CEILING(CAST(@TotalRecords AS FLOAT) / @PageSize) AS TotalPages
        FROM [dbo].[PlazaValetPoint]
        WHERE 1=1
        AND (@PlazaId IS NULL OR [PlazaId] = @PlazaId)
        AND (@CompanyId IS NULL OR [CompanyId] = @CompanyId)
        AND (@IsActive IS NULL OR [IsActive] = @IsActive)
        AND (@SearchTerm IS NULL OR [ValetPointName] LIKE '%' + @SearchTerm + '%')
        ORDER BY [CreatedOn] DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 4. UPDATE - Update existing valet point
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_Update]
    @Id INT,
    @PlazaId INT,
    @ValetPointName NVARCHAR(500),
    @IsActive BIT,
    @ModifiedBy INT,
    @CompanyId DECIMAL(18,0),
    @Latitude DECIMAL(8,6) = NULL,
    @Longitude DECIMAL(9,6) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Id is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('PlazaId is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @ValetPointName IS NULL OR LTRIM(RTRIM(@ValetPointName)) = ''
        BEGIN
            RAISERROR('ValetPointName is required', 16, 1);
            RETURN -1;
        END
        
        IF @ModifiedBy IS NULL OR @ModifiedBy <= 0
        BEGIN
            RAISERROR('ModifiedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CompanyId IS NULL OR @CompanyId <= 0
        BEGIN
            RAISERROR('CompanyId is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if record exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[PlazaValetPoint] WHERE [Id] = @Id)
        BEGIN
            RAISERROR('Valet point not found', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate valet point name within the same plaza (excluding current record)
        IF EXISTS(SELECT 1 FROM [dbo].[PlazaValetPoint] 
                  WHERE [PlazaId] = @PlazaId 
                  AND [ValetPointName] = @ValetPointName 
                  AND [CompanyId] = @CompanyId
                  AND [Id] != @Id
                  AND [IsActive] = 1)
        BEGIN
            RAISERROR('Valet point with the same name already exists in this plaza', 16, 1);
            RETURN -1;
        END
        
        -- Update record
        UPDATE [dbo].[PlazaValetPoint]
        SET 
            [PlazaId] = @PlazaId,
            [ValetPointName] = LTRIM(RTRIM(@ValetPointName)),
            [IsActive] = @IsActive,
            [ModifiedBy] = @ModifiedBy,
            [ModifiedOn] = GETDATE(),
            [CompanyId] = @CompanyId,
            [Latitude] = @Latitude,
            [Longitude] = @Longitude
        WHERE [Id] = @Id;
        
        -- Return updated record
        SELECT 
            [Id],
            [PlazaId],
            [ValetPointName],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            [CompanyId],
            [Latitude],
            [Longitude],
            'Valet point updated successfully' AS Message,
            1 AS Success
        FROM [dbo].[PlazaValetPoint]
        WHERE [Id] = @Id;
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 5. DELETE - Soft delete valet point
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_SoftDelete]
    @Id INT,
    @ModifiedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Id is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @ModifiedBy IS NULL OR @ModifiedBy <= 0
        BEGIN
            RAISERROR('ModifiedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if record exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[PlazaValetPoint] WHERE [Id] = @Id)
        BEGIN
            RAISERROR('Valet point not found', 16, 1);
            RETURN -1;
        END
        
        -- Soft delete (set IsActive to 0)
        UPDATE [dbo].[PlazaValetPoint]
        SET 
            [IsActive] = 0,
            [ModifiedBy] = @ModifiedBy,
            [ModifiedOn] = GETDATE()
        WHERE [Id] = @Id;
        
        -- Return success message
        SELECT 
            @Id AS Id,
            'Valet point deactivated successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 6. DELETE - Hard delete valet point (permanent)
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_HardDelete]
    @Id INT,
    @DeletedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Id is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @DeletedBy IS NULL OR @DeletedBy <= 0
        BEGIN
            RAISERROR('DeletedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if record exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[PlazaValetPoint] WHERE [Id] = @Id)
        BEGIN
            RAISERROR('Valet point not found', 16, 1);
            RETURN -1;
        END
        
        -- Store record info before deletion for audit
        DECLARE @ValetPointName NVARCHAR(500);
        SELECT @ValetPointName = [ValetPointName] FROM [dbo].[PlazaValetPoint] WHERE [Id] = @Id;
        
        -- Hard delete (permanent removal)
        DELETE FROM [dbo].[PlazaValetPoint]
        WHERE [Id] = @Id;
        
        -- Return success message
        SELECT 
            @Id AS Id,
            @ValetPointName AS ValetPointName,
            'Valet point deleted permanently' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 7. UTILITY - Get valet points by Plaza ID
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_GetByPlazaId]
    @PlazaId INT,
    @IsActive BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('PlazaId is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Get valet points for specific plaza
        SELECT 
            [Id],
            [PlazaId],
            [ValetPointName],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            [CompanyId],
            [Latitude],
            [Longitude]
        FROM [dbo].[PlazaValetPoint]
        WHERE [PlazaId] = @PlazaId
        AND (@IsActive IS NULL OR [IsActive] = @IsActive)
        ORDER BY [ValetPointName];
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 8. UTILITY - Bulk operations
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_BulkActivate]
    @ValetPointIds NVARCHAR(MAX), -- Comma-separated list of IDs
    @ModifiedBy INT,
    @IsActive BIT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @ValetPointIds IS NULL OR LTRIM(RTRIM(@ValetPointIds)) = ''
        BEGIN
            RAISERROR('ValetPointIds is required', 16, 1);
            RETURN -1;
        END
        
        IF @ModifiedBy IS NULL OR @ModifiedBy <= 0
        BEGIN
            RAISERROR('ModifiedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Create temp table for IDs
        CREATE TABLE #TempIds (Id INT);
        
        -- Parse comma-separated IDs
        INSERT INTO #TempIds (Id)
        SELECT CAST(value AS INT)
        FROM STRING_SPLIT(@ValetPointIds, ',')
        WHERE ISNUMERIC(value) = 1;
        
        -- Update records
        UPDATE [dbo].[PlazaValetPoint]
        SET 
            [IsActive] = @IsActive,
            [ModifiedBy] = @ModifiedBy,
            [ModifiedOn] = GETDATE()
        WHERE [Id] IN (SELECT Id FROM #TempIds);
        
        DECLARE @UpdatedCount INT = @@ROWCOUNT;
        
        -- Return result
        SELECT 
            @UpdatedCount AS UpdatedCount,
            CASE WHEN @IsActive = 1 THEN 'Valet points activated successfully' 
                 ELSE 'Valet points deactivated successfully' END AS Message,
            1 AS Success;
        
        -- Clean up
        DROP TABLE #TempIds;
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
            
        -- Clean up on error
        IF OBJECT_ID('tempdb..#TempIds') IS NOT NULL
            DROP TABLE #TempIds;
    END CATCH
END
GO

-- =============================================
-- 9. UTILITY - Get valet points with location within radius
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_GetByLocation]
    @CenterLatitude DECIMAL(8,6),
    @CenterLongitude DECIMAL(9,6),
    @RadiusKM DECIMAL(10,2) = 10.0,
    @CompanyId DECIMAL(18,0) = NULL,
    @IsActive BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @CenterLatitude IS NULL OR @CenterLongitude IS NULL
        BEGIN
            RAISERROR('Center coordinates are required', 16, 1);
            RETURN -1;
        END
        
        IF @RadiusKM <= 0
        BEGIN
            RAISERROR('Radius must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Get valet points within radius using Haversine formula
        SELECT 
            [Id],
            [PlazaId],
            [ValetPointName],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            [CompanyId],
            [Latitude],
            [Longitude],
            -- Calculate distance using Haversine formula
            (
                6371 * ACOS(
                    COS(RADIANS(@CenterLatitude)) * 
                    COS(RADIANS([Latitude])) * 
                    COS(RADIANS([Longitude]) - RADIANS(@CenterLongitude)) + 
                    SIN(RADIANS(@CenterLatitude)) * 
                    SIN(RADIANS([Latitude]))
                )
            ) AS DistanceKM
        FROM [dbo].[PlazaValetPoint]
        WHERE [Latitude] IS NOT NULL 
        AND [Longitude] IS NOT NULL
        AND (@CompanyId IS NULL OR [CompanyId] = @CompanyId)
        AND (@IsActive IS NULL OR [IsActive] = @IsActive)
        AND (
            6371 * ACOS(
                COS(RADIANS(@CenterLatitude)) * 
                COS(RADIANS([Latitude])) * 
                COS(RADIANS([Longitude]) - RADIANS(@CenterLongitude)) + 
                SIN(RADIANS(@CenterLatitude)) * 
                SIN(RADIANS([Latitude]))
            )
        ) <= @RadiusKM
        ORDER BY DistanceKM;
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 10. UTILITY - Get statistics
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_PlazaValetPoint_GetStatistics]
    @PlazaId INT = NULL,
    @CompanyId DECIMAL(18,0) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        SELECT 
            COUNT(*) AS TotalValetPoints,
            SUM(CASE WHEN [IsActive] = 1 THEN 1 ELSE 0 END) AS ActiveValetPoints,
            SUM(CASE WHEN [IsActive] = 0 THEN 1 ELSE 0 END) AS InactiveValetPoints,
            COUNT(DISTINCT [PlazaId]) AS TotalPlazas,
            COUNT(CASE WHEN [Latitude] IS NOT NULL AND [Longitude] IS NOT NULL THEN 1 END) AS PointsWithLocation,
            MIN([CreatedOn]) AS FirstCreated,
            MAX([CreatedOn]) AS LastCreated,
            MAX([ModifiedOn]) AS LastModified
        FROM [dbo].[PlazaValetPoint]
        WHERE (@PlazaId IS NULL OR [PlazaId] = @PlazaId)
        AND (@CompanyId IS NULL OR [CompanyId] = @CompanyId);
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- Create indexes for better performance
-- =============================================
CREATE NONCLUSTERED INDEX [IX_PlazaValetPoint_PlazaId] 
ON [dbo].[PlazaValetPoint] ([PlazaId])
INCLUDE ([ValetPointName], [IsActive], [CompanyId])
GO

CREATE NONCLUSTERED INDEX [IX_PlazaValetPoint_CompanyId] 
ON [dbo].[PlazaValetPoint] ([CompanyId])
INCLUDE ([PlazaId], [ValetPointName], [IsActive])
GO

CREATE NONCLUSTERED INDEX [IX_PlazaValetPoint_IsActive] 
ON [dbo].[PlazaValetPoint] ([IsActive])
INCLUDE ([PlazaId], [ValetPointName], [CompanyId])
GO

CREATE NONCLUSTERED INDEX [IX_PlazaValetPoint_Location] 
ON [dbo].[PlazaValetPoint] ([Latitude], [Longitude])
INCLUDE ([PlazaId], [ValetPointName], [IsActive], [CompanyId])
GO

-- =============================================
-- Example usage scripts
-- =============================================
/*
-- Example 1: Create a new valet point
DECLARE @NewId INT;
EXEC [dbo].[sp_PlazaValetPoint_Create] 
    @PlazaId = 1,
    @ValetPointName = 'Main Entrance Valet',
    @IsActive = 1,
    @CreatedBy = 1,
    @CompanyId = 1,
    @Latitude = 40.7128,
    @Longitude = -74.0060,
    @NewId = @NewId OUTPUT;

-- Example 2: Get all valet points for a plaza
EXEC [dbo].[sp_PlazaValetPoint_GetByPlazaId] 
    @PlazaId = 1,
    @IsActive = 1;

-- Example 3: Update valet point
EXEC [dbo].[sp_PlazaValetPoint_Update] 
    @Id = 1,
    @PlazaId = 1,
    @ValetPointName = 'Updated Valet Point Name',
    @IsActive = 1,
    @ModifiedBy = 1,
    @CompanyId = 1,
    @Latitude = 40.7128,
    @Longitude = -74.0060;

-- Example 4: Get valet points with pagination
EXEC [dbo].[sp_PlazaValetPoint_GetAll] 
    @PageNumber = 1,
    @PageSize = 10,
    @IsActive = 1;

-- Example 5: Get valet points by location
EXEC [dbo].[sp_PlazaValetPoint_GetByLocation] 
    @CenterLatitude = 40.7128,
    @CenterLongitude = -74.0060,
    @RadiusKM = 5.0,
    @IsActive = 1;

-- Example 6: Get statistics
EXEC [dbo].[sp_PlazaValetPoint_GetStatistics];

-- Example 7: Bulk activate/deactivate
EXEC [dbo].[sp_PlazaValetPoint_BulkActivate] 
    @ValetPointIds = '1,2,3,4,5',
    @ModifiedBy = 1,
    @IsActive = 1;

-- Example 8: Soft delete
EXEC [dbo].[sp_PlazaValetPoint_SoftDelete] 
    @Id = 1,
    @ModifiedBy = 1;
*/