// frontend/src/components/User/UserDialog.js
import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { useQuery } from '@tanstack/react-query';
import { companyApi } from '../../api/companyApi';
import { plazaApi } from '../../api/plazaApi';
import { userApi } from '../../api/userApi';

export default function UserDialog({ isOpen, onClose, onSubmit, initialData, title }) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    roleId: '',
    username: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    mobile: '',
    email: '',
    isActive: true,
    userCode: '',
    companyIds: [],
    plazaIds: []
  });
  const [errors, setErrors] = useState({});
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [showPassword, setShowPassword] = useState(!initialData);

  // Fetch roles
  const { data: rolesData } = useQuery({
    queryKey: ['roles'],
    queryFn: userApi.getRoles,
  });

  // Ensure roles is always an array
  const roles = Array.isArray(rolesData) ? rolesData :
               (rolesData?.roles || rolesData?.data || []);

  // Fetch companies
  const { data: companiesData } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
  });

  // Ensure companies is always an array
  const companies = Array.isArray(companiesData) ? companiesData :
                   (companiesData?.companies || companiesData?.data || []);

  // Fetch plazas based on selected companies
  const { data: plazasData, refetch: refetchPlazas } = useQuery({
    queryKey: ['plazas', { companyIds: formData.companyIds }],
    queryFn: async () => {
      // Ensure companyIds is an array and has valid values
      const validCompanyIds = Array.isArray(formData.companyIds)
        ? formData.companyIds.filter(id => id && id !== 'undefined' && id !== 'null')
        : [];

      if (validCompanyIds.length === 0) return [];

      // Fetch plazas for each company and combine them
      const plazaPromises = validCompanyIds.map(companyId =>
        plazaApi.getPlazasByCompany(companyId)
      );

      try {
        const plazaResults = await Promise.all(plazaPromises);
        return plazaResults.flat();
      } catch (error) {
        console.error('Error fetching plazas:', error);
        return [];
      }
    },
    enabled: Array.isArray(formData.companyIds) && formData.companyIds.length > 0,
  });

  // Ensure plazas is always an array
  const allPlazas = Array.isArray(plazasData) ? plazasData :
                   (plazasData?.plazas || plazasData?.data || []);

  // Initialize form with initial data if provided
  useEffect(() => {
    if (initialData) {
      setFormData({
        roleId: initialData.RoleId || '',
        username: initialData.Username || '',
        password: '',
        confirmPassword: '',
        firstName: initialData.FirstName || '',
        lastName: initialData.LastName || '',
        mobile: initialData.Mobile || '',
        email: initialData.Email || '',
        isActive: initialData.IsActive !== undefined ? initialData.IsActive : true,
        userCode: initialData.UserCode || '',
        companyIds: initialData.CompanyIds || [],
        plazaIds: initialData.PlazaIds || []
      });

      // Fetch user's companies and plazas if editing
      if (initialData.Id) {
        fetchUserAssignments(initialData.Id);
      }
    } else {
      // Reset form for new user
      setFormData({
        roleId: '',
        username: '',
        password: '',
        confirmPassword: '',
        firstName: '',
        lastName: '',
        mobile: '',
        email: '',
        isActive: true,
        userCode: '',
        companyIds: [],
        plazaIds: []
      });
      setShowPassword(true);
    }
  }, [initialData, isOpen]);

  // Fetch user's company and plaza assignments
  const fetchUserAssignments = async (userId) => {
    if (!userId) {
      console.warn('Invalid user ID in fetchUserAssignments:', userId);
      return;
    }

    try {
      const [userCompanies, userPlazas] = await Promise.all([
        userApi.getUserCompanies(userId),
        userApi.getUserPlazas(userId)
      ]);

      // Ensure we have valid arrays
      const companyIds = Array.isArray(userCompanies)
        ? userCompanies
            .filter(c => c && c.CompanyId)
            .map(c => c.CompanyId)
        : [];

      const plazaIds = Array.isArray(userPlazas)
        ? userPlazas
            .filter(p => p && p.Id)
            .map(p => p.Id)
        : [];

      setFormData(prev => ({
        ...prev,
        companyIds,
        plazaIds
      }));

      // Refetch plazas based on the user's companies
      if (refetchPlazas && companyIds.length > 0) {
        refetchPlazas();
      }
    } catch (error) {
      console.error('Error fetching user assignments:', error);
      // Set empty arrays as fallback
      setFormData(prev => ({
        ...prev,
        companyIds: [],
        plazaIds: []
      }));
    }
  };

  // Handle input change
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when field is changed
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Handle company selection
  const handleCompanyChange = (e) => {
    const { value, checked } = e.target;

    // Ensure we have a valid company ID
    const companyId = value && !isNaN(parseInt(value)) ? parseInt(value) : null;

    if (companyId === null) {
      console.warn('Invalid company ID in handleCompanyChange:', value);
      return;
    }

    // Ensure companyIds is always an array
    const currentCompanyIds = Array.isArray(formData.companyIds) ? formData.companyIds : [];

    let updatedCompanyIds;
    if (checked) {
      // Add the company ID if it's not already in the array
      updatedCompanyIds = [...currentCompanyIds, companyId];
    } else {
      // Remove the company ID
      updatedCompanyIds = currentCompanyIds.filter(id => id !== companyId);

      // Also remove plazas that belong to this company
      const plazasToRemove = Array.isArray(allPlazas)
        ? allPlazas
            .filter(plaza => plaza && plaza.CompanyId === companyId)
            .map(plaza => plaza.Id)
        : [];

      // Ensure plazaIds is always an array
      const currentPlazaIds = Array.isArray(formData.plazaIds) ? formData.plazaIds : [];

      setFormData(prev => ({
        ...prev,
        plazaIds: currentPlazaIds.filter(id => !plazasToRemove.includes(id))
      }));
    }

    setFormData(prev => ({
      ...prev,
      companyIds: updatedCompanyIds
    }));

    // Refetch plazas when companies change
    if (refetchPlazas) {
      refetchPlazas();
    }
  };

  // Handle plaza selection
  const handlePlazaChange = (e) => {
    const { value, checked } = e.target;

    // Ensure we have a valid plaza ID
    const plazaId = value && !isNaN(parseInt(value)) ? parseInt(value) : null;

    if (plazaId === null) {
      console.warn('Invalid plaza ID in handlePlazaChange:', value);
      return;
    }

    // Ensure plazaIds is always an array
    const currentPlazaIds = Array.isArray(formData.plazaIds) ? formData.plazaIds : [];

    let updatedPlazaIds;
    if (checked) {
      // Add the plaza ID if it's not already in the array
      updatedPlazaIds = [...currentPlazaIds, plazaId];
    } else {
      // Remove the plaza ID
      updatedPlazaIds = currentPlazaIds.filter(id => id !== plazaId);
    }

    setFormData(prev => ({
      ...prev,
      plazaIds: updatedPlazaIds
    }));
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.roleId) newErrors.roleId = 'Role is required';
    if (!formData.username) newErrors.username = 'Username is required';
    if (!initialData && !formData.password) newErrors.password = 'Password is required';
    if (formData.password && formData.password.length < 6) newErrors.password = 'Password must be at least 6 characters';
    if (formData.password && formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match';
    if (!formData.firstName) newErrors.firstName = 'First name is required';
    if (!formData.lastName) newErrors.lastName = 'Last name is required';
    if (!formData.email) newErrors.email = 'Email is required';
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';

    // Check if PlazaManager has at least one plaza assigned
    if (Array.isArray(roles) && formData.roleId) {
      const roleId = parseInt(formData.roleId);
      const selectedRole = roles.find(role => role && role.Id === roleId);

      if (selectedRole && selectedRole.Name === 'PlazaManager') {
        const hasPlazas = Array.isArray(formData.plazaIds) && formData.plazaIds.length > 0;
        if (!hasPlazas) {
          newErrors.plazaIds = 'Plaza Manager must be assigned to at least one plaza';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Prepare data for submission
      const userData = {
        roleId: parseInt(formData.roleId),
        username: formData.username,
        firstName: formData.firstName,
        lastName: formData.lastName,
        mobile: formData.mobile,
        email: formData.email,
        isActive: formData.isActive,
        userCode: formData.userCode,
        companyIds: formData.companyIds,
        plazaIds: formData.plazaIds
      };

      // Add password only if provided or creating new user
      if (formData.password) {
        userData.password = formData.password;
      }

      // Add ID if editing
      if (initialData && initialData.Id) {
        userData.id = initialData.Id;
      }

      onSubmit(userData);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex justify-between items-center pb-4 mb-4 border-b">
              <h3 className="text-lg leading-6 font-medium text-gray-900">{title || (initialData ? 'Edit User' : 'Create User')}</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-500 focus:outline-none"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Role */}
                <div>
                  <label htmlFor="roleId" className="block text-sm font-medium text-gray-700">
                    Role <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="roleId"
                    name="roleId"
                    value={formData.roleId}
                    onChange={handleChange}
                    className={`mt-1 block w-full py-2 px-3 border ${errors.roleId ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  >
                    <option value="">Select a role</option>
                    {roles.map(role => (
                      <option key={role.Id} value={role.Id}>{role.Name}</option>
                    ))}
                  </select>
                  {errors.roleId && <p className="mt-1 text-sm text-red-600">{errors.roleId}</p>}
                </div>

                {/* Username */}
                <div>
                  <label htmlFor="username" className="block text-sm font-medium text-gray-700">
                    Username <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    disabled={initialData}
                    className={`mt-1 block w-full py-2 px-3 border ${errors.username ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${initialData ? 'bg-gray-100' : ''}`}
                  />
                  {errors.username && <p className="mt-1 text-sm text-red-600">{errors.username}</p>}
                </div>

                {/* First Name */}
                <div>
                  <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                    First Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    className={`mt-1 block w-full py-2 px-3 border ${errors.firstName ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  />
                  {errors.firstName && <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>}
                </div>

                {/* Last Name */}
                <div>
                  <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                    Last Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    className={`mt-1 block w-full py-2 px-3 border ${errors.lastName ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  />
                  {errors.lastName && <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>}
                </div>

                {/* Email */}
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    className={`mt-1 block w-full py-2 px-3 border ${errors.email ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                  />
                  {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
                </div>

                {/* Mobile */}
                <div>
                  <label htmlFor="mobile" className="block text-sm font-medium text-gray-700">
                    Mobile
                  </label>
                  <input
                    type="text"
                    id="mobile"
                    name="mobile"
                    value={formData.mobile}
                    onChange={handleChange}
                    className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                {/* User Code */}
                <div>
                  <label htmlFor="userCode" className="block text-sm font-medium text-gray-700">
                    User Code
                  </label>
                  <input
                    type="text"
                    id="userCode"
                    name="userCode"
                    value={formData.userCode}
                    onChange={handleChange}
                    className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>

                {/* Password */}
                {showPassword && (
                  <>
                    <div>
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                        Password {!initialData && <span className="text-red-500">*</span>}
                      </label>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        value={formData.password}
                        onChange={handleChange}
                        className={`mt-1 block w-full py-2 px-3 border ${errors.password ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                      />
                      {errors.password && <p className="mt-1 text-sm text-red-600">{errors.password}</p>}
                    </div>

                    <div>
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                        Confirm Password {!initialData && <span className="text-red-500">*</span>}
                      </label>
                      <input
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleChange}
                        className={`mt-1 block w-full py-2 px-3 border ${errors.confirmPassword ? 'border-red-300' : 'border-gray-300'} bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                      />
                      {errors.confirmPassword && <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>}
                    </div>
                  </>
                )}

                {/* Active Status */}
                <div className="col-span-1 md:col-span-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isActive" className="ml-2 block text-sm text-gray-900">
                      Active
                    </label>
                  </div>
                </div>
              </div>

              {/* Password Toggle (for edit mode) */}
              {initialData && (
                <div>
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    {showPassword ? 'Cancel Password Change' : 'Change Password'}
                  </button>
                </div>
              )}

              {/* Company and Plaza Assignments */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Company Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assign Companies
                  </label>
                  <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md p-2">
                    {companies.length === 0 ? (
                      <p className="text-sm text-gray-500">No companies available</p>
                    ) : (
                      companies.map(company => (
                        <div key={company.Id} className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={`company-${company.Id}`}
                            value={company.Id}
                            checked={formData.companyIds.includes(company.Id)}
                            onChange={handleCompanyChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`company-${company.Id}`} className="ml-2 block text-sm text-gray-900">
                            {company.CompanyName}
                          </label>
                        </div>
                      ))
                    )}
                  </div>
                </div>

                {/* Plaza Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assign Plazas
                  </label>
                  <div className={`max-h-60 overflow-y-auto border ${errors.plazaIds ? 'border-red-300' : 'border-gray-300'} rounded-md p-2`}>
                    {allPlazas.length === 0 ? (
                      <p className="text-sm text-gray-500">No plazas available. Please select a company first.</p>
                    ) : (
                      allPlazas.map(plaza => (
                        <div key={plaza.Id} className="flex items-center mb-2">
                          <input
                            type="checkbox"
                            id={`plaza-${plaza.Id}`}
                            value={plaza.Id}
                            checked={formData.plazaIds.includes(plaza.Id)}
                            onChange={handlePlazaChange}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`plaza-${plaza.Id}`} className="ml-2 block text-sm text-gray-900">
                            {plaza.PlazaName} ({companies.find(c => c.Id === plaza.CompanyId)?.CompanyName || 'Unknown Company'})
                          </label>
                        </div>
                      ))
                    )}
                  </div>
                  {errors.plazaIds && <p className="mt-1 text-sm text-red-600">{errors.plazaIds}</p>}
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {initialData ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
