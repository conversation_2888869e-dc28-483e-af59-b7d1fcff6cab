import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { uhfReaderApi } from '../api/uhfReaderApi';
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import { laneApi } from '../api/laneApi';
import UHFReaderList from '../components/UHFReader/UHFReaderList';
import UHFReaderDialog from '../components/UHFReader/UHFReaderDialog';
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';

const ManageUHFReader = () => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUHFReader, setEditingUHFReader] = useState(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();
  
  // State for selected company and plaza in filters
  const [selectedFilterCompany, setSelectedFilterCompany] = useState('');
  const [selectedFilterPlaza, setSelectedFilterPlaza] = useState('');

  // Fetch companies for dropdown
  const { data: companies = [] } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => Array.isArray(data) ? data : []
  });

  // Fetch plazas based on selected company
  const { data: plazas = [], isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazas', selectedFilterCompany],
    queryFn: async () => {
      try {
        console.log(`Fetching plazas for company filter: ${selectedFilterCompany}`);
        const data = selectedFilterCompany 
          ? await plazaApi.getPlazasByCompany(selectedFilterCompany) 
          : await plazaApi.getAllPlazas();
        console.log('Plazas data received for filter:', data);
        return data;
      } catch (error) {
        console.error('Error fetching plazas for filter:', error);
        throw error;
      }
    },
    select: (data) => {
      console.log('Processing plazas data for select:', data);
      
      // Handle different response structures
      if (!data) return [];
      
      if (Array.isArray(data)) {
        return data;
      } else if (data.data && Array.isArray(data.data)) {
        return data.data;
      } else if (data.plazas && Array.isArray(data.plazas)) {
        return data.plazas;
      }
      
      console.warn('Unexpected plazas data structure:', data);
      return [];
    }
  });

  // Fetch lanes based on selected plaza
  const { data: lanes = [] } = useQuery({
    queryKey: ['lanes', selectedFilterPlaza],
    queryFn: () => selectedFilterPlaza 
      ? laneApi.getLanesByPlaza(selectedFilterPlaza) 
      : laneApi.getAllLanes(),
    select: (data) => Array.isArray(data) ? data : []
  });

  // Fetch UHF reader configurations
  const { data: uhfReaders = [], isLoading, error } = useQuery({
    queryKey: ['uhfReaders'],
    queryFn: uhfReaderApi.getAll,
    onError: (error) => {
      console.error('Error fetching UHF readers:', error);
      toast.showError('Failed to fetch UHF reader configurations');
    }
  });

  // Use the module filter hook for role-based filtering
  const {
    filteredData: filteredUHFReaders,
    filters,
    setFilters,
    canCreate,
    canEdit,
    canDelete
  } = useModuleFilter({
    data: uhfReaders,
    companies,
    plazas,
    lanes,
    companyIdField: 'CompanyID',
    plazaIdField: 'PlazaID',
    laneIdField: 'LaneID',
    module: 'UHF Reader'
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: uhfReaderApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['uhfReaders']);
      toast.showCrudSuccess('create', 'UHF reader configuration');
      setIsDialogOpen(false);
      setEditingUHFReader(null);
    },
    onError: (error) => {
      console.error('Error creating UHF reader:', error);
      toast.showError(error.response?.data?.message || 'Failed to create UHF reader configuration');
    }
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => uhfReaderApi.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries(['uhfReaders']);
      toast.showCrudSuccess('update', 'UHF reader configuration');
      setIsDialogOpen(false);
      setEditingUHFReader(null);
    },
    onError: (error) => {
      console.error('Error updating UHF reader:', error);
      toast.showError(error.response?.data?.message || 'Failed to update UHF reader configuration');
    }
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: uhfReaderApi.delete,
    onSuccess: () => {
      queryClient.invalidateQueries(['uhfReaders']);
      toast.showCrudSuccess('delete', 'UHF reader configuration');
    },
    onError: (error) => {
      console.error('Error deleting UHF reader:', error);
      toast.showError(error.response?.data?.message || 'Failed to delete UHF reader configuration');
    }
  });

  // Toggle status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: ({ id, enabled }) => uhfReaderApi.toggleStatus(id, enabled),
    onSuccess: () => {
      queryClient.invalidateQueries(['uhfReaders']);
      toast.showCrudSuccess('update', 'UHF reader status');
    },
    onError: (error) => {
      console.error('Error toggling UHF reader status:', error);
      toast.showError(error.response?.data?.message || 'Failed to update UHF reader status');
    }
  });

  const handleCreate = () => {
    setEditingUHFReader(null);
    setIsDialogOpen(true);
  };

  const handleEdit = (uhfReader) => {
    setEditingUHFReader(uhfReader);
    setIsDialogOpen(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this UHF reader configuration?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleToggleStatus = (id, currentStatus) => {
    const enabled = currentStatus === 'False';
    toggleStatusMutation.mutate({ id, enabled });
  };

  const handleSubmit = (data) => {
    if (editingUHFReader) {
      updateMutation.mutate({ id: editingUHFReader.UHFReaderID, data });
    } else {
      createMutation.mutate(data);
    }
  };

  // Handle company filter change
  const handleCompanyFilterChange = (e) => {
    const companyId = e.target.value;
    setSelectedFilterCompany(companyId);
    setSelectedFilterPlaza(''); // Reset plaza when company changes
    setFilters({ 
      ...filters, 
      companyId: companyId,
      plazaId: '', // Reset plaza filter
      laneId: ''   // Reset lane filter
    });
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle plaza filter change
  const handlePlazaFilterChange = (e) => {
    const plazaId = e.target.value;
    setSelectedFilterPlaza(plazaId);
    setFilters({ 
      ...filters, 
      plazaId: plazaId,
      laneId: '' // Reset lane filter when plaza changes
    });
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle lane filter change
  const handleLaneFilterChange = (e) => {
    const laneId = e.target.value;
    setFilters({ 
      ...filters, 
      laneId: laneId 
    });
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Handle page change
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when items per page changes
  };

  const clearFilters = () => {
    setFilters({});
    setSelectedFilterCompany('');
    setSelectedFilterPlaza('');
    setCurrentPage(1); // Reset to first page when filters are cleared
  };
  
  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error Loading UHF Reader Configurations</h3>
          <p className="text-red-600 mt-1">
            {error.response?.data?.message || 'Failed to load UHF reader configurations. Please try again.'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">UHF Reader Management</h1>
            <p className="text-gray-600 mt-1">
              Manage UHF reader configurations for toll plaza lanes
            </p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Filters</span>
            </button>

            <PermissionButton
              requiredModule="UHF Reader"
              requiredPermissions={["Create"]}
              onClick={handleCreate}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              <span>Add UHF Reader</span>
            </PermissionButton>
          </div>
        </div>

        {/* Filters */}
        {isFilterOpen && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Company Filter - Only show for SuperAdmin */}
              {user?.role === 'SuperAdmin' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Company
                  </label>
                  <select
                    value={selectedFilterCompany}
                    onChange={handleCompanyFilterChange}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">All Companies</option>
                    {companies.map((company) => (
                      <option key={company.Id} value={company.Id}>
                        {company.CompanyName}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Plaza Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Plaza
                </label>
                <select
                  value={selectedFilterPlaza}
                  onChange={handlePlazaFilterChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={user?.role === 'SuperAdmin' && !selectedFilterCompany}
                >
                  {user?.role === 'SuperAdmin' && !selectedFilterCompany ? (
                    <option value="">Select a company first</option>
                  ) : plazasLoading ? (
                    <option value="">Loading plazas...</option>
                  ) : plazasError ? (
                    <option value="">Error loading plazas</option>
                  ) : (
                    <>
                      <option value="">All Plazas</option>
                      {Array.isArray(plazas) && plazas.length > 0 ? plazas.map((plaza) => {
                        const id = plaza.Id || plaza.id;
                        const name = plaza.PlazaName || plaza.plazaName || plaza.name;
                        console.log('Rendering plaza filter option:', id, name);
                        
                        return (
                          <option key={id} value={id}>
                            {name}
                          </option>
                        );
                      }) : (
                        <option value="" disabled>No plazas available</option>
                      )}
                    </>
                  )}
                </select>
                {Array.isArray(plazas) && plazas.length === 0 && selectedFilterCompany && !plazasLoading && (
                  <p className="text-sm text-orange-500 mt-1">No plazas found for this company</p>
                )}
              </div>

              {/* Lane Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lane
                </label>
                <select
                  value={filters.laneId || ''}
                  onChange={handleLaneFilterChange}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  disabled={!selectedFilterPlaza}
                >
                  {!selectedFilterPlaza ? (
                    <option value="">Select a plaza first</option>
                  ) : (
                    <>
                      <option value="">All Lanes</option>
                      {Array.isArray(lanes) && lanes.length > 0 ? lanes.map((lane) => (
                        <option key={lane.LaneID} value={lane.LaneID}>
                          Lane {lane.LaneNumber} - {lane.LaneType}
                        </option>
                      )) : (
                        <option value="" disabled>No lanes available</option>
                      )}
                    </>
                  )}
                </select>
                {Array.isArray(lanes) && lanes.length === 0 && selectedFilterPlaza && (
                  <p className="text-sm text-orange-500 mt-1">No lanes found for this plaza</p>
                )}
              </div>
            </div>

            <div className="mt-4 flex justify-end">
              <button
                onClick={clearFilters}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}
      </div>

      {/* UHF Reader List */}
      <UHFReaderList
        uhfReaders={filteredUHFReaders}
        isLoading={isLoading}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onToggleStatus={handleToggleStatus}
        currentPage={currentPage}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChange}
        onItemsPerPageChange={handleItemsPerPageChange}
      />

      {/* UHF Reader Dialog */}
      {isDialogOpen && (
        <UHFReaderDialog
          isOpen={isDialogOpen}
          onClose={() => {
            setIsDialogOpen(false);
            setEditingUHFReader(null);
          }}
          onSubmit={handleSubmit}
          uhfReader={editingUHFReader}
          isLoading={createMutation.isLoading || updateMutation.isLoading}
        />
      )}
    </div>
  );
};

export default ManageUHFReader;
