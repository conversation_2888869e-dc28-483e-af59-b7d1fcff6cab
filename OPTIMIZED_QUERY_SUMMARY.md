# Optimized Dashboard Query Summary

## Single Query Structure

The dashboard controller now uses a single optimized query that retrieves all required data in one database call:

```sql
SELECT 
    -- Total Revenue (only from exits with parking fee)
    ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.Parking<PERSON>ee, 0) ELSE 0 END), 0) AS TotalRevenue,
    
    -- Four Wheeler Data
    ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.Parking<PERSON>ee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
    ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
    ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
    
    -- Two Wheeler Data
    ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
    ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
    ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
    
    -- Total Counts
    ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
    ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
```

## Key Features

1. **Conditional Aggregation**: Uses CASE statements to count/sum based on conditions
2. **Date Range Filtering**: Separate conditions for entry and exit dates
3. **Vehicle Type Logic**: 
   - Four Wheeler: `VehicleType <> 'Two Wheeler'`
   - Two Wheeler: `VehicleType = 'Two Wheeler'`
4. **Revenue Calculation**: Only from completed transactions (exits)
5. **Performance Optimized**: Single query with NOLOCK hint

## Expected Results

Based on your test data:
- Four Wheeler Entry Count: 738
- Four Wheeler Exit Count: 562
- Four Wheeler Remaining: 176 (738 - 562)

## Logging Output

The query will produce detailed logs:
```
🔍 Executing Optimized Summary Query: [SQL]
📊 Query Parameters: { startDate, endDate, ... }
✅ Optimized Query Result: { TotalRevenue, FourWheelerEntryCount, ... }
🧮 Calculated Remaining Counts:
  Four Wheeler: 738 entries - 562 exits = 176 remaining
  Two Wheeler: [counts] entries - [counts] exits = [remaining] remaining
  Total: [total entries] entries - [total exits] exits = [total remaining] remaining
```

This optimized approach provides accurate data with better performance and comprehensive logging.