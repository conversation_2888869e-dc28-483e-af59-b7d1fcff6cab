-- Create sp_Valet_CustomerSession_Get stored procedure
USE ParkwizOps;
GO

-- Create the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_CustomerSession_Get]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_CustomerSession_Get];
GO

CREATE PROCEDURE [dbo].[sp_Valet_CustomerSession_Get]
    @SessionId INT,
    @CustomerId DECIMAL(18,0)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @SessionId IS NULL OR @SessionId <= 0
        BEGIN
            RAISERROR('Session ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CustomerId IS NULL OR @CustomerId <= 0
        BEGIN
            RAISERROR('Customer ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Get session details
        SELECT 
            s.[Id] AS SessionId,
            s.[CustomerId],
            s.[PlazaValetPointId],
            s.[SessionData],
            s.[CreatedOn],
            s.[UpdatedOn],
            s.[IsActive],
            c.[Name] AS CustomerName,
            c.[MobileNumber] AS CustomerMobileNumber,
            pvp.[ValetPointName],
            p.[PlazaName],
            cm.[CompanyName]
        FROM [dbo].[ValetCustomerSessions] s
        JOIN [dbo].[ValetCustomers] c ON s.[CustomerId] = c.[Id]
        JOIN [dbo].[PlazaValetPoint] pvp ON s.[PlazaValetPointId] = pvp.[Id]
        JOIN [dbo].[Plaza] p ON pvp.[PlazaId] = p.[Id]
        JOIN [dbo].[tblCompanyMaster] cm ON pvp.[CompanyId] = cm.[Id]
        WHERE s.[Id] = @SessionId AND s.[CustomerId] = @CustomerId;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_CustomerSession_Get';