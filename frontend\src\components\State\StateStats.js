import React from 'react';
import { Building2, Building, Users, MapPin } from 'lucide-react';
import StatsCard from '../statsCard';

export default function StateStats({ stats }) {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">
        {stats.stateInfo.name}, {stats.stateInfo.country.name} Statistics
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatsCard title="Cities" value={stats.statistics.cityCount} icon={<MapPin className="w-6 h-6" />} />
        <StatsCard title="Companies" value={stats.statistics.companyCount} icon={<Building2 className="w-6 h-6" />} />
        <StatsCard title="Branches" value={stats.statistics.branchCount} icon={<Building className="w-6 h-6" />} />
        <StatsCard title="Clients" value={stats.statistics.clientCount} icon={<Users className="w-6 h-6" />} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4">Client Distribution by City</h3>
          <div className="space-y-3">
            {stats.clientDistribution.map((dist) => (
              <div key={dist.city} className="flex items-center">
                <div className="w-32 text-sm text-gray-600">{dist.city}</div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-600 rounded-full"
                      style={{
                        width: `${(dist.clientCount / stats.statistics.clientCount) * 100}%`,
                      }}
                    />
                  </div>
                </div>
                <div className="w-20 text-right text-sm text-gray-600">{dist.clientCount}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold mb-4">Company Distribution by City</h3>
          <div className="space-y-3">
            {stats.companyDistribution.map((dist) => (
              <div key={dist.city} className="flex items-center">
                <div className="w-32 text-sm text-gray-600">{dist.city}</div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-100 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-green-600 rounded-full"
                      style={{
                        width: `${(dist.companyCount / stats.statistics.companyCount) * 100}%`,
                      }}
                    />
                  </div>
                </div>
                <div className="w-20 text-right text-sm text-gray-600">{dist.companyCount}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
