require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function checkDateRanges() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Check overall date range
    const dateRangeResult = await sql.query(`
      SELECT 
        MIN(ExitDateTime) as EarliestDate, 
        MAX(ExitDateTime) as LatestDate 
      FROM tblParkwiz_Parking_Data 
      WHERE ExitDateTime IS NOT NULL
    `);
    
    console.log('Overall date range in database:');
    console.log('- Earliest date:', dateRangeResult.recordset[0].EarliestDate.toISOString());
    console.log('- Latest date:', dateRangeResult.recordset[0].LatestDate.toISOString());

    // Check June 2025 data
    const juneResult = await sql.query(`
      SELECT 
        COUNT(*) as Count, 
        SUM(ParkingFee + iTotalGSTFee) as Revenue 
      FROM tblParkwiz_Parking_Data 
      WHERE ExitDateTime >= '2025-06-01' 
        AND ExitDateTime <= '2025-06-30'
    `);
    
    console.log('\nJune 2025 data:');
    console.log('- Transactions:', juneResult.recordset[0].Count);
    console.log('- Revenue:', juneResult.recordset[0].Revenue);

    // Check May 2025 data
    const mayResult = await sql.query(`
      SELECT 
        COUNT(*) as Count, 
        SUM(ParkingFee + iTotalGSTFee) as Revenue 
      FROM tblParkwiz_Parking_Data 
      WHERE ExitDateTime >= '2025-05-01' 
        AND ExitDateTime <= '2025-05-31'
    `);
    
    console.log('\nMay 2025 data:');
    console.log('- Transactions:', mayResult.recordset[0].Count);
    console.log('- Revenue:', mayResult.recordset[0].Revenue);

    // Check first week of June 2025
    const weekResult = await sql.query(`
      SELECT 
        COUNT(*) as Count, 
        SUM(ParkingFee + iTotalGSTFee) as Revenue 
      FROM tblParkwiz_Parking_Data 
      WHERE ExitDateTime >= '2025-06-01' 
        AND ExitDateTime <= '2025-06-07'
    `);
    
    console.log('\nFirst week of June 2025:');
    console.log('- Transactions:', weekResult.recordset[0].Count);
    console.log('- Revenue:', weekResult.recordset[0].Revenue);

    // Check first half of 2025
    const halfYearResult = await sql.query(`
      SELECT 
        COUNT(*) as Count, 
        SUM(ParkingFee + iTotalGSTFee) as Revenue 
      FROM tblParkwiz_Parking_Data 
      WHERE ExitDateTime >= '2025-01-01' 
        AND ExitDateTime <= '2025-06-30'
    `);
    
    console.log('\nFirst half of 2025:');
    console.log('- Transactions:', halfYearResult.recordset[0].Count);
    console.log('- Revenue:', halfYearResult.recordset[0].Revenue);

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkDateRanges();