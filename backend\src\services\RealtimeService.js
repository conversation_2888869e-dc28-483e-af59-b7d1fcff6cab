// backend/src/services/RealtimeService.js
const { getRedisPublisher, getRedisSubscriber } = require('../config/redis');
const redisService = require('./RedisService');

/**
 * ===============================================================================
 * # Real-time Service with Redis Pub/Sub
 * ===============================================================================
 * 
 * Provides real-time communication capabilities for PWVMS using Redis Pub/Sub.
 * Features include:
 * - Live parking data updates
 * - System notifications
 * - Dashboard real-time metrics
 * - Lane status monitoring
 * - Alert broadcasting
 */

class RealtimeService {
  constructor() {
    this.publisher = null;
    this.subscriber = null;
    this.eventHandlers = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the real-time service
   */
  async initialize() {
    try {
      this.publisher = getRedisPublisher();
      this.subscriber = getRedisSubscriber();
      
      // Set up event handlers
      this.subscriber.on('message', this.handleMessage.bind(this));
      this.subscriber.on('error', (error) => {
        console.error('❌ Redis subscriber error:', error);
      });

      this.isInitialized = true;
      console.log('✅ RealtimeService initialized successfully');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize RealtimeService:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## CHANNEL MANAGEMENT
   * ===============================================================================
   */

  /**
   * Subscribe to a channel
   */
  async subscribe(channel, handler) {
    try {
      if (!this.isInitialized) {
        throw new Error('RealtimeService not initialized');
      }

      // Store the handler
      this.eventHandlers.set(channel, handler);
      
      // Subscribe to the channel
      await this.subscriber.subscribe(channel);
      
      console.log(`✅ Subscribed to channel: ${channel}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to subscribe to channel ${channel}:`, error);
      return false;
    }
  }

  /**
   * Unsubscribe from a channel
   */
  async unsubscribe(channel) {
    try {
      if (!this.isInitialized) {
        throw new Error('RealtimeService not initialized');
      }

      // Remove the handler
      this.eventHandlers.delete(channel);
      
      // Unsubscribe from the channel
      await this.subscriber.unsubscribe(channel);
      
      console.log(`✅ Unsubscribed from channel: ${channel}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to unsubscribe from channel ${channel}:`, error);
      return false;
    }
  }

  /**
   * Publish message to a channel
   */
  async publish(channel, data) {
    try {
      if (!this.isInitialized) {
        throw new Error('RealtimeService not initialized');
      }

      const message = {
        timestamp: new Date().toISOString(),
        channel,
        data
      };

      await this.publisher.publish(channel, JSON.stringify(message));
      
      console.log(`✅ Published to channel ${channel}:`, data);
      return true;
    } catch (error) {
      console.error(`❌ Failed to publish to channel ${channel}:`, error);
      return false;
    }
  }

  /**
   * Handle incoming messages
   */
  handleMessage(channel, message) {
    try {
      const handler = this.eventHandlers.get(channel);
      
      if (handler) {
        const parsedMessage = JSON.parse(message);
        handler(parsedMessage);
      } else {
        console.warn(`⚠️ No handler found for channel: ${channel}`);
      }
    } catch (error) {
      console.error(`❌ Error handling message from channel ${channel}:`, error);
    }
  }

  /**
   * ===============================================================================
   * ## PARKING DATA REAL-TIME UPDATES
   * ===============================================================================
   */

  /**
   * Broadcast new parking transaction
   */
  async broadcastParkingTransaction(transaction) {
    try {
      const channels = [
        'parking:transactions:all',
        `parking:transactions:plaza:${transaction.plazaCode}`,
        `parking:transactions:lane:${transaction.laneId}`
      ];

      const broadcastData = {
        type: 'PARKING_TRANSACTION',
        transaction: {
          id: transaction.id,
          plazaCode: transaction.plazaCode,
          laneId: transaction.laneId,
          vehicleNumber: transaction.vehicleNumber,
          entryTime: transaction.entryTime,
          exitTime: transaction.exitTime,
          parkingFee: transaction.parkingFee,
          paymentMode: transaction.paymentMode,
          duration: transaction.duration
        },
        timestamp: new Date().toISOString()
      };

      // Broadcast to all relevant channels
      for (const channel of channels) {
        await this.publish(channel, broadcastData);
      }

      // Update live parking data in Redis
      await this.updateLiveParkingData(transaction.plazaCode, transaction);

      return true;
    } catch (error) {
      console.error('❌ Failed to broadcast parking transaction:', error);
      return false;
    }
  }

  /**
   * Update live parking data
   */
  async updateLiveParkingData(plazaCode, transaction) {
    try {
      // Get current live data
      const currentData = await redisService.getLiveParkingData(plazaCode) || {
        plazaCode,
        todayTransactions: 0,
        todayRevenue: 0,
        currentOccupancy: 0,
        recentTransactions: [],
        lastUpdated: new Date().toISOString()
      };

      // Update metrics
      currentData.todayTransactions += 1;
      currentData.todayRevenue += (transaction.parkingFee || 0);
      
      // Add to recent transactions (keep last 10)
      currentData.recentTransactions.unshift({
        id: transaction.id,
        vehicleNumber: transaction.vehicleNumber,
        exitTime: transaction.exitTime,
        parkingFee: transaction.parkingFee,
        paymentMode: transaction.paymentMode
      });
      
      if (currentData.recentTransactions.length > 10) {
        currentData.recentTransactions = currentData.recentTransactions.slice(0, 10);
      }

      currentData.lastUpdated = new Date().toISOString();

      // Store updated data
      await redisService.setLiveParkingData(plazaCode, currentData);

      return true;
    } catch (error) {
      console.error('❌ Failed to update live parking data:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## LANE STATUS MONITORING
   * ===============================================================================
   */

  /**
   * Broadcast lane status update
   */
  async broadcastLaneStatus(plazaId, laneId, status) {
    try {
      const broadcastData = {
        type: 'LANE_STATUS_UPDATE',
        plazaId,
        laneId,
        status: {
          isActive: status.isActive,
          isOnline: status.isOnline,
          lastHeartbeat: status.lastHeartbeat,
          errorCount: status.errorCount,
          currentVehicle: status.currentVehicle
        },
        timestamp: new Date().toISOString()
      };

      const channels = [
        'lanes:status:all',
        `lanes:status:plaza:${plazaId}`,
        `lanes:status:lane:${laneId}`
      ];

      // Broadcast to all relevant channels
      for (const channel of channels) {
        await this.publish(channel, broadcastData);
      }

      // Update lane status in Redis
      await this.updateLaneStatus(plazaId, laneId, status);

      return true;
    } catch (error) {
      console.error('❌ Failed to broadcast lane status:', error);
      return false;
    }
  }

  /**
   * Update lane status in Redis
   */
  async updateLaneStatus(plazaId, laneId, status) {
    try {
      // Get current plaza lane statuses
      const currentStatuses = await redisService.getLaneStatus(plazaId) || {};
      
      // Update specific lane status
      currentStatuses[laneId] = {
        ...status,
        lastUpdated: new Date().toISOString()
      };

      // Store updated statuses
      await redisService.setLaneStatus(plazaId, currentStatuses);

      return true;
    } catch (error) {
      console.error('❌ Failed to update lane status:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## SYSTEM NOTIFICATIONS
   * ===============================================================================
   */

  /**
   * Broadcast system notification
   */
  async broadcastSystemNotification(notification) {
    try {
      const broadcastData = {
        type: 'SYSTEM_NOTIFICATION',
        notification: {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          severity: notification.severity, // info, warning, error, success
          targetUsers: notification.targetUsers, // array of user IDs or 'all'
          targetRoles: notification.targetRoles, // array of roles
          expiresAt: notification.expiresAt
        },
        timestamp: new Date().toISOString()
      };

      const channels = ['notifications:system'];

      // Add role-specific channels
      if (notification.targetRoles) {
        for (const role of notification.targetRoles) {
          channels.push(`notifications:role:${role}`);
        }
      }

      // Add user-specific channels
      if (notification.targetUsers && notification.targetUsers !== 'all') {
        for (const userId of notification.targetUsers) {
          channels.push(`notifications:user:${userId}`);
        }
      }

      // Broadcast to all relevant channels
      for (const channel of channels) {
        await this.publish(channel, broadcastData);
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to broadcast system notification:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## DASHBOARD REAL-TIME UPDATES
   * ===============================================================================
   */

  /**
   * Broadcast dashboard update
   */
  async broadcastDashboardUpdate(updateType, data, targetUsers = []) {
    try {
      const broadcastData = {
        type: 'DASHBOARD_UPDATE',
        updateType, // 'METRICS', 'CHART_DATA', 'ALERT'
        data,
        timestamp: new Date().toISOString()
      };

      const channels = ['dashboard:updates:all'];

      // Add user-specific channels
      for (const userId of targetUsers) {
        channels.push(`dashboard:updates:user:${userId}`);
      }

      // Broadcast to all relevant channels
      for (const channel of channels) {
        await this.publish(channel, broadcastData);
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to broadcast dashboard update:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## ALERT SYSTEM
   * ===============================================================================
   */

  /**
   * Broadcast system alert
   */
  async broadcastAlert(alert) {
    try {
      const broadcastData = {
        type: 'SYSTEM_ALERT',
        alert: {
          id: alert.id,
          level: alert.level, // 'low', 'medium', 'high', 'critical'
          category: alert.category, // 'system', 'security', 'performance', 'business'
          title: alert.title,
          message: alert.message,
          source: alert.source,
          affectedEntities: alert.affectedEntities, // plazas, lanes, users affected
          actionRequired: alert.actionRequired,
          autoResolve: alert.autoResolve
        },
        timestamp: new Date().toISOString()
      };

      const channels = [
        'alerts:all',
        `alerts:level:${alert.level}`,
        `alerts:category:${alert.category}`
      ];

      // Broadcast to all relevant channels
      for (const channel of channels) {
        await this.publish(channel, broadcastData);
      }

      // Store alert in Redis for persistence
      await this.storeAlert(alert);

      return true;
    } catch (error) {
      console.error('❌ Failed to broadcast alert:', error);
      return false;
    }
  }

  /**
   * Store alert in Redis
   */
  async storeAlert(alert) {
    try {
      const alertKey = `alert:${alert.id}`;
      const alertData = {
        ...alert,
        createdAt: new Date().toISOString(),
        status: 'active'
      };

      await redisService.set(alertKey, alertData, 86400); // Store for 24 hours

      // Add to active alerts list
      const activeAlertsKey = 'alerts:active';
      const activeAlerts = await redisService.get(activeAlertsKey) || [];
      
      activeAlerts.push(alert.id);
      await redisService.set(activeAlertsKey, activeAlerts, 86400);

      return true;
    } catch (error) {
      console.error('❌ Failed to store alert:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## UTILITY METHODS
   * ===============================================================================
   */

  /**
   * Get real-time statistics
   */
  async getRealtimeStats() {
    try {
      const stats = {
        activeChannels: Array.from(this.eventHandlers.keys()),
        subscriberCount: this.eventHandlers.size,
        isInitialized: this.isInitialized,
        timestamp: new Date().toISOString()
      };

      return {
        success: true,
        stats
      };
    } catch (error) {
      console.error('❌ Failed to get realtime stats:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown() {
    try {
      if (this.subscriber) {
        await this.subscriber.quit();
      }
      
      if (this.publisher) {
        await this.publisher.quit();
      }

      this.eventHandlers.clear();
      this.isInitialized = false;

      console.log('✅ RealtimeService shutdown completed');
      return true;
    } catch (error) {
      console.error('❌ Failed to shutdown RealtimeService:', error);
      return false;
    }
  }
}

// Create singleton instance
const realtimeService = new RealtimeService();

module.exports = realtimeService;