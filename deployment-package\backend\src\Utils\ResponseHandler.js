// ============= UTILS =============

// utils/responseHandler.js - Response handler utility
/**
 * Response Handler - Utility for consistent API responses
 */
const responseHandler = {
    /**
     * Success response (200 OK)
     * @param {Object} res - Express response object
     * @param {*} data - Response data
     * @param {string} message - Success message
     */
    success: (res, data, message = 'Operation successful') => {
      return res.status(200).json({
        success: true,
        message,
        data
      });
    },
  
    /**
     * Created response (201 Created)
     * @param {Object} res - Express response object
     * @param {*} data - Created resource data
     * @param {string} message - Success message
     */
    created: (res, data, message = 'Resource created successfully') => {
      return res.status(201).json({
        success: true,
        message,
        data
      });
    },
  
    /**
     * Bad request response (400 Bad Request)
     * @param {Object} res - Express response object
     * @param {string} message - Error message
     */
    badRequest: (res, message = 'Bad request') => {
      return res.status(400).json({
        success: false,
        message
      });
    },
  
    /**
     * Unauthorized response (401 Unauthorized)
     * @param {Object} res - Express response object
     * @param {string} message - Error message
     */
    unauthorized: (res, message = 'Unauthorized') => {
      return res.status(401).json({
        success: false,
        message
      });
    },
  
    /**
     * Forbidden response (403 Forbidden)
     * @param {Object} res - Express response object
     * @param {string} message - Error message
     */
    forbidden: (res, message = 'Forbidden') => {
      return res.status(403).json({
        success: false,
        message
      });
    },
  
    /**
     * Not found response (404 Not Found)
     * @param {Object} res - Express response object
     * @param {string} message - Error message
     */
    notFound: (res, message = 'Resource not found') => {
      return res.status(404).json({
        success: false,
        message
      });
    },
  
    /**
     * Server error response (500 Internal Server Error)
     * @param {Object} res - Express response object
     * @param {string} message - Error message
     */
    error: (res, message = 'Internal server error') => {
      return res.status(500).json({
        success: false,
        message
      });
    }
  };
  
  module.exports = { responseHandler };
  