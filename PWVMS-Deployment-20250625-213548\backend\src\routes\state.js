const express = require('express');
const router = express.Router();
const stateController = require('../controllers/stateController');
const auth = require('../middleware/auth');

/**
 * @route   POST /api/states
 * @desc    Create a new state
 * @access  Private (SuperAdmin only)
 */
router.post('/', auth(['Create']), stateController.createState);

/**
 * @route   GET /api/states
 * @desc    Get all states
 * @access  Private (All authenticated users)
 */
router.get('/', auth(['View']), stateController.getStates);

/**
 * @route   GET /api/states/country/:countryId
 * @desc    Get states by country ID
 * @access  Private (All authenticated users)
 */
router.get('/country/:countryId', auth(['View']), stateController.getStatesByCountry);

/**
 * @route   GET /api/states/:id
 * @desc    Get a state by ID
 * @access  Private (All authenticated users)
 */
router.get('/:id', auth(['View']), stateController.getStateById);

/**
 * @route   PUT /api/states/:id
 * @desc    Update a state
 * @access  Private (SuperAdmin only)
 */
router.put('/:id', auth(['Edit']), stateController.updateState);

/**
 * @route   DELETE /api/states/:id
 * @desc    Delete a state
 * @access  Private (SuperAdmin only)
 */
router.delete('/:id', auth(['Delete']), stateController.deleteState);

module.exports = router;