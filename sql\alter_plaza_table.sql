-- <PERSON><PERSON>t to modify the AddressId column in the Plaza table to allow NULL values

-- First, check if there are any foreign key constraints on the AddressId column
SELECT 
    f.name AS ForeignKeyName,
    OBJECT_NAME(f.parent_object_id) AS TableName,
    COL_NAME(fc.parent_object_id, fc.parent_column_id) AS ColumnName,
    OBJECT_NAME(f.referenced_object_id) AS ReferencedTableName,
    COL_NAME(fc.referenced_object_id, fc.referenced_column_id) AS ReferencedColumnName
FROM 
    sys.foreign_keys AS f
INNER JOIN 
    sys.foreign_key_columns AS fc ON f.object_id = fc.constraint_object_id
WHERE 
    OBJECT_NAME(f.parent_object_id) = 'Plaza' AND
    COL_NAME(fc.parent_object_id, fc.parent_column_id) = 'AddressId';

-- If there are foreign key constraints, we need to drop them first
-- (This is a placeholder - you'll need to replace 'FK_Name' with the actual foreign key name from the query above)
-- ALTER TABLE Plaza DROP CONSTRAINT FK_Name;

-- Now alter the column to allow NULL values
ALTER TABLE Plaza ALTER COLUMN AddressId INT NULL;

-- If there were foreign key constraints, recreate them
-- (This is a placeholder - you'll need to adjust based on the actual constraints)
-- ALTER TABLE Plaza ADD CONSTRAINT FK_Name FOREIGN KEY (AddressId) REFERENCES Address(Id);

-- Verify the change
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE 
FROM 
    INFORMATION_SCHEMA.COLUMNS 
WHERE 
    TABLE_NAME = 'Plaza' AND 
    COLUMN_NAME = 'AddressId';