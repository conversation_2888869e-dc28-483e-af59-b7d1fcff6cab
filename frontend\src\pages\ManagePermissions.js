import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Shield, 
  Users, 
  Settings, 
  Search, 
  Filter,
  RefreshCw,
  Download,
  Upload,
  Copy,
  CheckSquare,
  Square,
  AlertCircle
} from 'lucide-react';
import permissionManagementApi from '../api/permissionManagementApi';
import { useAuth } from '../contexts/authContext';
import { useToast } from '../contexts/ToastContext';
import PermissionMatrix from '../components/PermissionManagement/PermissionMatrix';
import RolePermissionEditor from '../components/PermissionManagement/RolePermissionEditor';

const ManagePermissions = () => {
  const { user } = useAuth();
  const toast = useToast();
  const queryClient = useQueryClient();

  // State management
  const [viewMode, setViewMode] = useState('matrix'); // 'matrix' or 'role-editor'
  const [selectedRole, setSelectedRole] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterModule, setFilterModule] = useState('');

  // Check if user has permission to access this page
  useEffect(() => {
    if (user && user.role !== 'SuperAdmin') {
      toast.showError('Access denied. Only SuperAdmin can manage permissions.');
      // Redirect or handle unauthorized access
      return;
    }
  }, [user, toast]);

  // Fetch data using React Query
  const { 
    data: modulesData, 
    isLoading: modulesLoading, 
    error: modulesError 
  } = useQuery({
    queryKey: ['permission-modules-tree'],
    queryFn: permissionManagementApi.getModulesTree,
    enabled: user?.role === 'SuperAdmin'
  });

  const { 
    data: rolesData, 
    isLoading: rolesLoading, 
    error: rolesError 
  } = useQuery({
    queryKey: ['permission-roles'],
    queryFn: permissionManagementApi.getRoles,
    enabled: user?.role === 'SuperAdmin'
  });

  const { 
    data: matrixData, 
    isLoading: matrixLoading, 
    error: matrixError 
  } = useQuery({
    queryKey: ['permission-matrix'],
    queryFn: permissionManagementApi.getPermissionMatrix,
    enabled: user?.role === 'SuperAdmin' && viewMode === 'matrix'
  });

  // Mutations for permission updates
  const updatePermissionsMutation = useMutation({
    mutationFn: ({ roleId, permissions }) => 
      permissionManagementApi.updateRolePermissions(roleId, permissions),
    onSuccess: () => {
      queryClient.invalidateQueries(['permission-matrix']);
      queryClient.invalidateQueries(['permission-roles']);
      toast.showSuccess('Permissions updated successfully');
    },
    onError: (error) => {
      console.error('Error updating permissions:', error);
      toast.showError(error.response?.data?.message || 'Failed to update permissions');
    }
  });

  const bulkUpdateMutation = useMutation({
    mutationFn: ({ roleId, updates }) => 
      permissionManagementApi.bulkUpdateRolePermissions(roleId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries(['permission-matrix']);
      queryClient.invalidateQueries(['permission-roles']);
      toast.showSuccess('Bulk permissions updated successfully');
    },
    onError: (error) => {
      console.error('Error in bulk update:', error);
      toast.showError(error.response?.data?.message || 'Failed to update permissions');
    }
  });

  // Event handlers
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
    setSelectedRole(null);
  };

  const handleRoleSelect = (role) => {
    setSelectedRole(role);
    setViewMode('role-editor');
  };

  const handlePermissionUpdate = (roleId, permissions) => {
    updatePermissionsMutation.mutate({ roleId, permissions });
  };

  const handleBulkUpdate = (roleId, updates) => {
    bulkUpdateMutation.mutate({ roleId, updates });
  };

  const handleRefresh = () => {
    queryClient.invalidateQueries(['permission-modules-tree']);
    queryClient.invalidateQueries(['permission-roles']);
    queryClient.invalidateQueries(['permission-matrix']);
    toast.showSuccess('Data refreshed');
  };

  // Loading and error states
  if (user?.role !== 'SuperAdmin') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Only SuperAdmin can access permission management.</p>
        </div>
      </div>
    );
  }

  const isLoading = modulesLoading || rolesLoading || (viewMode === 'matrix' && matrixLoading);
  const hasError = modulesError || rolesError || (viewMode === 'matrix' && matrixError);

  if (hasError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Data</h2>
          <p className="text-gray-600 mb-4">Failed to load permission data.</p>
          <button
            onClick={handleRefresh}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
              <Shield className="w-8 h-8 text-blue-600" />
              Permission Management
            </h1>
            <p className="text-gray-600 mt-2">
              Manage role-based permissions for modules and features
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
          {/* View Mode Selector */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">View:</span>
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => handleViewModeChange('matrix')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'matrix'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Matrix View
              </button>
              <button
                onClick={() => handleViewModeChange('role-editor')}
                className={`px-3 py-1 text-sm rounded-md transition-colors ${
                  viewMode === 'role-editor'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Role Editor
              </button>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search modules..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            {modulesData?.data && (
              <select
                value={filterModule}
                onChange={(e) => setFilterModule(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Modules</option>
                {modulesData.data.map(module => (
                  <option key={module.id} value={module.id}>
                    {module.name}
                  </option>
                ))}
              </select>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      {isLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Loading permission data...</p>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border">
          {viewMode === 'matrix' ? (
            <PermissionMatrix
              modules={modulesData?.data || []}
              roles={rolesData?.data || []}
              matrix={matrixData?.data || []}
              searchTerm={searchTerm}
              filterModule={filterModule}
              onPermissionUpdate={handlePermissionUpdate}
              onBulkUpdate={handleBulkUpdate}
              onRoleSelect={handleRoleSelect}
              isLoading={updatePermissionsMutation.isPending || bulkUpdateMutation.isPending}
            />
          ) : (
            <RolePermissionEditor
              roles={rolesData?.data || []}
              modules={modulesData?.data || []}
              selectedRole={selectedRole}
              onRoleSelect={setSelectedRole}
              onPermissionUpdate={handlePermissionUpdate}
              onBulkUpdate={handleBulkUpdate}
              searchTerm={searchTerm}
              filterModule={filterModule}
              isLoading={updatePermissionsMutation.isPending || bulkUpdateMutation.isPending}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default ManagePermissions;
