import React, { useState, useEffect } from 'react';
import { CreditCard, Smartphone, Wallet, ArrowRight, AlertCircle, Loader2, CheckCircle, Shield } from 'lucide-react';
import { valetCustomerApi } from '../../api/valetCustomerApi';
import { useToast } from '../../hooks/useToast';

const PaymentGateway = ({ 
  customerData,
  plazaInfo,
  transactionData,
  onPaymentSuccess, 
  onPaymentError,
  onBack,
  className = "" 
}) => {
  const [paymentOptions, setPaymentOptions] = useState([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [paymentAmount, setPaymentAmount] = useState(0);
  const [paymentBreakdown, setPaymentBreakdown] = useState(null);
  
  const toast = useToast();

  // Load payment options and amount
  useEffect(() => {
    loadPaymentOptions();
  }, [plazaInfo]);

  const loadPaymentOptions = async () => {
    try {
      setIsLoading(true);
      setError('');

      const response = await valetCustomerApi.getPaymentOptions(plazaInfo.plazaValetPointId);
      
      if (response.success) {
        setPaymentOptions(response.paymentMethods || []);
        setPaymentAmount(response.amount || 0);
        setPaymentBreakdown(response.breakdown || null);
        
        // Auto-select first available payment method
        if (response.paymentMethods && response.paymentMethods.length > 0) {
          setSelectedPaymentMethod(response.paymentMethods[0]);
        }
      } else {
        throw new Error(response.message || 'Failed to load payment options');
      }
      
    } catch (error) {
      console.error('Error loading payment options:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to load payment options';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentMethodSelect = (method) => {
    setSelectedPaymentMethod(method);
    setError('');
  };

  const initiatePayment = async () => {
    if (!selectedPaymentMethod) {
      setError('Please select a payment method');
      return;
    }

    try {
      setIsProcessing(true);
      setError('');

      const paymentData = {
        customerId: customerData.customerId,
        plazaId: plazaInfo.plazaId,
        plazaValetPointId: plazaInfo.plazaValetPointId,
        amount: paymentAmount,
        paymentMethod: selectedPaymentMethod.method,
        gateway: selectedPaymentMethod.gateway,
        customerDetails: {
          name: customerData.guestName,
          mobile: customerData.mobileNumber,
          vehicleNumber: customerData.vehicleNumber
        },
        transactionData
      };

      const response = await valetCustomerApi.initiatePayment(paymentData);
      
      if (response.success) {
        // Handle different payment gateways
        if (selectedPaymentMethod.gateway === 'razorpay') {
          await handleRazorpayPayment(response.paymentData);
        } else if (selectedPaymentMethod.gateway === 'phonepe') {
          await handlePhonePePayment(response.paymentData);
        } else {
          throw new Error('Unsupported payment gateway');
        }
      } else {
        throw new Error(response.message || 'Failed to initiate payment');
      }
      
    } catch (error) {
      console.error('Error initiating payment:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Payment initiation failed';
      setError(errorMessage);
      toast.showError(errorMessage);
      
      if (onPaymentError) {
        onPaymentError(error);
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRazorpayPayment = async (paymentData) => {
    return new Promise((resolve, reject) => {
      // Load Razorpay script if not already loaded
      if (!window.Razorpay) {
        const script = document.createElement('script');
        script.src = 'https://checkout.razorpay.com/v1/checkout.js';
        script.onload = () => processRazorpayPayment(paymentData, resolve, reject);
        script.onerror = () => reject(new Error('Failed to load Razorpay'));
        document.body.appendChild(script);
      } else {
        processRazorpayPayment(paymentData, resolve, reject);
      }
    });
  };

  const processRazorpayPayment = (paymentData, resolve, reject) => {
    const options = {
      key: paymentData.keyId,
      amount: paymentData.amount,
      currency: paymentData.currency || 'INR',
      name: 'Parkwiz Valet Service',
      description: `Valet service at ${plazaInfo.plazaName}`,
      order_id: paymentData.orderId,
      handler: async (response) => {
        try {
          await verifyPayment(response.razorpay_payment_id, paymentData.orderId);
          resolve(response);
        } catch (error) {
          reject(error);
        }
      },
      prefill: {
        name: customerData.guestName,
        contact: customerData.mobileNumber
      },
      theme: {
        color: '#EAB308' // Yellow theme
      },
      modal: {
        ondismiss: () => {
          reject(new Error('Payment cancelled by user'));
        }
      }
    };

    const rzp = new window.Razorpay(options);
    rzp.open();
  };

  const handlePhonePePayment = async (paymentData) => {
    // PhonePe integration - redirect to PhonePe payment page
    if (paymentData.redirectUrl) {
      window.location.href = paymentData.redirectUrl;
    } else {
      throw new Error('PhonePe payment URL not provided');
    }
  };

  const verifyPayment = async (paymentId, orderId) => {
    try {
      const response = await valetCustomerApi.verifyPayment(paymentId, orderId);
      
      if (response.success) {
        toast.showSuccess('Payment successful!');
        onPaymentSuccess({
          paymentId,
          orderId,
          transactionId: response.transactionId,
          paymentData: response.paymentData
        });
      } else {
        throw new Error(response.message || 'Payment verification failed');
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      throw error;
    }
  };

  const getPaymentMethodIcon = (method) => {
    switch (method.toLowerCase()) {
      case 'card':
      case 'credit_card':
      case 'debit_card':
        return <CreditCard className="w-6 h-6" />;
      case 'upi':
      case 'phonepe':
        return <Smartphone className="w-6 h-6" />;
      case 'wallet':
        return <Wallet className="w-6 h-6" />;
      default:
        return <CreditCard className="w-6 h-6" />;
    }
  };

  if (isLoading) {
    return (
      <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">Loading payment options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
      
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CreditCard className="w-8 h-8 text-yellow-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment</h2>
        <p className="text-gray-600">
          Complete your valet service payment
        </p>
      </div>

      {/* Customer & Plaza Info */}
      <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="flex justify-between items-start mb-2">
          <div>
            <h3 className="font-semibold text-gray-900">{customerData.guestName}</h3>
            <p className="text-sm text-gray-600">{customerData.vehicleNumber}</p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">{plazaInfo.plazaName}</p>
            <p className="text-xs text-gray-500">{plazaInfo.valetPointName}</p>
          </div>
        </div>
      </div>

      {/* Payment Amount */}
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex justify-between items-center">
          <span className="text-lg font-semibold text-blue-900">Total Amount</span>
          <span className="text-2xl font-bold text-blue-900">₹{paymentAmount}</span>
        </div>
        
        {paymentBreakdown && (
          <div className="mt-3 pt-3 border-t border-blue-200">
            <div className="space-y-1 text-sm">
              {paymentBreakdown.baseAmount && (
                <div className="flex justify-between">
                  <span className="text-blue-700">Base Amount</span>
                  <span className="text-blue-700">₹{paymentBreakdown.baseAmount}</span>
                </div>
              )}
              {paymentBreakdown.taxes && (
                <div className="flex justify-between">
                  <span className="text-blue-700">Taxes</span>
                  <span className="text-blue-700">₹{paymentBreakdown.taxes}</span>
                </div>
              )}
              {paymentBreakdown.fees && (
                <div className="flex justify-between">
                  <span className="text-blue-700">Service Fee</span>
                  <span className="text-blue-700">₹{paymentBreakdown.fees}</span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Payment Methods */}
      {paymentOptions.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Select Payment Method</h3>
          <div className="space-y-3">
            {paymentOptions.map((option, index) => (
              <button
                key={index}
                onClick={() => handlePaymentMethodSelect(option)}
                className={`
                  w-full p-4 border-2 rounded-lg text-left transition-colors
                  ${selectedPaymentMethod?.method === option.method
                    ? 'border-yellow-500 bg-yellow-50'
                    : 'border-gray-200 hover:border-gray-300'
                  }
                `}
                disabled={isProcessing}
              >
                <div className="flex items-center">
                  <div className={`mr-3 ${selectedPaymentMethod?.method === option.method ? 'text-yellow-600' : 'text-gray-400'}`}>
                    {getPaymentMethodIcon(option.method)}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{option.displayName}</h4>
                    <p className="text-sm text-gray-600">{option.description}</p>
                  </div>
                  {selectedPaymentMethod?.method === option.method && (
                    <CheckCircle className="w-5 h-5 text-yellow-600" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="mb-4 flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />
          <span className="text-sm text-red-700">{error}</span>
        </div>
      )}

      {/* Pay Button */}
      <button
        onClick={initiatePayment}
        disabled={!selectedPaymentMethod || isProcessing || paymentAmount <= 0}
        className={`
          w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors
          ${selectedPaymentMethod && !isProcessing && paymentAmount > 0
            ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }
        `}
      >
        {isProcessing ? (
          <>
            <Loader2 className="w-5 h-5 mr-2 animate-spin" />
            Processing Payment...
          </>
        ) : (
          <>
            Pay ₹{paymentAmount}
            <ArrowRight className="w-5 h-5 ml-2" />
          </>
        )}
      </button>

      {/* Security Notice */}
      <div className="mt-4 flex items-center justify-center text-sm text-gray-500">
        <Shield className="w-4 h-4 mr-1" />
        <span>Secure payment powered by trusted gateways</span>
      </div>

      {/* Back Button */}
      {onBack && (
        <button
          onClick={onBack}
          disabled={isProcessing}
          className="w-full mt-4 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
        >
          ← Back to Customer Details
        </button>
      )}
    </div>
  );
};

export default PaymentGateway;
