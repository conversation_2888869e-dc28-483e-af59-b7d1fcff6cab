# 🔧 Redis Configuration Detailed Guide for PWVMS

## 📋 Environment Variables Explained

### **Redis Connection Settings**

```env
# Redis Connection
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

#### **REDIS_HOST=localhost**
- **What it is**: The hostname or IP address where Redis server is running
- **Why localhost**: Since you're running Redis on the same server as your Node.js application
- **Production alternatives**:
  ```env
  # For Azure Redis Cache
  REDIS_HOST=your-redis-cache.redis.cache.windows.net
  
  # For AWS ElastiCache
  REDIS_HOST=your-cluster.abc123.cache.amazonaws.com
  
  # For separate server
  REDIS_HOST=*************
  ```

#### **REDIS_PORT=6379**
- **What it is**: The port Redis server listens on
- **Why 6379**: This is Redis's default port (like 80 for HTTP, 443 for HTTPS)
- **When to change**: Only if you configure Redis to use a different port
- **Security note**: In production, consider using non-standard ports

#### **REDIS_PASSWORD=**
- **What it is**: Authentication password for Redis
- **Why empty**: By default, Redis doesn't require authentication on localhost
- **Production requirement**: **MUST** set a strong password in production
- **Example**:
  ```env
  # Generate a strong password
  REDIS_PASSWORD=Parkwiz@Redis2024!Secure#Cache
  ```

#### **REDIS_DB=0**
- **What it is**: Redis database number (Redis has 16 databases by default: 0-15)
- **Why 0**: Default database, good for single-application use
- **When to change**: If you want to separate different types of data
- **Example usage**:
  ```env
  REDIS_DB=0  # Main application cache
  REDIS_DB=1  # Session storage
  REDIS_DB=2  # Rate limiting data
  ```

### **Cache TTL (Time To Live) Settings**

```env
# Cache TTL (seconds)
CACHE_TTL_DASHBOARD_SUMMARY=300      # 5 minutes
CACHE_TTL_DASHBOARD_CHARTS=600       # 10 minutes
CACHE_TTL_USER_SESSION=3600          # 1 hour
CACHE_TTL_LIVE_DATA=30               # 30 seconds
```

#### **Why Different TTL Values?**

1. **CACHE_TTL_DASHBOARD_SUMMARY=300 (5 minutes)**
   - **Reason**: Dashboard data changes frequently with new parking transactions
   - **Balance**: Fresh enough for business decisions, cached enough for performance
   - **Adjust if**: More real-time needed (reduce to 60-120), or less frequent updates (increase to 600)

2. **CACHE_TTL_DASHBOARD_CHARTS=600 (10 minutes)**
   - **Reason**: Chart data is more analytical, doesn't need to be as fresh
   - **Performance**: Longer cache = better performance for complex chart queries
   - **Business impact**: 10-minute old chart data is usually acceptable

3. **CACHE_TTL_USER_SESSION=3600 (1 hour)**
   - **Reason**: User sessions should persist but not forever
   - **Security**: Automatic logout after inactivity
   - **User experience**: Long enough to avoid frequent re-logins

4. **CACHE_TTL_LIVE_DATA=30 (30 seconds)**
   - **Reason**: "Live" data like current parking occupancy needs to be very fresh
   - **Real-time feel**: 30 seconds feels real-time to users
   - **Performance**: Still provides caching benefit for high-frequency requests

### **Rate Limiting Settings**

```env
# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000          # 15 minutes in milliseconds
RATE_LIMIT_MAX_REQUESTS=100          # 100 requests per window
```

#### **RATE_LIMIT_WINDOW_MS=900000 (15 minutes)**
- **What it is**: Time window for counting requests
- **Why 15 minutes**: Good balance between protection and usability
- **Calculation**: 15 minutes × 60 seconds × 1000 milliseconds = 900,000ms
- **Alternatives**:
  ```env
  RATE_LIMIT_WINDOW_MS=60000    # 1 minute (stricter)
  RATE_LIMIT_WINDOW_MS=3600000  # 1 hour (more lenient)
  ```

#### **RATE_LIMIT_MAX_REQUESTS=100**
- **What it is**: Maximum requests allowed per window per IP
- **Why 100**: Allows normal usage while preventing abuse
- **Calculation**: 100 requests ÷ 15 minutes = ~6.7 requests per minute (reasonable for dashboard usage)
- **Adjust based on usage patterns**:
  ```env
  RATE_LIMIT_MAX_REQUESTS=50   # Stricter for public APIs
  RATE_LIMIT_MAX_REQUESTS=200  # More lenient for internal tools
  ```

### **Feature Flags**

```env
# Features
REALTIME_ENABLED=true
PUBSUB_ENABLED=true
```

#### **REALTIME_ENABLED=true**
- **What it does**: Enables real-time dashboard updates
- **Impact**: Users see live data without refreshing
- **Disable if**: You want to reduce server load or troubleshoot issues

#### **PUBSUB_ENABLED=true**
- **What it does**: Enables Redis pub/sub for notifications
- **Impact**: System-wide notifications and alerts
- **Disable if**: You don't need notifications or want to simplify setup

## 🔧 Redis Server Configuration (redis.conf)

### **Network Settings**

```conf
port 6379
bind 127.0.0.1
```

#### **port 6379**
- **Purpose**: Which port Redis listens on
- **Default**: 6379 is the standard Redis port
- **Security**: Consider changing in production
- **Must match**: Your REDIS_PORT environment variable

#### **bind 127.0.0.1**
- **Purpose**: Which network interfaces Redis accepts connections from
- **127.0.0.1**: Only localhost (same machine)
- **Security**: Prevents external access
- **Production alternatives**:
  ```conf
  bind 127.0.0.1 *************  # Localhost + specific internal IP
  bind 0.0.0.0                  # All interfaces (NOT recommended without password)
  ```

### **Memory Management**

```conf
maxmemory 256mb
maxmemory-policy allkeys-lru
```

#### **maxmemory 256mb**
- **Purpose**: Maximum memory Redis can use
- **Why 256MB**: Good starting point for small to medium applications
- **Adjust based on**:
  - Available server RAM
  - Application cache needs
  - Other applications on server
- **Examples**:
  ```conf
  maxmemory 128mb   # Small application
  maxmemory 512mb   # Medium application
  maxmemory 1gb     # Large application
  maxmemory 2gb     # High-traffic application
  ```

#### **maxmemory-policy allkeys-lru**
- **Purpose**: What to do when memory limit is reached
- **allkeys-lru**: Remove least recently used keys from ALL keys
- **Why this policy**: Best for cache-only usage (your use case)
- **Other options**:
  ```conf
  maxmemory-policy volatile-lru    # Only remove LRU keys with expiration
  maxmemory-policy allkeys-random  # Remove random keys
  maxmemory-policy volatile-ttl    # Remove keys with shortest TTL first
  ```

### **Persistence Settings**

```conf
save 900 1      # Save if at least 1 key changed in 900 seconds (15 minutes)
save 300 10     # Save if at least 10 keys changed in 300 seconds (5 minutes)
save 60 10000   # Save if at least 10000 keys changed in 60 seconds (1 minute)
```

#### **Why These Settings?**
- **Purpose**: Automatically save data to disk
- **Balance**: Performance vs. data safety
- **Your use case**: Since you're using Redis for caching (not primary data), you could disable persistence entirely
- **Alternative for cache-only**:
  ```conf
  # Disable all persistence for pure cache
  save ""
  ```

## 🔐 Security Recommendations

### **For Development (Current Setup)**
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
```
✅ **Acceptable** - Redis only accessible from same machine

### **For Production (Required Changes)**
```env
REDIS_HOST=your-redis-server.com
REDIS_PORT=6380                    # Non-standard port
REDIS_PASSWORD=Parkwiz@Redis2024!  # Strong password
```

### **Redis Configuration for Production**
```conf
port 6380
bind 127.0.0.1
requirepass Parkwiz@Redis2024!
maxmemory 1gb
maxmemory-policy allkeys-lru
```

## 📊 Performance Tuning

### **Based on Your Database Setup**
Looking at your current database configuration:
```env
DB_POOL_MAX=10
DB_POOL_MIN=0
DB_TIMEOUT=30000
```

### **Recommended Redis Settings for Your Scale**
```env
# Optimized for your database load
CACHE_TTL_DASHBOARD_SUMMARY=300    # 5 minutes (good balance)
CACHE_TTL_DASHBOARD_CHARTS=900     # 15 minutes (reduce DB load)
CACHE_TTL_USER_SESSION=7200        # 2 hours (match your usage patterns)
CACHE_TTL_LIVE_DATA=60             # 1 minute (still feels live)

# Rate limiting for your scale
RATE_LIMIT_WINDOW_MS=900000        # 15 minutes
RATE_LIMIT_MAX_REQUESTS=150        # Slightly higher for business users
```

### **Redis Memory Sizing**
```conf
# For your application size, start with:
maxmemory 512mb

# Monitor and adjust based on:
# - Number of concurrent users
# - Amount of cached data
# - Available server RAM
```

## 🚀 Integration with Your Current Setup

### **Add to Your Existing .env File**
```env
# Add these Redis settings to your backend/.env file

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# Cache TTL Settings (seconds)
CACHE_TTL_DASHBOARD_SUMMARY=300
CACHE_TTL_DASHBOARD_CHARTS=600
CACHE_TTL_USER_SESSION=3600
CACHE_TTL_USER_PERMISSIONS=1800
CACHE_TTL_LIVE_DATA=30
CACHE_TTL_STATIC_DATA=86400

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=false

# Real-time Features
REALTIME_ENABLED=true
PUBSUB_ENABLED=true
NOTIFICATIONS_ENABLED=true

# Performance Settings
REDIS_POOL_SIZE=10
REDIS_IDLE_TIMEOUT=60000
REDIS_MAX_RETRIES=3

# Monitoring
REDIS_MONITORING_ENABLED=true
CACHE_METRICS_ENABLED=true
```

## 🔍 Monitoring and Troubleshooting

### **Check Redis Status**
```bash
# Test Redis connection
redis-cli ping
# Should return: PONG

# Check Redis info
redis-cli info memory
redis-cli info stats

# Monitor real-time commands
redis-cli monitor
```

### **Application Health Check**
Your enhanced health check endpoint will show:
```json
{
  "redis": {
    "connected": true,
    "memory": { "used": "45MB", "peak": "67MB" },
    "keyCount": 1247,
    "hitRate": "92.3%"
  }
}
```

## 🎯 Next Steps

1. **Add Redis settings to your .env file**
2. **Run the Redis setup script**: `.\scripts\setup-redis.ps1`
3. **Restart your application**
4. **Check health endpoint**: `http://localhost:5000/health-check`
5. **Monitor performance improvements**

Your PWVMS will now have enterprise-grade caching with these optimized settings! 🚀