const express = require('express');
const router = express.Router();
const companyMasterController = require('../controllers/CompanyMasterController');
const auth = require('../middleware/auth');

// Middleware to handle file uploads
const upload = companyMasterController.uploadCompanyFiles();

/**
 * @route   GET /api/companies
 * @desc    Get all companies
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.get('/', auth(['View']), companyMasterController.getAllCompanies);

/**
 * @route   GET /api/companies/:id/logo
 * @desc    Get company logo by ID
 * @access  Public (Logos can be public for display purposes)
 */
router.get('/:id/logo', companyMasterController.getCompanyLogo);

/**
 * @route   GET /logo/:id
 * @desc    Alternative endpoint for company logo (for easier debugging)
 * @access  Public
 */
router.get('/logo/:id', companyMasterController.getCompanyLogo);

/**
 * @route   GET /debug/:id
 * @desc    Debug endpoint to check company data
 * @access  Public (for debugging)
 */
router.get('/debug/:id', async (req, res) => {
  try {
    const id = req.params.id;
    const db = require('../config/database');
    const result = await db.query(`
      SELECT *, 
             CASE 
               WHEN CompanyLogo IS NOT NULL 
               THEN CONVERT(NVARCHAR(100), 'BINARY_DATA_EXISTS') 
               ELSE NULL 
             END AS CompanyLogoStatus
      FROM tblCompanyMaster 
      WHERE Id = @Id
    `, { Id: id });

    if (!result.recordset || result.recordset.length === 0) {
      return res.status(404).json({ message: 'Company not found' });
    }

    const company = { ...result.recordset[0] };
    
    // Check if logo exists
    const logoExists = !!company.CompanyLogo;
    
    // Don't send the binary data in the response
    if (company.CompanyLogo) {
      company.CompanyLogo = '[BINARY DATA]';
    }

    return res.json({
      company: {
        ...company,
        logoExists,
        logoUrl: logoExists ? `/api/companies/${id}/logo` : null
      }
    });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/companies/:companyId/plazas
 * @desc    Get plazas by company ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:companyId/plazas', auth(['View']), companyMasterController.getPlazaByCompany);

/**
 * @route   GET /api/companies/:id
 * @desc    Get company by ID
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.get('/:id', auth(['View']), companyMasterController.getCompanyById);

/**
 * @route   POST /api/companies
 * @desc    Create a new company
 * @access  Private (SuperAdmin only)
 */
router.post('/', auth(['Create']), upload, companyMasterController.createCompany);

/**
 * @route   PUT /api/companies/:id
 * @desc    Update a company
 * @access  Private (SuperAdmin, CompanyAdmin for their own company)
 */
router.put('/:id', auth(['Edit']), upload, companyMasterController.updateCompany);

/**
 * @route   DELETE /api/companies/:id
 * @desc    Delete a company
 * @access  Private (SuperAdmin only)
 */
router.delete('/:id', auth(['Delete']), companyMasterController.deleteCompany);

module.exports = router;
