# IIS Setup Instructions for PWVMS

## 1. Copy the modified files to your production environment

Copy the following files from your development environment to your production environment:
- web.config (from d:/PWVMS to C:\inetpub\wwwroot\PWVMS)
- frontend/build/index.html (from d:/PWVMS to C:\inetpub\wwwroot\PWVMS\frontend\build)

## 2. Verify IIS Application Setup

1. Open IIS Manager
2. Expand the server node
3. Expand "Sites"
4. Expand "Default Web Site"
5. Verify that "PWVMS" is listed as an application (it should have a different icon than regular folders)
6. If it's not an application, right-click on it and select "Convert to Application"

## 3. Install Required IIS Features

Make sure the following IIS features are installed:
- URL Rewrite Module (https://www.iis.net/downloads/microsoft/url-rewrite)
- IISNode (https://github.com/Azure/iisnode/releases)

## 4. Configure Application Pool

1. In IIS Manager, go to "Application Pools"
2. Find the application pool used by your PWVMS application (likely "DefaultAppPool")
3. Right-click on it and select "Advanced Settings"
4. Set "Enable 32-Bit Applications" to "True" if you're using 32-bit Node.js
5. Set "Identity" to a user account that has permissions to access your Node.js installation

## 5. Restart IIS

1. Open an elevated Command Prompt or PowerShell
2. Run: iisreset

## 6. Test Your Application

1. Open a web browser
2. Navigate to http://localhost/PWVMS
3. You should see your React application

## Troubleshooting

If you encounter issues:

1. Check the IIS logs at C:\inetpub\logs\LogFiles
2. Check the Node.js logs at C:\inetpub\wwwroot\PWVMS\iisnode
3. Make sure all file paths in web.config are correct
4. Verify that Node.js is installed and accessible to the IIS application pool identity
5. Check that all required Node.js modules are installed in the backend directory