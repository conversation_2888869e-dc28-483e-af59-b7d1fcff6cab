import React, { useEffect, useState } from 'react';
import { Plus, Edit2, Trash2, UserPlus } from 'lucide-react';
import {branchApi}  from '../api/branchApi'; // Updated to use branchApi
import {companyApi} from '../api/companyApi';
import BranchDialog from '../components/Branch/BranchDialog';
import InviteDialog from '../components/Branch/InviteDialog';
import SearchBar from '../components/SearchBar';
import Pagination from '../components/Pagination';
import toast from 'react-hot-toast';

const ITEMS_PER_PAGE = 10;

export default function BranchManagement() {
  const [branches, setBranches] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState(null);
  // const [selectedHeadId, setSelectedHeadId] = useState(null); // State for selected branch head ID

  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    fetchBranches();
    fetchCompanies();
  }, []);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const fetchBranches = async () => {
    try {
      const branches = await branchApi.getBranches(); // Updated to use branchApi
      setBranches(branches);
    } catch (error) {
      toast.error('Failed to fetch branches');
    }
  };

  const fetchCompanies = async () => {
    try {
      const companies = await companyApi.getCompanies(); // Updated to use branchApi
      setCompanies(companies);
    } catch (error) {
      toast.error('Failed to fetch companies');
    }
  };

  const handleCreateBranch = async (data) => {
    try {
      await branchApi.createBranch(data); // Updated to use branchApi
      toast.success('Branch created successfully');
      setIsCreateDialogOpen(false);
      fetchBranches();
    } catch (error) {
      toast.error('Failed to create branch');
    }
  };

  // const handleCreateBranch = async (data) => {
  //   try {
  //     const response = await branchApi.createBranch(data); // Ensure `branchApi.createBranch` is defined correctly
  //     console.log('Branch created:', response.data);
  //   } catch (error) {
  //     console.error('Error creating branch:', error.response?.data || error.message);
  //     throw error; // Rethrow to show error in the dialog
  //   }
  // };
  

  const handleUpdateBranch = async (data) => {
    if (!selectedBranch || !selectedBranch._id) {
      console.error('No selected branch or ID is undefined'); 
    return;
    }
    try {
      await branchApi.updateBranch(selectedBranch._id, data); // Updated to use branchApi
      toast.success('Branch updated successfully');
      setIsEditDialogOpen(false);
      setSelectedBranch(null);
      fetchBranches();
    } catch (error) {
      toast.error('Failed to update branch');
    }
  };

  const handleDeleteBranch = async (id) => {
    if (!window.confirm('Are you sure you want to delete this branch?')) return;
    try {
      await branchApi.deleteBranch(id); // Updated to use branchApi
      toast.success('Branch deleted successfully');
      fetchBranches();
    } catch (error) {
      toast.error('Failed to delete branch');
    }
  };

  // const handleInviteBranchHead = async (data) => {
  //   if (!selectedBranch) return;
  //   try {
  //     await branchApi.assignBranchHead(selectedBranch._id, data); // Updated to use branchApi
  //     toast.success('Invitation sent successfully');
  //     setIsInviteDialogOpen(false);
  //     setSelectedBranch(null);
  //   } catch (error) {
  //     toast.error('Failed to send invitation');
  //   }
  // };

  const handleInviteBranchHead = async (data) => {
    if (!selectedBranch) return; // Ensure there's a selected branch

    try {
        await branchApi.inviteBranchHead({ email: data.email, branchId: selectedBranch._id }); // Send invitation with email and branch ID
        toast.success('Invitation sent successfully');
        setIsInviteDialogOpen(false); // Close dialog after sending invitation
    } catch (error) {
        toast.error('Failed to send invitation');
    }
};

//    // Handle assigning a new branch head
//    const handleAssignBranchHead = async (headId) => {
//     if (!selectedBranch) return; // Ensure there's a selected branch

//     try {
//         await branchApi.assignBranchHead(selectedBranch._id, { branchHeadId: headId }); // Assign the selected user as the branch head
//         toast.success('Branch head assigned successfully');
//         fetchBranches(); // Refresh branches after assignment
//     } catch (error) {
//         toast.error('Failed to assign branch head');
//     }
// };


  const filteredBranches = branches.filter((branch) =>
    branch.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    branch.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (branch.company && branch.company.name.toLowerCase().includes(searchQuery.toLowerCase())) // Ensure company exists
  );

  const totalPages = Math.ceil(filteredBranches.length / ITEMS_PER_PAGE);
  const paginatedBranches = filteredBranches.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className="min-h-screen bg-[#FFFFFF] p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-[#000000]">Branch Management</h1>
          <button
            onClick={() => setIsCreateDialogOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-[#9AC8CE] text-white rounded hover:bg-[#7BA9AF] transition-colors focus:ring-2 focus:ring-offset-2 focus:ring-[#9AC8CE]"
          >
            <Plus size={20} />
            Add Branch
          </button>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <SearchBar
            value={searchQuery}
            onChange={setSearchQuery}
            placeholder="Search branches by name, location, or company..."
          />
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-[#9AC8CE]">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Branch Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Location
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Branch Head
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedBranches.map((branch) => (
                <tr key={branch.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#000000]">
                    {branch.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#727272]">
                    {branch.location}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#727272]">
                    {branch.company ? branch.company.name : 'No company assigned'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#727272]">
                    {branch.branchHead ? branch.branchHead.name : 'Not assigned'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-[#727272]">
                    <div className="flex gap-2">
                      <button
                        onClick={() => {
                          setSelectedBranch(branch);
                          setIsEditDialogOpen(true);
                        }}
                        className="p-1 hover:text-[#9AC8CE] transition-colors"
                      >
                        <Edit2 size={18} />
                      </button>
                      <button
                        onClick={() => handleDeleteBranch(branch._id)}
                        className="p-1 hover:text-red-500 transition-colors"
                      >
                        <Trash2 size={18} />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedBranch(branch);
                          setIsInviteDialogOpen(true);
                        }}
                        className="p-1 hover:text-[#9AC8CE] transition-colors"
                      >
                        <UserPlus size={18} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </div>
        )}

        {/* Dialogs */}
        <BranchDialog
          isOpen={isCreateDialogOpen}
          onClose={() => setIsCreateDialogOpen(false)}
          onSubmit={handleCreateBranch}
          companies={companies}
          title="Create New Branch"
        />

        <BranchDialog
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setSelectedBranch(null);
          }}
          onSubmit={handleUpdateBranch}
          companies={companies}
          initialData={selectedBranch}
          title="Edit Branch"
        />

        <InviteDialog
          isOpen={isInviteDialogOpen}
          onClose={() => {
            setIsInviteDialogOpen(false);
            setSelectedBranch(null);
          }}
          onSubmit={handleInviteBranchHead}
          branchName={selectedBranch?.name || ''}
        />
      </div>
    </div>
  );
}
