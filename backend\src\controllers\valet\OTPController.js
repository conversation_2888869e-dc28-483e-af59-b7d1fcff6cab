const db = require('../../config/database');
const sql = require('mssql');

/**
 * Valet OTP Controller
 * Handles OTP generation, verification, and management for valet system
 */

// Generate and send OTP
exports.generateOTP = async (req, res) => {
  try {
    const { mobileNumber, otpType = 'REGISTRATION' } = req.body;

    // Validate required fields
    if (!mobileNumber) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number is required'
      });
    }

    // Validate mobile number format
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!mobileRegex.test(mobileNumber)) {
      return res.status(400).json({
        success: false,
        message: 'Please enter a valid 10-digit mobile number'
      });
    }

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Set expiry time (5 minutes from now)
    const expiryMinutes = process.env.VALET_OTP_EXPIRY_MINUTES || 5;
    const expiryTime = new Date();
    expiryTime.setMinutes(expiryTime.getMinutes() + parseInt(expiryMinutes));

    let result = null;

    try {
      // Use stored procedure to generate OTP
      result = await db.query(`
        DECLARE @NewId INT;
        EXEC sp_Valet_OTP_Generate
          @MobileNumber = @mobileNumber,
          @OTPCode = @otp,
          @ExpiryMinutes = @expiryMinutes,
          @CreatedBy = 1,
          @NewId = @NewId OUTPUT;
        SELECT @NewId as Id;
      `, {
        mobileNumber,
        otp,
        expiryMinutes: parseInt(expiryMinutes)
      });

      if (!result.recordset[0] || !result.recordset[0].Id) {
        throw new Error('Failed to generate OTP using stored procedure');
      }

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      // Deactivate any existing active OTPs for this mobile number
      const deactivateQuery = `
        UPDATE OTP
        SET IsActive = 0, ModifiedBy = 1, ModifiedOn = GETDATE()
        WHERE MobileNumber = @mobileNumber AND IsActive = 1
      `;

      await db.query(deactivateQuery, { mobileNumber });

      // Insert new OTP
      const insertQuery = `
        INSERT INTO OTP (MobileNumber, OTP, ExpireTime, IsActive, CreatedBy, CreatedOn)
        OUTPUT INSERTED.Id
        VALUES (@mobileNumber, @otp, @expiryMinutes, 1, 1, GETDATE())
      `;

      result = await db.query(insertQuery, {
        mobileNumber,
        otp,
        expiryMinutes: parseInt(expiryMinutes)
      });
    }

    // TODO: Send SMS using SMS service
    // For now, we'll just log it (in production, integrate with SMS gateway)
    console.log(`OTP for ${mobileNumber}: ${otp} (Expires: ${expiryTime})`);

    // Insert SMS notification record
    const smsQuery = `
      INSERT INTO SMSNotifications (MobileNumber, Message, SMSType, Status, CreatedOn)
      VALUES (@mobileNumber, @message, @smsType, 'PENDING', GETDATE())
    `;
    
    const smsMessage = `Your Parkwiz valet OTP is: ${otp}. Valid for ${expiryMinutes} minutes. Do not share with anyone.`;
    
    await db.query(smsQuery, {
      mobileNumber,
      message: smsMessage,
      smsType: otpType
    });

    res.json({
      success: true,
      message: 'OTP sent successfully',
      data: {
        otpId: result.recordset[0].Id,
        expiryTime: expiryTime,
        // Don't send OTP in production for security
        ...(process.env.NODE_ENV === 'development' && { otp })
      }
    });

  } catch (error) {
    console.error('Error in generateOTP:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate OTP',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Verify OTP
exports.verifyOTP = async (req, res) => {
  try {
    const { mobileNumber, otp } = req.body;

    // Validate required fields
    if (!mobileNumber || !otp) {
      return res.status(400).json({
        success: false,
        message: 'Mobile number and OTP are required'
      });
    }

    try {
      // Use stored procedure to verify OTP
      const verifyResult = await db.query(`
        EXEC sp_Valet_OTP_Verify
          @MobileNumber = @mobileNumber,
          @OTPCode = @otp,
          @ModifiedBy = 1
      `, { mobileNumber, otp });

      // Check if verification was successful
      if (verifyResult.recordset.length === 0 || verifyResult.recordset[0].Success !== 1) {
        return res.status(400).json({
          success: false,
          message: verifyResult.recordset[0]?.Message || 'Invalid or expired OTP'
        });
      }

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      // Find active OTP for this mobile number
      const otpQuery = `
        SELECT Id, OTP, ExpireTime, CreatedOn
        FROM OTP
        WHERE MobileNumber = @mobileNumber
          AND IsActive = 1
          AND DATEADD(MINUTE, ExpireTime, CreatedOn) > GETDATE()
        ORDER BY CreatedOn DESC
      `;

      const otpResult = await db.query(otpQuery, { mobileNumber });

      if (otpResult.recordset.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or expired OTP'
        });
      }

      const otpRecord = otpResult.recordset[0];

      // Verify OTP
      if (otpRecord.OTP !== otp) {
        return res.status(400).json({
          success: false,
          message: 'Invalid OTP'
        });
      }

      // Deactivate the OTP after successful verification
      const deactivateQuery = `
        UPDATE OTP
        SET IsActive = 0, ModifiedBy = 1, ModifiedOn = GETDATE()
        WHERE Id = @otpId
      `;

      await db.query(deactivateQuery, { otpId: otpRecord.Id });
    }

    res.json({
      success: true,
      message: 'OTP verified successfully',
      data: {
        mobileNumber,
        verifiedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error in verifyOTP:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify OTP',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Resend OTP
exports.resendOTP = async (req, res) => {
  try {
    const { mobileNumber } = req.body;

    // Check if there's a recent OTP request (prevent spam)
    const recentOtpQuery = `
      SELECT TOP 1 CreatedOn
      FROM OTP 
      WHERE MobileNumber = @mobileNumber 
      ORDER BY CreatedOn DESC
    `;
    
    const recentOtp = await db.query(recentOtpQuery, { mobileNumber });
    
    if (recentOtp.recordset.length > 0) {
      const lastOtpTime = new Date(recentOtp.recordset[0].CreatedOn);
      const currentTime = new Date();
      const timeDifference = (currentTime - lastOtpTime) / 1000; // in seconds
      
      // Prevent resend if last OTP was sent within 60 seconds
      if (timeDifference < 60) {
        return res.status(429).json({
          success: false,
          message: `Please wait ${Math.ceil(60 - timeDifference)} seconds before requesting a new OTP`
        });
      }
    }

    // Generate new OTP using the same logic as generateOTP
    return exports.generateOTP(req, res);

  } catch (error) {
    console.error('Error in resendOTP:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resend OTP',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get OTP status
exports.getOTPStatus = async (req, res) => {
  try {
    const { mobileNumber } = req.params;

    let result = null;
    let otpRecord = null;
    let remainingTime = 0;

    try {
      // Use stored procedure to get OTP status
      result = await db.query(`
        EXEC sp_Valet_OTP_GetStatus @MobileNumber = @mobileNumber
      `, { mobileNumber });

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No active OTP found for this mobile number'
        });
      }

      otpRecord = result.recordset[0];
      remainingTime = otpRecord.RemainingSeconds || 0;

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      const otpQuery = `
        SELECT
          Id,
          ExpireTime,
          CreatedOn,
          CASE
            WHEN DATEADD(MINUTE, ExpireTime, CreatedOn) > GETDATE() THEN 'ACTIVE'
            ELSE 'EXPIRED'
          END as Status
        FROM OTP
        WHERE MobileNumber = @mobileNumber
          AND IsActive = 1
        ORDER BY CreatedOn DESC
      `;

      result = await db.query(otpQuery, { mobileNumber });

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'No active OTP found for this mobile number'
        });
      }

      otpRecord = result.recordset[0];
      remainingTime = otpRecord.Status === 'ACTIVE'
        ? Math.max(0, Math.floor((new Date(otpRecord.ExpireTime) - new Date()) / 1000))
        : 0;
    }

    res.json({
      success: true,
      data: {
        otpId: otpRecord.Id,
        status: otpRecord.Status,
        expiryTime: otpRecord.ExpireTime,
        remainingSeconds: remainingTime,
        createdOn: otpRecord.CreatedOn
      },
      message: 'OTP status retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getOTPStatus:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get OTP status',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Clean up expired OTPs (utility function)
exports.cleanupExpiredOTPs = async (req, res) => {
  try {
    try {
      // Use stored procedure to cleanup expired OTPs
      const result = await db.query('EXEC sp_Valet_OTP_Cleanup');

      const cleanupResult = result.recordset[0];

      res.json({
        success: cleanupResult.Success === 1,
        message: cleanupResult.Message || 'Cleaned up expired OTPs',
        cleanedCount: cleanupResult.DeactivatedCount || 0
      });

    } catch (error) {
      // Fall back to direct query if stored procedure fails
      console.warn('Stored procedure failed, using direct query:', error.message);

      const cleanupQuery = `
        UPDATE OTP
        SET IsActive = 0, ModifiedBy = 1, ModifiedOn = GETDATE()
        WHERE IsActive = 1 AND DATEADD(MINUTE, ExpireTime, CreatedOn) < GETDATE()
      `;

      const result = await db.query(cleanupQuery);

      res.json({
        success: true,
        message: `Cleaned up expired OTPs`,
        cleanedCount: result.rowsAffected[0] || 0
      });
    }

  } catch (error) {
    console.error('Error in cleanupExpiredOTPs:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup expired OTPs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

// Get OTP statistics (for admin)
exports.getOTPStatistics = async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    let query = `
      SELECT 
        COUNT(*) as TotalOTPs,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as ActiveOTPs,
        COUNT(CASE WHEN ExpireTime < GETDATE() THEN 1 END) as ExpiredOTPs,
        COUNT(DISTINCT MobileNumber) as UniqueMobileNumbers,
        AVG(DATEDIFF(SECOND, CreatedOn, ModifiedOn)) as AvgVerificationTime
      FROM OTP
      WHERE 1=1
    `;
    
    const queryParams = {};
    
    if (startDate) {
      query += ` AND CreatedOn >= @startDate`;
      queryParams.startDate = startDate;
    }
    
    if (endDate) {
      query += ` AND CreatedOn <= @endDate`;
      queryParams.endDate = endDate;
    }

    const result = await db.query(query, queryParams);

    res.json({
      success: true,
      statistics: result.recordset[0],
      message: 'OTP statistics retrieved successfully'
    });

  } catch (error) {
    console.error('Error in getOTPStatistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get OTP statistics',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};
