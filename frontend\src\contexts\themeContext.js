// frontend/src/contexts/themeContext.js
import React, { createContext, useState, useContext, useEffect } from 'react';

// Create a context for theme management
const ThemeContext = createContext();

// Theme options
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SAFFRON: 'saffron',
};

// Theme provider component
export const ThemeProvider = ({ children }) => {
  // Check if user has a saved theme preference in localStorage
  const getSavedTheme = () => {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === THEMES.DARK) return THEMES.DARK;
    if (savedTheme === THEMES.SAFFRON) return THEMES.SAFFRON;
    return THEMES.LIGHT;
  };

  // State to track current theme
  const [theme, setTheme] = useState(getSavedTheme);
  
  // Function to set a specific theme
  const setSpecificTheme = (newTheme) => {
    if (Object.values(THEMES).includes(newTheme)) {
      localStorage.setItem('theme', newTheme);
      setTheme(newTheme);
    }
  };

  // Function to toggle between themes (for backward compatibility)
  const toggleTheme = () => {
    setTheme(prevTheme => {
      let newTheme;
      if (prevTheme === THEMES.LIGHT) newTheme = THEMES.DARK;
      else if (prevTheme === THEMES.DARK) newTheme = THEMES.SAFFRON;
      else newTheme = THEMES.LIGHT;
      
      localStorage.setItem('theme', newTheme);
      return newTheme;
    });
  };

  // Apply theme class to the document body when theme changes
  useEffect(() => {
    // Remove all theme classes
    document.documentElement.classList.remove(THEMES.LIGHT, THEMES.DARK, THEMES.SAFFRON);
    // Add current theme class
    document.documentElement.classList.add(theme);
    
    // Also set a data attribute for components that need to use it in CSS
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  // Provide theme state and functions to children
  return (
    <ThemeContext.Provider value={{ 
      theme, 
      toggleTheme, 
      setTheme: setSpecificTheme,
      isDarkMode: theme === THEMES.DARK,
      isSaffronMode: theme === THEMES.SAFFRON,
      isLightMode: theme === THEMES.LIGHT
    }}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
