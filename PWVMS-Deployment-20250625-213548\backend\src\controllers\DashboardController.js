const db = require('../config/database');
const sql = require('mssql');

/**
 * ===============================================================================
 * # Dashboard Controller
 * ===============================================================================
 *
 * This controller provides endpoints for dashboard metrics and visualizations
 * with role-based access control. It aggregates data from the parking system
 * to provide insights based on user roles (SuperAdmin, CompanyAdmin, PlazaManager).
 *
 * @module DashboardController
 */

/**
 * Helper function to calculate date range based on selection
 * @param {string} dateRange - The date range selection (today, yesterday, week, month, year, or specific date in YYYY-MM-DD format)
 * @returns {Object} Object with startDate and endDate
 */
function calculateDateRange(dateRange) {
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date
    startDate = new Date(dateRange + 'T00:00:00.000Z');
    endDate = new Date(dateRange + 'T23:59:59.999Z');
    
    console.log(`Using specific date: ${dateRange}`);
  } else {
    // It's a predefined range
    switch(dateRange) {
      case 'today':
        // For today, use current date
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        // For yesterday, use current date - 1 day
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setDate(endDate.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        // For week, use the last 7 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 6);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        // For month, use the last 30 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 29);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'year':
        // For year, use the last 365 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 364);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        // Default to today
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
    }
  }
  
  console.log(`Date range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
  return { startDate, endDate };
}
const dashboardController = {
  /**
   * ===============================================================================
   * ## GET DASHBOARD SUMMARY
   * ===============================================================================
   *
   * Returns summary metrics for the dashboard based on user role and filters.
   * Different metrics are provided based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with dashboard metrics
   */
  getDashboardSummary: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId, laneId } = req.query;
      const { id: userId, role } = req.user;
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      let laneFilter = '';
      if (laneId) {
        laneFilter = 'AND (t.EntryLane = @laneId OR t.ExitLane = @laneId)';
        queryParams.laneId = laneId;
      }
      
      // Execute summary query
      const summaryQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
          COUNT(*) as TransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${plazaFilter.replace('p.PlazaCode', 't.PlazaCode')}
        ${laneFilter}
      `;
      
      const summaryResult = await db.query(summaryQuery, queryParams);
      
      // Get trend data (compare to previous period)
      const prevStartDate = new Date(startDate);
      const prevEndDate = new Date(endDate);
      const timeDiff = prevEndDate - prevStartDate;
      
      prevStartDate.setTime(prevStartDate.getTime() - timeDiff);
      prevEndDate.setTime(prevEndDate.getTime() - timeDiff);
      
      const trendQueryParams = { 
        ...queryParams, 
        prevStartDate, 
        prevEndDate 
      };
      
      const trendQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as PrevRevenue,
          COUNT(*) as PrevTransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as PrevVehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as PrevAvgDuration
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @prevStartDate AND @prevEndDate
        ${plazaFilter.replace('p.PlazaCode', 't.PlazaCode')}
        ${laneFilter}
      `;
      
      const trendResult = await db.query(trendQuery, trendQueryParams);
      
      // Calculate trends
      const summary = summaryResult.recordset[0];
      const prevSummary = trendResult.recordset[0];
      
      const calculateTrend = (current, previous) => {
        if (!previous || previous === 0) return 0;
        return Math.round(((current - previous) / previous) * 100);
      };
      
      const response = {
        totalRevenue: summary.TotalRevenue || 0,
        transactionCount: summary.TransactionCount || 0,
        vehicleCount: summary.VehicleCount || 0,
        avgDuration: summary.AvgDuration || 0,
        revenueTrend: calculateTrend(summary.TotalRevenue, prevSummary.PrevRevenue),
        transactionTrend: calculateTrend(summary.TransactionCount, prevSummary.PrevTransactionCount),
        vehicleTrend: calculateTrend(summary.VehicleCount, prevSummary.PrevVehicleCount),
        durationTrend: calculateTrend(summary.AvgDuration, prevSummary.PrevAvgDuration)
      };
      
      return res.status(200).json({
        success: true,
        data: response,
        message: 'Dashboard summary retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getDashboardSummary:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve dashboard summary',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PAYMENT METHOD
   * ===============================================================================
   *
   * Returns revenue breakdown by payment method with transaction counts.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with payment method revenue data
   */
  getRevenueByPaymentMethod: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute payment method query
      const paymentMethodQuery = `
        SELECT
          ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${plazaFilter.replace('p.PlazaCode', 't.PlazaCode')}
        GROUP BY t.PaymentMode
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(paymentMethodQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Revenue by payment method retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPaymentMethod:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by payment method',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET RECENT TRANSACTIONS
   * ===============================================================================
   *
   * Returns the most recent transactions based on user role and access permissions.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with recent transactions
   */
  getRecentTransactions: async (req, res) => {
    try {
      const { limit = 5, companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Base query parameters
      const queryParams = { 
        limit: parseInt(limit) 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute recent transactions query
      const recentTransactionsQuery = `
        SELECT TOP(@limit)
          t.PakringDataID,
          t.PlazaName,
          t.VehicleNumber,
          t.EntryDateTime,
          t.ExitDateTime,
          t.EntryLane,
          t.ExitLane,
          t.ParkedDuration,
          t.ParkingFee,
          t.iTotalGSTFee,
          t.PaymentMode,
          t.PaymentType
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime IS NOT NULL
        ${plazaFilter.replace('p.PlazaCode', 't.PlazaCode')}
        ORDER BY t.ExitDateTime DESC
      `;
      
      const result = await db.query(recentTransactionsQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Recent transactions retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRecentTransactions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve recent transactions',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET PEAK HOURS DATA
   * ===============================================================================
   *
   * Returns transaction counts by hour of day to identify peak hours.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with hourly transaction data
   */
  getPeakHoursData: async (req, res) => {
    try {
      const { dateRange = 'today', companyId, plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      let plazaFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      } else if (role === 'PlazaManager') {
        plazaFilter = 'AND t.PlazaCode IN (SELECT p.PlazaCode FROM Plaza p JOIN UserPlaza up ON p.Id = up.PlazaId WHERE up.UserId = @userId AND up.IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      if (plazaId) {
        plazaFilter = 'AND t.PlazaCode = (SELECT PlazaCode FROM Plaza WHERE Id = @plazaId)';
        queryParams.plazaId = plazaId;
      }
      
      // Execute peak hours query
      const peakHoursQuery = `
        SELECT
          DATEPART(HOUR, t.ExitDateTime) as hour,
          COUNT(*) as count
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        ${plazaFilter.replace('p.PlazaCode', 't.PlazaCode')}
        GROUP BY DATEPART(HOUR, t.ExitDateTime)
        ORDER BY hour
      `;
      
      const result = await db.query(peakHoursQuery, queryParams);
      
      // Fill in missing hours with zero counts
      const hourlyData = Array(24).fill().map((_, i) => ({
        hour: i,
        count: 0
      }));
      
      result.recordset.forEach(row => {
        hourlyData[row.hour].count = row.count;
      });
      
      return res.status(200).json({
        success: true,
        data: hourlyData,
        message: 'Peak hours data retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getPeakHoursData:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve peak hours data',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET REVENUE BY PLAZA
   * ===============================================================================
   *
   * Returns revenue breakdown by plaza with transaction counts.
   * Only accessible to SuperAdmin and CompanyAdmin roles.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with plaza revenue data
   */
  getRevenueByPlaza: async (req, res) => {
    try {
      const { dateRange = 'today', companyId } = req.query;
      const { id: userId, role } = req.user;
      
      // Only SuperAdmin and CompanyAdmin can access this endpoint
      if (role !== 'SuperAdmin' && role !== 'CompanyAdmin') {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this resource'
        });
      }
      
      // Calculate date range
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      // Base query parameters
      const queryParams = { 
        startDate, 
        endDate 
      };
      
      // Apply role-based filtering
      let companyFilter = '';
      
      if (role === 'CompanyAdmin') {
        companyFilter = 'AND p.CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)';
        queryParams.userId = userId;
      }
      
      // Entity-specific filtering
      if (companyId) {
        companyFilter = 'AND p.CompanyId = @companyId';
        queryParams.companyId = companyId;
      }
      
      // Execute plaza revenue query
      const plazaRevenueQuery = `
        SELECT
          t.PlazaName,
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          COUNT(*) as transactionCount
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        GROUP BY t.PlazaName
        ORDER BY totalRevenue DESC
      `;
      
      const result = await db.query(plazaRevenueQuery, queryParams);
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Revenue by plaza retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getRevenueByPlaza:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue by plaza',
        error: error.message
      });
    }
  },

  /**
   * ===============================================================================
   * ## GET LANE STATUS
   * ===============================================================================
   *
   * Returns the current status of lanes for a specific plaza.
   * Primarily used by PlazaManager role.
   *
   * @param {Object} req - Express request object with query parameters
   * @param {Object} res - Express response object
   * @returns {Object} JSON response with lane status data
   */
  getLaneStatus: async (req, res) => {
    try {
      const { plazaId } = req.query;
      const { id: userId, role } = req.user;
      
      if (!plazaId) {
        return res.status(400).json({
          success: false,
          message: 'Plaza ID is required'
        });
      }
      
      // Check if user has access to this plaza
      if (role !== 'SuperAdmin') {
        let accessQuery;
        
        if (role === 'CompanyAdmin') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
          `;
        } else if (role === 'PlazaManager') {
          accessQuery = `
            SELECT COUNT(*) as count
            FROM UserPlaza
            WHERE PlazaId = @plazaId AND UserId = @userId AND IsActive = 1
          `;
        }
        
        const accessResult = await db.query(accessQuery, {
          plazaId,
          userId
        });
        
        if (accessResult.recordset[0].count === 0) {
          return res.status(403).json({
            success: false,
            message: 'You do not have access to this plaza'
          });
        }
      }
      
      // Get lane status
      const laneStatusQuery = `
        SELECT
          l.LaneID,
          l.LaneNumber,
          l.LaneType,
          l.ActiveStatus,
          CASE 
            WHEN l.ActiveStatus = 1 THEN 'Active'
            ELSE 'Inactive'
          END as Status,
          (
            SELECT COUNT(*)
            FROM tblParkwiz_Parking_Data_OLD t
            WHERE (t.EntryLane = l.LaneNumber OR t.ExitLane = l.LaneNumber)
            AND t.PlazaCode = p.PlazaCode
            AND t.ExitDateTime >= DATEADD(HOUR, -24, GETDATE())
          ) as TransactionsLast24Hours
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaID = p.Id
        WHERE l.PlazaID = @plazaId
        ORDER BY l.LaneNumber
      `;
      
      const result = await db.query(laneStatusQuery, { plazaId });
      
      return res.status(200).json({
        success: true,
        data: result.recordset,
        message: 'Lane status retrieved successfully'
      });
    } catch (error) {
      console.error('Error in getLaneStatus:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve lane status',
        error: error.message
      });
    }
  }
};

module.exports = dashboardController;