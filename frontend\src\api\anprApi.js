import api from '../services/api'; // Shared Axios instance

export const anprApi = {
  /**
   * Fetches the list of all ANPR configurations.
   * GET /anpr-configs
   */
  getAllConfigurations: async (timestamp) => {
    try {
      // Use provided timestamp or generate a new one to prevent caching
      const cacheBuster = timestamp || new Date().getTime();
      const response = await api.get(`/anpr-configs?_=${cacheBuster}`);

      // Handle different response structures
      if (response.data && response.data.configurations && Array.isArray(response.data.configurations)) {
        return response.data.configurations;
      } else if (response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }

      // Default fallback
      console.warn('Unexpected ANPR API response structure:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching ANPR configurations:', error);
      return [];
    }
  },

  /**
   * Fetches the details of a single ANPR configuration by its ID.
   * GET /anpr-configs/:id
   */
  getConfigurationById: async (id) => {
    const response = await api.get(`/anpr-configs/${id}`);
    return response.data.configuration;
  },

  /**
   * Creates a new ANPR configuration.
   * POST /anpr-configs
   */
  createConfiguration: async (data) => {
    // Ensure boolean fields are properly formatted as strings
    const formattedData = {
      ...data,
      flgEnableANPR: String(data.flgEnableANPR === '1' ? '1' : '0'),
      ActiveStatus: String(data.ActiveStatus === '1' ? '1' : '0'),
      AllowBlacklistedVehicle: String(data.AllowBlacklistedVehicle === '1' ? '1' : '0'),
    };

    const response = await api.post('/anpr-configs', formattedData);
    return response.data;
  },

  /**
   * Updates an ANPR configuration by ID.
   * PUT /anpr-configs/:id
   */
  updateConfiguration: async (id, data) => {
    // Ensure boolean fields are properly formatted as strings
    const formattedData = {
      ...data,
      flgEnableANPR: String(data.flgEnableANPR === '1' ? '1' : '0'),
      ActiveStatus: String(data.ActiveStatus === '1' ? '1' : '0'),
      AllowBlacklistedVehicle: String(data.AllowBlacklistedVehicle === '1' ? '1' : '0'),
    };

    const response = await api.put(`/anpr-configs/${id}`, formattedData);
    return response.data;
  },

  /**
   * Deletes an ANPR configuration by ID (soft delete).
   * DELETE /anpr-configs/:id
   */
  deleteConfiguration: async (id, updatedBy) => {
    // Include UpdatedBy in the request body for soft delete
    await api.delete(`/anpr-configs/${id}`, { data: { UpdatedBy: updatedBy || 'SYSTEM_DELETE' } });
  },

  // Add other specific ANPR API calls if needed, e.g., toggle status if implemented
  // Example: Toggle status (if backend supports it)
  /*
  toggleConfigurationStatus: async (id, updatedBy) => {
    try {
      if (!id) {
        throw new Error('Configuration ID is required');
      }
      const response = await api.patch(`/anpr-configs/${id}/toggle-status`, { UpdatedBy: updatedBy || 'admin' });
      if (!response.data.success) {
        throw new Error(response.data.message || 'API returned unsuccessful response');
      }
      // Ensure newStatus is properly formatted as a string if needed
      if (response.data.newStatus !== undefined) {
        response.data.newStatus = String(response.data.newStatus);
      }
      return response.data;
    } catch (error) {
      console.error('Toggle ANPR status error:', error);
      throw error;
    }
  }
  */
};