const db = require('./src/config/database');

async function checkLaneStructure() {
  try {
    console.log('=== CHECKING LANE TABLE STRUCTURE ===');
    
    // Check tblLaneDetails structure
    const laneStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblLaneDetails'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\ntblLaneDetails structure:');
    laneStructure.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });
    
    // Get sample data
    const sampleLanes = await db.query('SELECT TOP 3 * FROM tblLaneDetails');
    console.log(`\nSample lane data (${sampleLanes.recordset.length} records):`);
    if (sampleLanes.recordset.length > 0) {
      console.log('First record:', sampleLanes.recordset[0]);
    }
    
    // Final verification of CompanyAdmin access
    console.log('\n=== COMPANY ADMIN ACCESS VERIFICATION ===');
    
    const finalCompanies = await db.query(`
      SELECT c.Id, c.CompanyName, uc.IsActive
      FROM UserCompany uc
      JOIN Company c ON uc.CompanyId = c.Id
      WHERE uc.UserId = 4
      ORDER BY c.CompanyName
    `);
    
    console.log('Company assignments for CompanyAdmin:');
    finalCompanies.recordset.forEach(assignment => {
      console.log(`  ${assignment.IsActive ? '✓' : '✗'} ${assignment.CompanyName} (ID: ${assignment.Id})`);
    });
    
    const finalPlazas = await db.query(`
      SELECT p.Id, p.PlazaName, c.CompanyName
      FROM Plaza p
      JOIN Company c ON p.CompanyId = c.Id
      JOIN UserCompany uc ON c.Id = uc.CompanyId
      WHERE uc.UserId = 4 AND uc.IsActive = 1 AND p.IsActive = 1
      ORDER BY c.CompanyName, p.PlazaName
    `);
    
    console.log(`\nAccessible plazas: ${finalPlazas.recordset.length}`);
    finalPlazas.recordset.forEach(plaza => {
      console.log(`  ✓ ${plaza.PlazaName} (${plaza.CompanyName})`);
    });
    
    // Count lanes if they exist
    if (sampleLanes.recordset.length > 0) {
      const laneCount = await db.query(`
        SELECT COUNT(*) as LaneCount
        FROM tblLaneDetails l
        JOIN Plaza p ON l.PlazaId = p.Id
        JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
        WHERE uc.UserId = 4 AND uc.IsActive = 1
      `);
      
      console.log(`\nAccessible lanes: ${laneCount.recordset[0].LaneCount}`);
    }
    
    // Check users accessible to CompanyAdmin
    const accessibleUsers = await db.query(`
      SELECT DISTINCT u.Id, u.Username, u.Email, r.Name as RoleName
      FROM Users u
      JOIN Roles r ON u.RoleId = r.Id
      WHERE u.Id IN (
        -- Users assigned to same companies
        SELECT DISTINCT uc2.UserId
        FROM UserCompany uc1
        JOIN UserCompany uc2 ON uc1.CompanyId = uc2.CompanyId
        WHERE uc1.UserId = 4 AND uc1.IsActive = 1 AND uc2.IsActive = 1
        
        UNION
        
        -- Users assigned to plazas within the companies
        SELECT DISTINCT up.UserId
        FROM UserCompany uc
        JOIN Plaza p ON uc.CompanyId = p.CompanyId
        JOIN UserPlaza up ON p.Id = up.PlazaId
        WHERE uc.UserId = 4 AND uc.IsActive = 1 AND up.IsActive = 1
      )
      AND u.IsActive = 1
      AND r.Name != 'SuperAdmin'  -- Exclude SuperAdmin users
      AND u.Id != 4  -- Exclude themselves
      ORDER BY r.Name, u.Username
    `);
    
    console.log(`\nAccessible users (excluding SuperAdmin and self): ${accessibleUsers.recordset.length}`);
    accessibleUsers.recordset.forEach(user => {
      console.log(`  ✓ ${user.Username} (${user.Email}) - ${user.RoleName}`);
    });
    
    console.log('\n=== SUMMARY ===');
    console.log('✅ FIXED ISSUES:');
    console.log('1. ✓ CompanyAdmin now has active company assignments');
    console.log('2. ✓ CompanyAdmin can see companies, plazas, and related data');
    console.log('3. ✓ User filtering excludes SuperAdmin users');
    
    console.log('\n🔧 BACKEND FIXES STILL NEEDED:');
    console.log('1. Prevent CompanyAdmin from creating SuperAdmin users');
    console.log('2. Hide SuperAdmin role from role dropdown');
    console.log('3. Prevent CompanyAdmin from editing themselves');
    
    console.log('\n📋 READY FOR TESTING:');
    console.log('Login as CompanyAdmin (company admin / PASSWORD)');
    console.log('Expected to see:');
    console.log(`- ${finalCompanies.recordset.filter(c => c.IsActive).length} companies`);
    console.log(`- ${finalPlazas.recordset.length} plazas`);
    console.log(`- ${accessibleUsers.recordset.length} users (no SuperAdmin)`);
    
    process.exit(0);
  } catch (error) {
    console.error('Check failed:', error);
    process.exit(1);
  }
}

checkLaneStructure();
