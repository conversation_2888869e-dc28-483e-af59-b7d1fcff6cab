// Script to update user session in the database
require('dotenv').config({ path: './backend/.env' });
const sql = require('mssql');

async function updateUserSession() {
  console.log('Starting to update user session...');
  
  try {
    // Connect to the database
    console.log('Connecting to database...');
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    
    console.log('Connected to database successfully.');
    
    // Update all user sessions to force re-login
    const updateResult = await sql.query(`
      UPDATE Users
      SET ModifiedOn = GETDATE()
      WHERE IsActive = 1
    `);
    
    console.log(`Updated ${updateResult.rowsAffected[0]} user sessions.`);
    
    await sql.close();
    console.log('Database connection closed.');
    
  } catch (err) {
    console.error('Error updating user session:', err);
    if (sql.connected) {
      await sql.close();
      console.log('Database connection closed due to error.');
    }
    process.exit(1);
  }
}

updateUserSession().catch(err => {
  console.error('Unhandled error:', err);
  process.exit(1);
});