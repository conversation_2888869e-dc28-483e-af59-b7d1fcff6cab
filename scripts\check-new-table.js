require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function checkNewTable() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Check if the table exists
    const tableCheck = await sql.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE' 
      AND TABLE_NAME LIKE '%Parkwiz%'
    `);
    
    console.log('Parkwiz tables in database:');
    tableCheck.recordset.forEach(table => console.log(table.TABLE_NAME));

    // Try to get data from the new table
    try {
      const result = await sql.query('SELECT TOP 5 * FROM tblParkwizParkingDataOld');
      console.log('\nNew table data sample:');
      console.log(JSON.stringify(result.recordset, null, 2));
      
      const countResult = await sql.query('SELECT COUNT(*) as TotalRecords FROM tblParkwizParkingDataOld');
      console.log(`\nTotal records in tblParkwizParkingDataOld: ${countResult.recordset[0].TotalRecords}`);
      
      const dateRangeResult = await sql.query(`
        SELECT 
          MIN(ExitDateTime) as EarliestDate, 
          MAX(ExitDateTime) as LatestDate 
        FROM tblParkwizParkingDataOld 
        WHERE ExitDateTime IS NOT NULL
      `);
      
      console.log('\nDate range:');
      if (dateRangeResult.recordset[0].EarliestDate) {
        console.log(`- Earliest date: ${dateRangeResult.recordset[0].EarliestDate.toISOString()}`);
        console.log(`- Latest date: ${dateRangeResult.recordset[0].LatestDate.toISOString()}`);
      } else {
        console.log('No records with ExitDateTime found');
      }
    } catch (error) {
      console.error('Error querying new table:', error.message);
    }

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkNewTable();