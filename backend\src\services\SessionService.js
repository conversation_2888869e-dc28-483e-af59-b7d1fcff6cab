// backend/src/services/SessionService.js
const redisService = require('./RedisService');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');

/**
 * ===============================================================================
 * # Session Service with Redis
 * ===============================================================================
 * 
 * Advanced session management service using Redis for PWVMS.
 * Provides secure, scalable session handling with features like:
 * - Multi-device session management
 * - Session invalidation
 * - Activity tracking
 * - Security monitoring
 */

class SessionService {
  constructor() {
    this.sessionPrefix = 'session:';
    this.userSessionsPrefix = 'user_sessions:';
    this.activeSessionsPrefix = 'active_sessions:';
  }

  /**
   * ===============================================================================
   * ## SESSION CREATION AND MANAGEMENT
   * ===============================================================================
   */

  /**
   * Create a new session
   */
  async createSession(user, deviceInfo = {}) {
    try {
      const sessionId = uuidv4();
      const sessionData = {
        sessionId,
        userId: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        permissions: user.permissions || [],
        companies: user.companies || [],
        plazas: user.plazas || [],
        deviceInfo: {
          userAgent: deviceInfo.userAgent || 'Unknown',
          ip: deviceInfo.ip || 'Unknown',
          platform: deviceInfo.platform || 'Unknown',
          browser: deviceInfo.browser || 'Unknown'
        },
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        isActive: true
      };

      // Store session data
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      await redisService.set(sessionKey, sessionData, 86400); // 24 hours

      // Add to user's active sessions
      await this.addToUserSessions(user.id, sessionId);

      // Add to global active sessions
      await this.addToActiveSessions(sessionId, user.id);

      console.log(`✅ Session created for user ${user.id}: ${sessionId}`);
      
      return {
        sessionId,
        sessionData,
        success: true
      };

    } catch (error) {
      console.error('Create session error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get session data
   */
  async getSession(sessionId) {
    try {
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      const sessionData = await redisService.get(sessionKey);
      
      if (!sessionData) {
        return {
          success: false,
          error: 'Session not found'
        };
      }

      // Update last activity
      sessionData.lastActivity = new Date().toISOString();
      await redisService.set(sessionKey, sessionData, 86400);

      return {
        success: true,
        sessionData
      };

    } catch (error) {
      console.error('Get session error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update session data
   */
  async updateSession(sessionId, updates) {
    try {
      const sessionResult = await this.getSession(sessionId);
      
      if (!sessionResult.success) {
        return sessionResult;
      }

      const updatedSession = {
        ...sessionResult.sessionData,
        ...updates,
        lastActivity: new Date().toISOString()
      };

      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      await redisService.set(sessionKey, updatedSession, 86400);

      return {
        success: true,
        sessionData: updatedSession
      };

    } catch (error) {
      console.error('Update session error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Destroy a session
   */
  async destroySession(sessionId) {
    try {
      // Get session data first
      const sessionResult = await this.getSession(sessionId);
      
      if (sessionResult.success) {
        const { userId } = sessionResult.sessionData;
        
        // Remove from user's active sessions
        await this.removeFromUserSessions(userId, sessionId);
        
        // Remove from global active sessions
        await this.removeFromActiveSessions(sessionId);
      }

      // Delete session data
      const sessionKey = `${this.sessionPrefix}${sessionId}`;
      await redisService.del(sessionKey);

      console.log(`✅ Session destroyed: ${sessionId}`);
      
      return {
        success: true,
        message: 'Session destroyed successfully'
      };

    } catch (error) {
      console.error('Destroy session error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * ===============================================================================
   * ## USER SESSION MANAGEMENT
   * ===============================================================================
   */

  /**
   * Get all sessions for a user
   */
  async getUserSessions(userId) {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessionIds = await redisService.get(userSessionsKey) || [];
      
      const sessions = [];
      
      for (const sessionId of sessionIds) {
        const sessionResult = await this.getSession(sessionId);
        if (sessionResult.success) {
          sessions.push(sessionResult.sessionData);
        }
      }

      return {
        success: true,
        sessions
      };

    } catch (error) {
      console.error('Get user sessions error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Destroy all sessions for a user
   */
  async destroyAllUserSessions(userId, exceptSessionId = null) {
    try {
      const userSessionsResult = await this.getUserSessions(userId);
      
      if (!userSessionsResult.success) {
        return userSessionsResult;
      }

      let destroyedCount = 0;
      
      for (const session of userSessionsResult.sessions) {
        if (session.sessionId !== exceptSessionId) {
          const destroyResult = await this.destroySession(session.sessionId);
          if (destroyResult.success) {
            destroyedCount++;
          }
        }
      }

      console.log(`✅ Destroyed ${destroyedCount} sessions for user ${userId}`);
      
      return {
        success: true,
        destroyedCount,
        message: `${destroyedCount} sessions destroyed`
      };

    } catch (error) {
      console.error('Destroy all user sessions error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Add session to user's active sessions
   */
  async addToUserSessions(userId, sessionId) {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessionIds = await redisService.get(userSessionsKey) || [];
      
      if (!sessionIds.includes(sessionId)) {
        sessionIds.push(sessionId);
        await redisService.set(userSessionsKey, sessionIds, 86400);
      }

      return true;
    } catch (error) {
      console.error('Add to user sessions error:', error);
      return false;
    }
  }

  /**
   * Remove session from user's active sessions
   */
  async removeFromUserSessions(userId, sessionId) {
    try {
      const userSessionsKey = `${this.userSessionsPrefix}${userId}`;
      const sessionIds = await redisService.get(userSessionsKey) || [];
      
      const updatedSessionIds = sessionIds.filter(id => id !== sessionId);
      await redisService.set(userSessionsKey, updatedSessionIds, 86400);

      return true;
    } catch (error) {
      console.error('Remove from user sessions error:', error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## GLOBAL SESSION MANAGEMENT
   * ===============================================================================
   */

  /**
   * Add to global active sessions
   */
  async addToActiveSessions(sessionId, userId) {
    try {
      const activeSessionsKey = this.activeSessionsPrefix;
      const activeSessions = await redisService.get(activeSessionsKey) || {};
      
      activeSessions[sessionId] = {
        userId,
        createdAt: new Date().toISOString()
      };
      
      await redisService.set(activeSessionsKey, activeSessions, 86400);
      return true;
    } catch (error) {
      console.error('Add to active sessions error:', error);
      return false;
    }
  }

  /**
   * Remove from global active sessions
   */
  async removeFromActiveSessions(sessionId) {
    try {
      const activeSessionsKey = this.activeSessionsPrefix;
      const activeSessions = await redisService.get(activeSessionsKey) || {};
      
      delete activeSessions[sessionId];
      
      await redisService.set(activeSessionsKey, activeSessions, 86400);
      return true;
    } catch (error) {
      console.error('Remove from active sessions error:', error);
      return false;
    }
  }

  /**
   * Get all active sessions
   */
  async getActiveSessions() {
    try {
      const activeSessionsKey = this.activeSessionsPrefix;
      const activeSessions = await redisService.get(activeSessionsKey) || {};
      
      return {
        success: true,
        activeSessions,
        count: Object.keys(activeSessions).length
      };
    } catch (error) {
      console.error('Get active sessions error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * ===============================================================================
   * ## SESSION VALIDATION AND SECURITY
   * ===============================================================================
   */

  /**
   * Validate session and check for security issues
   */
  async validateSession(sessionId, currentDeviceInfo = {}) {
    try {
      const sessionResult = await this.getSession(sessionId);
      
      if (!sessionResult.success) {
        return {
          valid: false,
          reason: 'Session not found'
        };
      }

      const session = sessionResult.sessionData;

      // Check if session is active
      if (!session.isActive) {
        return {
          valid: false,
          reason: 'Session is inactive'
        };
      }

      // Check session age
      const sessionAge = Date.now() - new Date(session.createdAt).getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours
      
      if (sessionAge > maxAge) {
        await this.destroySession(sessionId);
        return {
          valid: false,
          reason: 'Session expired'
        };
      }

      // Check for suspicious activity (optional)
      if (currentDeviceInfo.ip && session.deviceInfo.ip !== currentDeviceInfo.ip) {
        console.warn(`⚠️ IP change detected for session ${sessionId}: ${session.deviceInfo.ip} -> ${currentDeviceInfo.ip}`);
        // You might want to invalidate the session or require re-authentication
      }

      return {
        valid: true,
        session: session
      };

    } catch (error) {
      console.error('Validate session error:', error);
      return {
        valid: false,
        reason: 'Validation error'
      };
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions() {
    try {
      const activeSessionsResult = await this.getActiveSessions();
      
      if (!activeSessionsResult.success) {
        return {
          success: false,
          error: 'Failed to get active sessions'
        };
      }

      let cleanedCount = 0;
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      for (const [sessionId, sessionInfo] of Object.entries(activeSessionsResult.activeSessions)) {
        const sessionAge = now - new Date(sessionInfo.createdAt).getTime();
        
        if (sessionAge > maxAge) {
          const destroyResult = await this.destroySession(sessionId);
          if (destroyResult.success) {
            cleanedCount++;
          }
        }
      }

      console.log(`✅ Cleaned up ${cleanedCount} expired sessions`);
      
      return {
        success: true,
        cleanedCount,
        message: `${cleanedCount} expired sessions cleaned up`
      };

    } catch (error) {
      console.error('Cleanup expired sessions error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * ===============================================================================
   * ## SESSION STATISTICS
   * ===============================================================================
   */

  /**
   * Get session statistics
   */
  async getSessionStats() {
    try {
      const activeSessionsResult = await this.getActiveSessions();
      
      if (!activeSessionsResult.success) {
        return {
          success: false,
          error: 'Failed to get session stats'
        };
      }

      const stats = {
        totalActiveSessions: activeSessionsResult.count,
        sessionsByUser: {},
        sessionsByAge: {
          last15Minutes: 0,
          lastHour: 0,
          last24Hours: 0
        },
        timestamp: new Date().toISOString()
      };

      const now = Date.now();
      
      for (const [sessionId, sessionInfo] of Object.entries(activeSessionsResult.activeSessions)) {
        const userId = sessionInfo.userId;
        const sessionAge = now - new Date(sessionInfo.createdAt).getTime();
        
        // Count by user
        stats.sessionsByUser[userId] = (stats.sessionsByUser[userId] || 0) + 1;
        
        // Count by age
        if (sessionAge <= 15 * 60 * 1000) { // 15 minutes
          stats.sessionsByAge.last15Minutes++;
        }
        if (sessionAge <= 60 * 60 * 1000) { // 1 hour
          stats.sessionsByAge.lastHour++;
        }
        if (sessionAge <= 24 * 60 * 60 * 1000) { // 24 hours
          stats.sessionsByAge.last24Hours++;
        }
      }

      return {
        success: true,
        stats
      };

    } catch (error) {
      console.error('Get session stats error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Create singleton instance
const sessionService = new SessionService();

module.exports = sessionService;