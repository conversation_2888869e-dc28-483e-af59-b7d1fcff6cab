const express = require('express');
const router = express.Router();
const plazaValetPointController = require('../controllers/PlazaValetPointController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/plazavaletpoints
 * @desc    Get all plaza valet points
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), plazaValetPointController.getAllPlazaValetPoints);

/**
 * @route   GET /api/plazavaletpoints/:id
 * @desc    Get plaza valet point by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), plazaValetPointController.getPlazaValetPointById);

/**
 * @route   GET /api/plazavaletpoints/plaza/:plazaId
 * @desc    Get valet points by plaza ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/plaza/:plazaId', auth(['View']), plazaValetPointController.getValetPointsByPlaza);

/**
 * @route   POST /api/plazavaletpoints
 * @desc    Create a new plaza valet point
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.post('/', auth(['Create']), plazaValetPointController.createPlazaValetPoint);

/**
 * @route   PUT /api/plazavaletpoints/:id
 * @desc    Update a plaza valet point
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.put('/:id', auth(['Edit']), plazaValetPointController.updatePlazaValetPoint);

/**
 * @route   DELETE /api/plazavaletpoints/:id
 * @desc    Delete a plaza valet point (soft delete)
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.delete('/:id', auth(['Delete']), plazaValetPointController.deletePlazaValetPoint);

/**
 * @route   PATCH /api/plazavaletpoints/toggle-status/:id
 * @desc    Toggle plaza valet point active status
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.patch('/toggle-status/:id', auth(['Edit']), plazaValetPointController.toggleActiveStatus);

module.exports = router;
