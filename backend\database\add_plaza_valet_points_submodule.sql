-- Add PlazaValetPoints as a new submodule under Plaza Management
-- This script adds the PlazaValetPoints submodule and assigns permissions to roles

USE ParkwizOps;
GO

-- Check if PlazaValetPoints submodule already exists
IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'PlazaValetPoints')
BEGIN
    PRINT 'Adding PlazaValetPoints submodule...';
    
    -- Insert the new submodule under Plaza Management
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    (
        (SELECT Id FROM Modules WHERE Name = 'Plaza Management'), 
        'PlazaValetPoints', 
        'Manage plaza valet parking points', 
        '/manage-plaza-valet-point', 
        'map-pin', 
        3, 
        1, 
        1, 
        GETDATE()
    );
    
    PRINT 'PlazaValetPoints submodule added successfully.';
END
ELSE
BEGIN
    PRINT 'PlazaValetPoints submodule already exists.';
END

-- Get the SubModule ID for PlazaValetPoints
DECLARE @SubModuleId INT;
SELECT @SubModuleId = Id FROM SubModules WHERE Name = 'PlazaValetPoints';

-- Check if permissions already exist for this submodule
IF NOT EXISTS (SELECT 1 FROM SubModulePermissions WHERE SubModuleId = @SubModuleId)
BEGIN
    PRINT 'Adding permissions for PlazaValetPoints submodule...';
    
    -- Add permissions for PlazaValetPoints submodule
    INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @SubModuleId,
        p.Id,
        1,
        1,
        GETDATE()
    FROM Permissions p
    WHERE p.Name IN ('View', 'Create', 'Edit', 'Delete');
    
    PRINT 'Permissions added for PlazaValetPoints submodule.';
END
ELSE
BEGIN
    PRINT 'Permissions already exist for PlazaValetPoints submodule.';
END

-- Assign permissions to roles
PRINT 'Assigning permissions to roles...';

-- Get Role IDs
DECLARE @SuperAdminRoleId INT, @CompanyAdminRoleId INT, @PlazaManagerRoleId INT;
SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
SELECT @PlazaManagerRoleId = Id FROM Roles WHERE Name = 'PlazaManager';

-- SuperAdmin gets all permissions
IF @SuperAdminRoleId IS NOT NULL
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @SuperAdminRoleId,
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE sm.Name = 'PlazaValetPoints'
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp 
        WHERE rp.RoleId = @SuperAdminRoleId 
        AND rp.SubModulePermissionId = smp.Id
    );
    
    PRINT 'SuperAdmin permissions assigned.';
END

-- CompanyAdmin gets all permissions
IF @CompanyAdminRoleId IS NOT NULL
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @CompanyAdminRoleId,
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE sm.Name = 'PlazaValetPoints'
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp 
        WHERE rp.RoleId = @CompanyAdminRoleId 
        AND rp.SubModulePermissionId = smp.Id
    );
    
    PRINT 'CompanyAdmin permissions assigned.';
END

-- PlazaManager gets all permissions
IF @PlazaManagerRoleId IS NOT NULL
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        @PlazaManagerRoleId,
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE sm.Name = 'PlazaValetPoints'
    AND NOT EXISTS (
        SELECT 1 FROM RolePermissions rp 
        WHERE rp.RoleId = @PlazaManagerRoleId 
        AND rp.SubModulePermissionId = smp.Id
    );
    
    PRINT 'PlazaManager permissions assigned.';
END

-- Verify the setup
PRINT 'Verification:';
SELECT 
    m.Name as ModuleName,
    sm.Name as SubModuleName,
    sm.Description,
    sm.Route,
    sm.DisplayOrder
FROM SubModules sm
JOIN Modules m ON sm.ModuleId = m.Id
WHERE sm.Name = 'PlazaValetPoints';

SELECT 
    r.Name as RoleName,
    sm.Name as SubModuleName,
    p.Name as PermissionName
FROM RolePermissions rp
JOIN Roles r ON rp.RoleId = r.Id
JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'PlazaValetPoints'
ORDER BY r.Name, p.Name;

PRINT 'PlazaValetPoints submodule setup completed successfully!';
