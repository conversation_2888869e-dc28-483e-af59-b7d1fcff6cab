const axios = require('axios');

async function testFullPermissionAPI() {
  console.log('🔍 Testing Complete Permission Management API\n');

  try {
    // Step 1: Login
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'superadmin',
      password: 'Admin@123'
    });
    
    const token = loginResponse.data.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ Login successful\n');

    // Step 2: Test modules-tree endpoint
    console.log('2. 🌳 Testing modules-tree endpoint...');
    const modulesResponse = await axios.get('http://localhost:5000/api/permission-management/modules-tree', {
      headers,
      timeout: 15000
    });
    
    console.log('✅ Modules-tree endpoint successful');
    console.log(`   Success: ${modulesResponse.data.success}`);
    console.log(`   Modules count: ${modulesResponse.data.data.length}`);
    console.log(`   Message: ${modulesResponse.data.message}`);
    
    // Show detailed module structure
    console.log('\n   📋 Module Structure:');
    modulesResponse.data.data.forEach(module => {
      console.log(`   📁 ${module.name} (${module.subModules.length} submodules)`);
      module.subModules.forEach(subModule => {
        console.log(`     📄 ${subModule.name} - ${subModule.permissions.length} permissions`);
      });
    });

    // Step 3: Test roles endpoint
    console.log('\n3. 👥 Testing roles endpoint...');
    const rolesResponse = await axios.get('http://localhost:5000/api/permission-management/roles', {
      headers,
      timeout: 10000
    });
    
    console.log('✅ Roles endpoint successful');
    console.log(`   Success: ${rolesResponse.data.success}`);
    console.log(`   Roles count: ${rolesResponse.data.data.length}`);
    
    rolesResponse.data.data.forEach(role => {
      console.log(`   👤 ${role.Name} (ID: ${role.Id})`);
    });

    // Step 4: Test role permissions for SuperAdmin
    const superAdminRole = rolesResponse.data.data.find(r => r.Name === 'SuperAdmin');
    if (superAdminRole) {
      console.log('\n4. 🔑 Testing role permissions for SuperAdmin...');
      const rolePermissionsResponse = await axios.get(
        `http://localhost:5000/api/permission-management/roles/${superAdminRole.Id}/permissions`, 
        {
          headers,
          timeout: 10000
        }
      );
      
      console.log('✅ Role permissions endpoint successful');
      console.log(`   Success: ${rolePermissionsResponse.data.success}`);
      console.log(`   Permissions count: ${rolePermissionsResponse.data.data.length}`);
      
      // Group permissions by module and submodule
      const groupedPermissions = {};
      rolePermissionsResponse.data.data.forEach(perm => {
        const key = `${perm.ModuleName} > ${perm.SubModuleName}`;
        if (!groupedPermissions[key]) {
          groupedPermissions[key] = [];
        }
        groupedPermissions[key].push(perm.PermissionName);
      });
      
      console.log('\n   📋 SuperAdmin Permissions:');
      Object.keys(groupedPermissions).slice(0, 5).forEach(key => {
        console.log(`   ${key}: ${groupedPermissions[key].join(', ')}`);
      });
      console.log(`   ... and ${Object.keys(groupedPermissions).length - 5} more`);
    }

    // Step 5: Test creating a new role
    console.log('\n5. ➕ Testing create role endpoint...');
    const newRoleData = {
      name: `Test Role ${Date.now()}`
    };
    
    const createRoleResponse = await axios.post('http://localhost:5000/api/permission-management/roles', 
      newRoleData, 
      {
        headers,
        timeout: 10000
      }
    );
    
    console.log('✅ Create role endpoint successful');
    console.log(`   Success: ${createRoleResponse.data.success}`);
    console.log(`   New role: ${createRoleResponse.data.data.Name} (ID: ${createRoleResponse.data.data.Id})`);
    console.log(`   Message: ${createRoleResponse.data.message}`);
    
    const newRoleId = createRoleResponse.data.data.Id;

    // Step 6: Test updating role permissions
    console.log('\n6. 🔄 Testing update role permissions...');
    
    // Get some sample permissions to assign
    const samplePermissions = [1, 2, 3, 4, 5]; // First 5 SubModulePermission IDs
    
    const updatePermissionsResponse = await axios.put(
      `http://localhost:5000/api/permission-management/roles/${newRoleId}/permissions`,
      { permissions: samplePermissions },
      {
        headers,
        timeout: 10000
      }
    );
    
    console.log('✅ Update role permissions successful');
    console.log(`   Success: ${updatePermissionsResponse.data.success}`);
    console.log(`   Message: ${updatePermissionsResponse.data.message}`);

    // Step 7: Verify the permissions were assigned
    console.log('\n7. ✅ Verifying assigned permissions...');
    const verifyPermissionsResponse = await axios.get(
      `http://localhost:5000/api/permission-management/roles/${newRoleId}/permissions`, 
      {
        headers,
        timeout: 10000
      }
    );
    
    console.log('✅ Permission verification successful');
    console.log(`   Assigned permissions count: ${verifyPermissionsResponse.data.data.length}`);
    
    verifyPermissionsResponse.data.data.forEach(perm => {
      console.log(`   - ${perm.ModuleName} > ${perm.SubModuleName}: ${perm.PermissionName}`);
    });

    // Step 8: Test updating the role
    console.log('\n8. ✏️ Testing update role...');
    const updateRoleResponse = await axios.put(
      `http://localhost:5000/api/permission-management/roles/${newRoleId}`,
      { 
        name: `Updated Test Role ${Date.now()}`
      },
      {
        headers,
        timeout: 10000
      }
    );
    
    console.log('✅ Update role successful');
    console.log(`   Success: ${updateRoleResponse.data.success}`);
    console.log(`   Updated role: ${updateRoleResponse.data.data.Name}`);

    // Step 9: Test deleting the role
    console.log('\n9. 🗑️ Testing delete role...');
    const deleteRoleResponse = await axios.delete(
      `http://localhost:5000/api/permission-management/roles/${newRoleId}`,
      {
        headers,
        timeout: 10000
      }
    );
    
    console.log('✅ Delete role successful');
    console.log(`   Success: ${deleteRoleResponse.data.success}`);
    console.log(`   Message: ${deleteRoleResponse.data.message}`);

    console.log('\n🎉 All Permission Management API tests completed successfully!');

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Data:`, error.response.data);
    }
  }
}

testFullPermissionAPI().then(() => {
  console.log('\n🏁 All tests completed');
  process.exit(0);
}).catch(error => {
  console.log(`❌ Test crashed: ${error.message}`);
  process.exit(1);
});