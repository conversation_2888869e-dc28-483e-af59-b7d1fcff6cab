// frontend/src/components/layout/ThemeToggle.js
import React, { useState, useRef, useEffect } from 'react';
import { Moon, Sun, Palette } from 'lucide-react';
import { useTheme } from '../../contexts/themeContext';
import { THEMES } from '../../contexts/themeContext';

/**
 * ThemeToggle component that allows users to switch between light, dark, and saffron themes
 *
 * @returns {React.ReactNode} - The theme toggle button component
 */
const ThemeToggle = () => {
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Get icon based on current theme
  const getThemeIcon = () => {
    switch (theme) {
      case THEMES.DARK:
        return <Moon className="w-5 h-5" />;
      case THEMES.SAFFRON:
        return <Palette className="w-5 h-5" />;
      default:
        return <Sun className="w-5 h-5" />;
    }
  };

  // Get background color based on current theme
  const getButtonBgColor = () => {
    switch (theme) {
      case THEMES.DARK:
        return 'var(--color-accent)';
      case THEMES.SAFFRON:
        return 'var(--color-accent)';
      default:
        return 'var(--color-bg-secondary)';
    }
  };

  // Get text color based on current theme
  const getButtonTextColor = () => {
    switch (theme) {
      case THEMES.DARK:
        return 'var(--color-text-inverted)';
      case THEMES.SAFFRON:
        return 'var(--color-text-inverted)';
      default:
        return 'var(--color-accent)';
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Change theme"
        title="Change theme"
        style={{
          backgroundColor: getButtonBgColor(),
          color: getButtonTextColor(),
          boxShadow: '0 1px 3px var(--color-shadow)',
          transform: 'scale(1)',
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.transform = 'scale(1.05)';
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.transform = 'scale(1)';
        }}
      >
        {getThemeIcon()}
      </button>

      {isOpen && (
        <div 
          className="absolute right-0 mt-2 w-48 rounded-md shadow-lg z-50"
          style={{
            backgroundColor: 'var(--color-bg-dropdown)',
            borderColor: 'var(--color-border)',
            borderWidth: '1px',
          }}
        >
          <div className="py-1">
            <button
              onClick={() => {
                setTheme(THEMES.LIGHT);
                setIsOpen(false);
              }}
              className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
              style={{
                backgroundColor: theme === THEMES.LIGHT ? 'var(--color-bg-active)' : 'transparent',
                color: 'var(--color-text-primary)',
              }}
            >
              <Sun className="w-4 h-4 mr-2" />
              Light Theme
            </button>
            <button
              onClick={() => {
                setTheme(THEMES.DARK);
                setIsOpen(false);
              }}
              className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
              style={{
                backgroundColor: theme === THEMES.DARK ? 'var(--color-bg-active)' : 'transparent',
                color: 'var(--color-text-primary)',
              }}
            >
              <Moon className="w-4 h-4 mr-2" />
              Dark Theme
            </button>
            <button
              onClick={() => {
                setTheme(THEMES.SAFFRON);
                setIsOpen(false);
              }}
              className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
              style={{
                backgroundColor: theme === THEMES.SAFFRON ? 'var(--color-bg-active)' : 'transparent',
                color: 'var(--color-text-primary)',
              }}
            >
              <Palette className="w-4 h-4 mr-2" />
              Saffron Theme
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeToggle;
