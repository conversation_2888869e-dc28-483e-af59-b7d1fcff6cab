import React from 'react';
import { Building2, Building, Users } from 'lucide-react';
import StatsCard from '../statsCard';

export default function CityStats({ stats }) {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">
        {stats.cityInfo.name}, {stats.cityInfo.state.name}, {stats.cityInfo.state.country.name} Statistics
      </h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatsCard
          title="Companies"
          value={stats.statistics.companyCount}
          icon={<Building2 className="w-6 h-6" />}
        />
        <StatsCard
          title="Branches"
          value={stats.statistics.branchCount}
          icon={<Building className="w-6 h-6" />}
        />
        <StatsCard
          title="Clients"
          value={stats.statistics.clientCount}
          icon={<Users className="w-6 h-6" />}
        />
      </div>
    </div>
  );
}
