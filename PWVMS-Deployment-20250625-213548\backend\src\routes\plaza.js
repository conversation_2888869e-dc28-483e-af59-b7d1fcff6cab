const express = require('express');
const router = express.Router();
const companyMasterController = require('../controllers/CompanyMasterController');
const plazaController = require('../controllers/PlazaControlleer');  // Note: The file is actually named with a typo (PlazaControlleer.js)
const auth = require('../middleware/auth');

/**
 * @route   GET /api/plazas
 * @desc    Get all plazas
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), plazaController.getAllPlazas);

/**
 * @route   GET /api/plazas/:id
 * @desc    Get plaza by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), plazaController.getPlazaById);

/**
 * @route   POST /api/plazas
 * @desc    Create a new plaza
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), plazaController.uploadPlazaFiles(), plazaController.createPlaza);

/**
 * @route   PUT /api/plazas/:id
 * @desc    Update a plaza
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.put('/:id', auth(['Edit']), plazaController.uploadPlazaFiles(), plazaController.updatePlaza);

/**
 * @route   DELETE /api/plazas/:id
 * @desc    Delete a plaza
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), plazaController.deletePlaza);

/**
 * @route   GET /api/plazas/logo/:id
 * @desc    Get plaza logo
 * @access  Public
 */
// router.get('/logo/:id', plazaController.getPlazaLogo);

/**
 * @route   PATCH /api/plazas/toggle-status/:id
 * @desc    Toggle plaza active status
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.patch('/toggle-status/:id', auth(['Edit']), plazaController.toggleActiveStatus);

module.exports = router;

