import React, { useState, useEffect } from 'react';
import { Phone, ArrowRight, AlertCircle, Loader2 } from 'lucide-react';
import { valetCustomerApi } from '../../api/valetCustomerApi';
import { useToast } from '../../hooks/useToast';

const MobileNumberEntry = ({ 
  onNext, 
  onBack, 
  initialMobile = '',
  plazaInfo = null,
  className = "" 
}) => {
  const [mobileNumber, setMobileNumber] = useState(initialMobile);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isValid, setIsValid] = useState(false);
  
  const toast = useToast();

  // Validate mobile number format
  useEffect(() => {
    const validateMobile = (mobile) => {
      // Indian mobile number validation: 10 digits starting with 6-9
      const mobileRegex = /^[6-9]\d{9}$/;
      return mobileRegex.test(mobile);
    };

    setIsValid(validateMobile(mobileNumber));
    setError('');
  }, [mobileNumber]);

  const handleMobileChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 10) {
      setMobileNumber(value);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!isValid) {
      setError('Please enter a valid 10-digit mobile number');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      // Generate OTP for the mobile number
      const response = await valetCustomerApi.generateOTP(mobileNumber);
      
      if (response.success) {
        toast.showSuccess('OTP sent successfully!');
        onNext({
          mobileNumber,
          otpId: response.otpId,
          expiresAt: response.expiresAt
        });
      } else {
        throw new Error(response.message || 'Failed to send OTP');
      }

    } catch (error) {
      console.error('Error sending OTP:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Failed to send OTP';
      setError(errorMessage);
      toast.showError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`max-w-md mx-auto p-6 bg-white rounded-lg shadow-lg ${className}`}>
      
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Phone className="w-8 h-8 text-yellow-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Enter Mobile Number</h2>
        <p className="text-gray-600">
          We'll send you an OTP to verify your mobile number
        </p>
      </div>

      {/* Plaza Info */}
      {plazaInfo && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-1">{plazaInfo.plazaName}</h3>
          <p className="text-sm text-blue-700">{plazaInfo.valetPointName}</p>
          {plazaInfo.location && (
            <p className="text-xs text-blue-600 mt-1">{plazaInfo.location}</p>
          )}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        
        {/* Mobile Number Input */}
        <div>
          <label htmlFor="mobile" className="block text-sm font-medium text-gray-700 mb-2">
            Mobile Number
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">+91</span>
            </div>
            <input
              type="tel"
              id="mobile"
              value={mobileNumber}
              onChange={handleMobileChange}
              placeholder="Enter 10-digit mobile number"
              className={`
                block w-full pl-12 pr-4 py-3 border rounded-lg text-lg
                focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500
                ${error ? 'border-red-300' : isValid ? 'border-green-300' : 'border-gray-300'}
                ${isLoading ? 'bg-gray-50' : 'bg-white'}
              `}
              disabled={isLoading}
              autoComplete="tel"
              maxLength="10"
            />
            
            {/* Validation Indicator */}
            {mobileNumber.length > 0 && (
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                {isValid ? (
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                ) : (
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                )}
              </div>
            )}
          </div>
          
          {/* Helper Text */}
          <p className="mt-1 text-xs text-gray-500">
            Enter your mobile number without country code
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={!isValid || isLoading}
          className={`
            w-full flex items-center justify-center px-4 py-3 rounded-lg font-medium transition-colors
            ${isValid && !isLoading
              ? 'bg-yellow-500 hover:bg-yellow-600 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }
          `}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-5 h-5 mr-2 animate-spin" />
              Sending OTP...
            </>
          ) : (
            <>
              Send OTP
              <ArrowRight className="w-5 h-5 ml-2" />
            </>
          )}
        </button>
      </form>

      {/* Back Button */}
      {onBack && (
        <button
          onClick={onBack}
          disabled={isLoading}
          className="w-full mt-4 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors disabled:opacity-50"
        >
          ← Back to QR Scanner
        </button>
      )}

      {/* Terms */}
      <div className="mt-6 text-center">
        <p className="text-xs text-gray-500">
          By continuing, you agree to receive SMS messages for verification purposes.
          Standard message rates may apply.
        </p>
      </div>
    </div>
  );
};

export default MobileNumberEntry;
