import React, { useState, useEffect } from 'react';
import { Calendar } from 'lucide-react';

/**
 * DateRangePicker Component
 * 
 * A dropdown component for selecting date ranges for dashboard filtering
 * with an option to select a specific date
 * 
 * @param {Object} props - Component props
 * @param {string} props.value - Current selected date range
 * @param {Function} props.onChange - Function to call when selection changes
 */
export function DateRangePicker({ value = 'today', onChange }) {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  
  // Check if value is a specific date
  useEffect(() => {
    if (value && value.match(/^\d{4}-\d{2}-\d{2}$/)) {
      setSelectedDate(value);
      setShowDatePicker(true);
    }
  }, [value]);

  const dateRanges = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'week', label: 'Last 7 Days' },
    { value: 'month', label: 'Last 30 Days' },
    { value: 'year', label: 'Last 12 Months' },
    { value: 'specific', label: 'Specific Date' }
  ];

  const handleSelectChange = (e) => {
    const newValue = e.target.value;
    if (newValue === 'specific') {
      setShowDatePicker(true);
      // Default to today's date
      const today = new Date();
      const formattedDate = formatDateForInput(today);
      setSelectedDate(formattedDate);
      onChange(formattedDate);
    } else {
      setShowDatePicker(false);
      onChange(newValue);
    }
  };

  const handleDateChange = (e) => {
    const date = e.target.value;
    setSelectedDate(date);
    onChange(date);
  };

  // Format date as YYYY-MM-DD for input
  const formatDateForInput = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  return (
    <div className="flex flex-wrap gap-3 items-center">
      <div>
        <select
          value={showDatePicker ? 'specific' : value}
          onChange={handleSelectChange}
          className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {dateRanges.map((range) => (
            <option key={range.value} value={range.value}>
              {range.label}
            </option>
          ))}
        </select>
      </div>
      
      {showDatePicker && (
        <div>
          <input
            type="date"
            value={selectedDate}
            onChange={handleDateChange}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      )}
    </div>
  );
}