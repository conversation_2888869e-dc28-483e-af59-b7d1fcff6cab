// frontend/src/utils/permissionFilters.js

/**
 * Filter data based on user role and permissions
 *
 * @param {Array} data - The data array to filter
 * @param {Object} user - The current user object
 * @param {Object} options - Filter options
 * @param {string} options.companyIdField - The field name for company ID in the data objects
 * @param {string} options.plazaIdField - The field name for plaza ID in the data objects
 * @returns {Array} - The filtered data array
 */
export const filterDataByUserAccess = (data, user, options = {}) => {
  // Safety checks
  if (!data || !Array.isArray(data)) {
    return [];
  }

  if (!user) {
    return [];
  }

  const {
    companyIdField = 'CompanyId',
    plazaIdField = 'PlazaId',
    includeInactive = false
  } = options;

  // SuperAdmin can see all data
  if (user.role === 'SuperAdmin') {
    // Always include inactive items
    return [...data]; // Return a copy to avoid reference issues
  }

  // CompanyAdmin can only see data for their companies
  if (user.role === 'CompanyAdmin') {
    // Make sure user.companies exists and is an array
    if (!user.companies || !Array.isArray(user.companies) || user.companies.length === 0) {
      console.warn('CompanyAdmin has no companies assigned in user object:', user.id);
      return [];
    }

    // Extract company IDs, ensuring they are numbers
    const companyIds = user.companies
      .filter(company => company && (company.Id !== undefined && company.Id !== null))
      .map(company => {
        // Convert to number if it's a string
        const id = typeof company.Id === 'string' ? parseInt(company.Id, 10) : company.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (companyIds.length === 0) {
      console.warn('CompanyAdmin has no valid company IDs after filtering:', user.id);
      return [];
    }
    
    console.log('CompanyAdmin has access to companies:', companyIds);

    // Check if we're dealing with user data (users have RoleName field)
    const isUserData = data.length > 0 && data[0] && 'RoleName' in data[0];

    // Check if we're dealing with digital pay configurations
    const isDigitalPayConfig = data.length > 0 && data[0] && 'ConfigLaneID' in data[0];

    if (isUserData) {
      // For user data, check the companies array instead of a direct CompanyId field
      return data.filter(item => {
        // Do NOT include SuperAdmin users in the list for CompanyAdmin
        if (item.RoleName === 'SuperAdmin') {
          return false;
        }

        // Check if the user has a companies array
        if (!item.companies || !Array.isArray(item.companies) || item.companies.length === 0) {
          return false;
        }

        // Check if any of the user's companies match the CompanyAdmin's companies
        return item.companies.some(company => {
          const companyId = typeof company.Id === 'string' ? parseInt(company.Id, 10) : company.Id;
          return !isNaN(companyId) && companyIds.includes(companyId);
        });
      });
    } else if (isDigitalPayConfig) {
      // For digital pay configurations, include both active and inactive configurations
      return data.filter(item => {
        if (!item || item[companyIdField] === undefined || item[companyIdField] === null) {
          return false;
        }

        // Convert to number if it's a string
        const itemCompanyId = typeof item[companyIdField] === 'string'
          ? parseInt(item[companyIdField], 10)
          : item[companyIdField];

        return !isNaN(itemCompanyId) && companyIds.includes(itemCompanyId);
      });
    } else {
      // For other data types, use the standard filtering logic
      let filteredData = data.filter(item => {
        if (!item || item[companyIdField] === undefined || item[companyIdField] === null) {
          return false;
        }

        // Convert to number if it's a string
        const itemCompanyId = typeof item[companyIdField] === 'string'
          ? parseInt(item[companyIdField], 10)
          : item[companyIdField];

        return !isNaN(itemCompanyId) && companyIds.includes(itemCompanyId);
      });

      // Always include inactive items
      return filteredData;
    }
  }

  // PlazaManager can only see data for their plazas
  if (user.role === 'PlazaManager') {
    // Make sure user.plazas exists and is an array
    if (!user.plazas || !Array.isArray(user.plazas) || user.plazas.length === 0) {
      return [];
    }

    // Extract plaza IDs, ensuring they are numbers
    const plazaIds = user.plazas
      .filter(plaza => plaza && plaza.Id)
      .map(plaza => {
        // Convert to number if it's a string
        const id = typeof plaza.Id === 'string' ? parseInt(plaza.Id, 10) : plaza.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (plazaIds.length === 0) {
      return [];
    }

    // Extract company IDs from plazas, ensuring they are numbers
    const companyIds = [...new Set(
      user.plazas
        .filter(plaza => plaza && plaza.CompanyId)
        .map(plaza => {
          // Convert to number if it's a string
          const id = typeof plaza.CompanyId === 'string' ? parseInt(plaza.CompanyId, 10) : plaza.CompanyId;
          return isNaN(id) ? null : id;
        })
        .filter(id => id !== null)
    )];

    // Check if we're dealing with user data (users have RoleName field)
    const isUserData = data.length > 0 && data[0] && 'RoleName' in data[0];

    // Check if we're dealing with digital pay configurations
    const isDigitalPayConfig = data.length > 0 && data[0] && 'ConfigLaneID' in data[0];

    if (isUserData) {
      // For user data, check the plazas array instead of a direct PlazaId field
      return data.filter(item => {
        // Always include SuperAdmin users in the list
        if (item.RoleName === 'SuperAdmin') {
          return true;
        }

        // Include CompanyAdmin users for the same company
        if (item.RoleName === 'CompanyAdmin' && item.companies && Array.isArray(item.companies)) {
          return item.companies.some(company => {
            const companyId = typeof company.Id === 'string' ? parseInt(company.Id, 10) : company.Id;
            return !isNaN(companyId) && companyIds.includes(companyId);
          });
        }

        // Check if the user has a plazas array
        if (!item.plazas || !Array.isArray(item.plazas) || item.plazas.length === 0) {
          return false;
        }

        // Check if any of the user's plazas match the PlazaManager's plazas
        return item.plazas.some(plaza => {
          const plazaId = typeof plaza.Id === 'string' ? parseInt(plaza.Id, 10) : plaza.Id;
          return !isNaN(plazaId) && plazaIds.includes(plazaId);
        });
      });
    } else if (isDigitalPayConfig) {
      // For digital pay configurations, include both active and inactive configurations
      return data.filter(item => {
        if (!item || item[plazaIdField] === undefined || item[plazaIdField] === null) {
          return false;
        }

        // Convert to number if it's a string
        const itemPlazaId = typeof item[plazaIdField] === 'string'
          ? parseInt(item[plazaIdField], 10)
          : item[plazaIdField];

        return !isNaN(itemPlazaId) && plazaIds.includes(itemPlazaId);
      });
    } else {
      // For other data types, use the standard filtering logic
      let filteredData = data.filter(item => {
        if (!item || item[plazaIdField] === undefined || item[plazaIdField] === null) {
          return false;
        }

        // Convert to number if it's a string
        const itemPlazaId = typeof item[plazaIdField] === 'string'
          ? parseInt(item[plazaIdField], 10)
          : item[plazaIdField];

        return !isNaN(itemPlazaId) && plazaIds.includes(itemPlazaId);
      });

      // Always include inactive items
      return filteredData;
    }
  }

  return [];
};

/**
 * Get available companies for the current user
 *
 * @param {Array} companies - All companies
 * @param {Object} user - The current user object
 * @returns {Array} - The filtered companies array
 */
export const getAvailableCompanies = (companies, user) => {
  // Safety checks
  if (!companies || !Array.isArray(companies)) {
    return [];
  }

  if (!user) {
    return [];
  }

  // SuperAdmin can see all companies
  if (user.role === 'SuperAdmin') {
    return [...companies]; // Return a copy to avoid reference issues
  }

  // CompanyAdmin can only see their assigned companies
  if (user.role === 'CompanyAdmin') {
    // Make sure user.companies exists and is an array
    if (!user.companies || !Array.isArray(user.companies) || user.companies.length === 0) {
      return [];
    }

    // Extract company IDs, ensuring they are numbers
    const companyIds = user.companies
      .filter(company => company && company.Id)
      .map(company => {
        // Convert to number if it's a string
        const id = typeof company.Id === 'string' ? parseInt(company.Id, 10) : company.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (companyIds.length === 0) {
      return [];
    }

    // Filter companies, ensuring the Id exists and is valid
    return companies.filter(company => {
      if (!company || company.Id === undefined || company.Id === null) {
        return false;
      }

      // Convert to number if it's a string
      const companyId = typeof company.Id === 'string'
        ? parseInt(company.Id, 10)
        : company.Id;

      return !isNaN(companyId) && companyIds.includes(companyId);
    });
  }

  // PlazaManager can only see companies related to their plazas
  if (user.role === 'PlazaManager') {
    // Make sure user.plazas exists and is an array
    if (!user.plazas || !Array.isArray(user.plazas) || user.plazas.length === 0) {
      return [];
    }

    // Extract company IDs from plazas, ensuring they are numbers
    const companyIds = [...new Set(
      user.plazas
        .filter(plaza => plaza && plaza.CompanyId)
        .map(plaza => {
          // Convert to number if it's a string
          const id = typeof plaza.CompanyId === 'string' ? parseInt(plaza.CompanyId, 10) : plaza.CompanyId;
          return isNaN(id) ? null : id;
        })
        .filter(id => id !== null)
    )];

    if (companyIds.length === 0) {
      return [];
    }

    // Filter companies, ensuring the Id exists and is valid
    return companies.filter(company => {
      if (!company || company.Id === undefined || company.Id === null) {
        return false;
      }

      // Convert to number if it's a string
      const companyId = typeof company.Id === 'string'
        ? parseInt(company.Id, 10)
        : company.Id;

      return !isNaN(companyId) && companyIds.includes(companyId);
    });
  }

  return [];
};

/**
 * Get available plazas for the current user
 *
 * @param {Array} plazas - All plazas
 * @param {Object} user - The current user object
 * @param {number} [companyId] - Optional company ID to filter by
 * @param {boolean} [includeInactive=true] - Whether to include inactive plazas
 * @returns {Array} - The filtered plazas array
 */
export const getAvailablePlazas = (plazas, user, companyId = null, includeInactive = true) => {
  // Safety checks
  if (!plazas || !Array.isArray(plazas)) {
    return [];
  }

  if (!user) {
    return [];
  }

  let filteredPlazas = [...plazas]; // Create a copy to avoid reference issues

  // Filter by company ID if provided
  if (companyId !== null && companyId !== undefined) {
    // Convert companyId to number if it's a string
    const companyIdNum = typeof companyId === 'string' ? parseInt(companyId, 10) : companyId;

    if (!isNaN(companyIdNum)) {
      filteredPlazas = filteredPlazas.filter(plaza => {
        if (!plaza || plaza.CompanyId === undefined || plaza.CompanyId === null) {
          return false;
        }

        // Convert to number if it's a string
        const plazaCompanyId = typeof plaza.CompanyId === 'string'
          ? parseInt(plaza.CompanyId, 10)
          : plaza.CompanyId;

        return !isNaN(plazaCompanyId) && plazaCompanyId === companyIdNum;
      });
    }
  }

  // SuperAdmin can see all plazas (within the selected company if specified)
  if (user.role === 'SuperAdmin') {
    // If includeInactive is true, return all plazas
    // Otherwise, filter out inactive plazas
    if (includeInactive) {
      return filteredPlazas;
    } else {
      return filteredPlazas.filter(plaza =>
        plaza.IsActive === true ||
        plaza.IsActive === 1 ||
        plaza.IsActive === '1' ||
        plaza.IsActive === 'Y'
      );
    }
  }

  // CompanyAdmin can only see plazas for their companies
  if (user.role === 'CompanyAdmin') {
    // Make sure user.companies exists and is an array
    if (!user.companies || !Array.isArray(user.companies) || user.companies.length === 0) {
      return [];
    }

    // Extract company IDs, ensuring they are numbers
    const companyIds = user.companies
      .filter(company => company && company.Id)
      .map(company => {
        // Convert to number if it's a string
        const id = typeof company.Id === 'string' ? parseInt(company.Id, 10) : company.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (companyIds.length === 0) {
      return [];
    }

    // Filter plazas, ensuring the CompanyId exists and is valid
    return filteredPlazas.filter(plaza => {
      if (!plaza || plaza.CompanyId === undefined || plaza.CompanyId === null) {
        return false;
      }

      // Convert to number if it's a string
      const plazaCompanyId = typeof plaza.CompanyId === 'string'
        ? parseInt(plaza.CompanyId, 10)
        : plaza.CompanyId;

      return !isNaN(plazaCompanyId) && companyIds.includes(plazaCompanyId);
    });
  }

  // PlazaManager can only see their assigned plazas
  if (user.role === 'PlazaManager') {
    // Make sure user.plazas exists and is an array
    if (!user.plazas || !Array.isArray(user.plazas) || user.plazas.length === 0) {
      return [];
    }

    // Extract plaza IDs, ensuring they are numbers
    const plazaIds = user.plazas
      .filter(plaza => plaza && plaza.Id)
      .map(plaza => {
        // Convert to number if it's a string
        const id = typeof plaza.Id === 'string' ? parseInt(plaza.Id, 10) : plaza.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (plazaIds.length === 0) {
      return [];
    }

    // Filter plazas, ensuring the Id exists and is valid
    return filteredPlazas.filter(plaza => {
      if (!plaza || plaza.Id === undefined || plaza.Id === null) {
        return false;
      }

      // Convert to number if it's a string
      const plazaId = typeof plaza.Id === 'string'
        ? parseInt(plaza.Id, 10)
        : plaza.Id;

      return !isNaN(plazaId) && plazaIds.includes(plazaId);
    });
  }

  return [];
};

/**
 * Get available lanes for the current user
 *
 * @param {Array} lanes - All lanes
 * @param {Object} user - The current user object
 * @param {number} [plazaId] - Optional plaza ID to filter by
 * @param {number} [companyId] - Optional company ID to filter by
 * @returns {Array} - The filtered lanes array
 */
export const getAvailableLanes = (lanes, user, plazaId = null, companyId = null) => {
  // Safety checks
  if (!lanes || !Array.isArray(lanes)) {
    return [];
  }

  if (!user) {
    return [];
  }

  let filteredLanes = [...lanes]; // Create a copy to avoid reference issues

  // Filter by plaza ID if provided
  if (plazaId !== null && plazaId !== undefined && plazaId !== '') {
    // Convert plazaId to number if it's a string
    const plazaIdNum = typeof plazaId === 'string' ? parseInt(plazaId, 10) : plazaId;

    if (!isNaN(plazaIdNum)) {
      filteredLanes = filteredLanes.filter(lane => {
        if (!lane || lane.PlazaID === undefined || lane.PlazaID === null) {
          return false;
        }

        // Convert to number if it's a string
        const lanePlazaId = typeof lane.PlazaID === 'string'
          ? parseInt(lane.PlazaID, 10)
          : lane.PlazaID;

        return !isNaN(lanePlazaId) && lanePlazaId === plazaIdNum;
      });
    }
  }

  // Filter by company ID if provided
  if (companyId !== null && companyId !== undefined && companyId !== '' && !plazaId) {
    // Convert companyId to number if it's a string
    const companyIdNum = typeof companyId === 'string' ? parseInt(companyId, 10) : companyId;

    if (!isNaN(companyIdNum)) {
      filteredLanes = filteredLanes.filter(lane => {
        if (!lane || lane.CompanyID === undefined || lane.CompanyID === null) {
          return false;
        }

        // Convert to number if it's a string
        const laneCompanyId = typeof lane.CompanyID === 'string'
          ? parseInt(lane.CompanyID, 10)
          : lane.CompanyID;

        return !isNaN(laneCompanyId) && laneCompanyId === companyIdNum;
      });
    }
  }

  // SuperAdmin can see all lanes (within the selected plaza/company if specified)
  if (user.role === 'SuperAdmin') {
    return filteredLanes;
  }

  // CompanyAdmin can only see lanes for their companies
  if (user.role === 'CompanyAdmin') {
    // If already filtered by company ID, return the filtered lanes
    if (companyId !== null && companyId !== undefined && companyId !== '') {
      return filteredLanes;
    }

    // Make sure user.companies exists and is an array
    if (!user.companies || !Array.isArray(user.companies) || user.companies.length === 0) {
      return [];
    }

    // Extract company IDs, ensuring they are numbers
    const companyIds = user.companies
      .filter(company => company && company.Id)
      .map(company => {
        // Convert to number if it's a string
        const id = typeof company.Id === 'string' ? parseInt(company.Id, 10) : company.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (companyIds.length === 0) {
      return [];
    }

    // Filter lanes, ensuring the CompanyID exists and is valid
    return filteredLanes.filter(lane => {
      if (!lane || lane.CompanyID === undefined || lane.CompanyID === null) {
        return false;
      }

      // Convert to number if it's a string
      const laneCompanyId = typeof lane.CompanyID === 'string'
        ? parseInt(lane.CompanyID, 10)
        : lane.CompanyID;

      return !isNaN(laneCompanyId) && companyIds.includes(laneCompanyId);
    });
  }

  // PlazaManager can only see lanes for their assigned plazas
  if (user.role === 'PlazaManager') {
    // If already filtered by plaza ID, return the filtered lanes
    if (plazaId !== null && plazaId !== undefined && plazaId !== '') {
      // Make sure the plaza belongs to the user
      const plazaIdNum = typeof plazaId === 'string' ? parseInt(plazaId, 10) : plazaId;
      const userHasPlaza = user.plazas && Array.isArray(user.plazas) && user.plazas.some(plaza => {
        const userPlazaId = typeof plaza.Id === 'string' ? parseInt(plaza.Id, 10) : plaza.Id;
        return !isNaN(userPlazaId) && userPlazaId === plazaIdNum;
      });

      if (userHasPlaza) {
        return filteredLanes;
      }
    }

    // Make sure user.plazas exists and is an array
    if (!user.plazas || !Array.isArray(user.plazas) || user.plazas.length === 0) {
      return [];
    }

    // Extract plaza IDs, ensuring they are numbers
    const plazaIds = user.plazas
      .filter(plaza => plaza && plaza.Id)
      .map(plaza => {
        // Convert to number if it's a string
        const id = typeof plaza.Id === 'string' ? parseInt(plaza.Id, 10) : plaza.Id;
        return isNaN(id) ? null : id;
      })
      .filter(id => id !== null);

    if (plazaIds.length === 0) {
      return [];
    }

    // Filter lanes, ensuring the PlazaID exists and is valid
    return filteredLanes.filter(lane => {
      if (!lane || lane.PlazaID === undefined || lane.PlazaID === null) {
        return false;
      }

      // Convert to number if it's a string
      const lanePlazaId = typeof lane.PlazaID === 'string'
        ? parseInt(lane.PlazaID, 10)
        : lane.PlazaID;

      return !isNaN(lanePlazaId) && plazaIds.includes(lanePlazaId);
    });
  }

  return [];
};