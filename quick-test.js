const axios = require('axios');

async function quickTest() {
  console.log('🔍 Quick Permission Management Test\n');

  try {
    // Step 1: Login
    console.log('1. Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'superadmin',
      password: 'Admin@123'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ Login successful');

    // Step 2: Test modules-tree endpoint with timeout
    console.log('2. Testing modules-tree endpoint...');
    
    const headers = { 'Authorization': `Bearer ${token}` };
    
    try {
      const modulesResponse = await axios.get('http://localhost:5000/api/permission-management/modules-tree', {
        headers,
        timeout: 10000 // 10 second timeout
      });
      
      console.log('✅ Modules response received');
      console.log(`   Success: ${modulesResponse.data.success}`);
      console.log(`   Data length: ${modulesResponse.data.data ? modulesResponse.data.data.length : 'undefined'}`);
      console.log(`   Message: ${modulesResponse.data.message}`);
      
      if (modulesResponse.data.data && modulesResponse.data.data.length > 0) {
        console.log(`   First module: ${modulesResponse.data.data[0].name}`);
        console.log(`   Submodules: ${modulesResponse.data.data[0].subModules.length}`);
      } else {
        console.log('❌ No modules data returned - THIS IS THE ISSUE!');
      }
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('❌ Request timed out - server might be hanging');
      } else {
        console.log(`❌ Modules request failed: ${error.response?.data?.message || error.message}`);
      }
    }

    // Step 3: Test roles endpoint
    console.log('3. Testing roles endpoint...');
    
    try {
      const rolesResponse = await axios.get('http://localhost:5000/api/permission-management/roles', {
        headers,
        timeout: 10000
      });
      
      console.log('✅ Roles response received');
      console.log(`   Success: ${rolesResponse.data.success}`);
      console.log(`   Roles count: ${rolesResponse.data.data ? rolesResponse.data.data.length : 'undefined'}`);
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('❌ Roles request timed out');
      } else {
        console.log(`❌ Roles request failed: ${error.response?.data?.message || error.message}`);
      }
    }

    // Step 4: Test a simple endpoint to see if server is responsive
    console.log('4. Testing server responsiveness...');
    
    try {
      const testResponse = await axios.get('http://localhost:5000/api/permission-management/test', {
        timeout: 5000
      });
      console.log('✅ Server is responsive');
    } catch (error) {
      console.log('❌ Server not responsive');
    }

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }
}

quickTest().then(() => {
  console.log('\n🏁 Quick test completed');
  process.exit(0);
}).catch(error => {
  console.log(`❌ Test crashed: ${error.message}`);
  process.exit(1);
});