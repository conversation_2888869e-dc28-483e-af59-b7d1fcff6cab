// frontend/src/components/auth/PermissionContent.js
import React from 'react';
import { useAuth } from '../../contexts/authContext';

/**
 * PermissionContent component that conditionally renders content based on user permissions
 * Similar to PermissionGuard but with a more descriptive name for content-specific use cases
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authorized
 * @param {string[]} [props.requiredPermissions] - Array of permissions required to render children
 * @param {string} [props.requiredModule] - Module name for permission check
 * @param {string[]} [props.allowedRoles] - Array of roles allowed to render children
 * @param {number} [props.companyId] - Company ID to check access for
 * @param {number} [props.plazaId] - Plaza ID to check access for
 * @param {React.ReactNode} [props.fallback] - Component to render if not authorized
 * @returns {React.ReactNode} - The children or fallback component
 */
const PermissionContent = ({
  children,
  requiredPermissions = [],
  requiredModule,
  allowedRoles = [],
  companyId,
  plazaId,
  fallback = null
}) => {
  const {
    user,
    hasPermission,
    hasRole,
    hasCompanyAccess,
    hasPlazaAccess,
    isSuperAdmin
  } = useAuth();

  // If no user is logged in, render fallback
  if (!user) {
    return fallback;
  }

  // SuperAdmin can access everything
  if (isSuperAdmin()) {
    return children;
  }

  // Check role-based access if specified
  if (allowedRoles.length > 0 && !allowedRoles.some(role => hasRole(role))) {
    return fallback;
  }

  // Check permission-based access if specified
  if (requiredModule && requiredPermissions.length > 0) {
    const hasRequiredPermission = requiredPermissions.some(
      permission => hasPermission(requiredModule, permission)
    );

    if (!hasRequiredPermission) {
      return fallback;
    }
  }

  // Check company access if specified
  if (companyId && !hasCompanyAccess(companyId)) {
    return fallback;
  }

  // Check plaza access if specified
  if (plazaId && !hasPlazaAccess(plazaId)) {
    return fallback;
  }

  // User is authorized
  return children;
};

// Export both as default and named export for backward compatibility
export { PermissionContent };
export default PermissionContent;
