// Quick fix script to update ActiveStatus handling in LaneController.js
const fs = require('fs');
const path = require('path');

const filePath = path.join(__dirname, 'backend/src/controllers/LaneController.js');

// Read the file
let content = fs.readFileSync(filePath, 'utf8');

// Replace the first occurrence (createLane function) - line 632
const oldLine632 = '        ActiveStatus: ActiveStatus !== undefined ? ActiveStatus : true, // Default to active if not specified';
const newLine632 = '        ActiveStatus: processedActiveStatus !== undefined ? processedActiveStatus : \'Y\', // Use processed status (Y/N)';

// Find the first occurrence and replace it
const firstIndex = content.indexOf(oldLine632);
if (firstIndex !== -1) {
  content = content.substring(0, firstIndex) + newLine632 + content.substring(firstIndex + oldLine632.length);
  console.log('✅ Fixed createLane function ActiveStatus handling');
} else {
  console.log('❌ Could not find the line to replace in createLane function');
}

// Write the file back
fs.writeFileSync(filePath, content, 'utf8');
console.log('✅ File updated successfully');
console.log('Please restart your backend server to apply the changes.');