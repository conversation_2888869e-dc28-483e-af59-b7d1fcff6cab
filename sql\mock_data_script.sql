-- Mock Data Generation Script for PWVMS
-- This script creates mock data for the following tables:
-- 1. tblLaneDetails (Lane)
-- 2. tblLaneANPRConfiguration (ANPR)
-- 3. tblLaneFastagConfiguration (Fastag)
-- 4. tblLaneDigitalPayConfiguration (DigitalPay)
-- 5. tblLaneUHFReaderDetails (UHF)
-- 6. tbl_Parkwiz_Pass_Reg (PassRegistration)

-- Clear existing data (optional - comment out if you want to keep existing data)
DELETE FROM tbl_Parkwiz_Pass_Reg WHERE 1=1;
DELETE FROM tblLaneUHFReaderDetails WHERE 1=1;
DELETE FROM tblLaneDigitalPayConfiguration WHERE 1=1;
DELETE FROM tblLaneFastagConfiguration WHERE 1=1;
DELETE FROM tblLaneANPRConfiguration WHERE 1=1;
DELETE FROM tblLaneDetails WHERE 1=1;

-- Get existing company and plaza IDs
DECLARE @CompanyID1 INT, @CompanyID2 INT;
DECLARE @PlazaID1 INT, @PlazaID2 INT, @PlazaID3 INT;
DECLARE @PlazaCode1 VARCHAR(50), @PlazaCode2 VARCHAR(50), @PlazaCode3 VARCHAR(50);
DECLARE @PlazaName1 VARCHAR(100), @PlazaName2 VARCHAR(100), @PlazaName3 VARCHAR(100);
DECLARE @UpdatedBy INT = 1; -- Default admin user ID

-- Get first two companies
SELECT TOP 2 Id, CompanyName
INTO #TempCompanies
FROM tblCompanyMaster
ORDER BY Id;

SELECT @CompanyID1 = (SELECT TOP 1 Id FROM #TempCompanies ORDER BY Id);
SELECT @CompanyID2 = (SELECT TOP 1 Id FROM #TempCompanies WHERE Id <> @CompanyID1);

-- Get three plazas
SELECT TOP 3 Id, PlazaCode, PlazaName, CompanyId
INTO #TempPlazas
FROM Plaza
ORDER BY Id;

SELECT @PlazaID1 = Id, @PlazaCode1 = PlazaCode, @PlazaName1 = PlazaName FROM (SELECT TOP 1 * FROM #TempPlazas ORDER BY Id) AS P1;
SELECT @PlazaID2 = Id, @PlazaCode2 = PlazaCode, @PlazaName2 = PlazaName FROM (SELECT TOP 1 * FROM #TempPlazas WHERE Id <> @PlazaID1 ORDER BY Id) AS P2;
SELECT @PlazaID3 = Id, @PlazaCode3 = PlazaCode, @PlazaName3 = PlazaName FROM (SELECT TOP 1 * FROM #TempPlazas WHERE Id NOT IN (@PlazaID1, @PlazaID2) ORDER BY Id) AS P3;

-- If no companies or plazas exist, create default values
IF @CompanyID1 IS NULL
BEGIN
    SET @CompanyID1 = 1;
    SET @CompanyID2 = 2;
END

IF @PlazaID1 IS NULL
BEGIN
    SET @PlazaID1 = 1;
    SET @PlazaID2 = 2;
    SET @PlazaID3 = 3;
    SET @PlazaCode1 = 'PLZ001';
    SET @PlazaCode2 = 'PLZ002';
    SET @PlazaCode3 = 'PLZ003';
    SET @PlazaName1 = 'Plaza One';
    SET @PlazaName2 = 'Plaza Two';
    SET @PlazaName3 = 'Plaza Three';
END

-- 1. Insert mock data into tblLaneDetails
PRINT 'Creating mock data for tblLaneDetails...';

-- Entry Lanes
INSERT INTO tblLaneDetails (
    PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
    DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus, 
    iDisplayComport, DisplayPole, CashDrawer, MultipleExit, Antipassback, 
    HFPasscard, HFPassPort, CoinReaderPort, APS_Exit, flgKioskCamera, 
    flgReceiptPrint, flgGKeyDetails, flgCKeyCard, flgP4S, LOTFee, 
    flgPasscard, PGTID, pgActivationKey, Passcard_Reader_Type, PayTmPG, 
    FlgLPRCamera, LPRCamIP, LPRCamID, LPRCamPass, iGraceMinute, 
    flgPaperSensor, PGSLevel, PrinterMake, BarcodeType, PrinterPort, 
    sPaytmWallet, sPaytmMID, sPaytmKey, fRecyclerStatus, sSMSKey, 
    flgCCUpdateEx, LaneNumber2, VehicleType2, flgSubLane, RecyclerType
)
VALUES
-- Plaza 1 Entry Lanes
(
    @PlazaID1, @CompanyID1, 'E01', 'Entry', 'Main Entry Lane 1', 'E', 
    'Welcome to Plaza 1', '*************', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'No', 0, 1, 
    1, 'COM3', 'COM4', 0, 1, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '*************', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),
(
    @PlazaID1, @CompanyID1, 'E02', 'Entry', 'Main Entry Lane 2', 'E', 
    'Welcome to Plaza 1', '*************', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'No', 0, 1, 
    1, 'COM3', 'COM4', 0, 1, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '*************', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),

-- Plaza 1 Exit Lanes
(
    @PlazaID1, @CompanyID1, 'X01', 'Exit', 'Main Exit Lane 1', 'X', 
    'Thank you for visiting', '192.168.1.103', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'Yes', 1, 1, 
    1, 'COM3', 'COM4', 1, 0, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '*************', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),
(
    @PlazaID1, @CompanyID1, 'X02', 'Exit', 'Main Exit Lane 2', 'X', 
    'Thank you for visiting', '192.168.1.104', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'Yes', 1, 1, 
    1, 'COM3', 'COM4', 1, 0, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '*************', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),

-- Plaza 2 Entry Lanes
(
    @PlazaID2, @CompanyID1, 'E01', 'Entry', 'Main Entry Lane 1', 'E', 
    'Welcome to Plaza 2', '192.168.2.101', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'No', 0, 1, 
    1, 'COM3', 'COM4', 0, 1, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '*************', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),

-- Plaza 2 Exit Lanes
(
    @PlazaID2, @CompanyID1, 'X01', 'Exit', 'Main Exit Lane 1', 'X', 
    'Thank you for visiting', '192.168.2.103', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'Yes', 1, 1, 
    1, 'COM3', 'COM4', 1, 0, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '192.168.2.203', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),

-- Plaza 3 Entry Lanes
(
    @PlazaID3, @CompanyID2, 'E01', 'Entry', 'Main Entry Lane 1', 'E', 
    'Welcome to Plaza 3', '192.168.3.101', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'No', 0, 1, 
    1, 'COM3', 'COM4', 0, 1, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '192.168.3.201', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
),

-- Plaza 3 Exit Lanes
(
    @PlazaID3, @CompanyID2, 'X01', 'Exit', 'Main Exit Lane 1', 'X', 
    'Thank you for visiting', '192.168.3.103', 'Car', @UpdatedBy, 1, 
    'COM1', 'Standard', 'Yes', 1, 1, 
    1, 'COM3', 'COM4', 1, 0, 
    1, 0, 0, 0, 0, 
    1, 1, 'key123', 'HF', 0, 
    1, '192.168.3.203', 'admin', 'pass123', 5, 
    1, 1, 'Epson', 'QR', 'COM2', 
    0, '', '', 0, 'smskey1', 
    0, '', '', 0, 'Standard'
);

-- Get the inserted lane IDs
DECLARE @LaneID1 INT, @LaneID2 INT, @LaneID3 INT, @LaneID4 INT, @LaneID5 INT, @LaneID6 INT, @LaneID7 INT, @LaneID8 INT;

SELECT @LaneID1 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID1 AND LaneNumber = 'E01';
SELECT @LaneID2 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID1 AND LaneNumber = 'E02';
SELECT @LaneID3 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID1 AND LaneNumber = 'X01';
SELECT @LaneID4 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID1 AND LaneNumber = 'X02';
SELECT @LaneID5 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID2 AND LaneNumber = 'E01';
SELECT @LaneID6 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID2 AND LaneNumber = 'X01';
SELECT @LaneID7 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID3 AND LaneNumber = 'E01';
SELECT @LaneID8 = LaneID FROM tblLaneDetails WHERE PlazaID = @PlazaID3 AND LaneNumber = 'X01';

-- 2. Insert mock data into tblLaneANPRConfiguration
PRINT 'Creating mock data for tblLaneANPRConfiguration...';

INSERT INTO tblLaneANPRConfiguration (
    PlazaID, CompanyID, LaneID, PMSLaneNumber, flgEnableANPR, ANPROrgID,
    ANPRLaneID, ANPRPublicKey, ANPRPrivateKey, ANPRSource, ANPRAPIURL,
    ANPRAPIURL2, ActiveStatus, UpdateDateTime, UpdatedBy,
    AllowBlacklistedVehicle, ANPRVendor
)
VALUES
-- Plaza 1 Entry Lanes
(
    @PlazaID1, @CompanyID1, @LaneID1, 'E01', 1, 'ORG001',
    'ANPR001', 'pub_key_1', 'priv_key_1', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 1'
),
(
    @PlazaID1, @CompanyID1, @LaneID2, 'E02', 1, 'ORG001',
    'ANPR002', 'pub_key_2', 'priv_key_2', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 1'
),

-- Plaza 1 Exit Lanes
(
    @PlazaID1, @CompanyID1, @LaneID3, 'X01', 1, 'ORG001',
    'ANPR003', 'pub_key_3', 'priv_key_3', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 1'
),
(
    @PlazaID1, @CompanyID1, @LaneID4, 'X02', 1, 'ORG001',
    'ANPR004', 'pub_key_4', 'priv_key_4', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 1'
),

-- Plaza 2 Lanes
(
    @PlazaID2, @CompanyID1, @LaneID5, 'E01', 1, 'ORG002',
    'ANPR005', 'pub_key_5', 'priv_key_5', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 2'
),
(
    @PlazaID2, @CompanyID1, @LaneID6, 'X01', 1, 'ORG002',
    'ANPR006', 'pub_key_6', 'priv_key_6', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 2'
),

-- Plaza 3 Lanes
(
    @PlazaID3, @CompanyID2, @LaneID7, 'E01', 1, 'ORG003',
    'ANPR007', 'pub_key_7', 'priv_key_7', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 3'
),
(
    @PlazaID3, @CompanyID2, @LaneID8, 'X01', 1, 'ORG003',
    'ANPR008', 'pub_key_8', 'priv_key_8', 'Camera', 'https://api.anpr.com/v1',
    'https://api.anpr.com/v2', 1, GETDATE(), @UpdatedBy,
    0, 'ANPR Vendor 3'
);

-- 3. Insert mock data into tblLaneFastagConfiguration
PRINT 'Creating mock data for tblLaneFastagConfiguration...';

INSERT INTO tblLaneFastagConfiguration (
    PlazaID, CompanyID, LaneID, LaneNumber, PlazaGeoCode,
    PlazaName, PlazaSubType, PlazaType, LaneDirection, LaneReaderID, LaneStatus,
    LaneMode, LaneType, LaneFloor, UpdatedDateTime, UpdatedBy,
    FastagOrgID, FastagAgencyCode, FastagAPIAddress, LaneGate, flgTerminal1Exit
)
VALUES
-- Plaza 1 Entry Lanes
(
    @PlazaID1, @CompanyID1, @LaneID1, 'E01', 'GEO001',
    @PlazaName1, 'Highway', 'Toll', 'Entry', 'RDR001', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG001', 'AGC001', 'https://api.fastag.com/v1', 'Gate1', 0
),
(
    @PlazaID1, @CompanyID1, @LaneID2, 'E02', 'GEO001',
    @PlazaName1, 'Highway', 'Toll', 'Entry', 'RDR002', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG001', 'AGC001', 'https://api.fastag.com/v1', 'Gate2', 0
),

-- Plaza 1 Exit Lanes
(
    @PlazaID1, @CompanyID1, @LaneID3, 'X01', 'GEO001',
    @PlazaName1, 'Highway', 'Toll', 'Exit', 'RDR003', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG001', 'AGC001', 'https://api.fastag.com/v1', 'Gate3', 1
),
(
    @PlazaID1, @CompanyID1, @LaneID4, 'X02', 'GEO001',
    @PlazaName1, 'Highway', 'Toll', 'Exit', 'RDR004', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG001', 'AGC001', 'https://api.fastag.com/v1', 'Gate4', 1
),

-- Plaza 2 Lanes
(
    @PlazaID2, @CompanyID1, @LaneID5, 'E01', 'GEO002',
    @PlazaName2, 'Highway', 'Toll', 'Entry', 'RDR005', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG002', 'AGC002', 'https://api.fastag.com/v1', 'Gate1', 0
),
(
    @PlazaID2, @CompanyID1, @LaneID6, 'X01', 'GEO002',
    @PlazaName2, 'Highway', 'Toll', 'Exit', 'RDR006', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG002', 'AGC002', 'https://api.fastag.com/v1', 'Gate2', 1
),

-- Plaza 3 Lanes
(
    @PlazaID3, @CompanyID2, @LaneID7, 'E01', 'GEO003',
    @PlazaName3, 'Highway', 'Toll', 'Entry', 'RDR007', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG003', 'AGC003', 'https://api.fastag.com/v1', 'Gate1', 0
),
(
    @PlazaID3, @CompanyID2, @LaneID8, 'X01', 'GEO003',
    @PlazaName3, 'Highway', 'Toll', 'Exit', 'RDR008', 'Active',
    'Auto', 'Car', 'Ground', GETDATE(), @UpdatedBy,
    'FTAG003', 'AGC003', 'https://api.fastag.com/v1', 'Gate2', 1
);

-- 4. Insert mock data into tblLaneDigitalPayConfiguration
PRINT 'Creating mock data for tblLaneDigitalPayConfiguration...';

INSERT INTO tblLaneDigitalPayConfiguration (
    PlazaID, CompanyID, LaneID, PMSLaneNumber, EnableCardPayment,
    EnableUPIPhonePe, EnableSendSMS, MerchantID, MerchantKey,
    PhonePeMerchantID, PhonePeMerchantKey, PhonePeSaltKey, PhonePeSaltIndex,
    ActiveStatus, UpdateDateTime, UpdatedBy, AllowBlacklistedVehicle
)
VALUES
-- Plaza 1 Entry Lanes
(
    @PlazaID1, @CompanyID1, @LaneID1, 'E01', 'Y',
    'Y', 'Y', 'MERCH001', 'MKEY001',
    'PPMERCH001', 'PPKEY001', 'SALT001', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),
(
    @PlazaID1, @CompanyID1, @LaneID2, 'E02', 'Y',
    'Y', 'Y', 'MERCH002', 'MKEY002',
    'PPMERCH002', 'PPKEY002', 'SALT002', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),

-- Plaza 1 Exit Lanes
(
    @PlazaID1, @CompanyID1, @LaneID3, 'X01', 'Y',
    'Y', 'Y', 'MERCH003', 'MKEY003',
    'PPMERCH003', 'PPKEY003', 'SALT003', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),
(
    @PlazaID1, @CompanyID1, @LaneID4, 'X02', 'Y',
    'Y', 'Y', 'MERCH004', 'MKEY004',
    'PPMERCH004', 'PPKEY004', 'SALT004', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),

-- Plaza 2 Lanes
(
    @PlazaID2, @CompanyID1, @LaneID5, 'E01', 'Y',
    'Y', 'Y', 'MERCH005', 'MKEY005',
    'PPMERCH005', 'PPKEY005', 'SALT005', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),
(
    @PlazaID2, @CompanyID1, @LaneID6, 'X01', 'Y',
    'Y', 'Y', 'MERCH006', 'MKEY006',
    'PPMERCH006', 'PPKEY006', 'SALT006', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),

-- Plaza 3 Lanes
(
    @PlazaID3, @CompanyID2, @LaneID7, 'E01', 'Y',
    'Y', 'Y', 'MERCH007', 'MKEY007',
    'PPMERCH007', 'PPKEY007', 'SALT007', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
),
(
    @PlazaID3, @CompanyID2, @LaneID8, 'X01', 'Y',
    'Y', 'Y', 'MERCH008', 'MKEY008',
    'PPMERCH008', 'PPKEY008', 'SALT008', '1',
    'Y', GETDATE(), @UpdatedBy, 'N'
);

-- 5. Insert mock data into tblLaneUHFReaderDetails
PRINT 'Creating mock data for tblLaneUHFReaderDetails...';

INSERT INTO tblLaneUHFReaderDetails (
    PlazaID, CompanyID, LaneID, ReaderLaneNumber, EnableUHFReader,
    ReaderIPAddress, ReaderPort, IOBoardIPAddress, IOBoardPort,
    ActiveStatus, flgFastag, UpdatedDateTime, UpdatedBy
)
VALUES
-- Plaza 1 Entry Lanes
(
    @PlazaID1, @CompanyID1, @LaneID1, 'E01', 'True',
    '*************', '8001', '*************', '8011',
    '1', 'True', GETDATE(), @UpdatedBy
),
(
    @PlazaID1, @CompanyID1, @LaneID2, 'E02', 'True',
    '*************', '8002', '*************', '8012',
    '1', 'True', GETDATE(), @UpdatedBy
),

-- Plaza 1 Exit Lanes
(
    @PlazaID1, @CompanyID1, @LaneID3, 'X01', 'True',
    '*************', '8003', '*************', '8013',
    '1', 'True', GETDATE(), @UpdatedBy
),
(
    @PlazaID1, @CompanyID1, @LaneID4, 'X02', 'True',
    '*************', '8004', '*************', '8014',
    '1', 'True', GETDATE(), @UpdatedBy
),

-- Plaza 2 Lanes
(
    @PlazaID2, @CompanyID1, @LaneID5, 'E01', 'True',
    '*************', '8005', '*************', '8015',
    '1', 'True', GETDATE(), @UpdatedBy
),
(
    @PlazaID2, @CompanyID1, @LaneID6, 'X01', 'True',
    '*************', '8006', '192.168.2.212', '8016',
    '1', 'True', GETDATE(), @UpdatedBy
),

-- Plaza 3 Lanes
(
    @PlazaID3, @CompanyID2, @LaneID7, 'E01', 'True',
    '192.168.3.201', '8007', '192.168.3.211', '8017',
    '1', 'True', GETDATE(), @UpdatedBy
),
(
    @PlazaID3, @CompanyID2, @LaneID8, 'X01', 'True',
    '192.168.3.202', '8008', '192.168.3.212', '8018',
    '1', 'True', GETDATE(), @UpdatedBy
);

-- 6. Insert mock data into tbl_Parkwiz_Pass_Reg
PRINT 'Creating mock data for tbl_Parkwiz_Pass_Reg...';

INSERT INTO tbl_Parkwiz_Pass_Reg (
    PlazaCode, PlazaName, ApplicationID, GIN, IssueDate, HolderName, CompanyName,
    VehicleType, ContactNo, PassType, PassTariffType, EmailID,
    ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
    TagMasterTagID, flgANPRPassEnable, MarkDelete, PaymentMode, PaymentType
)
VALUES
-- Plaza 1 Passes
(
    @PlazaCode1, @PlazaName1, 'APP001', 'GIN001', GETDATE(), 'John Smith', 'ABC Corp',
    'Car', '9876543210', 'Monthly', 'Regular', '<EMAIL>',
    DATEADD(MONTH, 1, GETDATE()), 'MH01AB1234', 'PASS001', 1000.00,
    'TAG001', 'True', 0, 'Online', 'UPI'
),
(
    @PlazaCode1, @PlazaName1, 'APP002', 'GIN002', GETDATE(), 'Jane Doe', 'XYZ Ltd',
    'Car', '9876543211', 'Quarterly', 'Premium', '<EMAIL>',
    DATEADD(MONTH, 3, GETDATE()), 'MH01CD5678', 'PASS002', 2500.00,
    'TAG002', 'True', 0, 'Online', 'Card'
),
(
    @PlazaCode1, @PlazaName1, 'APP003', 'GIN003', GETDATE(), 'Robert Johnson', 'PQR Inc',
    'SUV', '9876543212', 'Annual', 'Premium', '<EMAIL>',
    DATEADD(YEAR, 1, GETDATE()), 'MH01EF9012', 'PASS003', 8000.00,
    'TAG003', 'True', 0, 'Cash', 'Cash'
),

-- Plaza 2 Passes
(
    @PlazaCode2, @PlazaName2, 'APP004', 'GIN004', GETDATE(), 'Sarah Williams', 'LMN Corp',
    'Car', '9876543213', 'Monthly', 'Regular', '<EMAIL>',
    DATEADD(MONTH, 1, GETDATE()), 'MH02AB1234', 'PASS004', 1000.00,
    'TAG004', 'True', 0, 'Online', 'UPI'
),
(
    @PlazaCode2, @PlazaName2, 'APP005', 'GIN005', GETDATE(), 'Michael Brown', 'DEF Ltd',
    'SUV', '9876543214', 'Quarterly', 'Premium', '<EMAIL>',
    DATEADD(MONTH, 3, GETDATE()), 'MH02CD5678', 'PASS005', 2500.00,
    'TAG005', 'True', 0, 'Online', 'Card'
),

-- Plaza 3 Passes
(
    @PlazaCode3, @PlazaName3, 'APP006', 'GIN006', GETDATE(), 'Emily Davis', 'GHI Inc',
    'Car', '9876543215', 'Monthly', 'Regular', '<EMAIL>',
    DATEADD(MONTH, 1, GETDATE()), 'MH03AB1234', 'PASS006', 1000.00,
    'TAG006', 'True', 0, 'Online', 'UPI'
),
(
    @PlazaCode3, @PlazaName3, 'APP007', 'GIN007', GETDATE(), 'David Wilson', 'JKL Corp',
    'SUV', '9876543216', 'Annual', 'Premium', '<EMAIL>',
    DATEADD(YEAR, 1, GETDATE()), 'MH03CD5678', 'PASS007', 8000.00,
    'TAG007', 'True', 0, 'Cash', 'Cash'
),
(
    @PlazaCode3, @PlazaName3, 'APP008', 'GIN008', GETDATE(), 'Olivia Taylor', 'MNO Ltd',
    'Car', '9876543217', 'Quarterly', 'Regular', '<EMAIL>',
    DATEADD(MONTH, 3, GETDATE()), 'MH03EF9012', 'PASS008', 2500.00,
    'TAG008', 'True', 0, 'Online', 'Card'
);

-- Clean up temporary tables
DROP TABLE #TempCompanies;
DROP TABLE #TempPlazas;

PRINT 'Mock data creation completed successfully!';