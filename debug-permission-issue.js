const express = require('express');
const cors = require('cors');
const db = require('./backend/src/config/database');

const app = express();
app.use(cors());
app.use(express.json());

// Test endpoint to debug permission management
app.get('/debug-permissions', async (req, res) => {
  try {
    console.log('🔍 Starting permission management debug...');

    // Test 1: Check if database connection works
    console.log('1. Testing database connection...');
    const dbTest = await db.query('SELECT DB_NAME() as dbName, GETDATE() as currentTime');
    console.log(`✅ Database: ${dbTest.recordset[0].dbName}`);

    // Test 2: Check individual table counts
    console.log('2. Checking table counts...');
    const modules = await db.query('SELECT COUNT(*) as count FROM Modules WHERE IsActive = 1');
    const subModules = await db.query('SELECT COUNT(*) as count FROM SubModules WHERE IsActive = 1');
    const permissions = await db.query('SELECT COUNT(*) as count FROM Permissions WHERE IsActive = 1');
    const subModulePermissions = await db.query('SELECT COUNT(*) as count FROM SubModulePermissions WHERE IsActive = 1');
    const roles = await db.query('SELECT COUNT(*) as count FROM Roles WHERE IsActive = 1');

    console.log(`✅ Active Modules: ${modules.recordset[0].count}`);
    console.log(`✅ Active SubModules: ${subModules.recordset[0].count}`);
    console.log(`✅ Active Permissions: ${permissions.recordset[0].count}`);
    console.log(`✅ Active SubModulePermissions: ${subModulePermissions.recordset[0].count}`);
    console.log(`✅ Active Roles: ${roles.recordset[0].count}`);

    // Test 3: Test the exact query from controller
    console.log('3. Testing controller query...');
    const controllerQuery = `
      SELECT 
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.Description as ModuleDescription,
        m.Icon as ModuleIcon,
        m.DisplayOrder as ModuleDisplayOrder,
        m.IsActive as ModuleIsActive,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        sm.Icon as SubModuleIcon,
        sm.IsActive as SubModuleIsActive,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        
        smp.Id as SubModulePermissionId
        
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId AND sm.IsActive = 1
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
      LEFT JOIN Permissions p ON smp.PermissionId = p.Id AND p.IsActive = 1
      WHERE m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.Id, p.Name
    `;

    const queryResult = await db.query(controllerQuery);
    console.log(`✅ Controller query returned: ${queryResult.recordset.length} rows`);

    // Test 4: Transform the data like the controller does
    console.log('4. Testing data transformation...');
    const modulesMap = new Map();

    queryResult.recordset.forEach(row => {
      // Create module if not exists
      if (!modulesMap.has(row.ModuleId)) {
        modulesMap.set(row.ModuleId, {
          id: row.ModuleId,
          name: row.ModuleName,
          description: row.ModuleDescription,
          icon: row.ModuleIcon,
          displayOrder: row.ModuleDisplayOrder,
          isActive: row.ModuleIsActive,
          subModules: new Map()
        });
      }

      const module = modulesMap.get(row.ModuleId);

      // Create submodule if not exists and if SubModuleId is not null
      if (row.SubModuleId && !module.subModules.has(row.SubModuleId)) {
        module.subModules.set(row.SubModuleId, {
          id: row.SubModuleId,
          name: row.SubModuleName,
          description: row.SubModuleName,
          route: row.SubModuleRoute,
          icon: row.SubModuleIcon,
          displayOrder: row.SubModuleId,
          isActive: row.SubModuleIsActive,
          permissions: []
        });
      }

      // Add permission if exists
      if (row.PermissionId && row.SubModuleId) {
        const subModule = module.subModules.get(row.SubModuleId);
        if (subModule && !subModule.permissions.find(p => p.id === row.PermissionId)) {
          subModule.permissions.push({
            id: row.PermissionId,
            name: row.PermissionName,
            description: row.PermissionDescription,
            subModulePermissionId: row.SubModulePermissionId
          });
        }
      }
    });

    // Convert Maps to Arrays for JSON response
    const modules_final = Array.from(modulesMap.values()).map(module => ({
      ...module,
      subModules: Array.from(module.subModules.values())
    }));

    console.log(`✅ Transformed into ${modules_final.length} modules`);

    // Test 5: Test roles query
    console.log('5. Testing roles query...');
    const rolesQuery = `
      SELECT 
        Id,
        Name,
        IsActive,
        CreatedOn
      FROM Roles
      WHERE IsActive = 1
      ORDER BY Name
    `;
    const rolesResult = await db.query(rolesQuery);
    console.log(`✅ Roles query returned: ${rolesResult.recordset.length} roles`);

    res.json({
      success: true,
      debug: {
        database: dbTest.recordset[0].dbName,
        counts: {
          modules: modules.recordset[0].count,
          subModules: subModules.recordset[0].count,
          permissions: permissions.recordset[0].count,
          subModulePermissions: subModulePermissions.recordset[0].count,
          roles: roles.recordset[0].count
        },
        queryResults: {
          rawRows: queryResult.recordset.length,
          transformedModules: modules_final.length,
          roles: rolesResult.recordset.length
        },
        sampleData: {
          modules: modules_final.slice(0, 2),
          roles: rolesResult.recordset.slice(0, 3)
        }
      }
    });

  } catch (error) {
    console.error('❌ Debug error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
});

const PORT = 5001; // Use different port to avoid conflict
app.listen(PORT, () => {
  console.log(`🔍 Debug server running on port ${PORT}`);
  console.log(`Test URL: http://localhost:${PORT}/debug-permissions`);
});