import api from '../services/api'; // Shared Axios instance

export const plazaApi = {
  /**
   * Fetches the list of all plazas.
   * GET /plazas
   */
  getAllPlazas: async () => {
    try {
      console.log('Fetching all plazas');
      const response = await api.get('/plazas'); // Matches router.get('/')
      console.log('Full API response from getAllPlazas:', response);
      console.log('API response data from getAllPlazas:', response.data);

      // Handle different response structures
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        console.log('Returning standard response format with data array');
        return response.data;
      } else if (response.data && Array.isArray(response.data)) {
        console.log('Returning direct array wrapped in data property');
        return { data: response.data };
      } else if (response.data && response.data.plazas && Array.isArray(response.data.plazas)) {
        console.log('Returning response with plazas array');
        return { data: response.data.plazas };
      } else if (response.data && typeof response.data === 'object') {
        // Try to find any array in the response
        const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));
        if (possibleArrays.length > 0) {
          console.log('Returning first array found in response');
          return { data: possibleArrays[0] };
        }
      }

      // Default fallback
      console.log('No valid data format found, returning empty array');
      return { data: [] };
    } catch (error) {
      console.error('Error fetching plazas:', error);
      return { data: [] }; // Return empty data instead of throwing
    }
  },

  /**
   * Fetches the details of a single plaza by its ID.
   * GET /plazas/:id
   */
  getPlazaById: async (id) => {
    const response = await api.get(`/plazas/${id}`); // Matches router.get('/:id')
    return response.data;
  },

  /**
   * Creates a new plaza with FormData support for file uploads.
   * POST /plazas
   */
  createPlaza: async (formData) => {
    try {
      console.log('Creating new plaza');

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      };

      const response = await api.post('/plazas', formData, config);
      return response.data;
    } catch (error) {
      console.error('Error in createPlaza:', error);
      throw error;
    }
  },

  /**
   * Updates an existing plaza.
   * PUT /plazas/:id
   */
  updatePlaza: async (id, formData) => {
    try {
      console.log('Updating plaza with ID:', id);

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      };

      const response = await api.put(`/plazas/${id}`, formData, config);
      return response.data;
    } catch (error) {
      console.error('Error in updatePlaza:', error);
      throw error;
    }
  },

  /**
   * Deletes a plaza by ID.
   * DELETE /plazas/:id
   */
  deletePlaza: async (id) => {
    try {
      console.log('Deleting plaza with ID:', id);
      const response = await api.delete(`/plazas/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error in deletePlaza:', error);
      throw error;
    }
  },

  /**
   * Fetches statistics for a specific plaza.
   * This is a placeholder - implement based on your actual backend endpoint
   */
  getPlazaStats: async (plazaId) => {
    try {
      const response = await api.get(`/plazas/${plazaId}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching plaza stats:', error);
      // Return placeholder stats if endpoint doesn't exist yet
      return {
        totalTransactions: 0,
        activeUsers: 0,
        dailyRevenue: 0,
        monthlyRevenue: 0
      };
    }
  },

  /**
   * Toggles the active status of a plaza.
   * PATCH /plazas/toggle-status/:id
   */
  toggleActiveStatus: async (id) => {
    try {
      console.log('Toggling plaza status for ID:', id);
      const response = await api.patch(`/plazas/toggle-status/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error toggling plaza status:', error);
      throw error;
    }
  },

  /**
   * Fetches the logo of a plaza by ID.
   * GET /plazas/logo/:id
   */
  getPlazaLogo: async (id) => {
    const response = await api.get(`/plazas/logo/${id}`, {
      responseType: 'blob',
    });
    return URL.createObjectURL(response.data);
  },

  /**
   * Fetches plazas by company ID.
   * GET /companies/:companyId/plazas
   */
  getPlazasByCompany: async (companyId) => {
    try {
      // If companyId is undefined or invalid, return empty array
      if (!companyId || companyId === 'undefined' || companyId === 'null') {
        console.warn('Invalid company ID provided to getPlazasByCompany:', companyId);
        return [];
      }

      console.log(`Fetching plazas for company ID: ${companyId}`);
      const response = await api.get(`/companies/${companyId}/plazas`);
      console.log('Full API response from getPlazasByCompany:', response);
      console.log('API response data from getPlazasByCompany:', response.data);

      // Handle different response formats
      if (response.data) {
        if (response.data.success && Array.isArray(response.data.data)) {
          // Standard API response format
          console.log('Returning plazas from response.data.data');
          return response.data.data;
        } else if (Array.isArray(response.data)) {
          // Direct array response
          console.log('Returning plazas directly from response.data array');
          return response.data;
        } else if (response.data.plazas && Array.isArray(response.data.plazas)) {
          // Response with plazas property
          console.log('Returning plazas from response.data.plazas');
          return response.data.plazas;
        } else if (typeof response.data === 'object') {
          // Try to find any array in the response
          const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));
          if (possibleArrays.length > 0) {
            console.log('Returning plazas from first array found in response');
            return possibleArrays[0];
          }
        }
      }
      
      // If we couldn't find a valid array, return the full response data
      // and let the component handle it
      console.log('Returning full response data for component to handle');
      return response.data || [];
    } catch (error) {
      console.error(`Error fetching plazas for company ${companyId}:`, error);
      return [];
    }
  }
};