// frontend/src/components/filters/ModuleFilters.js
import React, { useMemo } from 'react';
import { useAuth } from '../../contexts/authContext';
import { getAvailableCompanies, getAvailablePlazas, getAvailableLanes } from '../../utils/permissionFilters';
import { Filter } from 'lucide-react';

/**
 * Reusable filter component for filtering by company, plaza, and lane
 *
 * @param {Object} props - Component props
 * @param {Array} props.companies - Array of all companies
 * @param {Array} props.plazas - Array of all plazas
 * @param {Array} props.lanes - Array of all lanes
 * @param {Object} props.filters - Current filter values
 * @param {Function} props.onFilterChange - Function to call when filters change
 * @param {boolean} props.showCompanyFilter - Whether to show company filter
 * @param {boolean} props.showPlazaFilter - Whether to show plaza filter
 * @param {boolean} props.showLaneFilter - Whether to show lane filter
 * @param {boolean} props.loading - Whether data is loading
 * @returns {JSX.Element} - Filter component
 */
const ModuleFilters = ({
  companies = [],
  plazas = [],
  lanes = [],
  filters = {},
  onFilterChange,
  showCompanyFilter = true,
  showPlazaFilter = true,
  showLaneFilter = false,
  loading = false
}) => {
  const { user } = useAuth();

  // Use useMemo instead of useEffect to avoid infinite loops
  // Filter companies based on user role
  const availableCompanies = useMemo(() => {
    if (companies && companies.length > 0 && user) {
      return getAvailableCompanies(companies, user);
    }
    return [];
  }, [companies, user]);

  // Filter plazas based on selected company and user role
  const availablePlazas = useMemo(() => {
    if (plazas && plazas.length > 0 && user) {
      return getAvailablePlazas(plazas, user, filters.companyId, true); // Include inactive plazas
    }
    return [];
  }, [plazas, user, filters.companyId]);

  // Filter lanes based on selected plaza
  const availableLanes = useMemo(() => {
    if (lanes && lanes.length > 0 && filters.plazaId) {
      return getAvailableLanes(lanes, user, filters.plazaId, filters.companyId);
    }
    return [];
  }, [lanes, user, filters.plazaId, filters.companyId]);

  // Handle filter changes
  const handleFilterChange = (filterName, value) => {
    const newFilters = { ...filters };

    // If changing company, reset plaza and lane filters
    if (filterName === 'companyId' && value !== filters.companyId) {
      newFilters.plazaId = '';
      newFilters.laneId = '';
    }

    // If changing plaza, reset lane filter
    if (filterName === 'plazaId' && value !== filters.plazaId) {
      newFilters.laneId = '';
    }

    // Set the new filter value
    newFilters[filterName] = value;

    // Call the parent component's filter change handler
    onFilterChange(newFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    onFilterChange({});
  };

  return (
    <div className="flex flex-wrap gap-3 items-center">
      {/* Company Filter */}
      {showCompanyFilter && (
        <div>
          <select
            value={filters.companyId || ''}
            onChange={(e) => handleFilterChange('companyId', e.target.value)}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading || availableCompanies.length === 0}
          >
            <option value="">All Companies</option>
            {availableCompanies.map((company) => (
              <option key={company.Id} value={company.Id}>
                {company.CompanyName}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Plaza Filter */}
      {showPlazaFilter && (
        <div>
          <select
            value={filters.plazaId || ''}
            onChange={(e) => handleFilterChange('plazaId', e.target.value)}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading || availablePlazas.length === 0}
          >
            <option value="">All Plazas</option>
            {availablePlazas.map((plaza) => (
              <option key={plaza.Id} value={plaza.Id}>
                {plaza.PlazaName}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Lane Filter */}
      {showLaneFilter && (
        <div>
          <select
            value={filters.laneId || ''}
            onChange={(e) => handleFilterChange('laneId', e.target.value)}
            className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={loading || availableLanes.length === 0 || !filters.plazaId}
          >
            <option value="">All Lanes</option>
            {availableLanes.map((lane) => (
              <option key={lane.LaneID} value={lane.LaneID}>
                {lane.LaneNumber} - {lane.LaneDirection || lane.LaneType}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Clear Filters Button */}
      {Object.keys(filters).length > 0 && (
        <button
          onClick={clearFilters}
          className="p-2 rounded-md bg-blue-100 text-blue-700 hover:bg-blue-200"
          aria-label="Clear filters"
        >
          <Filter className="w-5 h-5" />
        </button>
      )}
    </div>
  );
};

export default ModuleFilters;
