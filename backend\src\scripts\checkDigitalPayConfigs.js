// Script to check Digital Pay configurations in the database
const db = require('../config/database');

async function checkDigitalPayConfigs() {
  try {
    // Connect to database
    await db.connect();
    console.log('Database connection successful');

    // Check all Digital Pay configurations
    const allConfigsQuery = `
      SELECT
        c.ConfigLaneID,
        c.PlazaID,
        c.LaneID,
        c.ActiveStatus,
        p.PlazaName,
        l.LaneNumber
      FROM tblLaneDigitalPayConfiguration c
      LEFT JOIN Plaza p ON c.PlazaID = p.Id
      LEFT JOIN tblLaneDetails l ON c.LaneID = l.LaneID
      ORDER BY c.ConfigLaneID DESC
    `;
    
    const allConfigsResult = await db.query(allConfigsQuery);
    console.log(`\nFound ${allConfigsResult.recordset.length} total Digital Pay configurations:`);
    
    // Count active and inactive configurations
    let activeCount = 0;
    let inactiveCount = 0;
    
    allConfigsResult.recordset.forEach(config => {
      const status = config.ActiveStatus ? config.ActiveStatus.trim() : 'null';
      console.log(`- Config ID: ${config.ConfigLaneID}, Plaza: ${config.PlazaName}, Lane: ${config.LaneNumber}, Status: ${status}`);
      
      if (status === 'Y' || status === '1') {
        activeCount++;
      } else {
        inactiveCount++;
      }
    });
    
    console.log(`\nSummary: ${activeCount} active, ${inactiveCount} inactive configurations`);
    
    // Insert a test inactive configuration if none exist
    if (inactiveCount === 0) {
      console.log('\nNo inactive configurations found. Creating a test inactive configuration...');
      
      // Get a valid plaza and lane
      const validPlazaLaneQuery = `
        SELECT TOP 1 p.Id as PlazaID, l.LaneID, p.CompanyId as CompanyID
        FROM Plaza p
        JOIN tblLaneDetails l ON p.Id = l.PlazaID
        WHERE p.IsActive = 1
      `;
      
      const validPlazaLaneResult = await db.query(validPlazaLaneQuery);
      
      if (validPlazaLaneResult.recordset.length > 0) {
        const { PlazaID, LaneID, CompanyID } = validPlazaLaneResult.recordset[0];
        
        // Check if a configuration already exists for this plaza and lane
        const existingConfigQuery = `
          SELECT ConfigLaneID
          FROM tblLaneDigitalPayConfiguration
          WHERE PlazaID = @plazaId AND LaneID = @laneId
        `;
        
        const existingConfigResult = await db.query(existingConfigQuery, {
          plazaId: PlazaID,
          laneId: LaneID
        });
        
        if (existingConfigResult.recordset.length > 0) {
          // Update the existing configuration to inactive
          const updateQuery = `
            UPDATE tblLaneDigitalPayConfiguration
            SET ActiveStatus = 'N',
                UpdatedBy = 'admin',
                UpdatedDateTime = GETDATE()
            WHERE ConfigLaneID = @configId
          `;
          
          await db.query(updateQuery, {
            configId: existingConfigResult.recordset[0].ConfigLaneID
          });
          
          console.log(`Updated existing configuration (ID: ${existingConfigResult.recordset[0].ConfigLaneID}) to inactive status`);
        } else {
          // Insert a new inactive configuration
          const insertQuery = `
            INSERT INTO tblLaneDigitalPayConfiguration (
              PlazaID,
              CompanyID,
              LaneID,
              EnableCardPayment,
              EnableUPIPhonePe,
              EnableSendSMS,
              ActiveStatus,
              UpdatedBy,
              UpdatedDateTime,
              UPIPGProvider,
              CardPaymentDeviceModel
            ) VALUES (
              @plazaId,
              @companyId,
              @laneId,
              '1',
              '1',
              '1',
              'N',
              'admin',
              GETDATE(),
              'PhonePe',
              'Ingenico'
            )
          `;
          
          await db.query(insertQuery, {
            plazaId: PlazaID,
            companyId: CompanyID,
            laneId: LaneID
          });
          
          console.log(`Created new inactive configuration for Plaza ID: ${PlazaID}, Lane ID: ${LaneID}`);
        }
      } else {
        console.log('No valid plaza and lane found to create a test configuration');
      }
    }

    console.log('\nDigital Pay configuration check complete');
  } catch (error) {
    console.error('Database connection or query error:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('\nDatabase connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the function
checkDigitalPayConfigs();
