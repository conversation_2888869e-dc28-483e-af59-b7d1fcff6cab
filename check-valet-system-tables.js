const sql = require('mssql');
require('dotenv').config({path: './backend/.env'});

async function checkValetSystemTables() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: 'pwvms',
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    console.log('=== VALET SYSTEM TABLES ANALYSIS ===\n');

    // Key valet-related tables to analyze
    const valetTables = [
      'Customer',
      'CustomerVehicle', 
      'OTP',
      'ParkingTransactions',
      'ParkingTransactionCashPayments',
      'ParkingTransactionVehicleImages',
      'ParkingBay',
      'ParkingZone',
      'PlazaValetPoint',
      'VehicleEntries',
      'AllowDriverLogin',
      'PhonePePaymentTransactions',
      'RazorPayTransactions'
    ];

    for (const tableName of valetTables) {
      console.log(`\n=== ${tableName} Table Structure ===`);
      
      try {
        const structureQuery = `
          SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            CHARACTER_MAXIMUM_LENGTH
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = '${tableName}'
          ORDER BY ORDINAL_POSITION
        `;
        
        const structure = await sql.query(structureQuery);
        if (structure.recordset.length > 0) {
          console.table(structure.recordset);
          
          // Get sample data count
          const countQuery = `SELECT COUNT(*) as RecordCount FROM [${tableName}]`;
          const countResult = await sql.query(countQuery);
          console.log(`📊 Total Records: ${countResult.recordset[0].RecordCount}`);
        } else {
          console.log(`❌ Table ${tableName} not found`);
        }
      } catch (error) {
        console.log(`❌ Error checking ${tableName}:`, error.message);
      }
    }

    await sql.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

checkValetSystemTables();
