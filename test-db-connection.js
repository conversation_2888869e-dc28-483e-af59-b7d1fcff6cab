const sql = require('mssql');
require('dotenv').config();

async function testDatabaseConnection() {
  console.log('🔍 Testing database connection directly...\n');

  const config = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_SERVER,
    database: process.env.DB_NAME,
    port: parseInt(process.env.DB_PORT || '1433'),
    options: {
      encrypt: process.env.DB_ENCRYPT === 'true',
      trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
      enableArithAbort: true,
      connectionTimeout: 30000,    // 30 seconds
      requestTimeout: 30000,       // 30 seconds
    },
    pool: {
      max: 5,
      min: 1,
      idleTimeoutMillis: 30000
    }
  };

  let pool = null;

  try {
    console.log('1. Connecting to database...');
    pool = await sql.connect(config);
    console.log('✅ Connection established');

    console.log('2. Testing simple query...');
    const simpleResult = await pool.request().query('SELECT 1 as test');
    console.log(`✅ Simple query result: ${simpleResult.recordset[0].test}`);

    console.log('3. Testing modules query...');
    const modulesResult = await pool.request().query('SELECT COUNT(*) as count FROM Modules');
    console.log(`✅ Modules count: ${modulesResult.recordset[0].count}`);

    console.log('4. Testing complex query (with timeout)...');
    const request = pool.request();
    request.timeout = 10000; // 10 seconds timeout
    
    const complexQuery = `
      SELECT TOP 5
        m.Id as ModuleId,
        m.Name as ModuleName,
        sm.Id as SubModuleId,
        sm.Name as SubModuleName
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId
      WHERE m.IsActive = 1
    `;

    const complexResult = await request.query(complexQuery);
    console.log(`✅ Complex query returned ${complexResult.recordset.length} rows`);

    if (complexResult.recordset.length > 0) {
      console.log('First row:', complexResult.recordset[0]);
    }

    console.log('\n🎉 All database tests passed!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    console.error('Error code:', error.code);
    console.error('Error number:', error.number);
  } finally {
    if (pool) {
      try {
        await pool.close();
        console.log('✅ Connection closed');
      } catch (closeError) {
        console.error('❌ Error closing connection:', closeError.message);
      }
    }
    process.exit(0);
  }
}

testDatabaseConnection();