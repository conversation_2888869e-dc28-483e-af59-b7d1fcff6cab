// Ultimate final script to create mock data with all schema issues fixed
const sql = require('mssql');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from backend/.env
dotenv.config({ path: path.join(__dirname, 'backend', '.env') });

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    enableArithAbort: true,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT || '30000'),
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT || '30000')
  }
};

// Execute a SQL query with proper error handling
async function executeQuery(pool, description, query) {
  try {
    console.log(`Executing: ${description}...`);
    await pool.request().query(query);
    console.log(`Success: ${description}`);
    return true;
  } catch (err) {
    console.error(`Error in "${description}":`, err.message);
    console.error('Query:', query);
    return false;
  }
}

async function createMockData() {
  let pool;
  try {
    console.log('Connecting to database...');
    console.log('Database config:', {
      server: dbConfig.server,
      database: dbConfig.database,
      user: dbConfig.user,
      port: dbConfig.port
    });
    
    pool = await sql.connect(dbConfig);
    console.log('Connected to database successfully');

    // Step 1: Get companies
    console.log('Step 1: Fetching company IDs...');
    const companiesResult = await pool.request().query(`
      SELECT TOP 2 Id, CompanyName FROM tblCompanyMaster ORDER BY Id
    `);
    
    if (companiesResult.recordset.length < 2) {
      console.log('Not enough companies found. Creating mock companies...');
      await executeQuery(pool, "Create mock companies", `
        INSERT INTO tblCompanyMaster (CompanyName, CompanyCode, IsActive)
        VALUES ('Mock Company 1', 'MC001', 1), ('Mock Company 2', 'MC002', 1)
      `);
      
      // Fetch again
      const newCompaniesResult = await pool.request().query(`
        SELECT TOP 2 Id, CompanyName FROM tblCompanyMaster ORDER BY Id
      `);
      companiesResult.recordset = newCompaniesResult.recordset;
    }
    
    const companyID1 = companiesResult.recordset[0].Id;
    const companyID2 = companiesResult.recordset[1].Id;
    
    console.log(`Using companies: ${companyID1}, ${companyID2}`);
    
    // Step 2: Get plazas
    console.log('Step 2: Fetching plaza IDs...');
    const plazasResult = await pool.request().query(`
      SELECT TOP 3 Id, PlazaCode, PlazaName, CompanyId FROM Plaza ORDER BY Id
    `);
    
    let plazaID1, plazaID2, plazaID3;
    let plazaCode1, plazaCode2, plazaCode3;
    let plazaName1, plazaName2, plazaName3;
    
    if (plazasResult.recordset.length < 3) {
      console.log('Not enough plazas found. Creating mock plazas...');
      await executeQuery(pool, "Create mock plazas", `
        INSERT INTO Plaza (PlazaName, PlazaCode, CompanyId, IsActive)
        VALUES 
        ('Mock Plaza 1', 'PLZ001', ${companyID1}, 1),
        ('Mock Plaza 2', 'PLZ002', ${companyID1}, 1),
        ('Mock Plaza 3', 'PLZ003', ${companyID2}, 1)
      `);
      
      // Fetch again
      const newPlazasResult = await pool.request().query(`
        SELECT TOP 3 Id, PlazaCode, PlazaName, CompanyId FROM Plaza ORDER BY Id
      `);
      plazasResult.recordset = newPlazasResult.recordset;
    }
    
    plazaID1 = plazasResult.recordset[0].Id;
    plazaCode1 = plazasResult.recordset[0].PlazaCode;
    plazaName1 = plazasResult.recordset[0].PlazaName;
    
    plazaID2 = plazasResult.recordset[1].Id;
    plazaCode2 = plazasResult.recordset[1].PlazaCode;
    plazaName2 = plazasResult.recordset[1].PlazaName;
    
    plazaID3 = plazasResult.recordset[2].Id;
    plazaCode3 = plazasResult.recordset[2].PlazaCode;
    plazaName3 = plazasResult.recordset[2].PlazaName;
    
    console.log(`Using plazas: ${plazaID1}, ${plazaID2}, ${plazaID3}`);
    
    // Step 3: Clear existing data
    console.log('Step 3: Clearing existing data...');
    await executeQuery(pool, "Delete pass registrations", `DELETE FROM tbl_Parkwiz_Pass_Reg WHERE 1=1`);
    await executeQuery(pool, "Delete UHF reader details", `DELETE FROM tblLaneUHFReaderDetails WHERE 1=1`);
    await executeQuery(pool, "Delete digital pay configurations", `DELETE FROM tblLaneDigitalPayConfiguration WHERE 1=1`);
    await executeQuery(pool, "Delete fastag configurations", `DELETE FROM tblLaneFastagConfiguration WHERE 1=1`);
    await executeQuery(pool, "Delete ANPR configurations", `DELETE FROM tblLaneANPRConfiguration WHERE 1=1`);
    await executeQuery(pool, "Delete lane details", `DELETE FROM tblLaneDetails WHERE 1=1`);
    
    // Step 4: Create lanes
    console.log('Step 4: Creating lanes...');
    
    // Plaza 1 Entry Lane 1
    await executeQuery(pool, "Create Plaza 1 Entry Lane 1", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID1}, ${companyID1}, 'E1', 'Entry', 'Main Entry Lane 1', 'E', 
        'Welcome to Plaza 1', '*************', 'Car', '1', '1'
      )
    `);
    
    // Plaza 1 Entry Lane 2
    await executeQuery(pool, "Create Plaza 1 Entry Lane 2", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID1}, ${companyID1}, 'E2', 'Entry', 'Main Entry Lane 2', 'E', 
        'Welcome to Plaza 1', '*************', 'Car', '1', '1'
      )
    `);
    
    // Plaza 1 Exit Lane 1
    await executeQuery(pool, "Create Plaza 1 Exit Lane 1", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID1}, ${companyID1}, 'X1', 'Exit', 'Main Exit Lane 1', 'X', 
        'Thank you for visiting', '*************', 'Car', '1', '1'
      )
    `);
    
    // Plaza 2 Entry Lane 1
    await executeQuery(pool, "Create Plaza 2 Entry Lane 1", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID2}, ${companyID1}, 'E1', 'Entry', 'Main Entry Lane 1', 'E', 
        'Welcome to Plaza 2', '*************', 'Car', '1', '1'
      )
    `);
    
    // Plaza 2 Exit Lane 1
    await executeQuery(pool, "Create Plaza 2 Exit Lane 1", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID2}, ${companyID1}, 'X1', 'Exit', 'Main Exit Lane 1', 'X', 
        'Thank you for visiting', '*************', 'Car', '1', '1'
      )
    `);
    
    // Plaza 3 Entry Lane 1
    await executeQuery(pool, "Create Plaza 3 Entry Lane 1", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID3}, ${companyID2}, 'E1', 'Entry', 'Main Entry Lane 1', 'E', 
        'Welcome to Plaza 3', '*************', 'Car', '1', '1'
      )
    `);
    
    // Plaza 3 Exit Lane 1
    await executeQuery(pool, "Create Plaza 3 Exit Lane 1", `
      INSERT INTO tblLaneDetails (
        PlazaID, CompanyID, LaneNumber, LaneType, LaneDetails, TypeCode, 
        DataForPrint, LaneIP, VehicleType, UpdatedBy, ActiveStatus
      )
      VALUES (
        ${plazaID3}, ${companyID2}, 'X1', 'Exit', 'Main Exit Lane 1', 'X', 
        'Thank you for visiting', '*************', 'Car', '1', '1'
      )
    `);
    
    // Step 5: Get the inserted lane IDs
    console.log('Step 5: Fetching lane IDs...');
    const lanesResult = await pool.request().query(`
      SELECT LaneID, PlazaID, LaneNumber FROM tblLaneDetails
    `);
    
    const lanes = {};
    lanesResult.recordset.forEach(lane => {
      const key = `${lane.PlazaID}_${lane.LaneNumber.trim()}`; // Trim to handle char padding
      lanes[key] = lane.LaneID;
    });
    
    console.log('Lane IDs:', lanes);
    
    // Step 6: Create ANPR configurations
    console.log('Step 6: Creating ANPR configurations...');
    for (const [key, laneID] of Object.entries(lanes)) {
      const [plazaID, laneNumber] = key.split('_');
      await executeQuery(pool, `Create ANPR config for lane ${laneNumber} in plaza ${plazaID}`, `
        INSERT INTO tblLaneANPRConfiguration (
          PlazaID, CompanyID, LaneID, PMSLaneNumber, flgEnableANPR, ANPROrgID,
          ANPRLaneID, ANPRPublicKey, ANPRPrivateKey, ANPRSource, ANPRAPIURL,
          ANPRAPIURL2, ActiveStatus, UpdateDateTime, UpdatedBy,
          AllowBlacklistedVehicle, ANPRVendor
        )
        VALUES (
          ${plazaID}, 
          ${plazaID == plazaID3 ? companyID2 : companyID1}, 
          ${laneID}, 
          '${laneNumber}', 
          '1', 
          'ORG${plazaID}',
          'ANPR${laneID}', 
          'pub_key', 
          'priv_key', 
          'Camera', 
          'https://api.anpr.com/v1',
          'https://api.anpr.com/v2', 
          '1', 
          GETDATE(), 
          '1',
          '0', 
          'ANPR'
        )
      `);
    }
    
    // Step 7: Create Fastag configurations
    console.log('Step 7: Creating Fastag configurations...');
    for (const [key, laneID] of Object.entries(lanes)) {
      const [plazaID, laneNumber] = key.split('_');
      const plazaName = plazaID == plazaID1 ? plazaName1 : (plazaID == plazaID2 ? plazaName2 : plazaName3);
      const isExit = laneNumber.startsWith('X');
      
      await executeQuery(pool, `Create Fastag config for lane ${laneNumber} in plaza ${plazaID}`, `
        INSERT INTO tblLaneFastagConfiguration (
          PlazaID, CompanyID, LaneID, LaneNumber, PlazaGeoCode,
          PlazaName, PlazaSubType, PlazaType, LaneDirection, LaneReaderID, LaneStatus,
          LaneMode, LaneType, LaneFloor, UpdatedDateTime, UpdatedBy,
          FastagOrgID, FastagAgencyCode, FastagAPIAddress, LaneGate, flgTerminal1Exit
        )
        VALUES (
          ${plazaID}, 
          ${plazaID == plazaID3 ? companyID2 : companyID1}, 
          ${laneID}, 
          '${laneNumber}', 
          'GEO${plazaID}',
          '${plazaName}', 
          'Highway', 
          'Toll', 
          '${isExit ? 'X' : 'E'}', 
          'RDR${laneID}', 
          'Active',
          'Auto', 
          'Car', 
          'Ground', 
          GETDATE(), 
          '1',
          'FTAG${plazaID}', 
          'AGC${plazaID}', 
          'https://api.fastag.com/v1', 
          'G${laneID}', 
          ${isExit ? 1 : 0}
        )
      `);
    }
    
    // Step 8: Create Digital Pay configurations
    console.log('Step 8: Creating Digital Pay configurations...');
    for (const [key, laneID] of Object.entries(lanes)) {
      const [plazaID, laneNumber] = key.split('_');
      
      await executeQuery(pool, `Create Digital Pay config for lane ${laneNumber} in plaza ${plazaID}`, `
        INSERT INTO tblLaneDigitalPayConfiguration (
          PlazaID, CompanyID, LaneID, LaneNumber, EnableCardPayment,
          EnableUPIPhonePe, EnableSendSMS, MerchantID,
          PhonePeMID, PhonePeKeyID, PhonePeIndexID,
          ActiveStatus, UpdatedDateTime, UpdatedBy, AllowBlacklistedVehicle
        )
        VALUES (
          ${plazaID}, 
          ${plazaID == plazaID3 ? companyID2 : companyID1}, 
          ${laneID}, 
          '${laneNumber}', 
          'Y',
          'Y', 
          'Y', 
          'MERCH${laneID}',
          'PPMERCH${laneID}', 
          'PPKEY${laneID}', 
          '1',
          'Y', 
          GETDATE(), 
          '1', 
          'N'
        )
      `);
    }
    
    // Step 9: Create UHF Reader configurations
    console.log('Step 9: Creating UHF Reader configurations...');
    for (const [key, laneID] of Object.entries(lanes)) {
      const [plazaID, laneNumber] = key.split('_');
      
      await executeQuery(pool, `Create UHF Reader config for lane ${laneNumber} in plaza ${plazaID}`, `
        INSERT INTO tblLaneUHFReaderDetails (
          PlazaID, CompanyID, LaneID, ReaderLaneNumber, EnableUHFReader,
          ReaderIPAddress, ReaderPort, IOBoardIPAddress, IOBoardPort,
          ActiveStatus, flgFastag, UpdatedDateTime, UpdatedBy
        )
        VALUES (
          ${plazaID}, 
          ${plazaID == plazaID3 ? companyID2 : companyID1}, 
          ${laneID}, 
          '${laneNumber}', 
          'True',
          '192.168.1.${laneID}', 
          '8001', 
          '192.168.1.${parseInt(laneID) + 100}', 
          '8002',
          '1', 
          'True', 
          GETDATE(), 
          '1'
        )
      `);
    }
    
    // Step 10: Create Pass Registrations
    console.log('Step 10: Creating Pass Registrations...');
    
    // Plaza 1 Passes
    await executeQuery(pool, "Create Plaza 1 Passes", `
      INSERT INTO tbl_Parkwiz_Pass_Reg (
        PlazaCode, PlazaName, ApplicationID, GIN, IssueDate, HolderName, CompanyName,
        VehicleType, ContactNo, PassType, PassTariffType, EmailID,
        ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
        TagMasterTagID, flgANPRPassEnable, MarkDelete, PaymentMode, PaymentType
      )
      VALUES
      (
        '${plazaCode1}', '${plazaName1}', 1, 'GIN001', GETDATE(), 'John Smith', 'ABC Corp',
        'Car', '9876543210', 'Monthly', 'Regular', '<EMAIL>',
        DATEADD(MONTH, 1, GETDATE()), 'MH01AB1234', 'PASS001', 1000,
        'TAG001', 'True', 0, 'Online', 'UPI'
      ),
      (
        '${plazaCode1}', '${plazaName1}', 2, 'GIN002', GETDATE(), 'Jane Doe', 'XYZ Ltd',
        'Car', '9876543211', 'Quarterly', 'Premium', '<EMAIL>',
        DATEADD(MONTH, 3, GETDATE()), 'MH01CD5678', 'PASS002', 2500,
        'TAG002', 'True', 0, 'Online', 'Card'
      )
    `);
    
    // Plaza 2 Passes
    await executeQuery(pool, "Create Plaza 2 Passes", `
      INSERT INTO tbl_Parkwiz_Pass_Reg (
        PlazaCode, PlazaName, ApplicationID, GIN, IssueDate, HolderName, CompanyName,
        VehicleType, ContactNo, PassType, PassTariffType, EmailID,
        ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
        TagMasterTagID, flgANPRPassEnable, MarkDelete, PaymentMode, PaymentType
      )
      VALUES
      (
        '${plazaCode2}', '${plazaName2}', 3, 'GIN003', GETDATE(), 'Robert Johnson', 'PQR Inc',
        'SUV', '9876543212', 'Annual', 'Premium', '<EMAIL>',
        DATEADD(YEAR, 1, GETDATE()), 'MH02AB1234', 'PASS003', 8000,
        'TAG003', 'True', 0, 'Cash', 'Cash'
      ),
      (
        '${plazaCode2}', '${plazaName2}', 4, 'GIN004', GETDATE(), 'Sarah Williams', 'LMN Corp',
        'Car', '9876543213', 'Monthly', 'Regular', '<EMAIL>',
        DATEADD(MONTH, 1, GETDATE()), 'MH02CD5678', 'PASS004', 1000,
        'TAG004', 'True', 0, 'Online', 'UPI'
      )
    `);
    
    // Plaza 3 Passes
    await executeQuery(pool, "Create Plaza 3 Passes", `
      INSERT INTO tbl_Parkwiz_Pass_Reg (
        PlazaCode, PlazaName, ApplicationID, GIN, IssueDate, HolderName, CompanyName,
        VehicleType, ContactNo, PassType, PassTariffType, EmailID,
        ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
        TagMasterTagID, flgANPRPassEnable, MarkDelete, PaymentMode, PaymentType
      )
      VALUES
      (
        '${plazaCode3}', '${plazaName3}', 5, 'GIN005', GETDATE(), 'Michael Brown', 'DEF Ltd',
        'SUV', '9876543214', 'Quarterly', 'Premium', '<EMAIL>',
        DATEADD(MONTH, 3, GETDATE()), 'MH03AB1234', 'PASS005', 2500,
        'TAG005', 'True', 0, 'Online', 'Card'
      ),
      (
        '${plazaCode3}', '${plazaName3}', 6, 'GIN006', GETDATE(), 'Emily Davis', 'GHI Inc',
        'Car', '9876543215', 'Monthly', 'Regular', '<EMAIL>',
        DATEADD(MONTH, 1, GETDATE()), 'MH03CD5678', 'PASS006', 1000,
        'TAG006', 'True', 0, 'Online', 'UPI'
      )
    `);
    
    console.log('Mock data creation completed successfully!');
  } catch (err) {
    console.error('Error creating mock data:', err);
  } finally {
    if (pool) {
      await pool.close();
      console.log('Database connection closed');
    }
  }
}

createMockData();