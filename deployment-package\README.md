# PWVMS - Parking and Toll Management System

## Package Contents

This deployment package contains:

- \rontend/build/\ - Production-ready React frontend
- \ackend/\ - Node.js Express backend with dependencies
- \web.config\ - IIS configuration file
- \deploy-to-iis.ps1\ - Deployment script
- \install-iisnode.ps1\ - Prerequisites installation script
- \DEPLOYMENT-GUIDE.md\ - Step-by-step deployment instructions

## System Requirements

- Windows Server 2016/2019/2022 or Windows 10/11
- IIS 10.0 or later
- Node.js LTS version
- SQL Server database
- 4GB RAM minimum (8GB recommended)
- 10GB free disk space

## Application Architecture

- Frontend: React.js
- Backend: Node.js with Express
- Database: Microsoft SQL Server
- Authentication: JWT-based

## Configuration

The application requires configuration in the \.env\ file located in the \ackend\ directory.
See \DEPLOYMENT-GUIDE.md\ for detailed configuration instructions.

## Post-Deployment Steps

After deployment, you should:

1. Create an administrator account
2. Configure company settings
3. Set up plazas and lanes
4. Configure security settings

## Maintenance

Regular maintenance tasks:

1. Database backups
2. Log rotation
3. Security updates

## Support

For technical support, please contact:
- Email: <EMAIL>
- Phone: (*************
