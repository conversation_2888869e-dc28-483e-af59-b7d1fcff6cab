# PWVMS (Parking and Toll Management System) Information

## Summary
PWVMS is a comprehensive parking and toll management system with a React frontend and Node.js backend. The system manages toll plazas, lanes, digital payments, and provides dashboards for monitoring operations.

## Structure
- **frontend/**: React-based UI application
- **backend/**: Node.js Express API server
- **scripts/**: Utility scripts for data management and system checks
- **sql/**: Database migration and setup scripts
- **docs/**: System documentation
- **deployment-package/**: Deployment artifacts for production

## Projects

### Backend (Node.js API)
**Configuration File**: backend/package.json

#### Language & Runtime
**Language**: JavaScript (Node.js)
**Version**: Node.js (implied v16+)
**Package Manager**: npm

#### Dependencies
**Main Dependencies**:
- express: ^4.21.1 (API framework)
- mssql: ^11.0.1 (SQL Server client)
- ioredis: ^5.3.2 (Redis client)
- jsonwebtoken: ^9.0.2 (Authentication)
- bcrypt: ^5.1.1 (Password hashing)
- multer: ^1.4.5-lts.2 (File uploads)
- node-cron: ^3.0.3 (Scheduled tasks)

**Development Dependencies**:
- jest: ^29.7.0 (Testing)
- supertest: ^7.0.0 (API testing)

#### Build & Installation
```bash
cd backend
npm install
npm start
```

#### Testing
**Framework**: Jest
**Test Location**: Not explicitly defined, likely in /tests directory
**Run Command**:
```bash
npm test
```

### Frontend (React Application)
**Configuration File**: frontend/package.json

#### Language & Runtime
**Language**: JavaScript/React
**Version**: React 18.3.1
**Package Manager**: npm
**Build System**: react-scripts (Create React App)

#### Dependencies
**Main Dependencies**:
- react: ^18.3.1
- react-dom: ^18.3.1
- react-router-dom: ^6.27.0
- @mui/material: ^6.1.5
- @tanstack/react-query: ^5.60.5
- @tanstack/react-table: ^8.21.3
- axios: ^1.7.7
- d3: ^7.9.0

**Development Dependencies**:
- tailwindcss: ^3.4.14
- postcss: ^8.4.47
- autoprefixer: ^10.4.20

#### Build & Installation
```bash
cd frontend
npm install
npm start
```

#### Build for Production
```bash
cd frontend
npm run build
```

### Database
**Type**: Microsoft SQL Server
**Connection**: Environment variables (DB_SERVER, DB_USER, DB_PASSWORD, DB_NAME)
**Schema Management**: SQL scripts in /sql directory

### Caching Layer
**Type**: Redis
**Configuration**: Environment variables (REDIS_HOST, REDIS_PORT, REDIS_PASSWORD)
**Usage**: Session management, dashboard data caching, real-time data

### Deployment
**Method**: IIS (Windows Server)
**Scripts**: 
- deploy-to-iis.ps1 (PowerShell deployment script)
- install-iisnode.ps1 (IIS Node.js module installation)

### Key Features
- **Authentication**: JWT-based with role-based permissions
- **Company Management**: Multi-company support
- **Plaza Management**: Toll plaza configuration
- **Lane Management**: Lane configuration for toll collection
- **Payment Processing**: Digital payments and Fastag integration
- **ANPR**: Automatic Number Plate Recognition
- **UHF Reader**: Ultra High Frequency RFID reader integration
- **Dashboard**: Real-time monitoring and reporting
- **User Management**: Role-based access control