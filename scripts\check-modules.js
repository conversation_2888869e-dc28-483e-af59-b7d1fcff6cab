const db = require('./src/config/database');

async function checkModules() {
  try {
    console.log('=== CHECKING MODULES AND SUBMODULES ===');
    
    // Step 1: Check all modules
    console.log('\n1. ALL MODULES:');
    const modules = await db.query('SELECT * FROM Modules ORDER BY DisplayOrder');
    modules.recordset.forEach(module => {
      console.log(`${module.Id}: ${module.Name} (${module.Path}) - Order: ${module.DisplayOrder}`);
    });
    
    // Step 2: Check all submodules by module
    console.log('\n2. SUBMODULES BY MODULE:');
    for (const module of modules.recordset) {
      const subModules = await db.query(`
        SELECT * FROM SubModules 
        WHERE ModuleId = ${module.Id} 
        ORDER BY DisplayOrder
      `);
      
      console.log(`\n${module.Name}:`);
      subModules.recordset.forEach(subModule => {
        console.log(`  ${subModule.Id}: ${subModule.Name} (${subModule.Path})`);
      });
    }
    
    // Step 3: Check what permissions CompanyAdmin and PlazaManager actually have
    console.log('\n3. CURRENT PERMISSIONS BY ROLE:');
    
    const rolePermissions = await db.query(`
      SELECT 
        r.Name as RoleName,
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        p.Name as PermissionName
      FROM RolePermissions rp
      JOIN Roles r ON rp.RoleId = r.Id
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE rp.IsActive = 1 AND r.Name IN ('CompanyAdmin', 'PlazaManager')
      ORDER BY r.Name, m.DisplayOrder, sm.DisplayOrder, p.Id
    `);
    
    // Group by role and module
    const permissionsByRole = {};
    rolePermissions.recordset.forEach(perm => {
      if (!permissionsByRole[perm.RoleName]) {
        permissionsByRole[perm.RoleName] = {};
      }
      if (!permissionsByRole[perm.RoleName][perm.ModuleName]) {
        permissionsByRole[perm.RoleName][perm.ModuleName] = {};
      }
      if (!permissionsByRole[perm.RoleName][perm.ModuleName][perm.SubModuleName]) {
        permissionsByRole[perm.RoleName][perm.ModuleName][perm.SubModuleName] = [];
      }
      permissionsByRole[perm.RoleName][perm.ModuleName][perm.SubModuleName].push(perm.PermissionName);
    });
    
    Object.keys(permissionsByRole).forEach(roleName => {
      console.log(`\n${roleName}:`);
      Object.keys(permissionsByRole[roleName]).forEach(moduleName => {
        console.log(`  ${moduleName}:`);
        Object.keys(permissionsByRole[roleName][moduleName]).forEach(subModuleName => {
          const permissions = permissionsByRole[roleName][moduleName][subModuleName];
          console.log(`    ${subModuleName}: ${permissions.join(', ')}`);
        });
      });
    });
    
    // Step 4: Check what's missing for Plaza access
    console.log('\n4. CHECKING PLAZA-RELATED PERMISSIONS:');
    
    // Look for any plaza-related submodules
    const plazaRelated = await db.query(`
      SELECT m.Name as ModuleName, sm.Name as SubModuleName, sm.Path
      FROM SubModules sm
      JOIN Modules m ON sm.ModuleId = m.Id
      WHERE sm.Name LIKE '%Plaza%' OR sm.Path LIKE '%plaza%'
      ORDER BY m.DisplayOrder, sm.DisplayOrder
    `);
    
    console.log('Plaza-related submodules:', plazaRelated.recordset);
    
    // Step 5: Check if the issue is with frontend permission checking
    console.log('\n5. CHECKING FRONTEND PERMISSION STRUCTURE:');
    
    // Check what the frontend might be looking for
    const allSubModules = await db.query(`
      SELECT 
        m.Name as ModuleName,
        m.Path as ModulePath,
        sm.Name as SubModuleName,
        sm.Path as SubModulePath,
        sm.Component
      FROM SubModules sm
      JOIN Modules m ON sm.ModuleId = m.Id
      ORDER BY m.DisplayOrder, sm.DisplayOrder
    `);
    
    console.log('\nAll available routes:');
    allSubModules.recordset.forEach(route => {
      console.log(`${route.ModuleName} -> ${route.SubModuleName} (${route.SubModulePath})`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Module check failed:', error);
    process.exit(1);
  }
}

checkModules();
