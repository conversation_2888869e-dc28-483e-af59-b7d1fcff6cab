// frontend/src/components/auth/index.js
// Export default exports as named exports
export { default as LoginForm } from './LoginForm';
export { default as RegisterForm } from './RegisterForm';
export { default as ProtectedRoute } from './ProtectedRoute';
export { default as UnauthorizedPage } from './UnauthorizedPage';
export { default as PermissionGuard } from './PermissionGuard';
export { default as PermissionButton } from './PermissionButton';
export { default as PermissionLink } from './PermissionLink';
export { default as PermissionContent } from './PermissionContent';

// Re-export named exports
export { ProtectedRoute } from './ProtectedRoute';
export { UnauthorizedPage } from './UnauthorizedPage';
export { PermissionGuard } from './PermissionGuard';
export { PermissionButton } from './PermissionButton';
export { PermissionLink } from './PermissionLink';
export { PermissionContent } from './PermissionContent';
