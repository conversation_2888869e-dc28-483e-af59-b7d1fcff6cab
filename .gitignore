# Dependencies
node_modules/
/frontend/node_modules/
/backend/node_modules/
/.pnp
.pnp.js

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
/backend/.env
/frontend/.env

# Build directories
/build/
/frontend/build/
/dist/
/out/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
/coverage
/frontend/coverage

# Production
/frontend/build

# Misc
.DS_Store
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Uploads directory (contains user uploads)
/backend/Uploads/*
!/backend/Uploads/Companies/.gitkeep
!/backend/Uploads/Plazas/.gitkeep

# Database files
*.sqlite
*.db

# Cache
.npm
.eslintcache