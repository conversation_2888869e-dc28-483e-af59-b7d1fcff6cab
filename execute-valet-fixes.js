const sql = require('mssql');
require('dotenv').config({ path: './backend/.env' });

const config = {
    server: process.env.DB_SERVER,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    options: {
        encrypt: true,
        trustServerCertificate: true
    }
};

async function executeValetFixes() {
    try {
        await sql.connect(config);
        console.log('🔧 EXECUTING VALET SYSTEM FIXES - ROOT CAUSE RESOLUTION...\n');

        // 1. Create PlazaValetPoints table
        console.log('📋 1. CREATING PlazaValetPoints TABLE:');
        console.log('====================================');
        
        try {
            await sql.query(`
                IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PlazaValetPoints]') AND type in (N'U'))
                BEGIN
                    CREATE TABLE [dbo].[PlazaValetPoints] (
                        [Id] INT IDENTITY(1,1) PRIMARY KEY,
                        [PlazaId] INT NOT NULL,
                        [CompanyId] INT NOT NULL,
                        [PointName] NVARCHAR(100) NOT NULL,
                        [PointCode] NVARCHAR(50) NULL,
                        [Location] NVARCHAR(200) NULL,
                        [Capacity] INT DEFAULT 10,
                        [IsActive] BIT DEFAULT 1,
                        [CreatedBy] INT NULL,
                        [CreatedOn] DATETIME DEFAULT GETDATE(),
                        [ModifiedBy] INT NULL,
                        [ModifiedOn] DATETIME NULL,
                        
                        CONSTRAINT FK_PlazaValetPoints_Plaza FOREIGN KEY ([PlazaId]) REFERENCES [Plaza]([Id]),
                        CONSTRAINT FK_PlazaValetPoints_Company FOREIGN KEY ([CompanyId]) REFERENCES [Company]([Id])
                    );
                END
            `);
            console.log('   ✅ PlazaValetPoints table created successfully');
        } catch (error) {
            console.log(`   ❌ Error creating PlazaValetPoints table: ${error.message}`);
        }

        // 2. Insert sample data
        console.log('\n📋 2. INSERTING SAMPLE VALET POINTS:');
        console.log('===================================');
        
        try {
            // Get first active plaza
            const plazaResult = await sql.query(`
                SELECT TOP 1 p.Id, p.CompanyId, p.PlazaName, c.CompanyName
                FROM Plaza p 
                INNER JOIN Company c ON p.CompanyId = c.Id
                WHERE p.IsActive = 1
            `);
            
            if (plazaResult.recordset.length > 0) {
                const plaza = plazaResult.recordset[0];
                console.log(`   📍 Using Plaza: ${plaza.PlazaName} (Company: ${plaza.CompanyName})`);
                
                // Check if valet points already exist
                const existingPoints = await sql.query(`
                    SELECT COUNT(*) as Count FROM PlazaValetPoints WHERE PlazaId = ${plaza.Id}
                `);
                
                if (existingPoints.recordset[0].Count === 0) {
                    await sql.query(`
                        INSERT INTO PlazaValetPoints (PlazaId, CompanyId, PointName, PointCode, Location, Capacity, IsActive, CreatedBy, CreatedOn)
                        VALUES 
                            (${plaza.Id}, ${plaza.CompanyId}, 'Main Entrance Valet', 'VP001', 'Main Entrance', 20, 1, 1, GETDATE()),
                            (${plaza.Id}, ${plaza.CompanyId}, 'Side Entrance Valet', 'VP002', 'Side Entrance', 15, 1, 1, GETDATE()),
                            (${plaza.Id}, ${plaza.CompanyId}, 'VIP Valet Point', 'VP003', 'VIP Section', 10, 1, 1, GETDATE())
                    `);
                    console.log('   ✅ Sample valet points inserted successfully');
                } else {
                    console.log('   ⚠️  Sample valet points already exist');
                }
            } else {
                console.log('   ❌ No active plaza found');
            }
        } catch (error) {
            console.log(`   ❌ Error inserting sample data: ${error.message}`);
        }

        // 3. Fix stored procedures
        console.log('\n📋 3. FIXING STORED PROCEDURES:');
        console.log('==============================');
        
        // Fix sp_Valet_OTP_Generate
        try {
            await sql.query(`
                IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_OTP_Generate]') AND type in (N'P', N'PC'))
                    DROP PROCEDURE [dbo].[sp_Valet_OTP_Generate]
            `);
            
            await sql.query(`
                CREATE PROCEDURE [dbo].[sp_Valet_OTP_Generate]
                    @MobileNumber NVARCHAR(15),
                    @OTPCode NVARCHAR(10),
                    @ExpiryMinutes INT,
                    @CreatedBy INT,
                    @NewId INT OUTPUT
                AS
                BEGIN
                    SET NOCOUNT ON;
                    
                    BEGIN TRY
                        UPDATE OTP 
                        SET IsActive = 0, ModifiedBy = @CreatedBy, ModifiedOn = GETDATE()
                        WHERE MobileNumber = @MobileNumber AND IsActive = 1;
                        
                        INSERT INTO OTP (MobileNumber, OTP, ExpireTime, IsActive, CreatedBy, CreatedOn)
                        VALUES (@MobileNumber, @OTPCode, @ExpiryMinutes, 1, @CreatedBy, GETDATE());
                        
                        SET @NewId = SCOPE_IDENTITY();
                        
                        SELECT 1 as Success, 'OTP generated successfully' as Message, @NewId as Id;
                        
                    END TRY
                    BEGIN CATCH
                        SET @NewId = 0;
                        SELECT 0 as Success, ERROR_MESSAGE() as Message, 0 as Id;
                    END CATCH
                END
            `);
            console.log('   ✅ sp_Valet_OTP_Generate fixed');
        } catch (error) {
            console.log(`   ❌ Error fixing sp_Valet_OTP_Generate: ${error.message}`);
        }

        // Fix sp_Valet_Customer_Create
        try {
            await sql.query(`
                IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Customer_Create]') AND type in (N'P', N'PC'))
                    DROP PROCEDURE [dbo].[sp_Valet_Customer_Create]
            `);
            
            await sql.query(`
                CREATE PROCEDURE [dbo].[sp_Valet_Customer_Create]
                    @MobileNumber NVARCHAR(15),
                    @Name NVARCHAR(100) = NULL,
                    @AddressId DECIMAL = NULL,
                    @CreatedBy INT,
                    @NewId INT OUTPUT
                AS
                BEGIN
                    SET NOCOUNT ON;
                    
                    BEGIN TRY
                        IF EXISTS (SELECT 1 FROM Customer WHERE MobileNumber = @MobileNumber AND IsActive = 1)
                        BEGIN
                            SELECT @NewId = Id FROM Customer WHERE MobileNumber = @MobileNumber AND IsActive = 1;
                            SELECT 1 as Success, 'Customer already exists' as Message, @NewId as Id;
                            RETURN;
                        END
                        
                        INSERT INTO Customer (Name, MobileNumber, AddressId, IsActive, CreatedBy, CreatedOn)
                        VALUES (@Name, @MobileNumber, @AddressId, 1, @CreatedBy, GETDATE());
                        
                        SET @NewId = SCOPE_IDENTITY();
                        
                        SELECT 1 as Success, 'Customer created successfully' as Message, @NewId as Id;
                        
                    END TRY
                    BEGIN CATCH
                        SET @NewId = 0;
                        SELECT 0 as Success, ERROR_MESSAGE() as Message, 0 as Id;
                    END CATCH
                END
            `);
            console.log('   ✅ sp_Valet_Customer_Create fixed');
        } catch (error) {
            console.log(`   ❌ Error fixing sp_Valet_Customer_Create: ${error.message}`);
        }

        // 4. Test the fixes
        console.log('\n📋 4. TESTING FIXES:');
        console.log('===================');
        
        // Test PlazaValetPoints
        try {
            const valetPointsCount = await sql.query(`
                SELECT COUNT(*) as Count FROM PlazaValetPoints WHERE IsActive = 1
            `);
            console.log(`   ✅ Active valet points: ${valetPointsCount.recordset[0].Count}`);
        } catch (error) {
            console.log(`   ❌ Error checking valet points: ${error.message}`);
        }

        // Test stored procedure with OUTPUT parameter
        try {
            const testResult = await sql.query(`
                DECLARE @TestNewId INT;
                EXEC sp_Valet_OTP_Generate 
                    @MobileNumber = '9999999999',
                    @OTPCode = '123456',
                    @ExpiryMinutes = 5,
                    @CreatedBy = 1,
                    @NewId = @TestNewId OUTPUT;
                SELECT @TestNewId as GeneratedId;
            `);
            console.log(`   ✅ OTP generation test successful - ID: ${testResult.recordset[0]?.GeneratedId || 'N/A'}`);
        } catch (error) {
            console.log(`   ❌ OTP generation test failed: ${error.message}`);
        }

        // 5. Final verification
        console.log('\n📊 FINAL VERIFICATION:');
        console.log('======================');
        
        const finalChecks = [
            { name: 'PlazaValetPoints table', query: "SELECT COUNT(*) as Count FROM PlazaValetPoints" },
            { name: 'Active valet points', query: "SELECT COUNT(*) as Count FROM PlazaValetPoints WHERE IsActive = 1" },
            { name: 'Customer table', query: "SELECT COUNT(*) as Count FROM Customer" },
            { name: 'OTP table', query: "SELECT COUNT(*) as Count FROM OTP" }
        ];
        
        for (const check of finalChecks) {
            try {
                const result = await sql.query(check.query);
                console.log(`   ✅ ${check.name}: ${result.recordset[0].Count} records`);
            } catch (error) {
                console.log(`   ❌ ${check.name}: ERROR - ${error.message}`);
            }
        }

        console.log('\n🎉 ROOT CAUSE RESOLUTION COMPLETE!');
        console.log('==================================');
        console.log('✅ PlazaValetPoints table created and populated');
        console.log('✅ Stored procedures fixed with OUTPUT parameters');
        console.log('✅ Database schema is now compatible with valet controllers');
        console.log('');
        console.log('🚀 NEXT STEPS:');
        console.log('1. Add NODE_ENV=development to backend/.env file');
        console.log('2. Restart the backend server');
        console.log('3. Run comprehensive API tests');
        console.log('4. All valet endpoints should now work properly!');

    } catch (error) {
        console.error('❌ Error executing valet fixes:', error);
    } finally {
        await sql.close();
    }
}

executeValetFixes();
