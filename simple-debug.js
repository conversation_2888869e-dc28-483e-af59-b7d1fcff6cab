const db = require('./backend/src/config/database');

async function debugPermissionIssue() {
  try {
    console.log('🔍 Starting comprehensive permission debug...\n');

    // Test the exact controller logic
    console.log('Testing getModulesTree controller logic...');
    
    const query = `
      SELECT 
        m.Id as ModuleId,
        m.Name as ModuleName,
        m.Description as ModuleDescription,
        m.Icon as ModuleIcon,
        m.DisplayOrder as ModuleDisplayOrder,
        m.IsActive as ModuleIsActive,
        
        sm.Id as SubModuleId,
        sm.Name as SubModuleName,
        sm.Path as SubModuleRoute,
        sm.Icon as SubModuleIcon,
        sm.IsActive as SubModuleIsActive,
        
        p.Id as PermissionId,
        p.Name as PermissionName,
        p.Description as PermissionDescription,
        
        smp.Id as SubModulePermissionId
        
      FROM Modules m
      LEFT JOIN SubModules sm ON m.Id = sm.ModuleId AND sm.IsActive = 1
      LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
      LEFT JOIN Permissions p ON smp.PermissionId = p.Id AND p.IsActive = 1
      WHERE m.IsActive = 1
      ORDER BY m.DisplayOrder, sm.Id, p.Name
    `;

    console.log('Executing query...');
    const result = await db.query(query);
    console.log(`✅ Query returned ${result.recordset.length} rows`);

    if (result.recordset.length === 0) {
      console.log('❌ No rows returned! Let me check why...');
      
      // Check each join condition
      console.log('\nChecking join conditions:');
      
      // Check modules
      const modulesCheck = await db.query('SELECT COUNT(*) as count FROM Modules WHERE IsActive = 1');
      console.log(`Modules (IsActive=1): ${modulesCheck.recordset[0].count}`);
      
      // Check submodules
      const submodulesCheck = await db.query('SELECT COUNT(*) as count FROM SubModules WHERE IsActive = 1');
      console.log(`SubModules (IsActive=1): ${submodulesCheck.recordset[0].count}`);
      
      // Check if submodules have valid ModuleId
      const submodulesWithModules = await db.query(`
        SELECT COUNT(*) as count 
        FROM SubModules sm 
        JOIN Modules m ON sm.ModuleId = m.Id 
        WHERE sm.IsActive = 1 AND m.IsActive = 1
      `);
      console.log(`SubModules with valid Modules: ${submodulesWithModules.recordset[0].count}`);
      
      // Check permissions
      const permissionsCheck = await db.query('SELECT COUNT(*) as count FROM Permissions WHERE IsActive = 1');
      console.log(`Permissions (IsActive=1): ${permissionsCheck.recordset[0].count}`);
      
      // Check submodule permissions
      const smpCheck = await db.query('SELECT COUNT(*) as count FROM SubModulePermissions WHERE IsActive = 1');
      console.log(`SubModulePermissions (IsActive=1): ${smpCheck.recordset[0].count}`);
      
      // Check if IsActive column exists and what values it has
      console.log('\nChecking IsActive values:');
      const moduleActiveValues = await db.query('SELECT DISTINCT IsActive FROM Modules');
      console.log('Modules IsActive values:', moduleActiveValues.recordset.map(r => r.IsActive));
      
      const subModuleActiveValues = await db.query('SELECT DISTINCT IsActive FROM SubModules');
      console.log('SubModules IsActive values:', subModuleActiveValues.recordset.map(r => r.IsActive));
      
      const permissionActiveValues = await db.query('SELECT DISTINCT IsActive FROM Permissions');
      console.log('Permissions IsActive values:', permissionActiveValues.recordset.map(r => r.IsActive));
      
      const smpActiveValues = await db.query('SELECT DISTINCT IsActive FROM SubModulePermissions');
      console.log('SubModulePermissions IsActive values:', smpActiveValues.recordset.map(r => r.IsActive));
      
      return;
    }

    // Transform flat result into hierarchical structure (exact controller logic)
    const modulesMap = new Map();

    result.recordset.forEach(row => {
      // Create module if not exists
      if (!modulesMap.has(row.ModuleId)) {
        modulesMap.set(row.ModuleId, {
          id: row.ModuleId,
          name: row.ModuleName,
          description: row.ModuleDescription,
          icon: row.ModuleIcon,
          displayOrder: row.ModuleDisplayOrder,
          isActive: row.ModuleIsActive,
          subModules: new Map()
        });
      }

      const module = modulesMap.get(row.ModuleId);

      // Create submodule if not exists and if SubModuleId is not null
      if (row.SubModuleId && !module.subModules.has(row.SubModuleId)) {
        module.subModules.set(row.SubModuleId, {
          id: row.SubModuleId,
          name: row.SubModuleName,
          description: row.SubModuleName,
          route: row.SubModuleRoute,
          icon: row.SubModuleIcon,
          displayOrder: row.SubModuleId,
          isActive: row.SubModuleIsActive,
          permissions: []
        });
      }

      // Add permission if exists
      if (row.PermissionId && row.SubModuleId) {
        const subModule = module.subModules.get(row.SubModuleId);
        if (subModule && !subModule.permissions.find(p => p.id === row.PermissionId)) {
          subModule.permissions.push({
            id: row.PermissionId,
            name: row.PermissionName,
            description: row.PermissionDescription,
            subModulePermissionId: row.SubModulePermissionId
          });
        }
      }
    });

    // Convert Maps to Arrays for JSON response
    const modules = Array.from(modulesMap.values()).map(module => ({
      ...module,
      subModules: Array.from(module.subModules.values())
    }));

    console.log(`✅ Transformed into ${modules.length} modules`);
    
    if (modules.length > 0) {
      console.log('\nModules summary:');
      modules.forEach(module => {
        console.log(`  ${module.name}: ${module.subModules.length} submodules`);
        module.subModules.forEach(subModule => {
          console.log(`    - ${subModule.name}: ${subModule.permissions.length} permissions`);
        });
      });
    }

    // Test roles query
    console.log('\nTesting roles query...');
    const rolesQuery = `
      SELECT 
        Id,
        Name,
        IsActive,
        CreatedOn
      FROM Roles
      WHERE IsActive = 1
      ORDER BY Name
    `;
    const rolesResult = await db.query(rolesQuery);
    console.log(`✅ Roles query returned ${rolesResult.recordset.length} roles`);
    
    if (rolesResult.recordset.length > 0) {
      console.log('Roles:');
      rolesResult.recordset.forEach(role => {
        console.log(`  - ${role.Name} (ID: ${role.Id})`);
      });
    }

    console.log('\n🎉 Debug completed successfully!');
    console.log('The controller logic should work. The issue might be:');
    console.log('1. Server not restarted after changes');
    console.log('2. Authentication middleware blocking requests');
    console.log('3. Route not properly registered');

  } catch (error) {
    console.error('❌ Debug failed:', error);
  } finally {
    await db.closePool();
    process.exit(0);
  }
}

debugPermissionIssue();