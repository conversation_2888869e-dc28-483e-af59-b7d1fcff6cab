const express = require('express');
const router = express.Router();
const laneDigitalPayConfigController = require('../controllers/LaneDigitalPayConfigController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/lane-digital-pay-configs
 * @desc    Get all lane digital pay configurations
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), laneDigitalPayConfigController.getAllConfigurations);

/**
 * @route   GET /api/lane-digital-pay-configs/:id
 * @desc    Get lane digital pay configuration by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), laneDigitalPayConfigController.getConfigurationById);

/**
 * @route   GET /api/lane-digital-pay-configs/lane/:laneId
 * @desc    Get lane digital pay configurations by lane ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/lane/:laneId', auth(['View']), laneDigitalPayConfigController.getConfigurationsByLane);

/**
 * @route   GET /api/lane-digital-pay-configs/plaza/:plazaId
 * @desc    Get lane digital pay configurations by plaza ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/plaza/:plazaId', auth(['View']), laneDigitalPayConfigController.getConfigurationsByPlaza);

/**
 * @route   POST /api/lane-digital-pay-configs
 * @desc    Create a new lane digital pay configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), laneDigitalPayConfigController.createConfiguration);

/**
 * @route   PUT /api/lane-digital-pay-configs/:id
 * @desc    Update a lane digital pay configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.put('/:id', auth(['Edit']), laneDigitalPayConfigController.updateConfiguration);

/**
 * @route   DELETE /api/lane-digital-pay-configs/:id
 * @desc    Delete a lane digital pay configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), laneDigitalPayConfigController.deleteConfiguration);

/**
 * @route   PATCH /api/lane-digital-pay-configs/:id/toggle-status
 * @desc    Toggle lane digital pay configuration active status
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.patch('/:id/toggle-status', auth(['Edit']), laneDigitalPayConfigController.toggleConfigurationStatus);

module.exports = router;