const axios = require('axios');

async function testEndpoints() {
  console.log('🔍 Testing Permission Management Endpoints\n');

  try {
    // Test 1: Non-authenticated endpoint
    console.log('1. Testing non-authenticated endpoint...');
    try {
      const response1 = await axios.get('http://localhost:5000/api/permission-management/test-no-auth', {
        timeout: 5000
      });
      console.log('✅ Non-auth test successful:', response1.data.message);
    } catch (error) {
      console.log('❌ Non-auth test failed:', error.message);
    }

    // Test 2: Database test without auth
    console.log('\n2. Testing database without auth...');
    try {
      const response2 = await axios.get('http://localhost:5000/api/permission-management/test-db-no-auth', {
        timeout: 10000
      });
      console.log('✅ Database test successful:', response2.data.message);
      console.log('   Data:', response2.data.data);
    } catch (error) {
      console.log('❌ Database test failed:', error.message);
    }

    // Test 3: Login and get token
    console.log('\n3. Testing login...');
    let token = null;
    try {
      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
        username: 'superadmin',
        password: 'Admin@123'
      }, {
        timeout: 10000
      });
      
      token = loginResponse.data.data.token;
      console.log('✅ Login successful, token received');
    } catch (error) {
      console.log('❌ Login failed:', error.message);
      return;
    }

    // Test 4: Authenticated test endpoint
    console.log('\n4. Testing authenticated endpoint...');
    try {
      const response4 = await axios.get('http://localhost:5000/api/permission-management/test', {
        headers: { 'Authorization': `Bearer ${token}` },
        timeout: 10000
      });
      console.log('✅ Authenticated test successful:', response4.data.message);
    } catch (error) {
      console.log('❌ Authenticated test failed:', error.message);
      if (error.response) {
        console.log('   Status:', error.response.status);
        console.log('   Data:', error.response.data);
      }
    }

    // Test 5: Modules tree endpoint
    console.log('\n5. Testing modules-tree endpoint...');
    try {
      const response5 = await axios.get('http://localhost:5000/api/permission-management/modules-tree', {
        headers: { 'Authorization': `Bearer ${token}` },
        timeout: 15000
      });
      console.log('✅ Modules-tree successful');
      console.log('   Modules count:', response5.data.data.length);
      console.log('   First module:', response5.data.data[0]?.name);
    } catch (error) {
      console.log('❌ Modules-tree failed:', error.message);
      if (error.response) {
        console.log('   Status:', error.response.status);
        console.log('   Data:', error.response.data);
      }
    }

  } catch (error) {
    console.log('❌ Test suite failed:', error.message);
  }
}

testEndpoints().then(() => {
  console.log('\n🏁 Test completed');
  process.exit(0);
}).catch(error => {
  console.log('❌ Test crashed:', error.message);
  process.exit(1);
});