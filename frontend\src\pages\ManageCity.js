import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { cityApi } from '../api/cityApi';
import { stateApi } from '../api/stateApi';
import CityList from '../components/City/CityList';
import CityDialog from '../components/City/CityDialog';
import CityStats from '../components/City/CityStats';

export default function CityManagement() {
  const queryClient = useQueryClient();
  const toast = useToast();
  const [selectedCity, setSelectedCity] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCity, setEditingCity] = useState(null);

  const { data: cities, isLoading: citiesLoading } = useQuery({
    queryKey: ['cities'],
    queryFn: () => cityApi.getCities(),
  });

  const { data: states, isLoading: statesLoading } = useQuery({
    queryKey: ['states'],
    queryFn: () => stateApi.getStates(),
  });

  const { data: cityStats } = useQuery({
    queryKey: ['cityStats', selectedCity],
    queryFn: () => selectedCity ? cityApi.getCityStats(selectedCity) : null,
    enabled: !!selectedCity,
  });

  const createMutation = useMutation({
    mutationFn: cityApi.createCity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cities'] });
      toast.showCrudSuccess('create', 'City');
      setDialogOpen(false);
    },
    onError: (error) => toast.showCrudError('create', 'city', error.response?.data?.message),
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => cityApi.updateCity(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cities'] });
      toast.showCrudSuccess('update', 'City');
      setDialogOpen(false);
      setEditingCity(null);
    },
    onError: (error) => toast.showCrudError('update', 'city', error.response?.data?.message),
  });

  const deleteMutation = useMutation({
    mutationFn: cityApi.deleteCity,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['cities'] });
      toast.showCrudSuccess('delete', 'City');
      if (selectedCity) setSelectedCity(null);
    },
    onError: (error) => toast.showCrudError('delete', 'city', error.response?.data?.message),
  });

  const handleSubmit = (data) => {
    if (editingCity) {
      updateMutation.mutate({ id: editingCity._id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const handleEdit = (city) => {
    setEditingCity(city);
    setDialogOpen(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this city?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingCity(null);
  };

  if (citiesLoading || statesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">City Management</h1>
          <button
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add City
          </button>
        </div>

        <div className="bg-white rounded-lg shadow">
          <CityList
            cities={cities || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={setSelectedCity}
          />
        </div>

        {selectedCity && cityStats && <CityStats stats={cityStats} />}

        {states && (
          <CityDialog
            isOpen={dialogOpen}
            onClose={handleCloseDialog}
            onSubmit={handleSubmit}
            initialData={
              editingCity
                ? {
                    name: editingCity.name,
                    code: editingCity.code,
                    state: editingCity.state._id,
                    status: editingCity.status,
                  }
                : undefined
            }
            title={editingCity ? 'Edit City' : 'Add City'}
            states={states}
          />
        )}
      </div>
    </div>
  );
}
