// src/lib/utils.js
// export function cn(...classes) {
//     return classes.filter(Boolean).join(' ');
//   }
  

// src/lib/utils.js

import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines class names using `clsx` and resolves conflicts using `twMerge`.
 * This ensures proper merging of Tailwind CSS classes.
 */
export function cn(...inputs) {
  return twMerge(clsx(inputs));
}