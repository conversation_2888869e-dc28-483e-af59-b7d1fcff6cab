-- =============================================
-- Valet Customer Management Stored Procedures
-- Created for comprehensive valet customer management
-- =============================================

-- =============================================
-- 1. CREATE - Register new customer
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Customer_Create]
    @MobileNumber NVARCHAR(15),
    @Name NVARCHAR(255),
    @AddressId DECIMAL(18,0) = NULL,
    @IsActive BIT = 1,
    @CreatedBy INT,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @MobileNumber IS NULL OR LTRIM(RTRIM(@MobileNumber)) = ''
        BEGIN
            RAISERROR('Mobile number is required', 16, 1);
            RETURN -1;
        END
        
        -- Validate mobile number format (10 digits)
        IF LEN(@MobileNumber) != 10 OR @MobileNumber NOT LIKE '[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]'
        BEGIN
            RAISERROR('Mobile number must be exactly 10 digits', 16, 1);
            RETURN -1;
        END
        
        IF @Name IS NULL OR LTRIM(RTRIM(@Name)) = ''
        BEGIN
            RAISERROR('Customer name is required', 16, 1);
            RETURN -1;
        END
        
        IF @CreatedBy IS NULL OR @CreatedBy <= 0
        BEGIN
            RAISERROR('CreatedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate mobile number
        IF EXISTS(SELECT 1 FROM [dbo].[Customer] WHERE [MobileNumber] = @MobileNumber AND [IsActive] = 1)
        BEGIN
            RAISERROR('Customer with this mobile number already exists', 16, 1);
            RETURN -1;
        END
        
        -- Insert new customer
        INSERT INTO [dbo].[Customer]
        (
            [MobileNumber],
            [Name],
            [AddressId],
            [IsActive],
            [CreatedBy],
            [CreatedOn]
        )
        VALUES
        (
            LTRIM(RTRIM(@MobileNumber)),
            LTRIM(RTRIM(@Name)),
            @AddressId,
            @IsActive,
            @CreatedBy,
            GETDATE()
        );
        
        SET @NewId = SCOPE_IDENTITY();
        
        -- Return success with customer details
        SELECT
            @NewId AS Id,
            @MobileNumber AS MobileNumber,
            @Name AS Name,
            @AddressId AS AddressId,
            'Customer registered successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @NewId = -1;
    END CATCH
END
GO

-- =============================================
-- 2. READ - Get customer by mobile number
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Customer_GetByMobile]
    @MobileNumber NVARCHAR(15)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @MobileNumber IS NULL OR LTRIM(RTRIM(@MobileNumber)) = ''
        BEGIN
            RAISERROR('Mobile number is required', 16, 1);
            RETURN -1;
        END
        
        -- Get customer by mobile number
        SELECT
            [Id],
            [MobileNumber],
            [Name],
            [AddressId],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn]
        FROM [dbo].[Customer]
        WHERE [MobileNumber] = @MobileNumber AND [IsActive] = 1;
        
        -- Check if customer exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'Customer not found' AS Message, 0 AS Success;
        END
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 3. READ - Get customer by ID
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Customer_GetById]
    @Id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Customer ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Get customer by ID
        SELECT
            [Id],
            [MobileNumber],
            [Name],
            [AddressId],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn]
        FROM [dbo].[Customer]
        WHERE [Id] = @Id;
        
        -- Check if customer exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'Customer not found' AS Message, 0 AS Success;
        END
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 4. UPDATE - Update customer details
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Customer_Update]
    @Id INT,
    @Name NVARCHAR(255),
    @AddressId DECIMAL(18,0) = NULL,
    @IsActive BIT,
    @ModifiedBy INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Customer ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @Name IS NULL OR LTRIM(RTRIM(@Name)) = ''
        BEGIN
            RAISERROR('Customer name is required', 16, 1);
            RETURN -1;
        END
        
        IF @ModifiedBy IS NULL OR @ModifiedBy <= 0
        BEGIN
            RAISERROR('ModifiedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if customer exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[Customer] WHERE [Id] = @Id)
        BEGIN
            RAISERROR('Customer not found', 16, 1);
            RETURN -1;
        END
        
        -- Update customer
        UPDATE [dbo].[Customer]
        SET
            [Name] = LTRIM(RTRIM(@Name)),
            [AddressId] = @AddressId,
            [IsActive] = @IsActive,
            [ModifiedBy] = @ModifiedBy,
            [ModifiedOn] = GETDATE()
        WHERE [Id] = @Id;
        
        -- Return updated customer
        SELECT
            [Id],
            [MobileNumber],
            [Name],
            [AddressId],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            'Customer updated successfully' AS Message,
            1 AS Success
        FROM [dbo].[Customer]
        WHERE [Id] = @Id;
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 5. READ - Get all customers with pagination
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Customer_GetAll]
    @IsActive BIT = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SearchTerm NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PageNumber < 1 SET @PageNumber = 1;
        IF @PageSize < 1 OR @PageSize > 1000 SET @PageSize = 50;
        
        -- Calculate offset
        DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
        
        -- Get total count
        DECLARE @TotalRecords INT;
        SELECT @TotalRecords = COUNT(*)
        FROM [dbo].[Customer]
        WHERE (@IsActive IS NULL OR [IsActive] = @IsActive)
        AND (@SearchTerm IS NULL OR [Name] LIKE '%' + @SearchTerm + '%' OR [MobileNumber] LIKE '%' + @SearchTerm + '%');
        
        -- Get paginated results
        SELECT
            [Id],
            [MobileNumber],
            [Name],
            [AddressId],
            [IsActive],
            [CreatedBy],
            [CreatedOn],
            [ModifiedBy],
            [ModifiedOn],
            @TotalRecords AS TotalRecords,
            @PageNumber AS CurrentPage,
            @PageSize AS PageSize,
            CEILING(CAST(@TotalRecords AS FLOAT) / @PageSize) AS TotalPages
        FROM [dbo].[Customer]
        WHERE (@IsActive IS NULL OR [IsActive] = @IsActive)
        AND (@SearchTerm IS NULL OR [Name] LIKE '%' + @SearchTerm + '%' OR [MobileNumber] LIKE '%' + @SearchTerm + '%')
        ORDER BY [CreatedOn] DESC
        OFFSET @Offset ROWS
        FETCH NEXT @PageSize ROWS ONLY;
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 6. CREATE - Add customer vehicle
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_CustomerVehicle_Create]
    @CustomerId INT,
    @VehicleNumber NVARCHAR(20),
    @IsActive BIT = 1,
    @CreatedBy INT,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Validation
        IF @CustomerId IS NULL OR @CustomerId <= 0
        BEGIN
            RAISERROR('Customer ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        IF @VehicleNumber IS NULL OR LTRIM(RTRIM(@VehicleNumber)) = ''
        BEGIN
            RAISERROR('Vehicle number is required', 16, 1);
            RETURN -1;
        END

        IF @CreatedBy IS NULL OR @CreatedBy <= 0
        BEGIN
            RAISERROR('CreatedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        -- Check if customer exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[Customer] WHERE [Id] = @CustomerId AND [IsActive] = 1)
        BEGIN
            RAISERROR('Customer not found or inactive', 16, 1);
            RETURN -1;
        END

        -- Check for duplicate vehicle number for this customer
        IF EXISTS(SELECT 1 FROM [dbo].[CustomerVehicle]
                  WHERE [CustomerId] = @CustomerId
                  AND [VehicleNumber] = @VehicleNumber
                  AND [IsActive] = 1)
        BEGIN
            RAISERROR('Vehicle already registered for this customer', 16, 1);
            RETURN -1;
        END

        -- Insert new vehicle
        INSERT INTO [dbo].[CustomerVehicle]
        (
            [CustomerId],
            [VehicleNumber],
            [IsActive],
            [CreatedBy],
            [CreatedOn]
        )
        VALUES
        (
            @CustomerId,
            UPPER(LTRIM(RTRIM(@VehicleNumber))),
            @IsActive,
            @CreatedBy,
            GETDATE()
        );

        SET @NewId = SCOPE_IDENTITY();

        -- Return success with vehicle details
        SELECT
            @NewId AS Id,
            @CustomerId AS CustomerId,
            UPPER(LTRIM(RTRIM(@VehicleNumber))) AS VehicleNumber,
            'Vehicle registered successfully' AS Message,
            1 AS Success;

    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;

        SET @NewId = -1;
    END CATCH
END
GO
