# Valet System Project Structure for PWVMS

## Complete File Organization Structure

### Backend Structure

```
backend/
├── src/
│   ├── controllers/
│   │   ├── valet/
│   │   │   ├── CustomerController.js
│   │   │   ├── ValetTransactionController.js
│   │   │   ├── ValetDriverController.js
│   │   │   ├── ValetControllerDashboard.js
│   │   │   ├── PaymentController.js
│   │   │   ├── OTPController.js
│   │   │   ├── QRCodeController.js
│   │   │   ├── SMSController.js
│   │   │   ├── VehicleTrackingController.js
│   │   │   └── ValetReportsController.js
│   │   └── (existing controllers...)
│   │
│   ├── routes/
│   │   ├── valet/
│   │   │   ├── customerRoutes.js
│   │   │   ├── transactionRoutes.js
│   │   │   ├── driverRoutes.js
│   │   │   ├── controllerRoutes.js
│   │   │   ├── paymentRoutes.js
│   │   │   ├── otpRoutes.js
│   │   │   ├── qrcodeRoutes.js
│   │   │   ├── smsRoutes.js
│   │   │   ├── trackingRoutes.js
│   │   │   └── reportsRoutes.js
│   │   └── (existing routes...)
│   │
│   ├── services/
│   │   ├── valet/
│   │   │   ├── PaymentGatewayService.js
│   │   │   ├── RazorPayService.js
│   │   │   ├── PhonePeService.js
│   │   │   ├── UPIService.js
│   │   │   ├── CashPaymentService.js
│   │   │   ├── SMSService.js
│   │   │   ├── OTPService.js
│   │   │   ├── NotificationService.js
│   │   │   ├── QRCodeService.js
│   │   │   ├── VehicleTrackingService.js
│   │   │   ├── ImageProcessingService.js
│   │   │   └── ReportingService.js
│   │   └── (existing services...)
│   │
│   ├── middleware/
│   │   ├── valetAuth.js
│   │   ├── driverAuth.js
│   │   ├── customerAuth.js
│   │   └── (existing middleware...)
│   │
│   └── utils/
│       ├── valet/
│       │   ├── valetHelpers.js
│       │   ├── paymentHelpers.js
│       │   ├── smsHelpers.js
│       │   └── qrCodeHelpers.js
│       └── (existing utils...)
│
├── database/
│   ├── Stored_Procedures/
│   │   ├── valet/
│   │   │   ├── customer_procedures.sql
│   │   │   ├── transaction_procedures.sql
│   │   │   ├── driver_procedures.sql
│   │   │   ├── payment_procedures.sql
│   │   │   ├── otp_procedures.sql
│   │   │   ├── tracking_procedures.sql
│   │   │   └── reports_procedures.sql
│   │   └── (existing procedures...)
│   │
│   ├── migrations/
│   │   ├── valet/
│   │   │   ├── 001_create_valet_tables.sql
│   │   │   ├── 002_migrate_customer_data.sql
│   │   │   ├── 003_migrate_transaction_data.sql
│   │   │   ├── 004_create_valet_indexes.sql
│   │   │   ├── 005_create_valet_procedures.sql
│   │   │   └── 006_setup_valet_permissions.sql
│   │   └── (existing migrations...)
│   │
│   └── seeds/
│       ├── valet/
│       │   ├── sample_customers.sql
│       │   ├── sample_drivers.sql
│       │   └── sample_transactions.sql
│       └── (existing seeds...)
│
└── scripts/
    ├── valet/
    │   ├── setupValetModule.js
    │   ├── migrateValetData.js
    │   └── testValetSystem.js
    └── (existing scripts...)
```

### Frontend Structure

```
frontend/
├── src/
│   ├── components/
│   │   ├── valet/
│   │   │   ├── customer/
│   │   │   │   ├── QRScanner.js
│   │   │   │   ├── MobileRegistration.js
│   │   │   │   ├── OTPVerification.js
│   │   │   │   ├── CustomerDetails.js
│   │   │   │   ├── PaymentSelection.js
│   │   │   │   ├── PaymentGateway.js
│   │   │   │   ├── VehicleStatus.js
│   │   │   │   ├── PickupRequest.js
│   │   │   │   └── CustomerDashboard.js
│   │   │   │
│   │   │   ├── driver/
│   │   │   │   ├── DriverLogin.js
│   │   │   │   ├── VehicleList.js
│   │   │   │   ├── VehicleDetails.js
│   │   │   │   ├── ParkingAssignment.js
│   │   │   │   ├── VehicleImageCapture.js
│   │   │   │   ├── PickupAlerts.js
│   │   │   │   ├── VehicleHandover.js
│   │   │   │   └── DriverDashboard.js
│   │   │   │
│   │   │   ├── controller/
│   │   │   │   ├── TransactionOverview.js
│   │   │   │   ├── DriverManagement.js
│   │   │   │   ├── VehicleAssignment.js
│   │   │   │   ├── CashPaymentAcceptance.js
│   │   │   │   ├── CustomerRequests.js
│   │   │   │   ├── VehicleHistory.js
│   │   │   │   ├── DailySalesReport.js
│   │   │   │   └── ControllerDashboard.js
│   │   │   │
│   │   │   ├── shared/
│   │   │   │   ├── ValetCard.js
│   │   │   │   ├── StatusBadge.js
│   │   │   │   ├── VehicleImage.js
│   │   │   │   ├── PaymentStatus.js
│   │   │   │   ├── LocationMap.js
│   │   │   │   └── ValetLoader.js
│   │   │   │
│   │   │   └── dialogs/
│   │   │       ├── CustomerDialog.js
│   │   │       ├── DriverDialog.js
│   │   │       ├── TransactionDialog.js
│   │   │       ├── PaymentDialog.js
│   │   │       └── VehicleImageDialog.js
│   │   │
│   │   └── (existing components...)
│   │
│   ├── pages/
│   │   ├── valet/
│   │   │   ├── customer/
│   │   │   │   ├── CustomerApp.js
│   │   │   │   ├── VehicleRegistration.js
│   │   │   │   ├── PaymentPage.js
│   │   │   │   └── VehicleTracking.js
│   │   │   │
│   │   │   ├── driver/
│   │   │   │   ├── DriverApp.js
│   │   │   │   ├── VehicleManagement.js
│   │   │   │   └── PickupManagement.js
│   │   │   │
│   │   │   ├── controller/
│   │   │   │   ├── ControllerApp.js
│   │   │   │   ├── TransactionManagement.js
│   │   │   │   └── ReportsPage.js
│   │   │   │
│   │   │   └── management/
│   │   │       ├── ManageValetCustomers.js
│   │   │       ├── ManageValetDrivers.js
│   │   │       ├── ManageValetTransactions.js
│   │   │       ├── ManageValetPayments.js
│   │   │       ├── ManageValetQRCodes.js
│   │   │       └── ValetReports.js
│   │   │
│   │   └── (existing pages...)
│   │
│   ├── api/
│   │   ├── valet/
│   │   │   ├── customerApi.js
│   │   │   ├── transactionApi.js
│   │   │   ├── driverApi.js
│   │   │   ├── controllerApi.js
│   │   │   ├── paymentApi.js
│   │   │   ├── otpApi.js
│   │   │   ├── qrcodeApi.js
│   │   │   ├── smsApi.js
│   │   │   ├── trackingApi.js
│   │   │   └── reportsApi.js
│   │   │
│   │   └── (existing apis...)
│   │
│   ├── hooks/
│   │   ├── valet/
│   │   │   ├── useValetCustomer.js
│   │   │   ├── useValetTransaction.js
│   │   │   ├── useValetDriver.js
│   │   │   ├── useValetPayment.js
│   │   │   ├── useQRScanner.js
│   │   │   ├── useVehicleTracking.js
│   │   │   └── useValetNotifications.js
│   │   │
│   │   └── (existing hooks...)
│   │
│   ├── contexts/
│   │   ├── valet/
│   │   │   ├── ValetContext.js
│   │   │   ├── CustomerContext.js
│   │   │   ├── DriverContext.js
│   │   │   └── ControllerContext.js
│   │   │
│   │   └── (existing contexts...)
│   │
│   ├── utils/
│   │   ├── valet/
│   │   │   ├── valetHelpers.js
│   │   │   ├── paymentHelpers.js
│   │   │   ├── qrCodeHelpers.js
│   │   │   ├── smsHelpers.js
│   │   │   └── trackingHelpers.js
│   │   │
│   │   └── (existing utils...)
│   │
│   └── styles/
│       ├── valet/
│       │   ├── customer.css
│       │   ├── driver.css
│       │   ├── controller.css
│       │   └── valet-common.css
│       │
│       └── (existing styles...)
│
└── public/
    ├── valet/
    │   ├── qr-codes/
    │   ├── vehicle-images/
    │   └── icons/
    │
    └── (existing public files...)
```

### Configuration Files

```
Root Directory/
├── valet_database_migration.sql
├── valet_database_migration_part2.sql
├── valet_data_migration.sql
├── VALET_SYSTEM_IMPLEMENTATION_PLAN.md
├── VALET_PROJECT_STRUCTURE.md
├── valet_module_setup.sql
└── valet_permissions_setup.sql
```

### Environment Variables (.env additions)

```bash
# Valet System Configuration
VALET_ENABLED=true
VALET_QR_BASE_URL=https://yourdomain.com/valet/qr/
VALET_IMAGE_STORAGE_PATH=/uploads/valet/images/

# SMS Configuration
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=PARKWIZ
SMS_API_URL=https://api.textlocal.in/send/

# Payment Gateway Configuration
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
PHONEPE_MERCHANT_ID=your_phonepe_merchant_id
PHONEPE_SALT_KEY=your_phonepe_salt_key
PHONEPE_STAGE_URL=https://api-preprod.phonepe.com/apis/hermes

# Valet Specific Settings
VALET_OTP_EXPIRY_MINUTES=5
VALET_PICKUP_ALERT_MINUTES=15
VALET_MAX_PARKING_HOURS=24
VALET_DEFAULT_VALET_FEE=50
VALET_DEFAULT_PARKING_FEE=100

# Real-time Features
VALET_WEBSOCKET_ENABLED=true
VALET_PUSH_NOTIFICATIONS_ENABLED=true
VALET_LOCATION_TRACKING_ENABLED=true
```

## Implementation Steps

### Phase 1: Database Setup
1. Run `valet_database_migration.sql`
2. Run `valet_database_migration_part2.sql`
3. Run `valet_data_migration.sql`
4. Run `valet_module_setup.sql`
5. Run `valet_permissions_setup.sql`

### Phase 2: Backend Development
1. Create all controller files in `backend/src/controllers/valet/`
2. Create all route files in `backend/src/routes/valet/`
3. Create all service files in `backend/src/services/valet/`
4. Create stored procedures in `backend/database/Stored_Procedures/valet/`
5. Update `server.js` to include valet routes

### Phase 3: Frontend Development
1. Create all component files in `frontend/src/components/valet/`
2. Create all page files in `frontend/src/pages/valet/`
3. Create all API files in `frontend/src/api/valet/`
4. Create hooks and contexts
5. Update navigation and routing

### Phase 4: Integration & Testing
1. Test customer registration flow
2. Test payment integration
3. Test driver assignment and vehicle parking
4. Test vehicle pickup and handover
5. Test controller dashboard operations
6. Test real-time notifications

### Phase 5: Deployment
1. Update environment variables
2. Deploy database changes
3. Deploy backend APIs
4. Deploy frontend changes
5. Configure payment gateways
6. Set up SMS service
7. Test production environment

This structure provides a complete roadmap for implementing the valet system into your PWVMS project while maintaining organization and scalability.
