const sql = require('mssql');
require('dotenv').config({path: './backend/.env'});

async function migrateValetData() {
  let pwvmsConnection = null;
  let parkwizOpsConnection = null;
  
  try {
    console.log('🚀 Migrating Valet Data from pwvms to ParkwizOps...');
    console.log('===================================================');
    
    // Connection configurations
    const pwvmsConfig = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: 'pwvms',
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    };

    const parkwizOpsConfig = {
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: 'ParkwizOps',
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    };

    // Connect to both databases
    console.log('🔗 Connecting to pwvms database...');
    pwvmsConnection = new sql.ConnectionPool(pwvmsConfig);
    await pwvmsConnection.connect();
    
    console.log('🔗 Connecting to ParkwizOps database...');
    parkwizOpsConnection = new sql.ConnectionPool(parkwizOpsConfig);
    await parkwizOpsConnection.connect();
    
    console.log('✅ Connected to both databases');
    console.log('');

    // Migration steps
    const migrations = [
      {
        name: 'PlazaValetPoint',
        sourceQuery: `
          SELECT PlazaId, ValetPointName, IsActive, CreatedBy, CreatedOn,
                 ModifiedBy, ModifiedOn, CompanyId, Latitude, Longitude
          FROM PlazaValetPoint 
          WHERE IsActive = 1
        `,
        targetTable: 'PlazaValetPoint',
        checkQuery: 'SELECT COUNT(*) as count FROM PlazaValetPoint'
      },
      {
        name: 'Customer',
        sourceQuery: `
          SELECT MobileNumber, Name, Address, IsActive, CreatedBy, CreatedOn,
                 ModifiedBy, ModifiedOn
          FROM Customer 
          WHERE IsActive = 1
        `,
        targetTable: 'Customer',
        checkQuery: 'SELECT COUNT(*) as count FROM Customer'
      },
      {
        name: 'CustomerVehicle',
        sourceQuery: `
          SELECT cv.CustomerId, cv.VehicleNumber, cv.IsActive, cv.CreatedBy, cv.CreatedOn,
                 cv.ModifiedBy, cv.ModifiedOn
          FROM CustomerVehicle cv
          INNER JOIN Customer c ON cv.CustomerId = c.Id
          WHERE cv.IsActive = 1 AND c.IsActive = 1
        `,
        targetTable: 'CustomerVehicle',
        checkQuery: 'SELECT COUNT(*) as count FROM CustomerVehicle'
      },
      {
        name: 'ValetDrivers',
        sourceQuery: `
          SELECT Name, MobileNumber, LicenseNumber, IsActive, CreatedBy, CreatedOn,
                 ModifiedBy, ModifiedOn, PlazaValetPointId
          FROM ValetDrivers 
          WHERE IsActive = 1
        `,
        targetTable: 'ValetDrivers',
        checkQuery: 'SELECT COUNT(*) as count FROM ValetDrivers'
      }
    ];

    // Execute migrations
    for (const migration of migrations) {
      console.log(`📊 Migrating ${migration.name}...`);
      
      try {
        // Check if target table has data
        const checkResult = await parkwizOpsConnection.request().query(migration.checkQuery);
        const existingCount = checkResult.recordset[0].count;
        
        if (existingCount > 0) {
          console.log(`   ⚠️ ${migration.name} already has ${existingCount} records. Skipping migration.`);
          continue;
        }

        // Get source data
        const sourceResult = await pwvmsConnection.request().query(migration.sourceQuery);
        const sourceData = sourceResult.recordset;
        
        if (sourceData.length === 0) {
          console.log(`   ℹ️ No data found in source ${migration.name} table.`);
          continue;
        }

        console.log(`   📥 Found ${sourceData.length} records in source ${migration.name}`);

        // Insert data into target
        let insertedCount = 0;
        for (const record of sourceData) {
          try {
            const columns = Object.keys(record);
            const values = Object.values(record);
            
            const columnNames = columns.join(', ');
            const parameterNames = columns.map((_, index) => `@param${index}`).join(', ');
            
            const insertQuery = `INSERT INTO ${migration.targetTable} (${columnNames}) VALUES (${parameterNames})`;
            
            const request = parkwizOpsConnection.request();
            columns.forEach((column, index) => {
              request.input(`param${index}`, values[index]);
            });
            
            await request.query(insertQuery);
            insertedCount++;
            
          } catch (insertError) {
            console.log(`   ⚠️ Failed to insert record: ${insertError.message}`);
          }
        }
        
        console.log(`   ✅ ${migration.name} migration completed. Inserted: ${insertedCount}/${sourceData.length} records`);
        
      } catch (migrationError) {
        console.error(`   ❌ ${migration.name} migration failed:`, migrationError.message);
      }
      
      console.log('');
    }

    // Final verification
    console.log('🔍 Final verification of migrated data...');
    for (const migration of migrations) {
      try {
        const result = await parkwizOpsConnection.request().query(migration.checkQuery);
        const count = result.recordset[0].count;
        console.log(`   📊 ${migration.name}: ${count} records`);
      } catch (error) {
        console.log(`   ⚠️ ${migration.name}: Could not verify`);
      }
    }

    console.log('');
    console.log('🎉 Valet data migration completed!');
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
  } finally {
    // Close connections
    if (pwvmsConnection) {
      await pwvmsConnection.close();
      console.log('🔌 pwvms connection closed');
    }
    if (parkwizOpsConnection) {
      await parkwizOpsConnection.close();
      console.log('🔌 ParkwizOps connection closed');
    }
  }
}

// Execute if run directly
if (require.main === module) {
  migrateValetData();
}

module.exports = { migrateValetData };
