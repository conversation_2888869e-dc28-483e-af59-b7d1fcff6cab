import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/authContext';
import { DashboardCard } from '../../components/DashboardCard';
import { EnhancedDashboardCard } from '../../components/EnhancedDashboardCard';
import { Chart } from '../../components/Chart';

import { AdvancedRevenueChart } from '../../components/AdvancedRevenueChart';
import { AdvancedPaymentMethodChart } from '../../components/AdvancedPaymentMethodChart';
import { AdvancedEntryExitChart } from '../../components/AdvancedEntryExitChart';
import { DateRangePicker } from '../../components/DateRangePicker';
import { EntityFilter } from '../../components/EntityFilter';
import { formatCurrency, formatNumber, formatDuration } from '../../utils/formatters';
import {
  CreditCard,
  Clock,
  Car,
  Activity,
  Filter,
  Truck,
  Bike
} from 'lucide-react';
import dashboardApi from '../../api/dashboardApi';

// Import skeleton loaders and progressive loading hook
import {
  DashboardCardSkeleton,
  ChartSkeleton,
  PaymentMethodChartSkeleton,
  FilterSkeleton,
  LoadingSpinner,
  LoadingOverlay
} from '../../components/SkeletonLoaders';
import { EnhancedDashboardCardSkeleton } from '../../components/EnhancedDashboardCard';
import { useProgressiveLoading, COMPONENT_IDS } from '../../hooks/useProgressiveLoading';

/**
 * DashboardHome Component
 * 
 * Main dashboard page showing key metrics and visualizations
 * based on user role and permissions
 */
const DashboardHome = () => {
  const { user, isSuperAdmin, isCompanyAdmin } = useAuth();
  const [dateRange, setDateRange] = useState('today');
  const [selectedEntity, setSelectedEntity] = useState({});
  const [dashboardData, setDashboardData] = useState({
    summary: {},
    revenueByPaymentMethod: [],
    recentTransactions: [],
    peakHours: [],
    dailyRevenue: [],
    entryExitData: [],
    loading: true,
    error: null
  });

  // Progressive loading hook
  const {
    setComponentLoading,
    isComponentLoading,
    isAnyLoading,
    overallProgress,
    isInitialLoad,
    completeInitialLoad,
    resetLoading
  } = useProgressiveLoading();

  // Fetch dashboard data when filters change
  useEffect(() => {
    fetchDashboardData();
  }, [dateRange, selectedEntity]);

  // Function to fetch all dashboard data with progressive loading
  const fetchDashboardData = async () => {
    try {
      // Reset loading states and start progressive loading
      resetLoading();
      setDashboardData(prev => ({ ...prev, loading: true, error: null }));

      // Set initial loading states for all components
      setComponentLoading(COMPONENT_IDS.FILTERS, false); // Filters load immediately
      setComponentLoading(COMPONENT_IDS.SUMMARY_CARDS, true);
      setComponentLoading(COMPONENT_IDS.REVENUE_CHART, true);
      setComponentLoading(COMPONENT_IDS.PAYMENT_CHART, true);
      setComponentLoading(COMPONENT_IDS.ENTRY_EXIT_CHART, true);

      // Initialize an object to store our data as it comes in
      const newData = {};

      // Use Promise.all to fetch data in parallel but handle individual failures
      const fetchPromises = [
        // Fetch summary data (highest priority)
        dashboardApi.getDashboardSummary({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.summary = response.data.data;
          // Update state as soon as summary data is available
          setDashboardData(prev => ({
            ...prev,
            summary: response.data.data,
            loading: false
          }));
          // Mark summary cards as loaded
          setComponentLoading(COMPONENT_IDS.SUMMARY_CARDS, false);
        })
        .catch(error => {
          console.error('Error fetching summary data:', error);
          newData.summaryError = 'Failed to load summary data';
          setComponentLoading(COMPONENT_IDS.SUMMARY_CARDS, false);
        }),
        
        // Fetch recent transactions (medium priority)
        dashboardApi.getRecentTransactions({
          ...selectedEntity,
          limit: 5
        })
        .then(response => {
          newData.recentTransactions = response.data.data;
          // Update state as soon as transaction data is available
          setDashboardData(prev => ({
            ...prev,
            recentTransactions: response.data.data
          }));
        })
        .catch(error => {
          console.error('Error fetching recent transactions:', error);
          newData.transactionsError = 'Failed to load recent transactions';
        }),

        // Fetch revenue by payment method (lower priority)
        dashboardApi.getRevenueByPaymentMethod({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.revenueByPaymentMethod = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            revenueByPaymentMethod: response.data.data
          }));
          // Mark payment chart as loaded
          setComponentLoading(COMPONENT_IDS.PAYMENT_CHART, false);
        })
        .catch(error => {
          console.error('Error fetching payment method data:', error);
          newData.paymentMethodError = 'Failed to load payment method data';
          setComponentLoading(COMPONENT_IDS.PAYMENT_CHART, false);
        }),

        // Fetch hourly entry/exit data directly from the new API endpoint
        dashboardApi.getHourlyEntryExitData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          const entryExitData = response.data.data;
          newData.entryExitData = entryExitData;
          setDashboardData(prev => ({
            ...prev,
            entryExitData: entryExitData
          }));
          setComponentLoading(COMPONENT_IDS.ENTRY_EXIT_CHART, false);
        })
        .catch(error => {
          console.error('Error fetching hourly entry/exit data:', error);
          // Set empty data if API fails - no more fallback to mock data
          setDashboardData(prev => ({
            ...prev,
            entryExitData: []
          }));
          setComponentLoading(COMPONENT_IDS.ENTRY_EXIT_CHART, false);
        }),

        // Fetch peak hours data (lowest priority)
        dashboardApi.getPeakHoursData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.peakHours = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            peakHours: response.data.data
          }));
        })
        .catch(error => {
          console.error('Error fetching peak hours data:', error);
          newData.peakHoursError = 'Failed to load peak hours data';
        }),

        // Fetch daily revenue data (for transaction overview chart)
        dashboardApi.getDailyRevenueData({
          dateRange,
          ...selectedEntity
        })
        .then(response => {
          newData.dailyRevenue = response.data.data;
          // Update state as data becomes available
          setDashboardData(prev => ({
            ...prev,
            dailyRevenue: response.data.data
          }));
          // Mark revenue chart as loaded
          setComponentLoading(COMPONENT_IDS.REVENUE_CHART, false);
        })
        .catch(error => {
          console.error('Error fetching daily revenue data:', error);
          newData.dailyRevenueError = 'Failed to load daily revenue data';
          setComponentLoading(COMPONENT_IDS.REVENUE_CHART, false);
        })
      ];

      // Wait for all promises to settle (either resolve or reject)
      await Promise.allSettled(fetchPromises);

      // Final update to ensure all data is in sync and loading state is false
      setDashboardData(prev => ({
        ...prev,
        ...newData,
        loading: false,
        // Only set overall error if all requests failed
        error: Object.keys(newData).length === 0 ? 'Failed to load dashboard data' : null
      }));

      // Complete initial load after a short delay for smooth UX
      setTimeout(() => {
        completeInitialLoad();
      }, 500);
      
    } catch (error) {
      console.error('Error in dashboard data fetching:', error);
      setDashboardData(prev => ({ 
        ...prev, 
        loading: false, 
        error: 'Failed to load dashboard data' 
      }));
    }
  };



  // Show loading overlay for initial load
  if (isInitialLoad && dashboardData.loading) {
    return (
      <LoadingOverlay
        message="Loading Dashboard..."
        progress={overallProgress}
      />
    );
  }

  // Render error state
  if (dashboardData.error) {
    return (
      <div className="p-6">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error</p>
          <p>{dashboardData.error}</p>
          <button 
            onClick={fetchDashboardData}
            className="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 sm:p-6">
      <div className="max-w-7xl mx-auto space-y-4 sm:space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
        </div>
        
        {/* Filters Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <div className="flex flex-wrap gap-3 items-center">
              <DateRangePicker value={dateRange} onChange={setDateRange} />
              {(isSuperAdmin() || isCompanyAdmin()) && (
                <EntityFilter 
                  userRole={user?.role} 
                  selectedEntity={selectedEntity} 
                  onChange={setSelectedEntity} 
                />
              )}
              {/* Debug user role - hidden */}
              <div style={{ display: 'none' }}>
                User role: {user?.role || 'undefined'}, 
                isSuperAdmin: {isSuperAdmin() ? 'true' : 'false'}, 
                isCompanyAdmin: {isCompanyAdmin() ? 'true' : 'false'}
              </div>
            </div>
          </div>
        </div>

        {/* KPI Cards - Enhanced 3-card layout with better structure */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {isComponentLoading(COMPONENT_IDS.SUMMARY_CARDS) ? (
            // Show skeleton loaders while loading
            <>
              <EnhancedDashboardCardSkeleton type="multi" />
              <EnhancedDashboardCardSkeleton type="multi" />
              <EnhancedDashboardCardSkeleton type="multi" />
            </>
          ) : (
            // Show actual cards when loaded
            <>
              {/* Card 1: Total Revenue with Summary Metrics (Multi) */}
              <div className="animate-fadeInUp">
                <EnhancedDashboardCard
                  title="Total Revenue"
                  type="multi"
                  icon={CreditCard}
                  color="bg-blue-500"
                  metrics={[
                    {
                      label: "Revenue",
                      value: formatCurrency(dashboardData.summary.totalRevenue || 0),
                      trend: dashboardData.summary.revenueTrend,
                      isMain: true
                    },
                    {
                      label: "Entry",
                      value: formatNumber(dashboardData.summary.totalCounts?.entryCount || 0),
                      trend: dashboardData.summary.totalCounts?.entryTrend
                    },
                    {
                      label: "Exit",
                      value: formatNumber(dashboardData.summary.totalCounts?.exitCount || 0),
                      trend: dashboardData.summary.totalCounts?.exitTrend
                    },
                    {
                      label: "Remaining",
                      value: formatNumber(dashboardData.summary.totalCounts?.remainingCount || 0),
                      trend: dashboardData.summary.totalCounts?.remainingTrend
                    }
                  ]}
                />
              </div>

              {/* Card 2: Four Wheeler Metrics (Enhanced Multi Metric) */}
              <div className="animate-fadeInUp" style={{ animationDelay: '0.1s' }}>
                <EnhancedDashboardCard
                  title="Four Wheeler"
                  type="multi"
                  icon={Truck}
                  color="bg-green-500"
                  metrics={[
                    {
                      label: "Revenue",
                      value: formatCurrency(dashboardData.summary.fourWheeler?.revenue || 0),
                      trend: dashboardData.summary.fourWheeler?.revenueTrend,
                      isMain: true
                    },
                    {
                      label: "Entry",
                      value: formatNumber(dashboardData.summary.fourWheeler?.entryCount || 0),
                      trend: dashboardData.summary.fourWheeler?.entryTrend
                    },
                    {
                      label: "Exit",
                      value: formatNumber(dashboardData.summary.fourWheeler?.exitCount || 0),
                      trend: dashboardData.summary.fourWheeler?.exitTrend
                    },
                    {
                      label: "Remaining",
                      value: formatNumber(dashboardData.summary.fourWheeler?.remainingCount || 0),
                      trend: dashboardData.summary.fourWheeler?.remainingTrend
                    }
                  ]}
                />
              </div>

              {/* Card 3: Two Wheeler Metrics (Enhanced Multi Metric) */}
              <div className="animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
                <EnhancedDashboardCard
                  title="Two Wheeler"
                  type="multi"
                  icon={Bike}
                  color="bg-purple-500"
                  metrics={[
                    {
                      label: "Revenue",
                      value: formatCurrency(dashboardData.summary.twoWheeler?.revenue || 0),
                      trend: dashboardData.summary.twoWheeler?.revenueTrend,
                      isMain: true
                    },
                    {
                      label: "Entry",
                      value: formatNumber(dashboardData.summary.twoWheeler?.entryCount || 0),
                      trend: dashboardData.summary.twoWheeler?.entryTrend
                    },
                    {
                      label: "Exit",
                      value: formatNumber(dashboardData.summary.twoWheeler?.exitCount || 0),
                      trend: dashboardData.summary.twoWheeler?.exitTrend
                    },
                    {
                      label: "Remaining",
                      value: formatNumber(dashboardData.summary.twoWheeler?.remainingCount || 0),
                      trend: dashboardData.summary.twoWheeler?.remainingTrend
                    }
                  ]}
                />
              </div>
            </>
          )}
        </div>

        {/* Advanced Daily Revenue Overview - Full Width */}
        <div className="w-full">
          {isComponentLoading(COMPONENT_IDS.REVENUE_CHART) ? (
            <ChartSkeleton height={450} title="Daily Revenue Overview" />
          ) : (
            <div className="animate-fadeInUp">
              <AdvancedRevenueChart
                data={dashboardData.dailyRevenue}
                title="Daily Revenue Overview"
                height={450}
                showAnimation={true}
                dateRange={dateRange}
              />
            </div>
          )}
        </div>

        {/* Advanced Payment Method Distribution */}
        {isComponentLoading(COMPONENT_IDS.PAYMENT_CHART) ? (
          <PaymentMethodChartSkeleton />
        ) : (
          <div className="animate-slideInRight">
            <AdvancedPaymentMethodChart
              data={dashboardData.revenueByPaymentMethod || []}
              title="Revenue by Payment Method"
              showRefresh={true}
            />
          </div>
        )}

        {/* Advanced Entry/Exit Flow Analysis - Full Width */}
        <div className="w-full">
          {isComponentLoading(COMPONENT_IDS.ENTRY_EXIT_CHART) ? (
            <ChartSkeleton height={450} title="Hourly Entry/Exit Flow Analysis" />
          ) : (
            <div className="animate-fadeInUp">
              <AdvancedEntryExitChart
                data={dashboardData.entryExitData || []}
                title="Hourly Entry/Exit Flow Analysis"
                height={450}
                showAnimation={true}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};



export default DashboardHome;
