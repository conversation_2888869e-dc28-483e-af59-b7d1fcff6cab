/**
 * Permission Helper Utility
 * Provides consistent permission checking functions for controllers
 */

const db = require('../config/database');
const { responseHandler } = require('./ResponseHandler');

/**
 * Check if a user has permission to access a specific module and perform an action
 * 
 * @param {Object} user - User object from request
 * @param {string} module - Module name (e.g., 'Plazas', 'Lanes', 'ANPR', 'DigitalPayment', 'Fastag')
 * @param {string} action - Action name (e.g., 'Create', 'Edit', 'View', 'Delete')
 * @param {Object} options - Additional options
 * @param {string} options.companyId - Company ID to check access for
 * @param {string} options.plazaId - Plaza ID to check access for
 * @param {string} options.laneId - Lane ID to check access for
 * @returns {Promise<boolean>} - Whether the user has permission
 */
const hasPermission = async (user, module, action, options = {}) => {
  // SuperAdmin has all permissions
  if (user.role === 'SuperAdmin') {
    return true;
  }

  // Check if user has the specific permission
  const permissionQuery = `
    SELECT COUNT(*) as count
    FROM RolePermissions rp
    JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
    JOIN Permissions p ON smp.PermissionId = p.Id
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    WHERE rp.RoleId = @roleId 
      AND sm.Name = @module 
      AND p.Name = @action 
      AND rp.IsActive = 1
  `;

  const permissionResult = await db.query(permissionQuery, {
    roleId: user.roleId,
    module,
    action
  });

  if (permissionResult.recordset[0].count === 0) {
    return false;
  }

  // Check company access if needed
  const { companyId } = options;
  if (companyId && user.role === 'CompanyAdmin') {
    const companyAccessQuery = `
      SELECT COUNT(*) as count
      FROM UserCompany
      WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
    `;

    const companyAccessResult = await db.query(companyAccessQuery, {
      userId: user.id,
      companyId
    });

    if (companyAccessResult.recordset[0].count === 0) {
      return false;
    }
  }

  // Check plaza access if needed
  const { plazaId } = options;
  if (plazaId) {
    if (user.role === 'PlazaManager') {
      const plazaAccessQuery = `
        SELECT COUNT(*) as count
        FROM UserPlaza
        WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
      `;

      const plazaAccessResult = await db.query(plazaAccessQuery, {
        userId: user.id,
        plazaId
      });

      if (plazaAccessResult.recordset[0].count === 0) {
        return false;
      }
    } else if (user.role === 'CompanyAdmin') {
      const plazaCompanyQuery = `
        SELECT COUNT(*) as count
        FROM Plaza p
        JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
        WHERE p.Id = @plazaId AND uc.UserId = @userId AND uc.IsActive = 1
      `;

      const plazaCompanyResult = await db.query(plazaCompanyQuery, {
        userId: user.id,
        plazaId
      });

      if (plazaCompanyResult.recordset[0].count === 0) {
        return false;
      }
    }
  }

  // Check lane access if needed
  const { laneId } = options;
  if (laneId) {
    if (user.role === 'PlazaManager') {
      const lanePlazaQuery = `
        SELECT COUNT(*) as count
        FROM tblLaneDetails l
        JOIN UserPlaza up ON l.PlazaID = up.PlazaId
        WHERE l.LaneID = @laneId AND up.UserId = @userId AND up.IsActive = 1
      `;

      const lanePlazaResult = await db.query(lanePlazaQuery, {
        userId: user.id,
        laneId
      });

      if (lanePlazaResult.recordset[0].count === 0) {
        return false;
      }
    } else if (user.role === 'CompanyAdmin') {
      const laneCompanyQuery = `
        SELECT COUNT(*) as count
        FROM tblLaneDetails l
        JOIN UserCompany uc ON l.CompanyID = uc.CompanyId
        WHERE l.LaneID = @laneId AND uc.UserId = @userId AND uc.IsActive = 1
      `;

      const laneCompanyResult = await db.query(laneCompanyQuery, {
        userId: user.id,
        laneId
      });

      if (laneCompanyResult.recordset[0].count === 0) {
        return false;
      }
    }
  }

  return true;
};

/**
 * Express middleware to check permissions
 * 
 * @param {string} module - Module name
 * @param {string} action - Action name
 * @returns {Function} Express middleware function
 */
const checkPermission = (module, action) => {
  return async (req, res, next) => {
    try {
      const user = req.user;
      
      // Extract IDs from request
      const companyId = req.params.companyId || req.body.companyId || req.query.companyId || req.body.CompanyID;
      const plazaId = req.params.plazaId || req.body.plazaId || req.query.plazaId || req.body.PlazaID;
      const laneId = req.params.laneId || req.body.laneId || req.query.laneId || req.body.LaneID;
      
      const hasAccess = await hasPermission(user, module, action, { companyId, plazaId, laneId });
      
      if (!hasAccess) {
        return responseHandler.forbidden(res, `You do not have permission to ${action.toLowerCase()} this ${module.toLowerCase()}`);
      }
      
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      return responseHandler.error(res, 'Permission check failed');
    }
  };
};

module.exports = {
  hasPermission,
  checkPermission
};
