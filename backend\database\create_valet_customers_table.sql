-- Create ValetCustomers table
USE ParkwizOps;
GO

-- Create the ValetCustomers table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ValetCustomers]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ValetCustomers](
        [Id] [decimal](18, 0) IDENTITY(1,1) NOT NULL,
        [Name] [nvarchar](100) NULL,
        [MobileNumber] [nvarchar](20) NOT NULL,
        [Email] [nvarchar](100) NULL,
        [CreatedBy] [decimal](18, 0) NOT NULL,
        [CreatedOn] [datetime] NOT NULL,
        [UpdatedOn] [datetime] NULL,
        [IsActive] [bit] NOT NULL,
        CONSTRAINT [PK_ValetCustomers] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

-- Insert a test customer if none exists
IF NOT EXISTS (SELECT * FROM [dbo].[ValetCustomers] WHERE [Id] = 1)
BEGIN
    SET IDENTITY_INSERT [dbo].[ValetCustomers] ON;
    
    INSERT INTO [dbo].[ValetCustomers]
    (
        [Id],
        [Name],
        [MobileNumber],
        [Email],
        [CreatedBy],
        [CreatedOn],
        [IsActive]
    )
    VALUES
    (
        1,
        'Test Customer',
        '9876543210',
        '<EMAIL>',
        1,
        GETDATE(),
        1
    );
    
    SET IDENTITY_INSERT [dbo].[ValetCustomers] OFF;
END
GO

-- Verify the table was created
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'ValetCustomers'
ORDER BY ORDINAL_POSITION;