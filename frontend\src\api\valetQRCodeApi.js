import api from '../services/api';

/**
 * Valet QR Code API - Admin operations for QR code management
 * Handles QR code generation, scanning, and management for valet points
 */
export const valetQRCodeApi = {
  
  // =============================================
  // QR CODE GENERATION (Admin)
  // =============================================
  
  /**
   * Generate QR code for a valet point
   * POST /api/valet/qrcode/generate
   */
  generateQRCode: async (plazaValetPointId, qrType = 'VALET_POINT') => {
    try {
      console.log('Generating QR code for valet point:', plazaValetPointId);
      const response = await api.post('/valet/qrcode/generate', {
        plazaValetPointId,
        qrType
      });
      return response.data;
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  },

  /**
   * Get QR codes for a plaza
   * GET /api/valet/qrcode/plaza/:plazaId
   */
  getQRCodesByPlaza: async (plazaId, pageNumber = 1, pageSize = 10) => {
    try {
      console.log('Getting QR codes for plaza:', plazaId);
      const response = await api.get(`/valet/qrcode/plaza/${plazaId}`, {
        params: { pageNumber, pageSize }
      });
      return response.data;
    } catch (error) {
      console.error('Error getting QR codes by plaza:', error);
      throw error;
    }
  },

  /**
   * Scan/Get QR code information (Admin)
   * GET /api/valet/qrcode/scan/:qrData
   */
  scanQRCode: async (qrData) => {
    try {
      console.log('Scanning QR code:', qrData);
      const response = await api.get(`/valet/qrcode/scan/${encodeURIComponent(qrData)}`);
      return response.data;
    } catch (error) {
      console.error('Error scanning QR code:', error);
      throw error;
    }
  },

  /**
   * Deactivate QR code
   * PUT /api/valet/qrcode/:qrCodeId/deactivate
   */
  deactivateQRCode: async (qrCodeId) => {
    try {
      console.log('Deactivating QR code:', qrCodeId);
      const response = await api.put(`/valet/qrcode/${qrCodeId}/deactivate`);
      return response.data;
    } catch (error) {
      console.error('Error deactivating QR code:', error);
      throw error;
    }
  },

  // =============================================
  // QR CODE VALIDATION (Customer - Public)
  // =============================================
  
  /**
   * Validate QR code for customer (Public endpoint)
   * POST /api/valet/qrcode/validate
   */
  validateQRCodeForCustomer: async (qrData) => {
    try {
      console.log('Validating QR code for customer:', qrData);
      const response = await api.post('/valet/qrcode/validate', { qrData });
      return response.data;
    } catch (error) {
      console.error('Error validating QR code for customer:', error);
      throw error;
    }
  }
};

export default valetQRCodeApi;
