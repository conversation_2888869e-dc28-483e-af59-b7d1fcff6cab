const sql = require('mssql');
require('dotenv').config({ path: './backend/.env' });

const config = {
    server: process.env.DB_SERVER,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    options: {
        encrypt: true,
        trustServerCertificate: true
    }
};

async function checkSpecificTables() {
    try {
        await sql.connect(config);
        console.log('🔍 Checking specific table structures for stored procedures...\n');

        // Check Plaza table columns
        console.log('📋 Plaza table columns:');
        const plazaColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Plaza'
            ORDER BY ORDINAL_POSITION
        `);
        
        plazaColumns.recordset.forEach(col => {
            console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
        });

        // Check PlazaRazorPayConfiguration table columns
        console.log('\n📋 PlazaRazorPayConfiguration table columns:');
        const razorPayColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'PlazaRazorPayConfiguration'
            ORDER BY ORDINAL_POSITION
        `);
        
        razorPayColumns.recordset.forEach(col => {
            console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
        });

        // Check PlazaPhonePeConfiguration table columns
        console.log('\n📋 PlazaPhonePeConfiguration table columns:');
        const phonePeColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'PlazaPhonePeConfiguration'
            ORDER BY ORDINAL_POSITION
        `);
        
        phonePeColumns.recordset.forEach(col => {
            console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
        });

        // Check SMSNotifications table columns
        console.log('\n📋 SMSNotifications table columns:');
        const smsColumns = await sql.query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'SMSNotifications'
            ORDER BY ORDINAL_POSITION
        `);
        
        smsColumns.recordset.forEach(col => {
            console.log(`   ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
        });

        console.log('\n✅ Table structure check completed!');

    } catch (error) {
        console.error('❌ Error checking table structures:', error);
    } finally {
        await sql.close();
    }
}

checkSpecificTables();
