const db = require('../../config/database');
const sql = require('mssql');

/**
 * Valet Customer Flow Controller
 * Manages customer session state, form data persistence, and flow progression through the valet process
 */

// Initialize customer session
exports.initializeSession = async (req, res) => {
  try {
    const { plazaValetPointId, customerId } = req.body;

    if (!plazaValetPointId || !customerId) {
      return res.status(400).json({
        success: false,
        message: 'Plaza valet point ID and customer ID are required'
      });
    }

    // Validate plaza valet point
    const valetPointQuery = `
      SELECT pvp.*, p.PlazaName, c.CompanyName, p.ValetFee
      FROM PlazaValetPoint pvp
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
      WHERE pvp.Id = @plazaValetPointId AND pvp.IsActive = 1
    `;
    
    const valetPointResult = await db.query(valetPointQuery, { plazaValetPointId });
    
    if (valetPointResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Valet point not found or inactive'
      });
    }

    const valetPoint = valetPointResult.recordset[0];

    try {
      // Try to initialize session using stored procedure
      const result = await db.query(`
        DECLARE @SessionId INT;
        EXEC sp_Valet_CustomerSession_Initialize
          @CustomerId = @customerId,
          @PlazaValetPointId = @plazaValetPointId,
          @SessionData = @sessionData,
          @CreatedBy = @customerId,
          @SessionId = @SessionId OUTPUT;
        SELECT @SessionId as SessionId;
      `, {
        customerId,
        plazaValetPointId,
        sessionData: JSON.stringify({
          step: 'INITIALIZED',
          valetPoint: {
            id: valetPoint.Id,
            name: valetPoint.ValetPointName,
            plazaName: valetPoint.PlazaName,
            companyName: valetPoint.CompanyName,
            charges: valetPoint.ValetCharges
          },
          timestamp: new Date()
        })
      });

      const sessionId = result.recordset[0].SessionId;

      res.json({
        success: true,
        message: 'Customer session initialized successfully',
        data: {
          sessionId,
          customerId,
          valetPoint: {
            id: valetPoint.Id,
            name: valetPoint.ValetPointName,
            plazaName: valetPoint.PlazaName,
            companyName: valetPoint.CompanyName,
            charges: valetPoint.ValetCharges
          },
          currentStep: 'INITIALIZED',
          nextStep: 'CUSTOMER_DETAILS'
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const insertSessionQuery = `
        INSERT INTO ValetCustomerSessions (
          CustomerId, PlazaValetPointId, SessionData, CreatedBy, CreatedOn, IsActive
        )
        OUTPUT INSERTED.Id
        VALUES (
          @customerId, @plazaValetPointId, @sessionData, @createdBy, GETDATE(), 1
        )
      `;

      const sessionData = JSON.stringify({
        step: 'INITIALIZED',
        valetPoint: {
          id: valetPoint.Id,
          name: valetPoint.ValetPointName,
          plazaName: valetPoint.PlazaName,
          companyName: valetPoint.CompanyName,
          charges: valetPoint.ValetCharges
        },
        timestamp: new Date()
      });

      const sessionResult = await db.query(insertSessionQuery, {
        customerId,
        plazaValetPointId,
        sessionData,
        createdBy: customerId
      });

      const sessionId = sessionResult.recordset[0].Id;

      res.json({
        success: true,
        message: 'Customer session initialized successfully',
        data: {
          sessionId,
          customerId,
          valetPoint: {
            id: valetPoint.Id,
            name: valetPoint.ValetPointName,
            plazaName: valetPoint.PlazaName,
            companyName: valetPoint.CompanyName,
            charges: valetPoint.ValetCharges
          },
          currentStep: 'INITIALIZED',
          nextStep: 'CUSTOMER_DETAILS'
        }
      });
    }

  } catch (error) {
    console.error('Error in initializeSession:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? 'Failed to initialize session' 
        : error.message
    });
  }
};

// Update customer session with form data
exports.updateSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { step, formData, customerId } = req.body;

    if (!sessionId || !step || !customerId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID, step, and customer ID are required'
      });
    }

    // Validate session exists and belongs to customer
    const sessionQuery = `
      SELECT Id, CustomerId, SessionData, IsActive
      FROM ValetCustomerSessions
      WHERE Id = @sessionId AND CustomerId = @customerId AND IsActive = 1
    `;

    const sessionResult = await db.query(sessionQuery, { sessionId, customerId });
    
    if (sessionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Session not found or inactive'
      });
    }

    const currentSession = sessionResult.recordset[0];
    let sessionData;
    
    try {
      sessionData = JSON.parse(currentSession.SessionData);
    } catch (error) {
      sessionData = {};
    }

    // Update session data with new step and form data
    sessionData.step = step;
    sessionData.lastUpdated = new Date();
    
    if (formData) {
      sessionData.formData = { ...sessionData.formData, ...formData };
    }

    // Determine next step based on current step
    const nextStep = this.getNextStep(step);

    try {
      // Try to update session using stored procedure
      await db.query(`
        EXEC sp_Valet_CustomerSession_Update
          @SessionId = @sessionId,
          @CustomerId = @customerId,
          @SessionData = @sessionData
      `, {
        sessionId,
        customerId,
        sessionData: JSON.stringify(sessionData)
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const updateQuery = `
        UPDATE ValetCustomerSessions
        SET SessionData = @sessionData,
            UpdatedOn = GETDATE()
        WHERE Id = @sessionId
      `;

      await db.query(updateQuery, {
        sessionId,
        sessionData: JSON.stringify(sessionData)
      });
    }

    res.json({
      success: true,
      message: 'Session updated successfully',
      data: {
        sessionId: parseInt(sessionId),
        currentStep: step,
        nextStep,
        formData: sessionData.formData || {},
        updatedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error in updateSession:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? 'Failed to update session' 
        : error.message
    });
  }
};

// Get current session state
exports.getSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { customerId } = req.query;

    if (!sessionId || !customerId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID and customer ID are required'
      });
    }

    try {
      // Try to get session using stored procedure
      const result = await db.query(`
        EXEC sp_Valet_CustomerSession_Get
          @SessionId = @sessionId,
          @CustomerId = @customerId
      `, { sessionId, customerId });

      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Session not found'
        });
      }

      const session = result.recordset[0];
      let sessionData;
      
      try {
        sessionData = JSON.parse(session.SessionData);
      } catch (error) {
        sessionData = {};
      }

      const nextStep = this.getNextStep(sessionData.step);

      res.json({
        success: true,
        message: 'Session retrieved successfully',
        data: {
          sessionId: session.Id,
          customerId: session.CustomerId,
          plazaValetPointId: session.PlazaValetPointId,
          currentStep: sessionData.step || 'INITIALIZED',
          nextStep,
          formData: sessionData.formData || {},
          valetPoint: sessionData.valetPoint || {},
          status: session.Status,
          createdOn: session.CreatedOn,
          updatedOn: session.UpdatedOn
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const sessionQuery = `
        SELECT cs.*, pvp.ValetPointName, p.PlazaName, c.CompanyName
        FROM ValetCustomerSessions cs
        JOIN PlazaValetPoint pvp ON cs.PlazaValetPointId = pvp.Id
        JOIN Plaza p ON pvp.PlazaId = p.Id
        JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
        WHERE cs.Id = @sessionId AND cs.CustomerId = @customerId
      `;

      const sessionResult = await db.query(sessionQuery, { sessionId, customerId });
      
      if (sessionResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Session not found'
        });
      }

      const session = sessionResult.recordset[0];
      let sessionData;
      
      try {
        sessionData = JSON.parse(session.SessionData);
      } catch (error) {
        sessionData = {};
      }

      const nextStep = this.getNextStep(sessionData.step);

      res.json({
        success: true,
        message: 'Session retrieved successfully',
        data: {
          sessionId: session.Id,
          customerId: session.CustomerId,
          plazaValetPointId: session.PlazaValetPointId,
          currentStep: sessionData.step || 'INITIALIZED',
          nextStep,
          formData: sessionData.formData || {},
          valetPoint: {
            id: session.PlazaValetPointId,
            name: session.ValetPointName,
            plazaName: session.PlazaName,
            companyName: session.CompanyName
          },
          status: session.Status,
          createdOn: session.CreatedOn,
          updatedOn: session.UpdatedOn
        }
      });
    }

  } catch (error) {
    console.error('Error in getSession:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to retrieve session'
        : error.message
    });
  }
};

// Complete customer session (when transaction is created)
exports.completeSession = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { transactionId, customerId } = req.body;

    if (!sessionId || !transactionId || !customerId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID, transaction ID, and customer ID are required'
      });
    }

    try {
      // Try to complete session using stored procedure
      await db.query(`
        EXEC sp_Valet_CustomerSession_Complete
          @SessionId = @sessionId,
          @CustomerId = @customerId,
          @TransactionId = @transactionId
      `, {
        sessionId,
        customerId,
        transactionId
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      const updateQuery = `
        UPDATE ValetCustomerSessions
        SET IsActive = 0,
            UpdatedOn = GETDATE()
        WHERE Id = @sessionId AND CustomerId = @customerId
      `;

      await db.query(updateQuery, {
        sessionId,
        transactionId,
        customerId
      });
    }

    res.json({
      success: true,
      message: 'Session completed successfully',
      data: {
        sessionId: parseInt(sessionId),
        transactionId,
        status: 'COMPLETED',
        completedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error in completeSession:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to complete session'
        : error.message
    });
  }
};

// Validate session step progression
exports.validateStepProgression = async (req, res) => {
  try {
    const { sessionId, targetStep, customerId } = req.body;

    if (!sessionId || !targetStep || !customerId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID, target step, and customer ID are required'
      });
    }

    // Get current session
    const sessionQuery = `
      SELECT SessionData, IsActive
      FROM ValetCustomerSessions
      WHERE Id = @sessionId AND CustomerId = @customerId
    `;

    const sessionResult = await db.query(sessionQuery, { sessionId, customerId });

    if (sessionResult.recordset.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Session not found'
      });
    }

    const session = sessionResult.recordset[0];

    if (session.Status !== 'ACTIVE') {
      return res.status(400).json({
        success: false,
        message: 'Session is not active'
      });
    }

    let sessionData;
    try {
      sessionData = JSON.parse(session.SessionData);
    } catch (error) {
      sessionData = { step: 'INITIALIZED' };
    }

    const currentStep = sessionData.step || 'INITIALIZED';
    const isValidProgression = this.isValidStepProgression(currentStep, targetStep);

    if (!isValidProgression) {
      return res.status(400).json({
        success: false,
        message: `Invalid step progression from ${currentStep} to ${targetStep}`,
        data: {
          currentStep,
          targetStep,
          allowedNextSteps: this.getAllowedNextSteps(currentStep)
        }
      });
    }

    res.json({
      success: true,
      message: 'Step progression is valid',
      data: {
        currentStep,
        targetStep,
        isValid: true
      }
    });

  } catch (error) {
    console.error('Error in validateStepProgression:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to validate step progression'
        : error.message
    });
  }
};

// Get customer session history
exports.getSessionHistory = async (req, res) => {
  try {
    const { customerId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'Customer ID is required'
      });
    }

    const offset = (page - 1) * limit;

    const historyQuery = `
      SELECT
        cs.Id as SessionId,
        cs.PlazaValetPointId,
        cs.Status,
        cs.CreatedOn,
        cs.CompletedOn,
        cs.TransactionId,
        pvp.ValetPointName,
        p.PlazaName,
        c.CompanyName,
        pt.PNR,
        pt.Status as TransactionStatus
      FROM ValetCustomerSessions cs
      JOIN PlazaValetPoint pvp ON cs.PlazaValetPointId = pvp.Id
      JOIN Plaza p ON pvp.PlazaId = p.Id
      JOIN tblCompanyMaster c ON pvp.CompanyId = c.Id
      WHERE cs.CustomerId = @customerId
      ORDER BY cs.CreatedOn DESC
      OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
    `;

    const result = await db.query(historyQuery, {
      customerId,
      offset: parseInt(offset),
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      message: 'Session history retrieved successfully',
      data: {
        sessions: result.recordset,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: result.recordset.length
        }
      }
    });

  } catch (error) {
    console.error('Error in getSessionHistory:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to retrieve session history'
        : error.message
    });
  }
};

// Helper method to get next step in the flow
exports.getNextStep = (currentStep) => {
  const stepFlow = {
    'INITIALIZED': 'CUSTOMER_DETAILS',
    'CUSTOMER_DETAILS': 'VEHICLE_DETAILS',
    'VEHICLE_DETAILS': 'PAYMENT_SELECTION',
    'PAYMENT_SELECTION': 'PAYMENT_PROCESSING',
    'PAYMENT_PROCESSING': 'PAYMENT_SUCCESS',
    'PAYMENT_SUCCESS': 'VEHICLE_HANDOVER',
    'VEHICLE_HANDOVER': 'COMPLETED',
    'COMPLETED': null
  };

  return stepFlow[currentStep] || null;
};

// Helper method to get allowed next steps
exports.getAllowedNextSteps = (currentStep) => {
  const allowedSteps = {
    'INITIALIZED': ['CUSTOMER_DETAILS'],
    'CUSTOMER_DETAILS': ['VEHICLE_DETAILS', 'CUSTOMER_DETAILS'], // Allow going back
    'VEHICLE_DETAILS': ['PAYMENT_SELECTION', 'CUSTOMER_DETAILS'],
    'PAYMENT_SELECTION': ['PAYMENT_PROCESSING', 'VEHICLE_DETAILS'],
    'PAYMENT_PROCESSING': ['PAYMENT_SUCCESS', 'PAYMENT_FAILED', 'PAYMENT_SELECTION'],
    'PAYMENT_SUCCESS': ['VEHICLE_HANDOVER'],
    'PAYMENT_FAILED': ['PAYMENT_SELECTION'],
    'VEHICLE_HANDOVER': ['COMPLETED'],
    'COMPLETED': []
  };

  return allowedSteps[currentStep] || [];
};

// Helper method to validate step progression
exports.isValidStepProgression = (currentStep, targetStep) => {
  const allowedNextSteps = this.getAllowedNextSteps(currentStep);
  return allowedNextSteps.includes(targetStep);
};
