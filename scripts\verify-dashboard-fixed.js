require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function verifyDashboardData() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Date range from the log
    const startDateStr = '2025-05-31T18:30:00.000Z';
    const endDateStr = '2025-06-30T18:29:59.999Z';

    console.log('Verifying dashboard data for date range:');
    console.log('- Start date:', startDateStr);
    console.log('- End date:', endDateStr);

    // Check total metrics
    const totalResult = await sql.query(`
      SELECT
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${startDateStr}' AND '${endDateStr}'
    `);
    
    console.log('\nTotal Metrics:');
    console.log('- Transaction Count:', totalResult.recordset[0].TransactionCount);
    console.log('- Total Revenue: ₹' + totalResult.recordset[0].TotalRevenue.toFixed(2));

    // Check revenue by payment method
    const paymentMethodResult = await sql.query(`
      SELECT
        PaymentMode,
        COUNT(*) as TransactionCount,
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${startDateStr}' AND '${endDateStr}'
      GROUP BY PaymentMode
      ORDER BY Revenue DESC
    `);
    
    console.log('\nRevenue by Payment Method:');
    paymentMethodResult.recordset.forEach(method => {
      console.log(`- ${method.PaymentMode}: ${method.TransactionCount} transactions, ₹${method.Revenue.toFixed(2)}`);
    });

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

verifyDashboardData();