const sql = require('mssql');
require('dotenv').config();

const config = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_DATABASE,
  options: {
    encrypt: true,
    trustServerCertificate: false
  }
};

async function updateStoredProcedure() {
  try {
    console.log('Connecting to database...');
    await sql.connect(config);
    console.log('Connected successfully');

    const procedureSQL = `
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_QRCode_GetByData]
    @QRCodeData NVARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Validation
        IF @QRCodeData IS NULL OR LTRIM(RTRIM(@QRCodeData)) = ''
        BEGIN
            RAISERROR('QR Code data is required', 16, 1);
            RETURN -1;
        END

        -- Get QR code details with plaza and valet point information
        SELECT
            vqr.[Id],
            vqr.[PlazaValetPointId],
            vqr.[PlazaId],
            vqr.[QRCodeData],
            vqr.[IsActive],
            vqr.[CreatedBy],
            vqr.[CreatedOn],
            p.[PlazaName],
            p.[Id] as PlazaId,
            c.[CompanyName],
            c.[Id] as CompanyId,
            pvp.[ValetPointName],
            pvp.[Id] as ValetPointId,
            pvp.[ParkingFee],
            pvp.[IsActive] as ValetPointActive
        FROM [dbo].[ValetQRCodes] vqr
        JOIN [dbo].[Plaza] p ON vqr.[PlazaId] = p.[Id]
        JOIN [dbo].[tblCompanyMaster] c ON p.[CompanyId] = c.[Id]
        LEFT JOIN [dbo].[PlazaValetPoint] pvp ON vqr.[PlazaValetPointId] = pvp.[Id]
        WHERE vqr.[QRCodeData] = @QRCodeData AND vqr.[IsActive] = 1 AND p.[IsActive] = 1
        ORDER BY vqr.[CreatedOn] DESC;

        -- Check if QR code exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'QR Code not found' AS Message, 0 AS Success;
        END

    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            ERROR_SEVERITY() AS ErrorSeverity,
            ERROR_STATE() AS ErrorState,
            ERROR_PROCEDURE() AS ErrorProcedure,
            ERROR_LINE() AS ErrorLine;
    END CATCH
END`;

    console.log('Executing stored procedure update...');
    await sql.query(procedureSQL);
    console.log('✅ Stored procedure updated successfully!');

    // Test the procedure
    console.log('Testing the updated procedure...');
    const testResult = await sql.query`EXEC sp_Valet_QRCode_GetByData @QRCodeData = '1_1751953664734_41ea95a1'`;
    console.log('Test result:', testResult.recordset);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await sql.close();
    console.log('Database connection closed');
  }
}

updateStoredProcedure();
