// backend/src/services/EmailService.js
const { createTransporter, emailConfig, getEmailTemplate } = require('../config/emailConfig');
const db = require('../config/database');

class EmailService {
  constructor() {
    this.transporter = createTransporter();
  }

  /**
   * Send email with retry logic
   */
  async sendEmail(to, subject, html, options = {}) {
    const mailOptions = {
      from: `${emailConfig.from.name} <${emailConfig.from.address}>`,
      to,
      subject,
      html,
      ...options
    };

    let lastError;
    for (let attempt = 1; attempt <= emailConfig.retry.attempts; attempt++) {
      try {
        const result = await this.transporter.sendMail(mailOptions);
        
        // Log successful email
        await this.logEmail(to, subject, html, 'sent', null, attempt);
        
        console.log(`✅ Email sent successfully to ${to} (attempt ${attempt})`);
        return result;
      } catch (error) {
        lastError = error;
        console.error(`❌ Email attempt ${attempt} failed:`, error.message);
        
        if (attempt < emailConfig.retry.attempts) {
          const delay = emailConfig.retry.delay * Math.pow(emailConfig.retry.backoff, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Log failed email
    await this.logEmail(to, subject, html, 'failed', lastError.message, emailConfig.retry.attempts);
    throw lastError;
  }

  /**
   * Log email attempts to database
   */
  async logEmail(recipientEmail, subject, body, status, errorMessage = null, retryCount = 0) {
    try {
      const query = `
        INSERT INTO EmailNotifications (
          RecipientEmail, Subject, Body, EmailType, Status, RetryCount, 
          ErrorMessage, CreatedAt
        ) VALUES (
          @recipientEmail, @subject, @body, @emailType, @status, @retryCount,
          @errorMessage, GETDATE()
        )
      `;

      await db.query(query, {
        recipientEmail,
        subject,
        body: body.substring(0, 4000), // Limit body length
        emailType: this.extractEmailType(subject),
        status,
        retryCount,
        errorMessage
      });
    } catch (error) {
      console.error('Failed to log email:', error.message);
    }
  }

  /**
   * Extract email type from subject
   */
  extractEmailType(subject) {
    if (subject.includes('Welcome')) return 'welcome';
    if (subject.includes('Activity')) return 'activity';
    if (subject.includes('Deletion')) return 'deletion';
    if (subject.includes('Unauthorized')) return 'unauthorized';
    return 'general';
  }

  /**
   * Send welcome email to new user
   */
  async sendWelcomeEmail(userEmail, userData, credentials = null) {
    const template = getEmailTemplate('welcome');
    const subject = template.subject;
    
    const html = this.generateWelcomeEmailHTML(userData, credentials);
    
    return await this.sendEmail(userEmail, subject, html);
  }

  /**
   * Send activity notification email
   */
  async sendActivityNotification(recipientEmail, actorData, action, entityType, entityData) {
    const template = getEmailTemplate('activity');
    const subject = template.subject
      .replace('{action}', action)
      .replace('{entity}', entityType);
    
    const html = this.generateActivityEmailHTML(actorData, action, entityType, entityData);
    
    return await this.sendEmail(recipientEmail, subject, html);
  }

  /**
   * Send deletion alert email
   */
  async sendDeletionAlert(recipientEmail, actorData, entityType, entityData) {
    const template = getEmailTemplate('deletion');
    const subject = template.subject.replace('{entity}', entityType);
    
    const html = this.generateDeletionEmailHTML(actorData, entityType, entityData);
    
    return await this.sendEmail(recipientEmail, subject, html);
  }

  /**
   * Generate welcome email HTML
   */
  generateWelcomeEmailHTML(userData, credentials) {
    const { baseUrl, companyName, supportEmail } = emailConfig.templates;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Welcome to ${companyName}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .credentials { background: #eff6ff; border: 1px solid #bfdbfe; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .footer { background: #374151; color: white; padding: 15px; text-align: center; font-size: 12px; }
          .btn { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Welcome to ${companyName}</h1>
          </div>
          <div class="content">
            <h2>Hello ${userData.FirstName} ${userData.LastName},</h2>
            <p>Your account has been successfully created with <strong>${userData.RoleName}</strong> privileges.</p>
            
            ${credentials ? `
            <div class="credentials">
              <h3>🔐 Your Login Credentials</h3>
              <p><strong>Username:</strong> ${userData.Username}</p>
              <p><strong>Password:</strong> ${credentials.password}</p>
              <p><strong>Role:</strong> ${userData.RoleName}</p>
            </div>
            <p><em>⚠️ Please change your password after first login for security.</em></p>
            ` : ''}
            
            <p>You can now access the system using the link below:</p>
            <a href="${baseUrl}/login" class="btn">Login to ${companyName}</a>
            
            <h3>📋 Your Access Includes:</h3>
            <ul>
              ${userData.RoleName === 'SuperAdmin' ? `
                <li>✅ Full system administration</li>
                <li>✅ Company and user management</li>
                <li>✅ All plaza and lane operations</li>
                <li>✅ System configuration</li>
              ` : userData.RoleName === 'CompanyAdmin' ? `
                <li>✅ Company plaza management</li>
                <li>✅ User management within company</li>
                <li>✅ Lane and ANPR configuration</li>
                <li>✅ Digital payment settings</li>
              ` : `
                <li>✅ Plaza operations management</li>
                <li>✅ Lane monitoring and control</li>
                <li>✅ ANPR and payment systems</li>
                <li>✅ Operational reporting</li>
              `}
            </ul>
          </div>
          <div class="footer">
            <p>Need help? Contact us at <a href="mailto:${supportEmail}" style="color: #60a5fa;">${supportEmail}</a></p>
            <p>&copy; 2024 ${companyName}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate activity notification email HTML
   */
  generateActivityEmailHTML(actorData, action, entityType, entityData) {
    const { companyName, supportEmail } = emailConfig.templates;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Activity Notification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .activity-details { background: #ecfdf5; border: 1px solid #a7f3d0; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .footer { background: #374151; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 Activity Notification</h1>
          </div>
          <div class="content">
            <h2>New ${action} Activity</h2>
            <p>A ${entityType} has been ${action}d in the system.</p>
            
            <div class="activity-details">
              <h3>📋 Activity Details</h3>
              <p><strong>Action:</strong> ${action.charAt(0).toUpperCase() + action.slice(1)}</p>
              <p><strong>Entity Type:</strong> ${entityType}</p>
              <p><strong>Performed By:</strong> ${actorData.FirstName} ${actorData.LastName} (${actorData.Username})</p>
              <p><strong>Role:</strong> ${actorData.RoleName}</p>
              <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
              ${entityData ? `<p><strong>Entity Details:</strong> ${JSON.stringify(entityData, null, 2)}</p>` : ''}
            </div>
            
            <p>This notification is sent to keep you informed of activities within your scope of management.</p>
          </div>
          <div class="footer">
            <p>This is an automated notification from ${companyName}</p>
            <p>Contact: <a href="mailto:${supportEmail}" style="color: #60a5fa;">${supportEmail}</a></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate deletion alert email HTML
   */
  generateDeletionEmailHTML(actorData, entityType, entityData) {
    const { companyName, supportEmail } = emailConfig.templates;
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Deletion Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9fafb; }
          .alert-details { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 15px 0; }
          .footer { background: #374151; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⚠️ Deletion Alert</h1>
          </div>
          <div class="content">
            <h2>🚨 ${entityType} Deleted</h2>
            <p>A ${entityType} has been permanently deleted from the system.</p>
            
            <div class="alert-details">
              <h3>🗑️ Deletion Details</h3>
              <p><strong>Entity Type:</strong> ${entityType}</p>
              <p><strong>Deleted By:</strong> ${actorData.FirstName} ${actorData.LastName} (${actorData.Username})</p>
              <p><strong>Role:</strong> ${actorData.RoleName}</p>
              <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
              ${entityData ? `<p><strong>Deleted Entity:</strong> ${JSON.stringify(entityData, null, 2)}</p>` : ''}
            </div>
            
            <p><strong>⚠️ Important:</strong> This action cannot be undone. Please verify this deletion was authorized.</p>
            <p>If this deletion was unauthorized, please contact the system administrator immediately.</p>
          </div>
          <div class="footer">
            <p>This is an automated security alert from ${companyName}</p>
            <p>Contact: <a href="mailto:${supportEmail}" style="color: #60a5fa;">${supportEmail}</a></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

module.exports = new EmailService();
