// Script to execute the mock data SQL script
const fs = require('fs');
const path = require('path');
const sql = require('mssql');
const dotenv = require('dotenv');

// Load environment variables from backend/.env
dotenv.config({ path: path.join(__dirname, 'backend', '.env') });

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    enableArithAbort: true,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT || '30000'),
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT || '30000')
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX || '10'),
    min: parseInt(process.env.DB_POOL_MIN || '0'),
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT || '30000')
  }
};

async function executeSqlScript() {
  try {
    console.log('Connecting to database...');
    console.log('Database config:', {
      server: dbConfig.server,
      database: dbConfig.database,
      user: dbConfig.user,
      port: dbConfig.port,
      options: {
        encrypt: dbConfig.options.encrypt,
        trustServerCertificate: dbConfig.options.trustServerCertificate
      }
    });
    
    const pool = await sql.connect(dbConfig);
    console.log('Connected to database successfully');

    // Read the SQL script
    const scriptPath = path.join(__dirname, 'mock_data_script.sql');
    console.log(`Reading SQL script from: ${scriptPath}`);
    
    if (!fs.existsSync(scriptPath)) {
      console.error(`SQL script file not found at: ${scriptPath}`);
      return;
    }
    
    const sqlScript = fs.readFileSync(scriptPath, 'utf8');
    console.log(`SQL script loaded, size: ${sqlScript.length} bytes`);

    // Split the script into individual statements
    const statements = sqlScript.split(/;\s*$/m).filter(stmt => stmt.trim());

    console.log(`Found ${statements.length} SQL statements to execute`);

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const stmt = statements[i];
      if (stmt.trim()) {
        try {
          console.log(`Executing statement ${i + 1}/${statements.length}...`);
          await pool.request().query(stmt);
          console.log(`Statement ${i + 1} executed successfully`);
        } catch (err) {
          console.error(`Error executing statement ${i + 1}:`, err);
          console.error('Statement:', stmt.substring(0, 200) + '...');
          
          // Continue with next statement
          console.log('Continuing with next statement...');
        }
      }
    }

    console.log('All statements executed. Closing connection...');
    await sql.close();
    console.log('Connection closed');
  } catch (err) {
    console.error('Database connection error:', err);
    console.error('Error details:', {
      message: err.message,
      code: err.code,
      number: err.number,
      state: err.state,
      class: err.class,
      lineNumber: err.lineNumber,
      serverName: err.serverName
    });
  }
}

executeSqlScript();