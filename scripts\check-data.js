const db = require('./src/config/database');

async function checkDashboardQuery() {
  try {
    // Use the same date range calculation as in the controller
    const referenceDate = new Date('2025-06-14T10:29:18.000Z');
    const startDate = new Date(referenceDate);
    startDate.setDate(1); // First day of the month
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(referenceDate);
    endDate.setMonth(endDate.getMonth() + 1);
    endDate.setDate(0); // Last day of the month
    endDate.setHours(23, 59, 59, 999);
    
    console.log(`Checking data for month: ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Test the exact query from the controller
    const summaryQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data t
      JOIN Plaza p ON t.PlazaCode = p.PlazaCode
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
    `;
    
    try {
      const summaryResult = await db.query(summaryQuery, { startDate, endDate });
      console.log('Summary result:', JSON.stringify(summaryResult.recordset[0], null, 2));
    } catch (err) {
      console.error('Error executing summary query:', err);
    }
    
    // Check if the join with Plaza table is causing issues
    console.log('Checking if Plaza table has matching PlazaCodes...');
    const plazaCodesQuery = `
      SELECT DISTINCT t.PlazaCode, t.PlazaName, p.PlazaCode as MatchingPlazaCode
      FROM tblParkwiz_Parking_Data t
      LEFT JOIN Plaza p ON t.PlazaCode = p.PlazaCode
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
    `;
    
    try {
      const plazaCodesResult = await db.query(plazaCodesQuery, { startDate, endDate });
      console.log('Plaza codes check:', JSON.stringify(plazaCodesResult.recordset, null, 2));
    } catch (err) {
      console.error('Error executing plaza codes query:', err);
    }
    
    // Try the query without the join to see if that's the issue
    const noJoinQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount,
        COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
        ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
    `;
    
    try {
      const noJoinResult = await db.query(noJoinQuery, { startDate, endDate });
      console.log('Query without join result:', JSON.stringify(noJoinResult.recordset[0], null, 2));
    } catch (err) {
      console.error('Error executing query without join:', err);
    }
    
  } catch (err) {
    console.error('Error:', err);
  } finally {
    await db.closePool();
  }
}

checkDashboardQuery();