-- =============================================
-- Valet QR Code Management Stored Procedures
-- Created for comprehensive QR code management
-- =============================================

-- =============================================
-- 1. CREATE - Generate QR Code
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_QRCode_Generate]
    @PlazaValetPointId INT,
    @PlazaId INT,
    @QRCodeData NVARCHAR(500),
    @CreatedBy INT,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PlazaValetPointId IS NULL OR @PlazaValetPointId <= 0
        BEGIN
            RAISERROR('Plaza Valet Point ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('Plaza ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        IF @QRCodeData IS NULL OR LTRIM(RTRIM(@QRCodeData)) = ''
        BEGIN
            RAISERROR('QR Code data is required', 16, 1);
            RETURN -1;
        END

        IF @CreatedBy IS NULL OR @CreatedBy <= 0
        BEGIN
            RAISERROR('CreatedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Insert new QR code
        INSERT INTO [dbo].[ValetQRCodes]
        (
            [PlazaValetPointId],
            [PlazaId],
            [QRCodeData],
            [IsActive],
            [CreatedBy],
            [CreatedOn]
        )
        VALUES
        (
            @PlazaValetPointId,
            @PlazaId,
            @QRCodeData,
            1,
            @CreatedBy,
            GETDATE()
        );
        
        SET @NewId = SCOPE_IDENTITY();
        
        -- Return success with QR code details
        SELECT
            @NewId AS Id,
            @PlazaValetPointId AS PlazaValetPointId,
            @PlazaId AS PlazaId,
            @QRCodeData AS QRCodeData,
            'QR Code generated successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @NewId = -1;
    END CATCH
END
GO

-- =============================================
-- 2. READ - Get QR Code by data
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_QRCode_GetByData]
    @QRCodeData NVARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Validation
        IF @QRCodeData IS NULL OR LTRIM(RTRIM(@QRCodeData)) = ''
        BEGIN
            RAISERROR('QR Code data is required', 16, 1);
            RETURN -1;
        END

        -- Get QR code details with plaza and valet point information
        SELECT
            vqr.[Id],
            vqr.[PlazaValetPointId],
            vqr.[PlazaId],
            vqr.[QRCodeData],
            vqr.[IsActive],
            vqr.[CreatedBy],
            vqr.[CreatedOn],
            p.[PlazaName],
            p.[Id] as PlazaId,
            c.[CompanyName],
            c.[Id] as CompanyId,
            pvp.[ValetPointName],
            pvp.[Id] as ValetPointId,
            pvp.[ParkingFee],
            pvp.[IsActive] as ValetPointActive
        FROM [dbo].[ValetQRCodes] vqr
        JOIN [dbo].[Plaza] p ON vqr.[PlazaId] = p.[Id]
        JOIN [dbo].[tblCompanyMaster] c ON p.[CompanyId] = c.[Id]
        LEFT JOIN [dbo].[PlazaValetPoint] pvp ON vqr.[PlazaValetPointId] = pvp.[Id]
        WHERE vqr.[QRCodeData] = @QRCodeData AND vqr.[IsActive] = 1 AND p.[IsActive] = 1
        ORDER BY vqr.[CreatedOn] DESC;

        -- Check if QR code exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'QR Code not found' AS Message, 0 AS Success;
        END

    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 3. READ - Get all QR codes for a plaza
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_QRCode_GetByPlaza]
    @PlazaId INT,
    @IsActive BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Validation
        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('Plaza ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        -- Get QR codes for plaza
        SELECT
            [Id],
            [PlazaValetPointId],
            [PlazaId],
            [QRCodeData],
            [IsActive],
            [CreatedBy],
            [CreatedOn]
        FROM [dbo].[ValetQRCodes]
        WHERE [PlazaId] = @PlazaId
        AND (@IsActive IS NULL OR [IsActive] = @IsActive)
        ORDER BY [CreatedOn] DESC;

    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO
