const sql = require('mssql');

async function checkPermissionStructure() {
  try {
    const config = {
      user: 'hparkwiz',
      password: 'Parkwiz@2020',
      server: 'parkwizvms.database.windows.net',
      database: 'ParkwizOps',
      port: 1433,
      options: {
        encrypt: true,
        trustServerCertificate: true
      },
      requestTimeout: 30000,
      connectionTimeout: 30000
    };

    console.log('🔍 Connecting to database...');
    const pool = await sql.connect(config);
    
    console.log('✅ Connected! Checking permission structure...\n');
    
    // Check SubModules table structure
    try {
      const subModulesStructure = await pool.request().query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'SubModules'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log('🏗️ SubModules table structure:');
      subModulesStructure.recordset.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Check sample SubModules data
      const subModulesData = await pool.request().query('SELECT TOP 5 * FROM SubModules');
      console.log('\n📊 Sample SubModules data:');
      subModulesData.recordset.forEach(row => {
        console.log(`  - ID: ${row.Id}, Name: ${row.Name}, ModuleId: ${row.ModuleId}, Path: ${row.Path}, Active: ${row.IsActive}`);
      });
      
    } catch (error) {
      console.log('❌ Error checking SubModules:', error.message);
    }
    
    // Check Permissions table structure
    try {
      const permissionsStructure = await pool.request().query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Permissions'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log('\n🏗️ Permissions table structure:');
      permissionsStructure.recordset.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Check sample Permissions data
      const permissionsData = await pool.request().query('SELECT TOP 5 * FROM Permissions');
      console.log('\n📊 Sample Permissions data:');
      permissionsData.recordset.forEach(row => {
        console.log(`  - ID: ${row.Id}, Name: ${row.Name}, Description: ${row.Description}, Active: ${row.IsActive}`);
      });
      
    } catch (error) {
      console.log('❌ Error checking Permissions:', error.message);
    }
    
    // Check SubModulePermissions table structure
    try {
      const subModulePermissionsStructure = await pool.request().query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'SubModulePermissions'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log('\n🏗️ SubModulePermissions table structure:');
      subModulePermissionsStructure.recordset.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Check sample SubModulePermissions data
      const subModulePermissionsData = await pool.request().query('SELECT TOP 5 * FROM SubModulePermissions');
      console.log('\n📊 Sample SubModulePermissions data:');
      subModulePermissionsData.recordset.forEach(row => {
        console.log(`  - ID: ${row.Id}, SubModuleId: ${row.SubModuleId}, PermissionId: ${row.PermissionId}, Active: ${row.IsActive}`);
      });
      
    } catch (error) {
      console.log('❌ Error checking SubModulePermissions:', error.message);
    }
    
    // Check Roles table structure
    try {
      const rolesStructure = await pool.request().query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Roles'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log('\n🏗️ Roles table structure:');
      rolesStructure.recordset.forEach(col => {
        console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
      });
      
      // Check sample Roles data
      const rolesData = await pool.request().query('SELECT TOP 5 * FROM Roles');
      console.log('\n📊 Sample Roles data:');
      rolesData.recordset.forEach(row => {
        console.log(`  - ID: ${row.Id}, Name: ${row.Name}, Active: ${row.IsActive}`);
      });
      
    } catch (error) {
      console.log('❌ Error checking Roles:', error.message);
    }
    
    // Test the actual query that's failing
    console.log('\n🧪 Testing the modules-tree query...');
    try {
      const testQuery = `
        SELECT 
          m.Id as ModuleId,
          m.Name as ModuleName,
          m.Description as ModuleDescription,
          m.Icon as ModuleIcon,
          m.DisplayOrder as ModuleDisplayOrder,
          m.IsActive as ModuleIsActive,
          
          sm.Id as SubModuleId,
          sm.Name as SubModuleName,
          sm.Path as SubModuleRoute,
          sm.Icon as SubModuleIcon,
          sm.IsActive as SubModuleIsActive,
          
          p.Id as PermissionId,
          p.Name as PermissionName,
          p.Description as PermissionDescription,
          
          smp.Id as SubModulePermissionId
          
        FROM Modules m
        LEFT JOIN SubModules sm ON m.Id = sm.ModuleId AND sm.IsActive = 1
        LEFT JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId AND smp.IsActive = 1
        LEFT JOIN Permissions p ON smp.PermissionId = p.Id AND p.IsActive = 1
        WHERE m.IsActive = 1
        ORDER BY m.DisplayOrder, sm.Id, p.Name
      `;
      
      const testResult = await pool.request().query(testQuery);
      console.log(`✅ Query executed successfully! Returned ${testResult.recordset.length} rows`);
      
      if (testResult.recordset.length > 0) {
        console.log('\n📊 First few results:');
        testResult.recordset.slice(0, 3).forEach(row => {
          console.log(`  - Module: ${row.ModuleName}, SubModule: ${row.SubModuleName || 'NULL'}, Permission: ${row.PermissionName || 'NULL'}`);
        });
      }
      
    } catch (error) {
      console.log('❌ Query test failed:', error.message);
    }
    
    await pool.close();
    console.log('\n✅ Permission structure check completed');
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  }
}

checkPermissionStructure().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('❌ Script error:', error.message);
  process.exit(1);
});