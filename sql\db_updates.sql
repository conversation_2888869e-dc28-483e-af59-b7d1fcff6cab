-- Create UserCompany table for company-level access control
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'UserCompany')
BEGIN
    CREATE TABLE UserCompany (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT FOREIGN KEY REFERENCES Users(Id),
        CompanyId INT FOREIGN KEY REFERENCES tblCompanyMaster(Id),
        IsActive BIT DEFAULT 1,
        CreatedBy INT,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedOn DATETIME
    );
    PRINT 'UserCompany table created successfully';
END
ELSE
BEGIN
    PRINT 'UserCompany table already exists';
END

-- Create Permissions table for granular permission control
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Permissions')
BEGIN
    CREATE TABLE Permissions (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        Description NVARCHAR(255),
        IsActive BIT DEFAULT 1,
        CreatedBy INT,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedOn DATETIME
    );
    PRINT 'Permissions table created successfully';
END
ELSE
BEGIN
    PRINT 'Permissions table already exists';
END

-- Create SubModulePermissions table to link permissions to submodules
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SubModulePermissions')
BEGIN
    CREATE TABLE SubModulePermissions (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        SubModuleId INT FOREIGN KEY REFERENCES SubModules(Id),
        PermissionId INT FOREIGN KEY REFERENCES Permissions(Id),
        IsActive BIT DEFAULT 1,
        CreatedBy INT,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedOn DATETIME
    );
    PRINT 'SubModulePermissions table created successfully';
END
ELSE
BEGIN
    PRINT 'SubModulePermissions table already exists';
END

-- Create RolePermissions table to link roles with specific permissions
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'RolePermissions')
BEGIN
    CREATE TABLE RolePermissions (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        RoleId INT FOREIGN KEY REFERENCES Roles(Id),
        SubModulePermissionId INT FOREIGN KEY REFERENCES SubModulePermissions(Id),
        IsActive BIT DEFAULT 1,
        CreatedBy INT,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedOn DATETIME
    );
    PRINT 'RolePermissions table created successfully';
END
ELSE
BEGIN
    PRINT 'RolePermissions table already exists';
END

-- Insert basic permissions
IF NOT EXISTS (SELECT 1 FROM Permissions WHERE Name = 'View')
BEGIN
    INSERT INTO Permissions (Name, Description, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ('View', 'Can view records', 1, 1, GETDATE()),
    ('Create', 'Can create new records', 1, 1, GETDATE()),
    ('Edit', 'Can edit existing records', 1, 1, GETDATE()),
    ('Delete', 'Can delete records', 1, 1, GETDATE()),
    ('Export', 'Can export data', 1, 1, GETDATE()),
    ('Import', 'Can import data', 1, 1, GETDATE()),
    ('Approve', 'Can approve actions', 1, 1, GETDATE());
    PRINT 'Basic permissions inserted successfully';
END
ELSE
BEGIN
    PRINT 'Basic permissions already exist';
END

-- Insert predefined roles if they don't exist
IF NOT EXISTS (SELECT 1 FROM Roles WHERE Name = 'SuperAdmin')
BEGIN
    INSERT INTO Roles (Name, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ('SuperAdmin', 1, 1, GETDATE()),
    ('CompanyAdmin', 1, 1, GETDATE()),
    ('PlazaManager', 1, 1, GETDATE());
    PRINT 'Predefined roles inserted successfully';
END
ELSE
BEGIN
    PRINT 'Predefined roles already exist';
END

-- Create a SuperAdmin user if it doesn't exist
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'superadmin')
BEGIN
    -- Note: Password is 'Admin@123' - you should use a proper hashing mechanism in production
    INSERT INTO Users (RoleId, Username, Password, FirstName, LastName, Email, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Roles WHERE Name = 'SuperAdmin'), 
     'superadmin', 
     'Admin@123', 
     'Super', 
     'Admin', 
     '<EMAIL>', 
     1, 
     1, 
     GETDATE());
    PRINT 'SuperAdmin user created successfully';
END
ELSE
BEGIN
    PRINT 'SuperAdmin user already exists';
END
