import React from 'react';
import { getResponsiveTextSize, formatPercentage } from '../utils/formatters';

export function DashboardCard({ title, value, trend, icon: Icon, color }) {
  // Get responsive text size based on value length
  const textSizeClass = getResponsiveTextSize(value);
  
  return (
    <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 hover:shadow-xl hover:border-amber-200 transition-all duration-300 transform hover:-translate-y-1">
      <div className="flex items-start justify-between gap-3">
        {/* Left side - Content */}
        <div className="flex-1 min-w-0">
          <p className="text-xs sm:text-sm text-gray-600 mb-1 truncate" title={title}>
            {title}
          </p>
          <h3 
            className={`${textSizeClass} font-bold text-gray-900 break-words leading-tight`}
            title={value}
          >
            {value}
          </h3>
          {trend !== undefined && !isNaN(trend) && (
            <div className="flex items-center mt-1 sm:mt-2">
              <span className={`inline-flex items-center text-xs sm:text-sm font-medium ${
                trend >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                <svg 
                  className={`w-3 h-3 mr-1 ${trend >= 0 ? 'rotate-0' : 'rotate-180'}`} 
                  fill="currentColor" 
                  viewBox="0 0 20 20"
                >
                  <path 
                    fillRule="evenodd" 
                    d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" 
                    clipRule="evenodd" 
                  />
                </svg>
                {formatPercentage(Math.abs(trend))}
              </span>
            </div>
          )}
        </div>
        
        {/* Right side - Icon */}
        <div className={`flex-shrink-0 p-2 sm:p-3 rounded-lg ${color} shadow-sm`}>
          <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
        </div>
      </div>
    </div>
  );
}
