# check-parking-data-tables.ps1 - PowerShell script to check parking data tables

# Load environment variables from .env file
$envFile = "d:/PWVMS/backend/.env"
$envContent = Get-Content $envFile
$envVars = @{}

foreach ($line in $envContent) {
    if ($line -match '^\s*([^#][^=]+)=(.*)$') {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        $envVars[$key] = $value
    }
}

# Database connection parameters
$dbServer = $envVars["DB_SERVER"]
$dbName = $envVars["DB_NAME"]
$dbUser = $envVars["DB_USER"]
$dbPassword = $envVars["DB_PASSWORD"]

Write-Host "Connecting to database..."
Write-Host "Server: $dbServer"
Write-Host "Database: $dbName"
Write-Host "User: $dbUser"

# Create connection string
$connectionString = "Server=$dbServer;Database=$dbName;User Id=$dbUser;Password=$dbPassword;Encrypt=True;TrustServerCertificate=True;"

# SQL queries to run
$queries = @{
    "Row Count Comparison" = @"
SELECT 
    t.name AS TableName,
    p.rows AS RowCount
FROM sys.tables t
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
WHERE i.index_id < 2  -- Clustered index or heap
AND (t.name = 'tblParkwiz_Parking_Data' OR t.name = 'tblParkwiz_Parking_Data_Old')
ORDER BY t.name
"@
    
    "Table Size Information" = @"
SELECT 
    t.name AS TableName,
    s.name AS SchemaName,
    p.rows AS RowCount,
    SUM(a.total_pages) * 8 / 1024 AS TotalSpaceMB,
    SUM(a.used_pages) * 8 / 1024 AS UsedSpaceMB
FROM sys.tables t
INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
INNER JOIN sys.allocation_units a ON p.partition_id = a.container_id
WHERE (t.name = 'tblParkwiz_Parking_Data' OR t.name = 'tblParkwiz_Parking_Data_Old')
GROUP BY t.name, s.name, p.rows
ORDER BY t.name
"@
    
    "Sample Data from tblParkwiz_Parking_Data" = @"
SELECT TOP 5 
    PakringDataID,
    PlazaName,
    VehicleNumber,
    EntryDateTime,
    ExitDateTime,
    ParkingFee,
    iTotalGSTFee,
    PaymentMode
FROM tblParkwiz_Parking_Data
WHERE ExitDateTime IS NOT NULL
ORDER BY ExitDateTime DESC
"@

    "Sample Data from tblParkwiz_Parking_Data_Old (if exists)" = @"
IF OBJECT_ID('tblParkwiz_Parking_Data_Old', 'U') IS NOT NULL
BEGIN
    SELECT TOP 5 
        PakringDataID,
        PlazaName,
        VehicleNumber,
        EntryDateTime,
        ExitDateTime,
        ParkingFee,
        iTotalGSTFee,
        PaymentMode
    FROM tblParkwiz_Parking_Data_Old
    WHERE ExitDateTime IS NOT NULL
    ORDER BY ExitDateTime DESC
END
ELSE
BEGIN
    SELECT 'Table does not exist' AS Status
END
"@

    "Date Range in tblParkwiz_Parking_Data" = @"
SELECT 
    MIN(EntryDateTime) AS OldestRecord,
    MAX(EntryDateTime) AS NewestRecord,
    DATEDIFF(day, MIN(EntryDateTime), MAX(EntryDateTime)) AS DateRangeInDays,
    COUNT(*) AS TotalRecords
FROM tblParkwiz_Parking_Data
"@

    "Date Range in tblParkwiz_Parking_Data_Old (if exists)" = @"
IF OBJECT_ID('tblParkwiz_Parking_Data_Old', 'U') IS NOT NULL
BEGIN
    SELECT 
        MIN(EntryDateTime) AS OldestRecord,
        MAX(EntryDateTime) AS NewestRecord,
        DATEDIFF(day, MIN(EntryDateTime), MAX(EntryDateTime)) AS DateRangeInDays,
        COUNT(*) AS TotalRecords
    FROM tblParkwiz_Parking_Data_Old
END
ELSE
BEGIN
    SELECT 'Table does not exist' AS Status
END
"@

    "Column Comparison" = @"
SELECT 
    t.name AS TableName,
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length AS MaxLength,
    c.is_nullable AS IsNullable
FROM sys.tables t
INNER JOIN sys.columns c ON t.object_id = c.object_id
INNER JOIN sys.types ty ON c.user_type_id = ty.user_type_id
WHERE (t.name = 'tblParkwiz_Parking_Data' OR t.name = 'tblParkwiz_Parking_Data_Old')
ORDER BY t.name, c.column_id
"@
}

try {
    # Load SQL Server module if available
    if (Get-Module -ListAvailable -Name SqlServer) {
        Import-Module SqlServer
        
        # Run each query
        foreach ($queryName in $queries.Keys) {
            Write-Host "`n--- $queryName ---" -ForegroundColor Cyan
            $query = $queries[$queryName]
            
            try {
                $results = Invoke-Sqlcmd -ConnectionString $connectionString -Query $query -ErrorAction Stop
                $results | Format-Table -AutoSize
            }
            catch {
                Write-Host "Error executing query: $_" -ForegroundColor Red
            }
        }
    }
    else {
        Write-Host "SqlServer module not found. Please install it using: Install-Module -Name SqlServer" -ForegroundColor Yellow
        
        # Alternative approach using .NET SqlClient
        Write-Host "Trying alternative approach with .NET SqlClient..." -ForegroundColor Yellow
        
        # Load the System.Data assembly
        Add-Type -AssemblyName System.Data.SqlClient
        
        # Create a connection
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        # Run each query
        foreach ($queryName in $queries.Keys) {
            Write-Host "`n--- $queryName ---" -ForegroundColor Cyan
            $query = $queries[$queryName]
            
            try {
                $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
                $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
                $dataset = New-Object System.Data.DataSet
                $adapter.Fill($dataset) | Out-Null
                
                $dataset.Tables[0] | Format-Table -AutoSize
            }
            catch {
                Write-Host "Error executing query: $_" -ForegroundColor Red
            }
        }
        
        # Close the connection
        $connection.Close()
    }
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
}