import React from 'react';
import { Edit, Trash2, Badge, User, Car, Calendar, Phone, Mail, ChevronLeft, ChevronRight } from 'lucide-react';
import { PermissionButton } from '../auth/PermissionButton';

const PassRegistrationList = ({ 
  passRegistrations, 
  isLoading, 
  onEdit, 
  onDelete, 
  pagination, 
  onPageChange,
  onItemsPerPageChange 
}) => {
  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!passRegistrations || passRegistrations.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-12 text-center">
          <Badge className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Pass Registrations</h3>
          <p className="text-gray-600 mb-4">
            Get started by creating your first pass registration.
          </p>
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const isExpired = (endDate) => {
    if (!endDate) return false;
    return new Date(endDate) < new Date();
  };

  const renderPagination = () => {
    // Always render pagination, even with just one page
    if (!pagination) {
      console.warn('Pagination object is missing');
      return null;
    }

    // Ensure all pagination properties exist
    const currentPage = pagination.currentPage || 1;
    const totalPages = pagination.totalPages || 1;
    const totalRecords = pagination.totalRecords || 0;
    const pageSize = pagination.pageSize || 10;
    
    // Calculate indexes for display
    const indexOfLastItem = Math.min(currentPage * pageSize, totalRecords);
    const indexOfFirstItem = Math.min((currentPage - 1) * pageSize + 1, totalRecords);
    
    // Handle items per page change
    const handleItemsPerPageChange = (e) => {
      const value = parseInt(e.target.value, 10);
      console.log('Changing items per page to:', value);
      if (onItemsPerPageChange) {
        onItemsPerPageChange(value);
      } else {
        console.warn('onItemsPerPageChange handler is not provided');
      }
    };

    return (
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex flex-col sm:flex-row justify-between items-center">
          <div className="flex items-center mb-4 sm:mb-0">
            <span className="text-sm text-gray-700 mr-4">
              Showing {indexOfFirstItem}-{indexOfLastItem} of {totalRecords}
            </span>
            <select
              value={pageSize}
              onChange={handleItemsPerPageChange}
              className="border border-gray-300 rounded-md px-2 py-1 text-sm"
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={20}>20 per page</option>
              <option value={50}>50 per page</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onPageChange(1)}
              disabled={currentPage === 1}
              className={`p-2 rounded-md ${
                currentPage === 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span className="sr-only">First Page</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            </button>
            
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`p-2 rounded-md ${
                currentPage === 1
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span className="sr-only">Previous Page</span>
              <ChevronLeft className="h-5 w-5" />
            </button>
            
            <div className="flex items-center">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around current page
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => onPageChange(pageNum)}
                    className={`px-3 py-1 rounded-md mx-1 ${
                      currentPage === pageNum
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>
            
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`p-2 rounded-md ${
                currentPage === totalPages
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span className="sr-only">Next Page</span>
              <ChevronRight className="h-5 w-5" />
            </button>
            
            <button
              onClick={() => onPageChange(totalPages)}
              disabled={currentPage === totalPages}
              className={`p-2 rounded-md ${
                currentPage === totalPages
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span className="sr-only">Last Page</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                <path fillRule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  };

  // For debugging
  console.log('Pagination object in list component:', pagination);
  
  // Debug the first pass registration to see the structure
  if (passRegistrations && passRegistrations.length > 0) {
    console.log('First pass registration in list component:', passRegistrations[0]);
    if (passRegistrations[0].PlazaName !== undefined) {
      console.log('PlazaName type:', typeof passRegistrations[0].PlazaName);
      console.log('PlazaName value:', passRegistrations[0].PlazaName);
      
      // Check if PlazaName is an array
      if (Array.isArray(passRegistrations[0].PlazaName)) {
        console.log('PlazaName is an array with length:', passRegistrations[0].PlazaName.length);
        console.log('First element:', passRegistrations[0].PlazaName[0]);
        if (passRegistrations[0].PlazaName.length > 1) {
          console.log('Second element:', passRegistrations[0].PlazaName[1]);
          console.log('Are elements identical?', passRegistrations[0].PlazaName[0] === passRegistrations[0].PlazaName[1]);
        }
      }
    }
  }
  
  return (
    <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Pass Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Holder Information
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Vehicle Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Validity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {passRegistrations.map((pass) => (
              <tr key={pass.PassRegID} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <Badge className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        GIN: {pass.GIN}
                      </div>
                      <div className="text-sm text-gray-500">
                        Plaza: {pass.PlazaName ? (
                          // Check if PlazaName is an array
                          Array.isArray(pass.PlazaName) ? (
                            // If it's an array, just show the first element
                            pass.PlazaName[0] || 'N/A'
                          ) : (
                            // If it's not an array, show it as is
                            pass.PlazaName
                          )
                        ) : 'N/A'}
                      </div>
                      <div className="flex items-center mt-1">
                        <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                          pass.PassType === 'Monthly' ? 'bg-blue-100 text-blue-800' :
                          pass.PassType === 'Yearly' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {pass.PassType}
                        </span>
                        {pass.flgANPRPassEnable === 'True' && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                            ANPR
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="flex items-center mb-1">
                      <User className="w-4 h-4 text-gray-400 mr-1" />
                      <span className="font-medium">{pass.HolderName}</span>
                    </div>
                    {pass.CompanyName && (
                      <div className="text-sm text-gray-500 mb-1">
                        {pass.CompanyName}
                      </div>
                    )}
                    <div className="flex items-center text-sm text-gray-500 mb-1">
                      <Phone className="w-4 h-4 text-gray-400 mr-1" />
                      {pass.ContactNo}
                    </div>
                    {pass.EmailID && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Mail className="w-4 h-4 text-gray-400 mr-1" />
                        {pass.EmailID}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="flex items-center mb-1">
                      <Car className="w-4 h-4 text-gray-400 mr-1" />
                      <span className="font-medium">{pass.VehicleNo}</span>
                    </div>
                    <div className="text-sm text-gray-500 mb-1">
                      {pass.VehicleType}
                    </div>
                    {pass.TagMasterTagID && (
                      <div className="text-sm text-gray-500">
                        Tag: {pass.TagMasterTagID}
                      </div>
                    )}
                    {pass.PaidAmount && (
                      <div className="text-sm font-medium text-green-600">
                        ₹{pass.PaidAmount}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <div className="flex items-center mb-1">
                      <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-500">Issued:</span>
                    </div>
                    <div className="text-sm font-medium mb-2">
                      {formatDate(pass.IssueDate)}
                    </div>
                    <div className="flex items-center mb-1">
                      <Calendar className="w-4 h-4 text-gray-400 mr-1" />
                      <span className="text-sm text-gray-500">Expires:</span>
                    </div>
                    <div className={`text-sm font-medium ${
                      isExpired(pass.ContractEndDate) ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {formatDate(pass.ContractEndDate)}
                    </div>
                    {isExpired(pass.ContractEndDate) && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 mt-1">
                        Expired
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <PermissionButton
                      requiredModule="Pass Registration"
                      requiredPermissions={["Edit"]}
                      onClick={() => onEdit(pass)}
                      className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="Edit Pass"
                    >
                      <Edit className="w-4 h-4" />
                    </PermissionButton>

                    <PermissionButton
                      requiredModule="Pass Registration"
                      requiredPermissions={["Delete"]}
                      onClick={() => onDelete(pass.PassRegID)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete Pass"
                    >
                      <Trash2 className="w-4 h-4" />
                    </PermissionButton>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Always render pagination if we have items */}
      {pagination && passRegistrations && passRegistrations.length > 0 ? (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center">
            <div className="flex items-center mb-4 sm:mb-0">
              <span className="text-sm text-gray-700 mr-4">
                Showing {Math.min((pagination.currentPage - 1) * pagination.pageSize + 1, pagination.totalRecords)}-
                {Math.min(pagination.currentPage * pagination.pageSize, pagination.totalRecords)} of {pagination.totalRecords}
              </span>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  const value = parseInt(e.target.value, 10);
                  console.log('Changing items per page to:', value);
                  if (onItemsPerPageChange) onItemsPerPageChange(value);
                }}
                className="border border-gray-300 rounded-md px-2 py-1 text-sm"
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onPageChange(1)}
                disabled={pagination.currentPage === 1}
                className={`p-2 rounded-md ${
                  pagination.currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">First Page</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              </button>
              
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className={`p-2 rounded-md ${
                  pagination.currentPage === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">Previous Page</span>
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              <div className="flex items-center">
                {Array.from({ length: Math.min(5, pagination.totalPages || 1) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if ((pagination.totalPages || 1) <= 5) {
                    pageNum = i + 1;
                  } else if (pagination.currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (pagination.currentPage >= (pagination.totalPages || 1) - 2) {
                    pageNum = (pagination.totalPages || 1) - 4 + i;
                  } else {
                    pageNum = pagination.currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => onPageChange(pageNum)}
                      className={`px-3 py-1 rounded-md mx-1 ${
                        pagination.currentPage === pageNum
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === (pagination.totalPages || 1)}
                className={`p-2 rounded-md ${
                  pagination.currentPage === (pagination.totalPages || 1)
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">Next Page</span>
                <ChevronRight className="h-5 w-5" />
              </button>
              
              <button
                onClick={() => onPageChange(pagination.totalPages || 1)}
                disabled={pagination.currentPage === (pagination.totalPages || 1)}
                className={`p-2 rounded-md ${
                  pagination.currentPage === (pagination.totalPages || 1)
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-200'
                }`}
              >
                <span className="sr-only">Last Page</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  <path fillRule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      ) : (passRegistrations && passRegistrations.length > 0) ? (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              Showing {passRegistrations.length} items
            </div>
            <div>
              <select
                value={10}
                onChange={(e) => {
                  const value = parseInt(e.target.value, 10);
                  if (onItemsPerPageChange) onItemsPerPageChange(value);
                }}
                className="border border-gray-300 rounded-md px-2 py-1 text-sm"
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default PassRegistrationList;
