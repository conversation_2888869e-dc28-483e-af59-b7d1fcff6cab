-- =============================================
-- Valet Transaction Management Stored Procedures
-- Created for comprehensive transaction management
-- =============================================

-- =============================================
-- 1. CREATE - Create new valet transaction
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Transaction_Create]
    @PNRNumber UNIQUEIDENTIFIER,
    @ParkingPin DECIMAL(18,0),
    @CompanyId DECIMAL(18,0),
    @PlazaId DECIMAL(18,0),
    @CustomerVehicleNumber NVARCHAR(50),
    @CustomerMobileNumber NVARCHAR(15) = NULL,
    @CustomerName NVARCHAR(255) = NULL,
    @IsAnyValuableItem BIT = 0,
    @AnyValuableItem NVARCHAR(500) = NULL,
    @ValetFee DECIMAL(18,2) = 0,
    @ParkingFee DECIMAL(18,2) = 0,
    @TotalFee DECIMAL(18,2) = 0,
    @Source INT = 1, -- 1=Mobile App, 2=Web, 3=Controller
    @PayAt INT = 1, -- 1=Entry, 2=Exit
    @VehicleType INT = 1, -- 1=Car, 2=Bike, etc.
    @PlazaValetPointId DECIMAL(18,0) = NULL,
    @EntryBy DECIMAL(18,0),
    @NewId DECIMAL(18,0) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PNRNumber IS NULL
        BEGIN
            RAISERROR('PNR Number is required', 16, 1);
            RETURN -1;
        END
        
        IF @ParkingPin IS NULL OR @ParkingPin <= 0
        BEGIN
            RAISERROR('Parking Pin is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CompanyId IS NULL OR @CompanyId <= 0
        BEGIN
            RAISERROR('Company ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @PlazaId IS NULL OR @PlazaId <= 0
        BEGIN
            RAISERROR('Plaza ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @CustomerVehicleNumber IS NULL OR LTRIM(RTRIM(@CustomerVehicleNumber)) = ''
        BEGIN
            RAISERROR('Customer vehicle number is required', 16, 1);
            RETURN -1;
        END
        
        IF @EntryBy IS NULL OR @EntryBy <= 0
        BEGIN
            RAISERROR('EntryBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate PNR
        IF EXISTS(SELECT 1 FROM [dbo].[ParkingTransactions] WHERE [PNRNumber] = @PNRNumber)
        BEGIN
            RAISERROR('Transaction with this PNR already exists', 16, 1);
            RETURN -1;
        END
        
        -- Check for duplicate Parking Pin
        IF EXISTS(SELECT 1 FROM [dbo].[ParkingTransactions] WHERE [ParkingPin] = @ParkingPin)
        BEGIN
            RAISERROR('Transaction with this Parking Pin already exists', 16, 1);
            RETURN -1;
        END
        
        -- Insert new transaction
        INSERT INTO [dbo].[ParkingTransactions]
        (
            [PNRNumber],
            [ParkingPin],
            [CompanyId],
            [PlazaId],
            [CustomerVehicleNumber],
            [CustomerMobileNumber],
            [CustomerName],
            [IsAnyValuableItem],
            [AnyValuableItem],
            [ValetFee],
            [ParkingFee],
            [TotalFee],
            [Source],
            [PayAt],
            [PaymentType],
            [IsPaymentCompleted],
            [IsPromoCodeTransaction],
            [VehicleType],
            [EntryBy],
            [EntryDateTime],
            [PlazaValetPointId],
            [IsRequestMyVehicleLater],
            [RequestMyVehicleLaterTimeInterval],
            [IsVoid],
            [IsTransactionCanceled],
            [TransactionStatus],
            [IsKeyHandover]
        )
        VALUES
        (
            @PNRNumber,
            @ParkingPin,
            @CompanyId,
            @PlazaId,
            UPPER(LTRIM(RTRIM(@CustomerVehicleNumber))),
            @CustomerMobileNumber,
            @CustomerName,
            @IsAnyValuableItem,
            @AnyValuableItem,
            @ValetFee,
            @ParkingFee,
            @TotalFee,
            @Source,
            @PayAt,
            NULL, -- PaymentType - to be set during payment
            0, -- IsPaymentCompleted
            0, -- IsPromoCodeTransaction
            @VehicleType,
            @EntryBy,
            GETDATE(),
            @PlazaValetPointId,
            0, -- IsRequestMyVehicleLater
            0, -- RequestMyVehicleLaterTimeInterval
            0, -- IsVoid
            0, -- IsTransactionCanceled
            1, -- TransactionStatus: 1=Active, 2=Completed, 3=Cancelled
            0  -- IsKeyHandover
        );
        
        SET @NewId = SCOPE_IDENTITY();
        
        -- Return success with transaction details
        SELECT 
            @NewId AS Id,
            @PNRNumber AS PNRNumber,
            @ParkingPin AS ParkingPin,
            @CustomerVehicleNumber AS VehicleNumber,
            @TotalFee AS TotalFee,
            'Valet transaction created successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @NewId = -1;
    END CATCH
END
GO

-- =============================================
-- 2. READ - Get transaction by PNR
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Transaction_GetByPNR]
    @PNRNumber UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PNRNumber IS NULL
        BEGIN
            RAISERROR('PNR Number is required', 16, 1);
            RETURN -1;
        END
        
        -- Get transaction by PNR
        SELECT 
            [Id],
            [PNRNumber],
            [ParkingPin],
            [CompanyId],
            [PlazaId],
            [CustomerVehicleNumber],
            [CustomerMobileNumber],
            [CustomerName],
            [IsAnyValuableItem],
            [AnyValuableItem],
            [ValetFee],
            [ParkingFee],
            [TotalFee],
            [Source],
            [PayAt],
            [PaymentType],
            [IsPaymentCompleted],
            [VehicleType],
            [EntryBy],
            [EntryDateTime],
            [PlazaValetPointId],
            [DriverId],
            [DriverAssignedBy],
            [DriverAssignedOn],
            [ParkingBayId],
            [RequestMyVehicleDateTime],
            [IsRequestMyVehicleLater],
            [PickupDriverId],
            [AssignDriverOn],
            [PickupVehicleOn],
            [ArrivedAtPlazaValetPointOn],
            [AssignToControllerId],
            [AssignToControllerBy],
            [AssignToControllerOn],
            [ExitDateTime],
            [ExitBy],
            [TransactionStatus],
            [IsKeyHandover],
            [KeyAcceptedBy],
            CASE [TransactionStatus]
                WHEN 1 THEN 'Active'
                WHEN 2 THEN 'Completed'
                WHEN 3 THEN 'Cancelled'
                ELSE 'Unknown'
            END AS StatusText
        FROM [dbo].[ParkingTransactions]
        WHERE [PNRNumber] = @PNRNumber;
        
        -- Check if transaction exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'Transaction not found' AS Message, 0 AS Success;
        END
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 3. READ - Get transaction by Parking Pin
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Transaction_GetByPin]
    @ParkingPin DECIMAL(18,0)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @ParkingPin IS NULL OR @ParkingPin <= 0
        BEGIN
            RAISERROR('Parking Pin is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Get transaction by Parking Pin
        SELECT 
            [Id],
            [PNRNumber],
            [ParkingPin],
            [CompanyId],
            [PlazaId],
            [CustomerVehicleNumber],
            [CustomerMobileNumber],
            [CustomerName],
            [IsAnyValuableItem],
            [AnyValuableItem],
            [ValetFee],
            [ParkingFee],
            [TotalFee],
            [Source],
            [PayAt],
            [PaymentType],
            [IsPaymentCompleted],
            [VehicleType],
            [EntryBy],
            [EntryDateTime],
            [PlazaValetPointId],
            [DriverId],
            [DriverAssignedBy],
            [DriverAssignedOn],
            [ParkingBayId],
            [RequestMyVehicleDateTime],
            [IsRequestMyVehicleLater],
            [PickupDriverId],
            [AssignDriverOn],
            [PickupVehicleOn],
            [ArrivedAtPlazaValetPointOn],
            [AssignToControllerId],
            [AssignToControllerBy],
            [AssignToControllerOn],
            [ExitDateTime],
            [ExitBy],
            [TransactionStatus],
            [IsKeyHandover],
            [KeyAcceptedBy],
            CASE [TransactionStatus]
                WHEN 1 THEN 'Active'
                WHEN 2 THEN 'Completed'
                WHEN 3 THEN 'Cancelled'
                ELSE 'Unknown'
            END AS StatusText
        FROM [dbo].[ParkingTransactions]
        WHERE [ParkingPin] = @ParkingPin;
        
        -- Check if transaction exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'Transaction not found' AS Message, 0 AS Success;
        END
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 4. UPDATE - Update transaction status
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_Transaction_UpdateStatus]
    @Id DECIMAL(18,0),
    @TransactionStatus INT,
    @ModifiedBy DECIMAL(18,0),
    @DriverId DECIMAL(18,0) = NULL,
    @ParkingBayId DECIMAL(18,0) = NULL,
    @AssignToControllerId DECIMAL(18,0) = NULL,
    @IsKeyHandover BIT = NULL,
    @KeyAcceptedBy DECIMAL(18,0) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Validation
        IF @Id IS NULL OR @Id <= 0
        BEGIN
            RAISERROR('Transaction ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        IF @TransactionStatus IS NULL OR @TransactionStatus NOT IN (1, 2, 3)
        BEGIN
            RAISERROR('Transaction Status must be 1 (Active), 2 (Completed), or 3 (Cancelled)', 16, 1);
            RETURN -1;
        END

        IF @ModifiedBy IS NULL OR @ModifiedBy <= 0
        BEGIN
            RAISERROR('ModifiedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END

        -- Check if transaction exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[ParkingTransactions] WHERE [Id] = @Id)
        BEGIN
            RAISERROR('Transaction not found', 16, 1);
            RETURN -1;
        END

        -- Update transaction
        UPDATE [dbo].[ParkingTransactions]
        SET
            [TransactionStatus] = @TransactionStatus,
            [DriverId] = COALESCE(@DriverId, [DriverId]),
            [DriverAssignedBy] = CASE WHEN @DriverId IS NOT NULL THEN @ModifiedBy ELSE [DriverAssignedBy] END,
            [DriverAssignedOn] = CASE WHEN @DriverId IS NOT NULL THEN GETDATE() ELSE [DriverAssignedOn] END,
            [ParkingBayId] = COALESCE(@ParkingBayId, [ParkingBayId]),
            [ParkedBayAssignedBy] = CASE WHEN @ParkingBayId IS NOT NULL THEN @ModifiedBy ELSE [ParkedBayAssignedBy] END,
            [ParkedBayAssignedOn] = CASE WHEN @ParkingBayId IS NOT NULL THEN GETDATE() ELSE [ParkedBayAssignedOn] END,
            [AssignToControllerId] = COALESCE(@AssignToControllerId, [AssignToControllerId]),
            [AssignToControllerBy] = CASE WHEN @AssignToControllerId IS NOT NULL THEN @ModifiedBy ELSE [AssignToControllerBy] END,
            [AssignToControllerOn] = CASE WHEN @AssignToControllerId IS NOT NULL THEN GETDATE() ELSE [AssignToControllerOn] END,
            [IsKeyHandover] = COALESCE(@IsKeyHandover, [IsKeyHandover]),
            [KeyAcceptedBy] = COALESCE(@KeyAcceptedBy, [KeyAcceptedBy]),
            [ExitDateTime] = CASE WHEN @TransactionStatus = 2 THEN GETDATE() ELSE [ExitDateTime] END,
            [ExitBy] = CASE WHEN @TransactionStatus = 2 THEN @ModifiedBy ELSE [ExitBy] END
        WHERE [Id] = @Id;

        -- Return updated transaction
        SELECT
            [Id],
            [PNRNumber],
            [ParkingPin],
            [CustomerVehicleNumber],
            [TransactionStatus],
            CASE [TransactionStatus]
                WHEN 1 THEN 'Active'
                WHEN 2 THEN 'Completed'
                WHEN 3 THEN 'Cancelled'
                ELSE 'Unknown'
            END AS StatusText,
            [DriverId],
            [ParkingBayId],
            [AssignToControllerId],
            [IsKeyHandover],
            'Transaction updated successfully' AS Message,
            1 AS Success
        FROM [dbo].[ParkingTransactions]
        WHERE [Id] = @Id;

    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO
