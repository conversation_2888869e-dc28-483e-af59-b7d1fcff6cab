import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';
import { plazaApi } from '../../api/plazaApi';
import { useQuery } from '@tanstack/react-query';

export default function FastagDialog({ isOpen, onClose, onSubmit, initialData, title, plazas: allPlazas, companies, lanes }) {
  const { user } = useAuth();
  
  // State to track the selected company's plazas
  const [companyPlazas, setCompanyPlazas] = useState([]);
  const [formData, setFormData] = useState({
    PlazaID: '',
    CompanyID: '',
    LaneID: '',
    LaneNumber: '',
    PlazaGeoCode: '',
    PlazaName: '',
    PlazaSubType: '',
    PlazaType: '',
    LaneDirection: '',
    LaneReaderID: '',
    LaneStatus: 'Active',
    LaneMode: '',
    LaneType: '',
    LaneFloor: '',
    UpdatedBy: 'admin', // Default value for UpdatedBy
    FastagOrgID: '',
    FastagAgencyCode: '',
    FastagAPIAddress: '',
    LaneGate: '',
    flgTerminal1Exit: 'N'
  });

  const [errors, setErrors] = useState({});
  const [availableLanes, setAvailableLanes] = useState([]);

  // Initialize form data when initialData changes or dialog opens
  useEffect(() => {
    if (initialData) {
      console.log('Initializing form with data:', initialData);
      setFormData({
        PlazaID: initialData.PlazaID || '',
        CompanyID: initialData.CompanyID || '',
        LaneID: initialData.LaneID || '',
        LaneNumber: initialData.LaneNumber || '',
        PlazaGeoCode: initialData.PlazaGeoCode || '',
        PlazaName: initialData.PlazaName || '',
        PlazaSubType: initialData.PlazaSubType || '',
        PlazaType: initialData.PlazaType || '',
        LaneDirection: initialData.LaneDirection || '',
        LaneReaderID: initialData.LaneReaderID || '',
        LaneStatus: initialData.LaneStatus || 'Active',
        LaneMode: initialData.LaneMode || '',
        LaneType: initialData.LaneType || '',
        LaneFloor: initialData.LaneFloor || '',
        UpdatedBy: initialData.UpdatedBy || 'admin',
        FastagOrgID: initialData.FastagOrgID || '',
        FastagAgencyCode: initialData.FastagAgencyCode || '',
        FastagAPIAddress: initialData.FastagAPIAddress || '',
        LaneGate: initialData.LaneGate || '',
        flgTerminal1Exit: initialData.flgTerminal1Exit || 'N'
      });
    } else {
      resetForm();
    }
  }, [initialData, isOpen]);

  // Filter lanes based on selected plaza
  useEffect(() => {
    if (formData.PlazaID && lanes) {
      const filteredLanes = lanes.filter(lane =>
        lane.PlazaID === formData.PlazaID ||
        lane.PlazaID === Number(formData.PlazaID)
      );
      setAvailableLanes(filteredLanes);
    } else {
      setAvailableLanes([]);
    }
  }, [formData.PlazaID, lanes]);

  // Update LaneNumber and PlazaName when LaneID changes
  useEffect(() => {
    if (formData.LaneID && availableLanes.length > 0) {
      const selectedLane = availableLanes.find(lane =>
        lane.LaneID === formData.LaneID ||
        lane.LaneID === Number(formData.LaneID)
      );
      if (selectedLane) {
        console.log('Selected lane:', selectedLane);
        setFormData(prev => ({
          ...prev,
          LaneNumber: selectedLane.LaneNumber
        }));
      }
    }
  }, [formData.LaneID, availableLanes]);

  // Fetch plazas for the selected company
  const { data: fetchedCompanyPlazas, isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazasByCompany', formData.CompanyID],
    queryFn: async () => {
      try {
        // Log the API call
        console.log(`Calling API: /companies/${formData.CompanyID}/plazas`);
        
        // Make the API call
        const data = await plazaApi.getPlazasByCompany(formData.CompanyID);
        
        // Log the response
        console.log(`API response for company ${formData.CompanyID} plazas:`, data);
        
        return data;
      } catch (error) {
        console.error(`API call failed for company ${formData.CompanyID} plazas:`, error);
        throw error;
      }
    },
    enabled: !!formData.CompanyID, // Only run the query if a company is selected
    onSuccess: (data) => {
      console.log('Setting company plazas state with:', data);
      setCompanyPlazas(data || []);
    },
    onError: (error) => {
      console.error('Error fetching plazas for company:', error);
    }
  });

  // Update PlazaName when PlazaID changes
  useEffect(() => {
    if (formData.PlazaID && allPlazas) {
      const selectedPlaza = allPlazas.find(plaza =>
        plaza.PlazaID === formData.PlazaID ||
        plaza.PlazaID === Number(formData.PlazaID) ||
        plaza.Id === formData.PlazaID ||
        plaza.Id === Number(formData.PlazaID)
      );
      if (selectedPlaza) {
        console.log('Selected plaza:', selectedPlaza);
        setFormData(prev => ({
          ...prev,
          PlazaName: selectedPlaza.PlazaName || selectedPlaza.Name
        }));
      }
    }
  }, [formData.PlazaID, allPlazas]);

  const resetForm = () => {
    const defaultFormData = {
      PlazaID: '',
      CompanyID: '',
      LaneID: '',
      LaneNumber: '',
      PlazaGeoCode: '',
      PlazaName: '',
      PlazaSubType: '',
      PlazaType: '',
      LaneDirection: '',
      LaneReaderID: '',
      LaneStatus: 'Active',
      LaneMode: '',
      LaneType: '',
      LaneFloor: '',
      UpdatedBy: 'admin',
      FastagOrgID: '',
      FastagAgencyCode: '',
      FastagAPIAddress: '',
      LaneGate: '',
      flgTerminal1Exit: 'N'
    };

    console.log('Resetting form to defaults:', defaultFormData);
    setFormData(defaultFormData);
    setErrors({});
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // For checkboxes, convert boolean to string 'Y' or 'N' to match database format
    const newValue = type === 'checkbox' ? (checked ? 'Y' : 'N') : value;

    console.log(`Field ${name} changed to:`, newValue, type === 'checkbox' ? `(checked: ${checked})` : '');

    // Create a new form data object with the updated value
    let updatedFormData = {
      ...formData,
      [name]: newValue
    };

    // If company changes, reset plaza and lane
    if (name === 'CompanyID') {
      console.log('Company changed, resetting PlazaID and LaneID');
      updatedFormData = {
        ...updatedFormData,
        PlazaID: '',
        LaneID: '',
        LaneNumber: '',
        PlazaName: ''
      };
    }

    // If plaza changes, reset lane
    if (name === 'PlazaID') {
      console.log('Plaza changed, resetting LaneID');
      updatedFormData = {
        ...updatedFormData,
        LaneID: '',
        LaneNumber: ''
      };
    }

    setFormData(updatedFormData);

    // Clear error for this field if any
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    if (!formData.PlazaID) newErrors.PlazaID = 'Plaza is required';
    if (!formData.CompanyID) newErrors.CompanyID = 'Company is required';
    if (!formData.LaneID) newErrors.LaneID = 'Lane is required';
    if (!formData.FastagOrgID) newErrors.FastagOrgID = 'Fastag Organization ID is required';
    if (!formData.FastagAgencyCode) newErrors.FastagAgencyCode = 'Fastag Agency Code is required';
    if (!formData.FastagAPIAddress) newErrors.FastagAPIAddress = 'Fastag API Address is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Add current user ID for tracking who created/modified the record
      const dataToSubmit = {
        ...formData,
        UpdatedBy: user?.id || formData.UpdatedBy || 'admin'
      };

      console.log('Form is valid, submitting data:', dataToSubmit);
      onSubmit(dataToSubmit);
    } else {
      console.log('Form validation failed:', errors);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-800">{title || 'Fastag Configuration'}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 focus:outline-none"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Company Selection */}
            <div>
              <label htmlFor="CompanyID" className="block text-sm font-medium text-gray-700 mb-1">
                Company <span className="text-red-500">*</span>
              </label>
              <select
                id="CompanyID"
                name="CompanyID"
                value={formData.CompanyID}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.CompanyID ? 'border-red-500' : 'border-gray-300'}`}
              >
                <option value="">Select Company</option>
                {companies && companies.map((company) => (
                  <option key={company.CompanyID || company.Id} value={company.CompanyID || company.Id}>
                    {company.CompanyName || company.Name}
                  </option>
                ))}
              </select>
              {errors.CompanyID && <p className="text-red-500 text-xs mt-1">{errors.CompanyID}</p>}
            </div>

            {/* Plaza Selection */}
            <div>
              <label htmlFor="PlazaID" className="block text-sm font-medium text-gray-700 mb-1">
                Plaza <span className="text-red-500">*</span>
              </label>
              <select
                id="PlazaID"
                name="PlazaID"
                value={formData.PlazaID}
                onChange={handleChange}
                disabled={!formData.CompanyID}
                className={`w-full p-2 border rounded-md ${errors.PlazaID ? 'border-red-500' : 'border-gray-300'}`}
              >
                {!formData.CompanyID ? (
                  <option value="">Select a company first</option>
                ) : plazasLoading ? (
                  <option value="">Loading plazas...</option>
                ) : (
                  <>
                    <option value="">Select Plaza</option>
                    {(() => {
                      if (plazasError) {
                        console.error('Error loading plazas:', plazasError);
                        return <option value="">Error loading plazas</option>;
                      }
                      
                      if (fetchedCompanyPlazas) {
                        // Handle different response structures
                        let plazaItems = [];
                        
                        if (Array.isArray(fetchedCompanyPlazas)) {
                          plazaItems = fetchedCompanyPlazas;
                        } else if (fetchedCompanyPlazas.plazas && Array.isArray(fetchedCompanyPlazas.plazas)) {
                          plazaItems = fetchedCompanyPlazas.plazas;
                        } else if (fetchedCompanyPlazas.data && Array.isArray(fetchedCompanyPlazas.data)) {
                          plazaItems = fetchedCompanyPlazas.data;
                        }
                        
                        if (plazaItems.length > 0) {
                          console.log('Rendering plaza options:', plazaItems);
                          
                          return plazaItems.map((plaza) => {
                            // Check if plaza has the required properties
                            const id = plaza.Id || plaza.id || plaza.PlazaID || plaza.plazaID;
                            const name = plaza.PlazaName || plaza.plazaName || plaza.name;
                            
                            console.log('Plaza option:', id, name);

                            if (id && name) {
                              return (
                                <option key={id} value={id}>
                                  {name}
                                </option>
                              );
                            }
                            return null;
                          });
                        }
                      }
                      
                      return null;
                    })()}
                  </>
                )}
              </select>
              {errors.PlazaID && <p className="text-red-500 text-xs mt-1">{errors.PlazaID}</p>}
            </div>

            {/* Lane Selection */}
            <div>
              <label htmlFor="LaneID" className="block text-sm font-medium text-gray-700 mb-1">
                Lane <span className="text-red-500">*</span>
              </label>
              <select
                id="LaneID"
                name="LaneID"
                value={formData.LaneID}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.LaneID ? 'border-red-500' : 'border-gray-300'}`}
                disabled={!formData.PlazaID}
              >
                <option value="">Select Lane</option>
                {availableLanes.map((lane) => (
                  <option key={lane.LaneID} value={lane.LaneID}>
                    {lane.LaneNumber} - {lane.LaneDirection}
                  </option>
                ))}
              </select>
              {errors.LaneID && <p className="text-red-500 text-xs mt-1">{errors.LaneID}</p>}
            </div>

            {/* Lane Number (Read-only) */}
            <div>
              <label htmlFor="LaneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Lane Number
              </label>
              <input
                type="text"
                id="LaneNumber"
                name="LaneNumber"
                value={formData.LaneNumber}
                readOnly
                className="w-full p-2 border border-gray-300 rounded-md bg-gray-100"
              />
            </div>

            {/* Fastag Organization ID */}
            <div>
              <label htmlFor="FastagOrgID" className="block text-sm font-medium text-gray-700 mb-1">
                Fastag Organization ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="FastagOrgID"
                name="FastagOrgID"
                value={formData.FastagOrgID}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.FastagOrgID ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.FastagOrgID && <p className="text-red-500 text-xs mt-1">{errors.FastagOrgID}</p>}
            </div>

            {/* Fastag Agency Code */}
            <div>
              <label htmlFor="FastagAgencyCode" className="block text-sm font-medium text-gray-700 mb-1">
                Fastag Agency Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="FastagAgencyCode"
                name="FastagAgencyCode"
                value={formData.FastagAgencyCode}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.FastagAgencyCode ? 'border-red-500' : 'border-gray-300'}`}
              />
              {errors.FastagAgencyCode && <p className="text-red-500 text-xs mt-1">{errors.FastagAgencyCode}</p>}
            </div>

            {/* Fastag API Address */}
            <div className="md:col-span-2">
              <label htmlFor="FastagAPIAddress" className="block text-sm font-medium text-gray-700 mb-1">
                Fastag API Address <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="FastagAPIAddress"
                name="FastagAPIAddress"
                value={formData.FastagAPIAddress}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${errors.FastagAPIAddress ? 'border-red-500' : 'border-gray-300'}`}
                placeholder="https://example.com/api"
              />
              {errors.FastagAPIAddress && <p className="text-red-500 text-xs mt-1">{errors.FastagAPIAddress}</p>}
            </div>

            {/* Lane Reader ID */}
            <div>
              <label htmlFor="LaneReaderID" className="block text-sm font-medium text-gray-700 mb-1">
                Lane Reader ID
              </label>
              <input
                type="text"
                id="LaneReaderID"
                name="LaneReaderID"
                value={formData.LaneReaderID}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Lane Status */}
            <div>
              <label htmlFor="LaneStatus" className="block text-sm font-medium text-gray-700 mb-1">
                Lane Status
              </label>
              <select
                id="LaneStatus"
                name="LaneStatus"
                value={formData.LaneStatus}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Maintenance">Maintenance</option>
              </select>
            </div>

            {/* Lane Gate */}
            <div>
              <label htmlFor="LaneGate" className="block text-sm font-medium text-gray-700 mb-1">
                Lane Gate
              </label>
              <input
                type="text"
                id="LaneGate"
                name="LaneGate"
                value={formData.LaneGate}
                onChange={handleChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Terminal 1 Exit Flag */}
            <div className="flex items-center">
              <input
                type="checkbox"
                id="flgTerminal1Exit"
                name="flgTerminal1Exit"
                checked={formData.flgTerminal1Exit === 'Y'}
                onChange={handleChange}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded"
              />
              <label htmlFor="flgTerminal1Exit" className="ml-2 block text-sm text-gray-700">
                Terminal 1 Exit
              </label>
            </div>
          </div>

          <div className="mt-8 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <PermissionButton
              type="submit"
              className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              requiredModule="Fastag"
              requiredPermissions={initialData ? ["Edit"] : ["Create"]}
              companyId={initialData?.CompanyID}
              plazaId={initialData?.PlazaID}
            >
              {initialData ? 'Update' : 'Create'}
            </PermissionButton>
          </div>
        </form>
      </div>
    </div>
  );
}