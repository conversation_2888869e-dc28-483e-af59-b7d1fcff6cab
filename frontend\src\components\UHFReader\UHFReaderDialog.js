import React, { useState, useEffect, useMemo } from 'react';
import { X } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { companyApi } from '../../api/companyApi';
import { plazaApi } from '../../api/plazaApi';
import { laneApi } from '../../api/laneApi';
import { useAuth } from '../../contexts/authContext';

const UHFReaderDialog = ({ isOpen, onClose, onSubmit, uhfReader, isLoading }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    PlazaID: '',
    CompanyID: '',
    LaneID: '',
    ReaderLaneNumber: '',
    EnableUHFReader: 'False',
    ReaderIPAddress: '',
    ReaderPort: '',
    IOBoardIPAddress: '',
    IOBoardPort: '',
    flgFastag: 'False'
  });

  const [selectedCompany, setSelectedCompany] = useState('');
  const [selectedPlaza, setSelectedPlaza] = useState('');

  // Fetch companies
  const { data: companies = [] } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    enabled: user?.role === 'SuperAdmin'
  });

  // Fetch plazas based on selected company
  const { data: plazasData, isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazas', selectedCompany],
    queryFn: async () => {
      try {
        console.log(`Fetching plazas for company: ${selectedCompany}`);
        const data = selectedCompany 
          ? await plazaApi.getPlazasByCompany(selectedCompany) 
          : await plazaApi.getAllPlazas();
        console.log('Plazas data received:', data);
        return data;
      } catch (error) {
        console.error('Error fetching plazas:', error);
        throw error;
      }
    },
    enabled: !!selectedCompany || user?.role !== 'SuperAdmin'
  });

  // Extract plazas array from the response with better handling of different structures
  const plazas = useMemo(() => {
    if (!plazasData) return [];
    
    // Handle different response structures
    if (Array.isArray(plazasData)) {
      return plazasData;
    } else if (plazasData.data && Array.isArray(plazasData.data)) {
      return plazasData.data;
    } else if (plazasData.plazas && Array.isArray(plazasData.plazas)) {
      return plazasData.plazas;
    }
    
    console.warn('Unexpected plazas data structure:', plazasData);
    return [];
  }, [plazasData]);

  // Fetch lanes based on selected plaza
  const { data: lanes = [] } = useQuery({
    queryKey: ['lanes', selectedPlaza],
    queryFn: () => laneApi.getLanesByPlaza(selectedPlaza),
    enabled: !!selectedPlaza
  });

  useEffect(() => {
    if (uhfReader) {
      setFormData({
        PlazaID: uhfReader.PlazaID || '',
        CompanyID: uhfReader.CompanyID || '',
        LaneID: uhfReader.LaneID || '',
        ReaderLaneNumber: uhfReader.ReaderLaneNumber || '',
        EnableUHFReader: uhfReader.EnableUHFReader || 'False',
        ReaderIPAddress: uhfReader.ReaderIPAddress || '',
        ReaderPort: uhfReader.ReaderPort || '',
        IOBoardIPAddress: uhfReader.IOBoardIPAddress || '',
        IOBoardPort: uhfReader.IOBoardPort || '',
        flgFastag: uhfReader.flgFastag || 'False'
      });
      setSelectedCompany(uhfReader.CompanyID || '');
      setSelectedPlaza(uhfReader.PlazaID || '');
    } else {
      // Reset form for new entry
      setFormData({
        PlazaID: '',
        CompanyID: '',
        LaneID: '',
        ReaderLaneNumber: '',
        EnableUHFReader: 'False',
        ReaderIPAddress: '',
        ReaderPort: '',
        IOBoardIPAddress: '',
        IOBoardPort: '',
        flgFastag: 'False'
      });
      setSelectedCompany('');
      setSelectedPlaza('');
    }
  }, [uhfReader]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCompanyChange = (e) => {
    const companyId = e.target.value;
    setSelectedCompany(companyId);
    setFormData(prev => ({
      ...prev,
      CompanyID: companyId,
      PlazaID: '',
      LaneID: ''
    }));
    setSelectedPlaza('');
  };

  const handlePlazaChange = (e) => {
    const plazaId = e.target.value;
    setSelectedPlaza(plazaId);
    setFormData(prev => ({
      ...prev,
      PlazaID: plazaId,
      LaneID: ''
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {uhfReader ? 'Edit UHF Reader Configuration' : 'Add UHF Reader Configuration'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Company Selection - Only for SuperAdmin */}
          {user?.role === 'SuperAdmin' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company *
              </label>
              <select
                value={selectedCompany}
                onChange={handleCompanyChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Company</option>
                {companies.map((company) => (
                  <option key={company.Id} value={company.Id}>
                    {company.CompanyName}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* Plaza Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Plaza *
            </label>
            <select
              value={selectedPlaza}
              onChange={handlePlazaChange}
              required
              disabled={user?.role === 'SuperAdmin' && !selectedCompany}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {user?.role === 'SuperAdmin' && !selectedCompany ? (
                <option value="">Select a company first</option>
              ) : plazasLoading ? (
                <option value="">Loading plazas...</option>
              ) : plazasError ? (
                <option value="">Error loading plazas</option>
              ) : (
                <>
                  <option value="">Select Plaza</option>
                  {Array.isArray(plazas) && plazas.length > 0 ? plazas.map((plaza) => {
                    const id = plaza.Id || plaza.id;
                    const name = plaza.PlazaName || plaza.plazaName || plaza.name;
                    console.log('Rendering plaza option:', id, name);
                    
                    return (
                      <option key={id} value={id}>
                        {name}
                      </option>
                    );
                  }) : (
                    <option value="">No plazas available</option>
                  )}
                </>
              )}
            </select>
          </div>

          {/* Lane Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Lane *
            </label>
            <select
              name="LaneID"
              value={formData.LaneID}
              onChange={handleInputChange}
              required
              disabled={!selectedPlaza}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {!selectedPlaza ? (
                <option value="">Select a plaza first</option>
              ) : (
                <>
                  <option value="">Select Lane</option>
                  {Array.isArray(lanes) ? lanes.map((lane) => (
                    <option key={lane.LaneID} value={lane.LaneID}>
                      Lane {lane.LaneNumber} - {lane.LaneType}
                    </option>
                  )) : (
                    <option value="">No lanes available</option>
                  )}
                </>
              )}
            </select>
          </div>

          {/* Reader Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reader Lane Number
              </label>
              <input
                type="text"
                name="ReaderLaneNumber"
                value={formData.ReaderLaneNumber}
                onChange={handleInputChange}
                maxLength={2}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="01"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Enable UHF Reader
              </label>
              <select
                name="EnableUHFReader"
                value={formData.EnableUHFReader}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="False">Disabled</option>
                <option value="True">Enabled</option>
              </select>
            </div>
          </div>

          {/* Network Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reader IP Address
              </label>
              <input
                type="text"
                name="ReaderIPAddress"
                value={formData.ReaderIPAddress}
                onChange={handleInputChange}
                maxLength={16}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="*************"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reader Port
              </label>
              <input
                type="text"
                name="ReaderPort"
                value={formData.ReaderPort}
                onChange={handleInputChange}
                maxLength={10}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="8080"
              />
            </div>
          </div>

          {/* IO Board Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                IO Board IP Address
              </label>
              <input
                type="text"
                name="IOBoardIPAddress"
                value={formData.IOBoardIPAddress}
                onChange={handleInputChange}
                maxLength={16}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="*************"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                IO Board Port
              </label>
              <input
                type="text"
                name="IOBoardPort"
                value={formData.IOBoardPort}
                onChange={handleInputChange}
                maxLength={10}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="8081"
              />
            </div>
          </div>

          {/* FASTag Configuration */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              FASTag Support
            </label>
            <select
              name="flgFastag"
              value={formData.flgFastag}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="False">Disabled</option>
              <option value="True">Enabled</option>
            </select>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Saving...' : (uhfReader ? 'Update' : 'Create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UHFReaderDialog;
