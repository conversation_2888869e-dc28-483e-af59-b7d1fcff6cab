// frontend/src/utils/initLocalStorage.js

/**
 * Initialize localStorage with active plazas
 * This ensures that we have a copy of all plazas in localStorage
 * so we can toggle their status without relying on the backend
 * 
 * @param {Array} plazas - Array of plazas from the API
 */
export const initPlazasInLocalStorage = (plazas) => {
  if (!plazas || !Array.isArray(plazas)) {
    console.warn('No plazas provided to initPlazasInLocalStorage');
    return;
  }

  try {
    // Get existing plazas from localStorage
    let storedPlazas = [];
    const storedPlazasJson = localStorage.getItem('plazas');
    if (storedPlazasJson) {
      try {
        const parsedStoredPlazas = JSON.parse(storedPlazasJson);
        if (Array.isArray(parsedStoredPlazas)) {
          storedPlazas = parsedStoredPlazas;
        }
      } catch (e) {
        console.error('Error parsing stored plazas from localStorage:', e);
      }
    }

    // Create a map of stored plazas by ID for easy lookup
    const storedPlazaMap = {};
    storedPlazas.forEach(plaza => {
      storedPlazaMap[plaza.Id] = plaza;
    });

    // Add any new plazas from the API to the stored plazas
    plazas.forEach(plaza => {
      if (!storedPlazaMap[plaza.Id]) {
        storedPlazas.push(plaza);
      }
    });

    // Save the updated plazas to localStorage
    localStorage.setItem('plazas', JSON.stringify(storedPlazas));

    console.log(`Initialized ${storedPlazas.length} plazas in localStorage`);
  } catch (error) {
    console.error('Error initializing plazas in localStorage:', error);
  }
};

/**
 * Initialize localStorage with inactive plazas
 * This ensures that we have a copy of all inactive plazas in localStorage
 * for backward compatibility
 * 
 * @param {Array} plazas - Array of plazas from the API
 */
export const initInactivePlazasInLocalStorage = (plazas) => {
  if (!plazas || !Array.isArray(plazas)) {
    console.warn('No plazas provided to initInactivePlazasInLocalStorage');
    return;
  }

  try {
    // Get existing inactive plazas from localStorage
    let inactivePlazas = [];
    const inactivePlazasJson = localStorage.getItem('inactivePlazas');
    if (inactivePlazasJson) {
      try {
        const parsedInactivePlazas = JSON.parse(inactivePlazasJson);
        if (Array.isArray(parsedInactivePlazas)) {
          inactivePlazas = parsedInactivePlazas;
        }
      } catch (e) {
        console.error('Error parsing inactive plazas from localStorage:', e);
      }
    }

    // Create a map of inactive plazas by ID for easy lookup
    const inactivePlazaMap = {};
    inactivePlazas.forEach(plaza => {
      inactivePlazaMap[plaza.Id] = plaza;
    });

    // Add any inactive plazas from the API to the stored inactive plazas
    plazas.forEach(plaza => {
      if (plaza.IsActive === false || plaza.IsActive === 0 || plaza.IsActive === '0' || plaza.IsActive === 'N') {
        if (!inactivePlazaMap[plaza.Id]) {
          inactivePlazas.push(plaza);
        }
      }
    });

    // Save the updated inactive plazas to localStorage
    localStorage.setItem('inactivePlazas', JSON.stringify(inactivePlazas));

    console.log(`Initialized ${inactivePlazas.length} inactive plazas in localStorage`);
  } catch (error) {
    console.error('Error initializing inactive plazas in localStorage:', error);
  }
};
