const express = require('express');
const cors = require('cors');
const db = require('./src/config/database');

const app = express();
app.use(cors());
app.use(express.json());

// Simple test endpoint
app.get('/test-simple', (req, res) => {
  res.json({ success: true, message: 'Simple endpoint works' });
});

// Database test endpoint
app.get('/test-db', async (req, res) => {
  console.log('🔍 Testing database connection...');
  
  try {
    const result = await db.query('SELECT TOP 1 Id, Name FROM Modules');
    console.log('✅ Database query successful');
    
    res.json({
      success: true,
      message: 'Database test successful',
      data: result.recordset[0]
    });
  } catch (error) {
    console.error('❌ Database error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Modules endpoint
app.get('/test-modules', async (req, res) => {
  console.log('🔍 Testing modules query...');
  
  try {
    const result = await db.query(`
      SELECT Id, Name, Description, Icon, DisplayOrder, IsActive 
      FROM Modules 
      WHERE IsActive = 1 
      ORDER BY DisplayOrder
    `);
    
    console.log(`✅ Modules query successful: ${result.recordset.length} modules`);
    
    const modules = result.recordset.map(module => ({
      id: module.Id,
      name: module.Name,
      description: module.Description,
      icon: module.Icon,
      displayOrder: module.DisplayOrder,
      isActive: module.IsActive
    }));
    
    res.json({
      success: true,
      data: modules,
      message: 'Modules retrieved successfully'
    });
    
  } catch (error) {
    console.error('❌ Modules error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Test hourly entry/exit endpoint
app.get('/test-hourly-entry-exit', async (req, res) => {
  console.log('🔍 Testing hourly entry/exit query...');
  
  try {
    // Simple test query to check if the table exists and has data
    const testQuery = `
      SELECT TOP 5
        DATEPART(HOUR, EntryDateTime) as entry_hour,
        DATEPART(HOUR, ExitDateTime) as exit_hour,
        VehicleNumber,
        EntryDateTime,
        ExitDateTime
      FROM tblParkwiz_Parking_Data WITH (NOLOCK)
      WHERE EntryDateTime IS NOT NULL OR ExitDateTime IS NOT NULL
      ORDER BY ISNULL(EntryDateTime, ExitDateTime) DESC
    `;
    
    const result = await db.query(testQuery);
    
    console.log(`✅ Hourly entry/exit test query successful: ${result.recordset.length} records`);
    
    res.json({
      success: true,
      data: result.recordset,
      message: 'Hourly entry/exit test successful',
      count: result.recordset.length
    });
    
  } catch (error) {
    console.error('❌ Hourly entry/exit test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

const PORT = 5001;

app.listen(PORT, () => {
  console.log(`🚀 Minimal test server running on port ${PORT}`);
  console.log(`Test endpoints:`);
  console.log(`  - http://localhost:${PORT}/test-simple`);
  console.log(`  - http://localhost:${PORT}/test-db`);
  console.log(`  - http://localhost:${PORT}/test-modules`);
  console.log(`  - http://localhost:${PORT}/test-hourly-entry-exit`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down...');
  await db.closePool();
  process.exit(0);
});