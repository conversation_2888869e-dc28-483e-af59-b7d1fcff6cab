-- =============================================
-- CREATE MISSING VALET SYSTEM COMPONENTS
-- This fixes the ROOT CAUSE of valet API failures
-- Database: ParkwizOps
-- =============================================

USE ParkwizOps;
GO

PRINT 'Creating missing Valet system components...';
PRINT '============================================';

-- =============================================
-- 1. CREATE PlazaValetPoints TABLE (CRITICAL - ROOT CAUSE)
-- =============================================
PRINT 'Creating PlazaValetPoints table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PlazaValetPoints]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PlazaValetPoints] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [PlazaId] INT NOT NULL,
        [CompanyId] INT NOT NULL,
        [PointName] NVARCHAR(100) NOT NULL,
        [PointCode] NVARCHAR(50) NULL,
        [Location] NVARCHAR(200) NULL,
        [Capacity] INT DEFAULT 10,
        [IsActive] BIT DEFAULT 1,
        [CreatedBy] INT NULL,
        [CreatedOn] DATETIME DEFAULT GETDATE(),
        [ModifiedBy] INT NULL,
        [ModifiedOn] DATETIME NULL,
        
        -- Foreign key constraints
        CONSTRAINT FK_PlazaValetPoints_Plaza FOREIGN KEY ([PlazaId]) REFERENCES [Plaza]([Id]),
        CONSTRAINT FK_PlazaValetPoints_Company FOREIGN KEY ([CompanyId]) REFERENCES [Company]([Id])
    );
    
    PRINT '   ✅ PlazaValetPoints table created successfully';
END
ELSE
BEGIN
    PRINT '   ⚠️  PlazaValetPoints table already exists';
END
GO

-- =============================================
-- 2. INSERT SAMPLE PlazaValetPoints DATA
-- =============================================
PRINT 'Inserting sample PlazaValetPoints data...';

-- Get first active plaza and company for sample data
DECLARE @SamplePlazaId INT, @SampleCompanyId INT;

SELECT TOP 1 @SamplePlazaId = p.Id, @SampleCompanyId = p.CompanyId
FROM Plaza p 
WHERE p.IsActive = 1;

IF @SamplePlazaId IS NOT NULL
BEGIN
    -- Insert sample valet points if none exist
    IF NOT EXISTS (SELECT 1 FROM PlazaValetPoints WHERE PlazaId = @SamplePlazaId)
    BEGIN
        INSERT INTO PlazaValetPoints (PlazaId, CompanyId, PointName, PointCode, Location, Capacity, IsActive, CreatedBy, CreatedOn)
        VALUES 
            (@SamplePlazaId, @SampleCompanyId, 'Main Entrance Valet', 'VP001', 'Main Entrance', 20, 1, 1, GETDATE()),
            (@SamplePlazaId, @SampleCompanyId, 'Side Entrance Valet', 'VP002', 'Side Entrance', 15, 1, 1, GETDATE()),
            (@SamplePlazaId, @SampleCompanyId, 'VIP Valet Point', 'VP003', 'VIP Section', 10, 1, 1, GETDATE());
        
        PRINT '   ✅ Sample valet points inserted successfully';
    END
    ELSE
    BEGIN
        PRINT '   ⚠️  Sample valet points already exist';
    END
END
ELSE
BEGIN
    PRINT '   ❌ No active plaza found - cannot insert sample data';
END
GO

-- =============================================
-- 3. FIX STORED PROCEDURES - Add missing OUTPUT parameters
-- =============================================
PRINT 'Fixing stored procedures with OUTPUT parameters...';

-- Fix sp_Valet_OTP_Generate
PRINT 'Fixing sp_Valet_OTP_Generate...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_OTP_Generate]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_OTP_Generate];
GO

CREATE PROCEDURE [dbo].[sp_Valet_OTP_Generate]
    @MobileNumber NVARCHAR(15),
    @OTPCode NVARCHAR(10),
    @ExpiryMinutes INT,
    @CreatedBy INT,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Deactivate existing OTPs for this mobile number
        UPDATE OTP 
        SET IsActive = 0, ModifiedBy = @CreatedBy, ModifiedOn = GETDATE()
        WHERE MobileNumber = @MobileNumber AND IsActive = 1;
        
        -- Insert new OTP
        INSERT INTO OTP (MobileNumber, OTP, ExpireTime, IsActive, CreatedBy, CreatedOn)
        VALUES (@MobileNumber, @OTPCode, @ExpiryMinutes, 1, @CreatedBy, GETDATE());
        
        SET @NewId = SCOPE_IDENTITY();
        
        SELECT 1 as Success, 'OTP generated successfully' as Message, @NewId as Id;
        
    END TRY
    BEGIN CATCH
        SET @NewId = 0;
        SELECT 0 as Success, ERROR_MESSAGE() as Message, 0 as Id;
    END CATCH
END;
GO

-- Fix sp_Valet_Customer_Create
PRINT 'Fixing sp_Valet_Customer_Create...';

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Customer_Create]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Customer_Create];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Customer_Create]
    @MobileNumber NVARCHAR(15),
    @Name NVARCHAR(100) = NULL,
    @AddressId DECIMAL = NULL,
    @CreatedBy INT,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if customer already exists
        IF EXISTS (SELECT 1 FROM Customer WHERE MobileNumber = @MobileNumber AND IsActive = 1)
        BEGIN
            SELECT @NewId = Id FROM Customer WHERE MobileNumber = @MobileNumber AND IsActive = 1;
            SELECT 1 as Success, 'Customer already exists' as Message, @NewId as Id;
            RETURN;
        END
        
        -- Insert new customer
        INSERT INTO Customer (Name, MobileNumber, AddressId, IsActive, CreatedBy, CreatedOn)
        VALUES (@Name, @MobileNumber, @AddressId, 1, @CreatedBy, GETDATE());
        
        SET @NewId = SCOPE_IDENTITY();
        
        SELECT 1 as Success, 'Customer created successfully' as Message, @NewId as Id;
        
    END TRY
    BEGIN CATCH
        SET @NewId = 0;
        SELECT 0 as Success, ERROR_MESSAGE() as Message, 0 as Id;
    END CATCH
END;
GO

-- =============================================
-- 4. SET NODE_ENV in database (for reference)
-- =============================================
PRINT 'Setting up environment configuration...';

-- Create a configuration table if it doesn't exist (optional)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SystemConfiguration]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[SystemConfiguration] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [ConfigKey] NVARCHAR(100) NOT NULL UNIQUE,
        [ConfigValue] NVARCHAR(500) NULL,
        [Description] NVARCHAR(200) NULL,
        [IsActive] BIT DEFAULT 1,
        [CreatedOn] DATETIME DEFAULT GETDATE(),
        [ModifiedOn] DATETIME NULL
    );
    
    -- Insert default configurations
    INSERT INTO SystemConfiguration (ConfigKey, ConfigValue, Description)
    VALUES 
        ('NODE_ENV', 'development', 'Application environment'),
        ('VALET_OTP_EXPIRY_MINUTES', '5', 'OTP expiry time in minutes'),
        ('VALET_SYSTEM_ENABLED', '1', 'Enable/disable valet system');
    
    PRINT '   ✅ SystemConfiguration table created with default values';
END
ELSE
BEGIN
    PRINT '   ⚠️  SystemConfiguration table already exists';
END
GO

-- =============================================
-- 5. CREATE SAMPLE VALET USERS
-- =============================================
PRINT 'Creating sample valet users...';

DECLARE @ValetControllerRoleId INT, @ValetDriverRoleId INT;
DECLARE @SamplePlazaId2 INT, @SampleCompanyId2 INT;

-- Get role IDs
SELECT @ValetControllerRoleId = Id FROM Roles WHERE Name = 'ValetController';
SELECT @ValetDriverRoleId = Id FROM Roles WHERE Name = 'ValetDriver';

-- Get sample plaza and company
SELECT TOP 1 @SamplePlazaId2 = p.Id, @SampleCompanyId2 = p.CompanyId
FROM Plaza p WHERE p.IsActive = 1;

IF @ValetControllerRoleId IS NOT NULL AND @SamplePlazaId2 IS NOT NULL
BEGIN
    -- Create sample valet controller user
    IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'valet.controller')
    BEGIN
        INSERT INTO Users (RoleId, Username, Password, HashPassword, FirstName, LastName, Mobile, Email, IsActive, CreatedBy, CreatedOn)
        VALUES (@ValetControllerRoleId, 'valet.controller', 'password123', 'hashed_password_here', 'Valet', 'Controller', '9876543210', '<EMAIL>', 1, 1, GETDATE());
        
        DECLARE @ValetControllerUserId INT = SCOPE_IDENTITY();
        
        -- Link to ValetControllers table
        INSERT INTO ValetControllers (UserId, PlazaValetPointId, CompanyId, PlazaId, IsActive, CreatedBy, CreatedOn)
        SELECT @ValetControllerUserId, pvp.Id, @SampleCompanyId2, @SamplePlazaId2, 1, 1, GETDATE()
        FROM PlazaValetPoints pvp 
        WHERE pvp.PlazaId = @SamplePlazaId2 AND pvp.IsActive = 1
        ORDER BY pvp.Id
        OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY;
        
        PRINT '   ✅ Sample valet controller user created';
    END
    
    -- Create sample valet driver user
    IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'valet.driver')
    BEGIN
        INSERT INTO Users (RoleId, Username, Password, HashPassword, FirstName, LastName, Mobile, Email, IsActive, CreatedBy, CreatedOn)
        VALUES (@ValetDriverRoleId, 'valet.driver', 'password123', 'hashed_password_here', 'Valet', 'Driver', '**********', '<EMAIL>', 1, 1, GETDATE());
        
        DECLARE @ValetDriverUserId INT = SCOPE_IDENTITY();
        
        -- Link to ValetDrivers table
        INSERT INTO ValetDrivers (UserId, DriverLicenseNumber, PhoneNumber, IsActive, CompanyId, PlazaId, PlazaValetPointId, CreatedBy, CreatedOn)
        SELECT @ValetDriverUserId, 'DL123456789', '**********', 1, @SampleCompanyId2, @SamplePlazaId2, pvp.Id, 1, GETDATE()
        FROM PlazaValetPoints pvp 
        WHERE pvp.PlazaId = @SamplePlazaId2 AND pvp.IsActive = 1
        ORDER BY pvp.Id
        OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY;
        
        PRINT '   ✅ Sample valet driver user created';
    END
END
ELSE
BEGIN
    PRINT '   ❌ Cannot create valet users - missing roles or plaza data';
END
GO

-- =============================================
-- VERIFICATION
-- =============================================
PRINT '';
PRINT 'Verifying valet system components...';
PRINT '====================================';

-- Check PlazaValetPoints
SELECT COUNT(*) as ValetPointsCount FROM PlazaValetPoints WHERE IsActive = 1;
PRINT 'Active valet points created';

-- Check valet users
SELECT COUNT(*) as ValetUsersCount 
FROM Users u
INNER JOIN Roles r ON u.RoleId = r.Id
WHERE r.Name IN ('ValetController', 'ValetDriver') AND u.IsActive = 1;
PRINT 'Active valet users created';

-- Test fixed stored procedures
DECLARE @TestNewId INT;
EXEC sp_Valet_OTP_Generate 
    @MobileNumber = '9999999999',
    @OTPCode = '123456',
    @ExpiryMinutes = 5,
    @CreatedBy = 1,
    @NewId = @TestNewId OUTPUT;
PRINT 'OTP generation test completed';

PRINT '';
PRINT '🎉 VALET SYSTEM ROOT CAUSE FIXED!';
PRINT '=================================';
PRINT '✅ PlazaValetPoints table created with sample data';
PRINT '✅ Stored procedures fixed with OUTPUT parameters';
PRINT '✅ Sample valet users created';
PRINT '✅ System configuration added';
PRINT '';
PRINT '🚀 Valet API endpoints should now work properly!';
PRINT 'Ready for comprehensive API testing!';
PRINT '==========================================';
