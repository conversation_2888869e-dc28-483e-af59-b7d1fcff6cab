const sql = require('mssql');
require('dotenv').config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT) || 30000,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT) || 30000,
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 30000,
  }
};

async function checkSubModulesStructure() {
  let pool;
  
  try {
    console.log('Connecting to database...');
    pool = await sql.connect(dbConfig);
    console.log('Connected successfully!');
    
    // Check SubModules table structure
    console.log('\n=== SubModules Table Structure ===');
    const columnsResult = await pool.request().query(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'SubModules'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('Columns in SubModules table:');
    columnsResult.recordset.forEach(col => {
      console.log(`- ${col.COLUMN_NAME} (${col.DATA_TYPE}) - Nullable: ${col.IS_NULLABLE}`);
    });
    
    // Check existing SubModules
    console.log('\n=== Existing SubModules ===');
    const subModulesResult = await pool.request().query(`
      SELECT 
        sm.*,
        m.Name as ModuleName
      FROM SubModules sm
      LEFT JOIN Modules m ON sm.ModuleId = m.Id
      ORDER BY sm.ModuleId, sm.Id
    `);
    
    console.log('Existing SubModules:');
    subModulesResult.recordset.forEach(sm => {
      console.log(`- ID: ${sm.Id}, Module: ${sm.ModuleName}, Name: ${sm.Name}`);
    });
    
    // Check Modules table
    console.log('\n=== Modules Table ===');
    const modulesResult = await pool.request().query(`
      SELECT * FROM Modules ORDER BY Id
    `);
    
    console.log('Existing Modules:');
    modulesResult.recordset.forEach(m => {
      console.log(`- ID: ${m.Id}, Name: ${m.Name}`);
    });
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

checkSubModulesStructure();
