const db = require('../config/database');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const { responseHandler } = require('../Utils/ResponseHandler');

/**
 * Authentication Controller
 * Handles user authentication, token generation, and password management
 */
class AuthController {
  /**
   * Login a user and generate JWT token
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async login(req, res) {
    try {
      const { username, password } = req.body;

      // Validate required fields
      if (!username || !password) {
        return responseHandler.badRequest(res, 'Username and password are required');
      }

      // Query to get user with role information
      const query = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Username = @username AND u.IsActive = 1
      `;

      const result = await db.query(query, { username });

      if (result.recordset.length === 0) {
        return responseHandler.unauthorized(res, 'Invalid username or password');
      }

      const user = result.recordset[0];

      // Verify password - try bcrypt first, then fallback to direct comparison for backward compatibility
      let isPasswordValid = false;

      // Check if password is hashed with bcrypt
      if (user.Password.startsWith('$2a$') || user.Password.startsWith('$2b$') || user.Password.startsWith('$2y$')) {
        // Password is hashed with bcrypt, use bcrypt.compare
        isPasswordValid = await bcrypt.compare(password, user.Password);
      } else {
        // For backward compatibility, check if password matches directly
        isPasswordValid = (password === user.Password);

        // If password matches, update it to use bcrypt hashing for future logins
        if (isPasswordValid) {
          const hashedPassword = await bcrypt.hash(password, 10);
          await db.query(`
            UPDATE Users
            SET Password = @password, ModifiedOn = GETDATE()
            WHERE Id = @userId
          `, {
            userId: user.Id,
            password: hashedPassword
          });
          console.log(`Updated password hash for user: ${user.Username} (ID: ${user.Id})`);
        }
      }

      if (!isPasswordValid) {
        return responseHandler.unauthorized(res, 'Invalid username or password');
      }

      // Generate JWT token
      const token = jwt.sign(
        {
          userId: user.Id,
          role: user.RoleName
        },
        process.env.JWT_SECRET || 'your-secret-key',
        { expiresIn: '24h' }
      );

      // Get user permissions
      const permissionsQuery = `
        SELECT DISTINCT sm.Name as SubModule, p.Name as Permission
        FROM RolePermissions rp
        JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
        JOIN Permissions p ON smp.PermissionId = p.Id
        JOIN SubModules sm ON smp.SubModuleId = sm.Id
        WHERE rp.RoleId = @roleId AND rp.IsActive = 1
      `;

      const permissionsResult = await db.query(permissionsQuery, { roleId: user.RoleId });

      // Format permissions as an object for easier frontend use
      const permissions = {};
      permissionsResult.recordset.forEach(item => {
        if (!permissions[item.SubModule]) {
          permissions[item.SubModule] = [];
        }
        permissions[item.SubModule].push(item.Permission);
      });

      // Get accessible companies for the user
      let companies = [];

      // SuperAdmin has access to all companies
      if (user.RoleName === 'SuperAdmin') {
        const companiesQuery = `SELECT Id, CompanyName FROM tblCompanyMaster WHERE IsActive = 1`;
        const companiesResult = await db.query(companiesQuery);
        companies = companiesResult.recordset;
      }
      // Other roles have access to specific companies
      else {
        const companiesQuery = `
          SELECT c.Id, c.CompanyName
          FROM UserCompany uc
          JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND c.IsActive = 1
        `;
        const companiesResult = await db.query(companiesQuery, { userId: user.Id });
        companies = companiesResult.recordset;
      }

      // Get accessible plazas for the user
      let plazas = [];

      // SuperAdmin and CompanyAdmin have access based on their scope
      if (user.RoleName === 'SuperAdmin') {
        const plazasQuery = `SELECT Id, PlazaName, CompanyId FROM Plaza WHERE IsActive = 1`;
        const plazasResult = await db.query(plazasQuery);
        plazas = plazasResult.recordset;
      }
      else if (user.RoleName === 'CompanyAdmin') {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName, p.CompanyId
          FROM Plaza p
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId: user.Id });
        plazas = plazasResult.recordset;
      }
      // PlazaManager has access to specific plazas
      else {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName, p.CompanyId
          FROM UserPlaza up
          JOIN Plaza p ON up.PlazaId = p.Id
          WHERE up.UserId = @userId AND up.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId: user.Id });
        plazas = plazasResult.recordset;
      }

      // Update last login time
      await db.query(`
        UPDATE Users
        SET ModifiedOn = GETDATE()
        WHERE Id = @userId
      `, { userId: user.Id });

      // Return user data and token directly in the response data
      // This matches what the frontend expects
      return res.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
          token,
          user: {
            id: user.Id,
            username: user.Username,
            firstName: user.FirstName,
            lastName: user.LastName,
            email: user.Email,
            role: user.RoleName,
            permissions: permissions,
            companies: companies,
            plazas: plazas
          }
        }
      });
    } catch (error) {
      console.error('Error in login controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Get current user information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCurrentUser(req, res) {
    try {
      const userId = req.user.id;

      // Query to get user with role information
      const query = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Id = @userId AND u.IsActive = 1
      `;

      const result = await db.query(query, { userId });

      if (result.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = result.recordset[0];

      // Get user permissions
      const permissionsQuery = `
        SELECT DISTINCT sm.Name as SubModule, p.Name as Permission
        FROM RolePermissions rp
        JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
        JOIN Permissions p ON smp.PermissionId = p.Id
        JOIN SubModules sm ON smp.SubModuleId = sm.Id
        WHERE rp.RoleId = @roleId AND rp.IsActive = 1
      `;

      const permissionsResult = await db.query(permissionsQuery, { roleId: user.RoleId });

      // Format permissions as an object for easier frontend use
      const permissions = {};
      permissionsResult.recordset.forEach(item => {
        if (!permissions[item.SubModule]) {
          permissions[item.SubModule] = [];
        }
        permissions[item.SubModule].push(item.Permission);
      });

      // Get accessible companies for the user
      let companies = [];

      // SuperAdmin has access to all companies
      if (user.RoleName === 'SuperAdmin') {
        const companiesQuery = `SELECT Id, CompanyName FROM tblCompanyMaster WHERE IsActive = 1`;
        const companiesResult = await db.query(companiesQuery);
        companies = companiesResult.recordset;
      }
      // Other roles have access to specific companies
      else {
        const companiesQuery = `
          SELECT c.Id, c.CompanyName
          FROM UserCompany uc
          JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND c.IsActive = 1
        `;
        const companiesResult = await db.query(companiesQuery, { userId: user.Id });
        companies = companiesResult.recordset;
      }

      // Get accessible plazas for the user
      let plazas = [];

      // SuperAdmin and CompanyAdmin have access based on their scope
      if (user.RoleName === 'SuperAdmin') {
        const plazasQuery = `SELECT Id, PlazaName, CompanyId FROM Plaza WHERE IsActive = 1`;
        const plazasResult = await db.query(plazasQuery);
        plazas = plazasResult.recordset;
      }
      else if (user.RoleName === 'CompanyAdmin') {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName, p.CompanyId
          FROM Plaza p
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId: user.Id });
        plazas = plazasResult.recordset;
      }
      // PlazaManager has access to specific plazas
      else {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName, p.CompanyId
          FROM UserPlaza up
          JOIN Plaza p ON up.PlazaId = p.Id
          WHERE up.UserId = @userId AND up.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId: user.Id });
        plazas = plazasResult.recordset;
      }

      // Return user data directly in the response
      return res.status(200).json({
        success: true,
        message: 'User retrieved successfully',
        data: {
          id: user.Id,
          username: user.Username,
          firstName: user.FirstName,
          lastName: user.LastName,
          email: user.Email,
          role: user.RoleName,
          permissions: permissions,
          companies: companies,
          plazas: plazas
        }
      });
    } catch (error) {
      console.error('Error in getCurrentUser controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Change user password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async changePassword(req, res) {
    try {
      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body;

      // Validate required fields
      if (!currentPassword || !newPassword) {
        return responseHandler.badRequest(res, 'Current password and new password are required');
      }

      // Get user
      const userQuery = `SELECT * FROM Users WHERE Id = @userId AND IsActive = 1`;
      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = userResult.recordset[0];

      // Verify current password - try bcrypt first, then fallback to direct comparison
      let isPasswordValid = false;

      // Check if password is hashed with bcrypt
      if (user.Password.startsWith('$2a$') || user.Password.startsWith('$2b$') || user.Password.startsWith('$2y$')) {
        // Password is hashed with bcrypt, use bcrypt.compare
        isPasswordValid = await bcrypt.compare(currentPassword, user.Password);
      } else {
        // For backward compatibility, check if password matches directly
        isPasswordValid = (currentPassword === user.Password);
      }

      if (!isPasswordValid) {
        return responseHandler.badRequest(res, 'Current password is incorrect');
      }

      // Hash the new password
      const updatedPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      await db.query(`
        UPDATE Users
        SET Password = @password, ModifiedBy = @userId, ModifiedOn = GETDATE()
        WHERE Id = @userId
      `, {
        userId,
        password: updatedPassword
      });

      return responseHandler.success(res, null, 'Password changed successfully');
    } catch (error) {
      console.error('Error in changePassword controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Logout user (client-side only, just for API completeness)
   * @param {Object} _ - Express request object (not used)
   * @param {Object} res - Express response object
   */
  static async logout(_, res) {
    // JWT tokens are stateless, so logout is handled on the client side
    // by removing the token from storage
    return responseHandler.success(res, null, 'Logout successful');
  }
}

module.exports = AuthController;
