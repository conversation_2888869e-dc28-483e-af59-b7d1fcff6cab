# Cleanup script for PWVMS deployment
# This script removes test files and unnecessary utility scripts

Write-Host "Starting cleanup process..." -ForegroundColor Green

# Test files in backend
Write-Host "Removing backend test files..." -ForegroundColor Yellow
Remove-Item -Path "d:/PWVMS/backend/src/test*.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/config/testConnection.js" -Force

# Utility scripts for setup and maintenance
Write-Host "Removing utility scripts..." -ForegroundColor Yellow
Remove-Item -Path "d:/PWVMS/backend/src/check*.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/update*.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/fix*.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/create*.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/dropUserTable.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/listTables.js" -Force
Remove-Item -Path "d:/PWVMS/backend/src/deleteOldRoles.js" -Force

# Test files in frontend
Write-Host "Removing frontend test files..." -ForegroundColor Yellow
Remove-Item -Path "d:/PWVMS/frontend/src/testApi.js" -Force
Remove-Item -Path "d:/PWVMS/frontend/src/testPlazaApi.js" -Force

# Backup files
Write-Host "Removing backup files..." -ForegroundColor Yellow
Get-ChildItem -Path "d:/PWVMS" -Recurse -Include "*.bak", "*.backup" | ForEach-Object {
    Write-Host "Removing $($_.FullName)" -ForegroundColor Gray
    Remove-Item -Path $_.FullName -Force
}

Write-Host "Cleanup completed successfully!" -ForegroundColor Green