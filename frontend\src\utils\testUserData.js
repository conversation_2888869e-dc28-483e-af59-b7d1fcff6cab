// Test utility to verify user data structure for companies and plazas display
// This can be used in browser console to test the feature

export const testUserData = {
  // Sample user with companies and plazas
  userWithAssignments: {
    Id: 1,
    Username: "testuser",
    FirstName: "Test",
    LastName: "User",
    Email: "<EMAIL>",
    RoleName: "CompanyAdmin",
    companies: [
      { Id: 1, CompanyName: "ABC Corporation" },
      { Id: 2, CompanyName: "XYZ Limited" },
      { Id: 3, CompanyName: "Very Long Company Name That Should Be Truncated" }
    ],
    plazas: [
      { Id: 1, PlazaName: "Main Plaza" },
      { Id: 2, PlazaName: "Secondary Plaza" },
      { Id: 3, PlazaName: "Another Very Long Plaza Name That Should Be Truncated" },
      { Id: 4, PlazaName: "Fourth Plaza" },
      { Id: 5, PlazaName: "Fifth Plaza" }
    ]
  },

  // Sample user with no assignments
  userWithoutAssignments: {
    Id: 2,
    Username: "noassign",
    FirstName: "No",
    LastName: "Assignment",
    Email: "<EMAIL>",
    RoleName: "PlazaManager",
    companies: [],
    plazas: []
  },

  // Sample SuperAdmin user
  superAdminUser: {
    Id: 3,
    Username: "superadmin",
    FirstName: "Super",
    LastName: "Admin",
    Email: "<EMAIL>",
    RoleName: "SuperAdmin",
    companies: [
      { Id: 1, CompanyName: "Company A" },
      { Id: 2, CompanyName: "Company B" },
      { Id: 3, CompanyName: "Company C" }
    ],
    plazas: [
      { Id: 1, PlazaName: "Plaza 1" },
      { Id: 2, PlazaName: "Plaza 2" }
    ]
  }
};

// Test function to verify the display logic
export const testDisplayLogic = (user) => {
  console.log('Testing user:', user.Username);
  console.log('Role:', user.RoleName);
  
  // Test companies display
  if (user.companies && user.companies.length > 0) {
    console.log('Companies to display:', user.companies.slice(0, 3));
    if (user.companies.length > 3) {
      console.log('Additional companies:', user.companies.length - 3);
    }
  } else {
    console.log('No companies assigned');
  }
  
  // Test plazas display
  if (user.plazas && user.plazas.length > 0) {
    console.log('Plazas to display:', user.plazas.slice(0, 3));
    if (user.plazas.length > 3) {
      console.log('Additional plazas:', user.plazas.length - 3);
    }
  } else {
    console.log('No plazas assigned');
  }
  
  // Test truncation logic
  user.companies?.forEach(company => {
    if (company.CompanyName.length > 15) {
      console.log(`Company "${company.CompanyName}" will be truncated to "${company.CompanyName.substring(0, 15)}..."`);
    }
  });
  
  user.plazas?.forEach(plaza => {
    if (plaza.PlazaName.length > 15) {
      console.log(`Plaza "${plaza.PlazaName}" will be truncated to "${plaza.PlazaName.substring(0, 15)}..."`);
    }
  });
  
  console.log('---');
};

// Run tests for all sample users
export const runAllTests = () => {
  console.log('=== Testing User Companies & Plazas Display Logic ===');
  testDisplayLogic(testUserData.userWithAssignments);
  testDisplayLogic(testUserData.userWithoutAssignments);
  testDisplayLogic(testUserData.superAdminUser);
  console.log('=== Tests Complete ===');
};

// Usage in browser console:
// import { runAllTests } from './utils/testUserData.js';
// runAllTests();
