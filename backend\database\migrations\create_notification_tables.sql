-- Create Email Notifications Table
-- This table logs all email notifications sent by the system
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmailNotifications' AND xtype='U')
BEGIN
    CREATE TABLE EmailNotifications (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        RecipientEmail NVARCHAR(255) NOT NULL,
        Subject NVARCHAR(500) NOT NULL,
        Body NVARCHAR(MAX) NOT NULL,
        EmailType NVARCHAR(50) NOT NULL, -- 'welcome', 'activity', 'deletion', 'unauthorized', 'general'
        Status NVARCHAR(20) NOT NULL, -- 'sent', 'failed', 'pending'
        RetryCount INT DEFAULT 0,
        ErrorMessage NVARCHAR(MAX) NULL,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        SentAt DATETIME2 NULL,
        
        INDEX IX_EmailNotifications_RecipientEmail (RecipientEmail),
        INDEX IX_EmailNotifications_EmailType (EmailType),
        INDEX IX_EmailNotifications_Status (Status),
        INDEX IX_EmailNotifications_CreatedAt (CreatedAt)
    );
    
    PRINT '✅ EmailNotifications table created successfully';
END
ELSE
BEGIN
    PRINT '⚠️ EmailNotifications table already exists';
END

-- Create Activity Notifications Table
-- This table logs all hierarchical notifications sent for user activities
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ActivityNotifications' AND xtype='U')
BEGIN
    CREATE TABLE ActivityNotifications (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        ActorUserId INT NOT NULL, -- User who performed the action
        Action NVARCHAR(50) NOT NULL, -- 'create', 'update', 'delete', 'toggle'
        EntityType NVARCHAR(50) NOT NULL, -- 'user', 'company', 'plaza', 'lane', 'anpr', 'digitalpay', 'fastag'
        EntityId INT NULL, -- ID of the affected entity
        NotifiedUserId INT NOT NULL, -- User who received the notification
        NotificationMethod NVARCHAR(20) NOT NULL, -- 'email', 'sms', 'push'
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        
        FOREIGN KEY (ActorUserId) REFERENCES Users(Id),
        FOREIGN KEY (NotifiedUserId) REFERENCES Users(Id),
        
        INDEX IX_ActivityNotifications_ActorUserId (ActorUserId),
        INDEX IX_ActivityNotifications_NotifiedUserId (NotifiedUserId),
        INDEX IX_ActivityNotifications_Action (Action),
        INDEX IX_ActivityNotifications_EntityType (EntityType),
        INDEX IX_ActivityNotifications_CreatedAt (CreatedAt)
    );
    
    PRINT '✅ ActivityNotifications table created successfully';
END
ELSE
BEGIN
    PRINT '⚠️ ActivityNotifications table already exists';
END

-- Create Email Templates Table (Optional - for future use)
-- This table can store customizable email templates
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmailTemplates' AND xtype='U')
BEGIN
    CREATE TABLE EmailTemplates (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        TemplateName NVARCHAR(100) NOT NULL UNIQUE,
        Subject NVARCHAR(500) NOT NULL,
        HtmlBody NVARCHAR(MAX) NOT NULL,
        TextBody NVARCHAR(MAX) NULL,
        TemplateType NVARCHAR(50) NOT NULL, -- 'welcome', 'activity', 'deletion', 'unauthorized'
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME2 DEFAULT GETDATE(),
        ModifiedAt DATETIME2 DEFAULT GETDATE(),
        CreatedBy INT NULL,
        ModifiedBy INT NULL,
        
        FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
        FOREIGN KEY (ModifiedBy) REFERENCES Users(Id),
        
        INDEX IX_EmailTemplates_TemplateName (TemplateName),
        INDEX IX_EmailTemplates_TemplateType (TemplateType),
        INDEX IX_EmailTemplates_IsActive (IsActive)
    );
    
    PRINT '✅ EmailTemplates table created successfully';
END
ELSE
BEGIN
    PRINT '⚠️ EmailTemplates table already exists';
END

-- Insert default email templates
IF NOT EXISTS (SELECT * FROM EmailTemplates WHERE TemplateName = 'welcome_user')
BEGIN
    INSERT INTO EmailTemplates (TemplateName, Subject, HtmlBody, TemplateType) VALUES
    ('welcome_user', 'Welcome to ParkwizOps - Your Account is Ready', 
     '<h1>Welcome {{firstName}} {{lastName}}</h1><p>Your account has been created with {{roleName}} privileges.</p>', 
     'welcome'),
    
    ('activity_notification', 'Activity Notification - {{action}} performed', 
     '<h1>Activity Notification</h1><p>{{actorName}} has {{action}}d a {{entityType}}.</p>', 
     'activity'),
    
    ('deletion_alert', '⚠️ Deletion Alert - {{entityType}} removed', 
     '<h1>Deletion Alert</h1><p>{{actorName}} has deleted a {{entityType}}.</p>', 
     'deletion'),
    
    ('unauthorized_attempt', '🚨 Unauthorized Access Attempt', 
     '<h1>Security Alert</h1><p>Unauthorized access attempt detected.</p>', 
     'unauthorized');
    
    PRINT '✅ Default email templates inserted';
END
ELSE
BEGIN
    PRINT '⚠️ Default email templates already exist';
END

-- Create indexes for better performance
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_EmailNotifications_Composite')
BEGIN
    CREATE INDEX IX_EmailNotifications_Composite 
    ON EmailNotifications (EmailType, Status, CreatedAt DESC);
    PRINT '✅ Composite index created on EmailNotifications';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ActivityNotifications_Composite')
BEGIN
    CREATE INDEX IX_ActivityNotifications_Composite 
    ON ActivityNotifications (EntityType, Action, CreatedAt DESC);
    PRINT '✅ Composite index created on ActivityNotifications';
END

-- Create view for notification summary
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'vw_NotificationSummary')
BEGIN
    EXEC('
    CREATE VIEW vw_NotificationSummary AS
    SELECT 
        an.EntityType,
        an.Action,
        COUNT(*) as NotificationCount,
        MAX(an.CreatedAt) as LastNotification,
        actor_role.Name as ActorRole,
        notified_role.Name as NotifiedRole
    FROM ActivityNotifications an
    JOIN Users actor ON an.ActorUserId = actor.Id
    JOIN Users notified ON an.NotifiedUserId = notified.Id
    JOIN Roles actor_role ON actor.RoleId = actor_role.Id
    JOIN Roles notified_role ON notified.RoleId = notified_role.Id
    WHERE an.CreatedAt >= DATEADD(day, -30, GETDATE())
    GROUP BY an.EntityType, an.Action, actor_role.Name, notified_role.Name
    ');
    PRINT '✅ NotificationSummary view created';
END
ELSE
BEGIN
    PRINT '⚠️ NotificationSummary view already exists';
END

-- Create stored procedure for cleanup old notifications
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_CleanupOldNotifications')
BEGIN
    EXEC('
    CREATE PROCEDURE sp_CleanupOldNotifications
        @DaysToKeep INT = 90
    AS
    BEGIN
        SET NOCOUNT ON;
        
        DECLARE @CutoffDate DATETIME2 = DATEADD(day, -@DaysToKeep, GETDATE());
        DECLARE @DeletedEmails INT, @DeletedActivities INT;
        
        -- Delete old email notifications
        DELETE FROM EmailNotifications 
        WHERE CreatedAt < @CutoffDate AND Status = ''sent'';
        SET @DeletedEmails = @@ROWCOUNT;
        
        -- Delete old activity notifications
        DELETE FROM ActivityNotifications 
        WHERE CreatedAt < @CutoffDate;
        SET @DeletedActivities = @@ROWCOUNT;
        
        PRINT ''Cleanup completed:'';
        PRINT ''- Email notifications deleted: '' + CAST(@DeletedEmails AS NVARCHAR(10));
        PRINT ''- Activity notifications deleted: '' + CAST(@DeletedActivities AS NVARCHAR(10));
    END
    ');
    PRINT '✅ CleanupOldNotifications stored procedure created';
END
ELSE
BEGIN
    PRINT '⚠️ CleanupOldNotifications stored procedure already exists';
END

PRINT '🎉 Notification system database setup completed successfully!';
PRINT '';
PRINT '📋 Summary of created objects:';
PRINT '- EmailNotifications table';
PRINT '- ActivityNotifications table';
PRINT '- EmailTemplates table';
PRINT '- vw_NotificationSummary view';
PRINT '- sp_CleanupOldNotifications procedure';
PRINT '';
PRINT '🔧 Next steps:';
PRINT '1. Configure SMTP settings in environment variables';
PRINT '2. Test email functionality with EmailService';
PRINT '3. Integrate NotificationService with controllers';
PRINT '4. Set up automated cleanup job for old notifications';
