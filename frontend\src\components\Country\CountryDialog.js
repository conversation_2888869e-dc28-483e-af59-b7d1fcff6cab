import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';

export default function CountryDialog({ isOpen, onClose, onSave, onSubmit, country, initialData, title }) {
  // Support both onSave and onSubmit props
  const saveHandler = onSave || onSubmit;
  
  const [formData, setFormData] = useState({
    Id: '',
    name: '',
    isActive: 1
  });

  useEffect(() => {
    if (country) {
      setFormData({
        Id: country.Id || '',
        name: country.Name || '',
        isActive: country.IsActive !== undefined ? country.IsActive : 1
      });
    } else if (initialData) {
      // Support for initialData format
      setFormData({
        Id: initialData.Id || initialData._id || '',
        name: initialData.Name || initialData.name || '',
        isActive: initialData.IsActive !== undefined ? initialData.IsActive : 
                 (initialData.status === 'active' ? 1 : 0)
      });
    } else {
      setFormData({
        Id: '',
        name: '',
        isActive: 1
      });
    }
  }, [country, initialData]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name === 'IsActive' ? 'isActive' : name === 'Name' ? 'name' : name]: type === 'checkbox' ? (checked ? 1 : 0) : value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (typeof saveHandler === 'function') {
      saveHandler(formData);
    } else {
      console.error("No save handler (onSave or onSubmit) provided to CountryDialog");
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 md:p-0">
      <div className="bg-white rounded-lg w-full max-w-sm md:max-w-md lg:max-w-lg transition-all duration-300 ease-in-out transform">
        <div className="flex justify-between items-center p-4 md:p-6 border-b border-gray-200">
          <h2 className="text-lg md:text-xl font-semibold text-gray-800">
            {title || (country ? 'Edit Country' : 'Add Country')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-2 rounded-full transition-colors duration-200"
            aria-label="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 md:p-6 space-y-5">
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="name">
              Country Name
            </label>
            <input
              type="text"
              id="name"
              name="Name"
              value={formData.name}
              onChange={handleChange}
              className="shadow-sm border border-gray-300 rounded-md w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              required
              placeholder="Enter country name"
            />
          </div>

          <div className="mb-4">
            <label className="flex items-center space-x-2 cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  name="IsActive"
                  checked={formData.isActive === 1}
                  onChange={handleChange}
                  className="sr-only"
                />
                <div className={`block w-14 h-7 rounded-full transition-colors duration-200 ease-in-out ${formData.isActive === 1 ? 'bg-blue-500' : 'bg-gray-300'}`}></div>
                <div className={`absolute left-1 top-1 bg-white w-5 h-5 rounded-full transition-transform duration-200 ease-in-out ${formData.isActive === 1 ? 'transform translate-x-7' : ''}`}></div>
              </div>
              <span className="text-gray-700 text-sm font-medium group-hover:text-gray-900 transition-colors duration-200">
                {formData.isActive === 1 ? 'Active' : 'Inactive'}
              </span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-300 text-sm md:text-base"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm md:text-base"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}