/**
 * <PERSON><PERSON>t to hash existing plain text passwords in the database
 * Run this script once to convert all existing passwords to bcrypt hashes
 */

const db = require('./backend/src/config/database');
const bcrypt = require('bcrypt');

async function hashExistingPasswords() {
  try {
    console.log('Connecting to database...');
    await db.connect();
    console.log('Connected to database successfully');

    // Get all users with plain text passwords
    console.log('Fetching users...');
    const users = await db.query(`SELECT Id, Password FROM Users WHERE IsActive = 1`);
    
    if (users.recordset.length === 0) {
      console.log('No users found');
      return;
    }

    console.log(`Found ${users.recordset.length} users. Starting password hashing...`);
    
    // Process each user
    for (const user of users.recordset) {
      try {
        // Skip if password is already hashed (bcrypt hashes start with $2a$, $2b$, or $2y$)
        if (user.Password && (
            user.Password.startsWith('$2a$') || 
            user.Password.startsWith('$2b$') || 
            user.Password.startsWith('$2y$')
        )) {
          console.log(`User ID ${user.Id}: Password already hashed, skipping`);
          continue;
        }

        // Hash the password
        const hashedPassword = await bcrypt.hash(user.Password, 10);
        
        // Update the user's password
        await db.query(`
          UPDATE Users 
          SET Password = @password, ModifiedOn = GETDATE() 
          WHERE Id = @userId
        `, { 
          userId: user.Id, 
          password: hashedPassword 
        });
        
        console.log(`User ID ${user.Id}: Password hashed successfully`);
      } catch (userError) {
        console.error(`Error processing user ID ${user.Id}:`, userError);
      }
    }
    
    console.log('Password hashing completed');
  } catch (error) {
    console.error('Error in hashExistingPasswords:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('Database connection closed');
    } catch (closeError) {
      console.error('Error closing database connection:', closeError);
    }
  }
}

// Run the function
hashExistingPasswords();
