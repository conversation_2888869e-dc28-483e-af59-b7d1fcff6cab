-- Add Security module and permissions for CompanyAdmin
-- Check if Security module exists
IF NOT EXISTS (SELECT 1 FROM Modules WHERE Name = 'Security')
BEGIN
    -- Create Security module
    INSERT INTO Modules (Name, Description, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES ('Security', 'Security settings and permissions', 'security', 10, 1, 1, GETDATE());
    
    DECLARE @ModuleId INT = SCOPE_IDENTITY();
    
    -- Create Security submodule
    INSERT INTO SubModules (ModuleId, Name, Icon, Path, IsActive, CreatedBy, CreatedOn)
    VALUES (@ModuleId, 'Security', 'security', '/security', 1, 1, GETDATE());
    
    DECLARE @SubModuleId INT = SCOPE_IDENTITY();
    
    -- Get View permission ID
    DECLARE @PermissionId INT;
    SELECT @PermissionId = Id FROM Permissions WHERE Name = 'View';
    
    -- Create SubModulePermission entry
    INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
    VALUES (@SubModuleId, @PermissionId, 1, 1, GETDATE());
    
    DECLARE @SubModulePermissionId INT = SCOPE_IDENTITY();
    
    -- Get role IDs
    DECLARE @SuperAdminRoleId INT, @CompanyAdminRoleId INT;
    SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
    SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
    
    -- Grant permissions to roles
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
    
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
    
    PRINT 'Security module and permissions created successfully';
END
ELSE
BEGIN
    PRINT 'Security module already exists';
    
    -- Get module ID
    DECLARE @ModuleId INT;
    SELECT @ModuleId = Id FROM Modules WHERE Name = 'Security';
    
    -- Check if Security submodule exists
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE ModuleId = @ModuleId AND Name = 'Security')
    BEGIN
        -- Create Security submodule
        INSERT INTO SubModules (ModuleId, Name, Icon, Path, IsActive, CreatedBy, CreatedOn)
        VALUES (@ModuleId, 'Security', 'security', '/security', 1, 1, GETDATE());
        
        DECLARE @SubModuleId INT = SCOPE_IDENTITY();
        
        -- Get View permission ID
        DECLARE @PermissionId INT;
        SELECT @PermissionId = Id FROM Permissions WHERE Name = 'View';
        
        -- Create SubModulePermission entry
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SubModuleId, @PermissionId, 1, 1, GETDATE());
        
        DECLARE @SubModulePermissionId INT = SCOPE_IDENTITY();
        
        -- Get role IDs
        DECLARE @SuperAdminRoleId INT, @CompanyAdminRoleId INT;
        SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
        SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
        
        -- Grant permissions to roles
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
        
        INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
        
        PRINT 'Security submodule and permissions created successfully';
    END
    ELSE
    BEGIN
        PRINT 'Security submodule already exists';
        
        -- Get submodule ID
        DECLARE @SubModuleId INT;
        SELECT @SubModuleId = Id FROM SubModules WHERE ModuleId = @ModuleId AND Name = 'Security';
        
        -- Get View permission ID
        DECLARE @PermissionId INT;
        SELECT @PermissionId = Id FROM Permissions WHERE Name = 'View';
        
        -- Check if permission exists
        IF NOT EXISTS (SELECT 1 FROM SubModulePermissions 
                      WHERE SubModuleId = @SubModuleId AND PermissionId = @PermissionId)
        BEGIN
            -- Create SubModulePermission entry
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@SubModuleId, @PermissionId, 1, 1, GETDATE());
            
            DECLARE @SubModulePermissionId INT = SCOPE_IDENTITY();
            
            -- Get role IDs
            DECLARE @SuperAdminRoleId INT, @CompanyAdminRoleId INT;
            SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
            SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
            
            -- Grant permissions to roles
            INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
            
            INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
            
            PRINT 'View permission created for Security submodule';
        END
        ELSE
        BEGIN
            PRINT 'View permission already exists for Security submodule';
            
            -- Get SubModulePermission ID
            DECLARE @SubModulePermissionId INT;
            SELECT @SubModulePermissionId = Id FROM SubModulePermissions 
            WHERE SubModuleId = @SubModuleId AND PermissionId = @PermissionId;
            
            -- Get role IDs
            DECLARE @SuperAdminRoleId INT, @CompanyAdminRoleId INT;
            SELECT @SuperAdminRoleId = Id FROM Roles WHERE Name = 'SuperAdmin';
            SELECT @CompanyAdminRoleId = Id FROM Roles WHERE Name = 'CompanyAdmin';
            
            -- Grant permissions to roles if not already granted
            IF NOT EXISTS (SELECT 1 FROM RolePermissions 
                          WHERE RoleId = @SuperAdminRoleId AND SubModulePermissionId = @SubModulePermissionId)
            BEGIN
                INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
                VALUES (@SuperAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
                
                PRINT 'Permission granted to SuperAdmin';
            END
            ELSE
            BEGIN
                PRINT 'SuperAdmin already has the permission';
            END
            
            IF NOT EXISTS (SELECT 1 FROM RolePermissions 
                          WHERE RoleId = @CompanyAdminRoleId AND SubModulePermissionId = @SubModulePermissionId)
            BEGIN
                INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
                VALUES (@CompanyAdminRoleId, @SubModulePermissionId, 1, 1, GETDATE());
                
                PRINT 'Permission granted to CompanyAdmin';
            END
            ELSE
            BEGIN
                PRINT 'CompanyAdmin already has the permission';
            END
        END
    END
END