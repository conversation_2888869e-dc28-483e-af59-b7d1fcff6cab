const sql = require("mssql");
const db = require("../config/database"); // adjust path if needed

const stateController = {
  // Create a new state
  createState: async (req, res) => {
    try {
      const { name, countryId, isActive = true } = req.body;

      if (!name) {
        return res.status(400).json({ error: "State name is required." });
      }

      if (!countryId) {
        return res.status(400).json({ error: "Country ID is required." });
      }

      const query = `
        INSERT INTO State (Name, CountryId, IsActive, CreatedBy, CreatedOn)
        VALUES (@name, @countryId, @isActive, @createdBy, @createdOn);
      `;

      const result = await db.query(query, {
        name,
        countryId,
        isActive,
        createdBy: null,
        createdOn: new Date(),
      });

      res.status(201).json({ success: true, message: "✅ State created successfully", result });
    } catch (error) {
      console.error("Create state failed:", error.message);
      res.status(500).json({ error: "Failed to create state" });
    }
  },

  // Get all states
  getStates: async (req, res) => {
    try {
      const query = `
        SELECT s.*, c.Name as CountryName 
        FROM State s
        LEFT JOIN Country c ON s.CountryId = c.Id
      `;
      const result = await db.query(query);
      res.json({ states: result.recordset });
    } catch (error) {
      console.error("Get states failed:", error.message);
      res.status(500).json({ error: "Failed to fetch states" });
    }
  },

  // Get states by country ID
  getStatesByCountry: async (req, res) => {
    try {
      const { countryId } = req.params;
      const result = await db.query(
        "SELECT * FROM State WHERE CountryId = @countryId", 
        { countryId }
      );
      res.json({ states: result.recordset });
    } catch (error) {
      console.error("Get states by country failed:", error.message);
      res.status(500).json({ error: "Failed to fetch states by country" });
    }
  },

  // Get a state by ID
  getStateById: async (req, res) => {
    try {
      const { id } = req.params;
      const query = `
        SELECT s.*, c.Name as CountryName 
        FROM State s
        LEFT JOIN Country c ON s.CountryId = c.Id
        WHERE s.Id = @id
      `;
      const result = await db.query(query, { id });

      if (result.recordset.length === 0) {
        return res.status(404).json({ error: "State not found" });
      }

      res.json({ state: result.recordset[0] });
    } catch (error) {
      console.error("Get state by ID failed:", error.message);
      res.status(500).json({ error: "Failed to fetch state" });
    }
  },

  // Update a state
  updateState: async (req, res) => {
    try {
      const { id } = req.params;
      const { name, countryId, isActive } = req.body;

      const result = await db.query(`
        UPDATE State
        SET Name = @name, CountryId = @countryId, IsActive = @isActive,
            ModifiedBy = @modifiedBy, ModifiedOn = @modifiedOn
        WHERE Id = @id
      `, {
        id,
        name,
        countryId,
        isActive,
        modifiedBy: null,
        modifiedOn: new Date(),
      });

      if (result.rowsAffected[0] === 0) {
        return res.status(404).json({ error: "State not found or nothing changed" });
      }

      res.json({ message: "✅ State updated successfully" });
    } catch (error) {
      console.error("Update state failed:", error.message);
      res.status(500).json({ error: "Failed to update state" });
    }
  },

  // Delete a state
  deleteState: async (req, res) => {
    try {
      const { id } = req.params;
      await db.query("DELETE FROM State WHERE Id = @id", { id });
      res.json({ message: "🗑️ State deleted successfully" });
    } catch (error) {
      console.error("Delete state failed:", error.message);
      res.status(500).json({ error: "Failed to delete state" });
    }
  }
};

module.exports = stateController;