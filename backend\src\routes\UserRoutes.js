// backend/src/routes/UserRoutes.js
const express = require('express');
const router = express.Router();
const UserController = require('../controllers/UserController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/users
 * @desc    Get all users
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.get('/', auth(['View']), UserController.getAllUsers);

/**
 * @route   GET /api/users/roles
 * @desc    Get all roles
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.get('/roles', auth(['View']), UserController.getAllRoles);

/**
 * @route   GET /api/users/:id
 * @desc    Get a user by ID
 * @access  Private (SuperAdmin, CompanyAdmin, or self)
 */
router.get('/:id', auth(['View']), UserController.getUserById);

/**
 * @route   GET /api/users/:id/companies
 * @desc    Get user's company assignments
 * @access  Private (SuperAdmin, CompanyAdmin, or self)
 */
router.get('/:id/companies', auth(['View']), UserController.getUserCompanies);

/**
 * @route   GET /api/users/:id/plazas
 * @desc    Get user's plaza assignments
 * @access  Private (SuperAdmin, CompanyAdmin, or self)
 */
router.get('/:id/plazas', auth(['View']), UserController.getUserPlazas);

/**
 * @route   POST /api/users
 * @desc    Create a new user
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), UserController.createUser);

/**
 * @route   PUT /api/users/:id
 * @desc    Update an existing user
 * @access  Private (SuperAdmin, CompanyAdmin, or self)
 */
router.put('/:id', auth(['Edit']), UserController.updateUser);

/**
 * @route   PUT /api/users/:id/change-password
 * @desc    Change user's password
 * @access  Private (SuperAdmin, CompanyAdmin, or self)
 */
router.put('/:id/change-password', auth(), UserController.changePassword);

/**
 * @route   DELETE /api/users/:id
 * @desc    Delete a user
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), UserController.deleteUser);

module.exports = router;
