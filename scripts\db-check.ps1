# db-check.ps1 - PowerShell script to check SQL Server database

# Load environment variables from .env file
$envFile = "d:/PWVMS/backend/.env"
$envContent = Get-Content $envFile
$envVars = @{}

foreach ($line in $envContent) {
    if ($line -match '^\s*([^#][^=]+)=(.*)$') {
        $key = $matches[1].Trim()
        $value = $matches[2].Trim()
        $envVars[$key] = $value
    }
}

# Database connection parameters
$dbServer = $envVars["DB_SERVER"]
$dbName = $envVars["DB_NAME"]
$dbUser = $envVars["DB_USER"]
$dbPassword = $envVars["DB_PASSWORD"]

Write-Host "Connecting to database..."
Write-Host "Server: $dbServer"
Write-Host "Database: $dbName"
Write-Host "User: $dbUser"

# Create connection string
$connectionString = "Server=$dbServer;Database=$dbName;User Id=$dbUser;Password=$dbPassword;Encrypt=True;TrustServerCertificate=True;"

# SQL queries to run
$queries = @{
    "Database Information" = "SELECT DB_NAME() as DatabaseName, @@VERSION as SQLServerVersion, @@CONNECTIONS as TotalConnections"
    
    "Tables Used by Dashboard" = @"
SELECT TOP 10 
    t.name AS TableName,
    s.name AS SchemaName,
    p.rows AS RowCount
FROM sys.tables t
INNER JOIN sys.schemas s ON t.schema_id = s.schema_id
INNER JOIN sys.indexes i ON t.object_id = i.object_id
INNER JOIN sys.partitions p ON i.object_id = p.object_id AND i.index_id = p.index_id
WHERE i.index_id < 2  -- Clustered index or heap
AND (t.name LIKE '%Parkwiz%' OR t.name LIKE '%Plaza%' OR t.name LIKE '%Lane%')
ORDER BY p.rows DESC
"@
    
    "Sample Data from tblParkwiz_Parking_Data" = @"
SELECT TOP 5 
    PakringDataID,
    PlazaName,
    VehicleNumber,
    EntryDateTime,
    ExitDateTime,
    ParkingFee,
    iTotalGSTFee,
    PaymentMode
FROM tblParkwiz_Parking_Data
WHERE ExitDateTime IS NOT NULL
ORDER BY ExitDateTime DESC
"@
    
    "Payment Methods Distribution" = @"
SELECT
    ISNULL(PaymentMode, 'Unknown') as PaymentMode,
    COUNT(*) as TransactionCount,
    SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)) as TotalRevenue
FROM tblParkwiz_Parking_Data
WHERE ExitDateTime IS NOT NULL
GROUP BY PaymentMode
ORDER BY TotalRevenue DESC
"@
    
    "Plaza Information" = "SELECT TOP 10 * FROM Plaza"
}

try {
    # Load SQL Server module if available
    if (Get-Module -ListAvailable -Name SqlServer) {
        Import-Module SqlServer
        
        # Run each query
        foreach ($queryName in $queries.Keys) {
            Write-Host "`n--- $queryName ---" -ForegroundColor Cyan
            $query = $queries[$queryName]
            
            try {
                $results = Invoke-Sqlcmd -ConnectionString $connectionString -Query $query -ErrorAction Stop
                $results | Format-Table -AutoSize
            }
            catch {
                Write-Host "Error executing query: $_" -ForegroundColor Red
            }
        }
    }
    else {
        Write-Host "SqlServer module not found. Please install it using: Install-Module -Name SqlServer" -ForegroundColor Yellow
        
        # Alternative approach using .NET SqlClient
        Write-Host "Trying alternative approach with .NET SqlClient..." -ForegroundColor Yellow
        
        # Load the System.Data assembly
        Add-Type -AssemblyName System.Data.SqlClient
        
        # Create a connection
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $connection.Open()
        
        # Run each query
        foreach ($queryName in $queries.Keys) {
            Write-Host "`n--- $queryName ---" -ForegroundColor Cyan
            $query = $queries[$queryName]
            
            try {
                $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
                $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
                $dataset = New-Object System.Data.DataSet
                $adapter.Fill($dataset) | Out-Null
                
                $dataset.Tables[0] | Format-Table -AutoSize
            }
            catch {
                Write-Host "Error executing query: $_" -ForegroundColor Red
            }
        }
        
        # Close the connection
        $connection.Close()
    }
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
}