import api from '../services/api'; // Shared Axios instance

export const companyApi = {
  /**
   * Fetches the list of all companies.
   * GET /companies
   */
  getCompanies: async () => {
    try {
      const response = await api.get('/companies'); // Matches router.get('/')

      // Handle different response structures
      if (response.data && response.data.companies && Array.isArray(response.data.companies)) {
        return response.data.companies;
      } else if (response.data && Array.isArray(response.data)) {
        return response.data;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      }

      // Default fallback
      console.warn('Unexpected companies API response structure:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching companies:', error);
      return [];
    }
  },

  /**
   * Fetches the details of a single company by its ID.
   * GET /companies/:id
   */
  getCompanyById: async (id) => {
    const response = await api.get(`/companies/${id}`); // Matches router.get('/:id')
    return response.data;
  },

  /**
   * Creates a new company.
   * POST /companies
   */
  createCompany: async (data) => {
    console.log('Creating company with data:', data);
    const response = await api.post('/companies', data); // Matches router.post('/')
    return response.data.company;
  },

  /**
   * Updates a company by ID.
   * PUT /companies/:id
   */
  updateCompany: async (id, data) => {
    console.log('Updating company with ID:', id, 'and data:', data);
    const response = await api.put(`/companies/${id}`, data); // Matches router.put('/:id')
    return response.data.company;
  },

  /**
   * Deletes a company by ID.
   * DELETE /companies/:id
   */
  deleteCompany: async (id) => {
    await api.delete(`/companies/${id}`); // Matches router.delete('/:id')
  },

  /**
   * Fetches the branches for a specific company by ID.
   * GET /companies/:companyId/branches
   */
  getBranchesForCompany: async (companyId) => {
    const response = await api.get(`/companies/${companyId}/branches`); // Matches router.get('/:companyId/branches')
    return response.data.branches;
  },
};
