# Fastag API Documentation

This document provides comprehensive information about the Fastag API implementation and how to use it in your React components.

## Overview

The Fastag API provides a complete set of functions for managing Fastag configurations in the PWVMS system. It includes advanced features like:

- Request caching to improve performance
- Error handling with detailed logging
- Request cancellation to prevent race conditions
- Batch operations for efficient data management
- Import/export functionality
- Connection testing

## API Methods

### Basic CRUD Operations

- `getAllConfigurations(options, useCache)` - Get all Fastag configurations with optional filtering
- `getConfigurationById(id, useCache)` - Get a specific Fastag configuration by ID
- `getConfigurationsByPlaza(plazaId, useCache)` - Get configurations for a specific plaza
- `getConfigurationsByLane(laneId, useCache)` - Get configurations for a specific lane
- `createConfiguration(data)` - Create a new Fastag configuration
- `updateConfiguration(id, data)` - Update an existing Fastag configuration
- `deleteConfiguration(id)` - Delete a Fastag configuration

### Advanced Operations

- `bulkCreateOrUpdate(data)` - Create or update multiple configurations at once
- `toggleConfigurationStatus(id, updatedBy)` - Toggle the active status of a configuration
- `validateConfiguration(data)` - Validate a configuration before saving
- `testConnection(connectionData)` - Test the connection to a Fastag API endpoint
- `exportConfigurations(format, filters)` - Export configurations to CSV or Excel
- `importConfigurations(file)` - Import configurations from a file

### Cache Management

- `clearCache(endpoint)` - Clear the cache for a specific endpoint or all endpoints
- `cancelRequest(endpoint)` - Cancel an ongoing request for a specific endpoint

## React Hook

A custom React hook `useFastagApi` is provided to make it easier to use the Fastag API in your components. It handles loading states, error handling, and data management.

### Example Usage

```jsx
import React, { useEffect, useState } from 'react';
import { useFastagApi } from '../hooks/useFastagApi';

function FastagConfigList() {
  const { 
    loading, 
    error, 
    data, 
    getAllConfigurations,
    deleteConfiguration 
  } = useFastagApi();

  useEffect(() => {
    // Load all configurations when component mounts
    getAllConfigurations();
  }, [getAllConfigurations]);

  const handleDelete = async (id) => {
    const success = await deleteConfiguration(id);
    if (success) {
      // Refresh the list after deletion
      getAllConfigurations(false); // Skip cache to get fresh data
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!data) return <div>No data available</div>;

  return (
    <div>
      <h2>Fastag Configurations</h2>
      <ul>
        {data.map(config => (
          <li key={config.id}>
            {config.PlazaName} - Lane {config.LaneNumber}
            <button onClick={() => handleDelete(config.id)}>Delete</button>
          </li>
        ))}
      </ul>
    </div>
  );
}
```

### Creating or Updating a Configuration

```jsx
import React, { useState } from 'react';
import { useFastagApi } from '../hooks/useFastagApi';

function FastagConfigForm({ initialData, onSuccess }) {
  const [formData, setFormData] = useState(initialData || {
    PlazaID: '',
    CompanyID: '',
    LaneID: '',
    // ... other fields
  });

  const { 
    loading, 
    error, 
    createConfiguration,
    updateConfiguration,
    validateConfiguration
  } = useFastagApi();

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate the form data first
    const validationResult = await validateConfiguration(formData);
    if (!validationResult.valid) {
      // Handle validation errors
      return;
    }
    
    let result;
    if (initialData && initialData.id) {
      // Update existing configuration
      result = await updateConfiguration(initialData.id, formData);
    } else {
      // Create new configuration
      result = await createConfiguration(formData);
    }
    
    if (result) {
      onSuccess(result);
    }
  };

  // Form rendering and input handlers...
}
```

## Best Practices

1. **Use the cache wisely**: Enable caching for read operations that don't need the freshest data, disable it when you need real-time data.

2. **Cancel pending requests**: When navigating away from a component, use the `cancelRequest` method to prevent unnecessary network traffic.

3. **Error handling**: Always check for errors when using the API methods, especially in forms and critical operations.

4. **Batch operations**: Use `bulkCreateOrUpdate` for creating or updating multiple configurations at once to reduce API calls.

5. **Testing connections**: Before saving a configuration, use `testConnection` to verify that the Fastag API endpoint is accessible.

## Advanced Features

### Exporting Data

The `exportConfigurations` method allows exporting Fastag configurations to CSV or Excel format. It automatically triggers a file download in the browser.

```jsx
const handleExport = async () => {
  // Export all configurations to Excel
  await exportConfigurations('excel');
  
  // Or export with filters
  await exportConfigurations('csv', { PlazaID: selectedPlazaId });
};
```

### Importing Data

The `importConfigurations` method allows importing Fastag configurations from a file.

```jsx
const handleFileChange = async (e) => {
  const file = e.target.files[0];
  if (file) {
    const result = await importConfigurations(file);
    if (result.success) {
      // Handle successful import
      getAllConfigurations(false); // Refresh the list with fresh data
    }
  }
};
```

### Testing Connections

Before saving a configuration, you can test the connection to the Fastag API endpoint.

```jsx
const testFastagConnection = async () => {
  const connectionData = {
    FastagAPIAddress: formData.FastagAPIAddress,
    FastagOrgID: formData.FastagOrgID,
    FastagAgencyCode: formData.FastagAgencyCode
  };
  
  const result = await testConnection(connectionData);
  if (result.success) {
    // Connection successful
    alert('Connection successful!');
  } else {
    // Connection failed
    alert(`Connection failed: ${result.message}`);
  }
};
```

## Troubleshooting

### Common Issues

1. **Stale data**: If you're seeing outdated data, try calling the API methods with `useCache` set to `false`.

2. **Request cancellation**: If you're cancelling requests but still seeing network traffic, make sure you're using the correct endpoint path.

3. **Import/export issues**: For import/export operations, ensure the file format matches the expected format.

### Debugging

The Fastag API includes detailed error logging to help diagnose issues. Check the browser console for error messages when operations fail.

## Conclusion

The Fastag API provides a robust and feature-rich interface for managing Fastag configurations in the PWVMS system. By using the provided React hook, you can easily integrate these capabilities into your components with minimal boilerplate code.