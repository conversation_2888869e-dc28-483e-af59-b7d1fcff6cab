const express = require('express');
const router = express.Router();
const CustomerFlowController = require('../../controllers/valet/CustomerFlowController');
const { auth } = require('../../middleware/auth');

/**
 * Valet Customer Flow Routes
 * Manages customer session state, form data persistence, and flow progression
 */

// Initialize customer session
router.post('/initialize', auth(), CustomerFlowController.initializeSession);

// Update customer session with form data
router.put('/update/:sessionId', auth(), CustomerFlowController.updateSession);

// Get current session state
router.get('/session/:sessionId', auth(), CustomerFlowController.getSession);

// Complete customer session (when transaction is created)
router.post('/complete/:sessionId', auth(), CustomerFlowController.completeSession);

// Validate session step progression
router.post('/validate-step', auth(), CustomerFlowController.validateStepProgression);

// Get customer session history
router.get('/history/:customerId', auth(), CustomerFlowController.getSessionHistory);

module.exports = router;
