// Script to verify permissions in ParkwizOps database
require('dotenv').config({ path: './backend/.env' });
const sql = require('mssql');

async function verifyPermissions() {
  console.log('Verifying permissions...');
  
  try {
    // Connect to the database
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    
    // Check CompanyAdmin permissions
    console.log('CompanyAdmin Permissions:');
    const companyAdminQuery = `
      SELECT sm.Name as SubModule, p.Name as Permission 
      FROM RolePermissions rp 
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id 
      JOIN SubModules sm ON smp.SubModuleId = sm.Id 
      JOIN Permissions p ON smp.PermissionId = p.Id 
      WHERE rp.RoleId = 7 
      ORDER BY sm.Name, p.Name
    `;
    const companyAdminResult = await sql.query(companyAdminQuery);
    
    // Display first 10 permissions
    console.log('Sample of CompanyAdmin permissions:');
    companyAdminResult.recordset.slice(0, 10).forEach(row => {
      console.log(`${row.SubModule}: ${row.Permission}`);
    });
    console.log(`Total CompanyAdmin permissions: ${companyAdminResult.recordset.length}`);
    
    // Check PlazaManager permissions
    console.log('\nPlazaManager Permissions:');
    const plazaManagerQuery = `
      SELECT sm.Name as SubModule, p.Name as Permission 
      FROM RolePermissions rp 
      JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id 
      JOIN SubModules sm ON smp.SubModuleId = sm.Id 
      JOIN Permissions p ON smp.PermissionId = p.Id 
      WHERE rp.RoleId = 8 
      ORDER BY sm.Name, p.Name
    `;
    const plazaManagerResult = await sql.query(plazaManagerQuery);
    
    // Display first 10 permissions
    console.log('Sample of PlazaManager permissions:');
    plazaManagerResult.recordset.slice(0, 10).forEach(row => {
      console.log(`${row.SubModule}: ${row.Permission}`);
    });
    console.log(`Total PlazaManager permissions: ${plazaManagerResult.recordset.length}`);
    
    await sql.close();
    console.log('Verification complete.');
    
  } catch (err) {
    console.error('Error verifying permissions:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

verifyPermissions().catch(err => {
  console.error('Unhandled error:', err);
});