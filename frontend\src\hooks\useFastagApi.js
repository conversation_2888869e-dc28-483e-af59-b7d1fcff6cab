import { useState, useEffect, useCallback } from 'react';
import { fastagApi } from '../api/fastagApi';

/**
 * Custom hook for interacting with Fastag API
 * Provides loading states, error handling, and data management
 */
export const useFastagApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);

  /**
   * Reset the hook state
   */
  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  /**
   * Fetch all Fastag configurations
   * @param {Object} options - Optional parameters for filtering, pagination, etc.
   * @param {boolean} useCache - Whether to use cached data if available
   */
  const getAllConfigurations = useCallback(async (options = {}, useCache = true) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.getAllConfigurations(options, useCache);
      setData(result);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to fetch Fastag configurations');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch a single Fastag configuration by ID
   * @param {string|number} id - The ID of the configuration to fetch
   * @param {boolean} useCache - Whether to use cached data if available
   */
  const getConfigurationById = useCallback(async (id, useCache = true) => {
    if (!id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.getConfigurationById(id, useCache);
      setData(result);
      return result;
    } catch (err) {
      setError(err.message || `Failed to fetch Fastag configuration with ID ${id}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch Fastag configurations by plaza ID
   * @param {string|number} plazaId - The ID of the plaza
   * @param {boolean} useCache - Whether to use cached data if available
   */
  const getConfigurationsByPlaza = useCallback(async (plazaId, useCache = true) => {
    if (!plazaId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.getConfigurationsByPlaza(plazaId, useCache);
      setData(result);
      return result;
    } catch (err) {
      setError(err.message || `Failed to fetch Fastag configurations for plaza ${plazaId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Fetch Fastag configurations by lane ID
   * @param {string|number} laneId - The ID of the lane
   * @param {boolean} useCache - Whether to use cached data if available
   */
  const getConfigurationsByLane = useCallback(async (laneId, useCache = true) => {
    if (!laneId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.getConfigurationsByLane(laneId, useCache);
      setData(result);
      return result;
    } catch (err) {
      setError(err.message || `Failed to fetch Fastag configurations for lane ${laneId}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Create a new Fastag configuration
   * @param {Object} configData - The configuration data to create
   */
  const createConfiguration = useCallback(async (configData) => {
    if (!configData) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.createConfiguration(configData);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to create Fastag configuration');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Update an existing Fastag configuration
   * @param {string|number} id - The ID of the configuration to update
   * @param {Object} configData - The updated configuration data
   */
  const updateConfiguration = useCallback(async (id, configData) => {
    if (!id || !configData) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.updateConfiguration(id, configData);
      return result;
    } catch (err) {
      setError(err.message || `Failed to update Fastag configuration with ID ${id}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Delete a Fastag configuration
   * @param {string|number} id - The ID of the configuration to delete
   */
  const deleteConfiguration = useCallback(async (id) => {
    if (!id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      await fastagApi.deleteConfiguration(id);
      return true;
    } catch (err) {
      setError(err.message || `Failed to delete Fastag configuration with ID ${id}`);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Toggle the status of a Fastag configuration
   * @param {string|number} id - The ID of the configuration
   * @param {string} updatedBy - The user who is updating the status
   */
  const toggleConfigurationStatus = useCallback(async (id, updatedBy = 'admin') => {
    if (!id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.toggleConfigurationStatus(id, updatedBy);
      return result;
    } catch (err) {
      setError(err.message || `Failed to toggle status for Fastag configuration with ID ${id}`);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Validate a Fastag configuration before saving
   * @param {Object} configData - The configuration data to validate
   */
  const validateConfiguration = useCallback(async (configData) => {
    if (!configData) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.validateConfiguration(configData);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to validate Fastag configuration');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Test connection to a Fastag API endpoint
   * @param {Object} connectionData - The connection data to test
   */
  const testConnection = useCallback(async (connectionData) => {
    if (!connectionData) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.testConnection(connectionData);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to test Fastag API connection');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Export Fastag configurations
   * @param {string} format - The export format ('csv' or 'excel')
   * @param {Object} filters - Optional filters to apply before exporting
   */
  const exportConfigurations = useCallback(async (format = 'csv', filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const blob = await fastagApi.exportConfigurations(format, filters);
      
      // Create a download link and trigger it
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `fastag-configurations.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      
      return true;
    } catch (err) {
      setError(err.message || 'Failed to export Fastag configurations');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Import Fastag configurations from a file
   * @param {File} file - The file to import
   */
  const importConfigurations = useCallback(async (file) => {
    if (!file) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const result = await fastagApi.importConfigurations(file);
      return result;
    } catch (err) {
      setError(err.message || 'Failed to import Fastag configurations');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Clear the API cache
   * @param {string} endpoint - Optional specific endpoint to clear
   */
  const clearCache = useCallback((endpoint) => {
    fastagApi.clearCache(endpoint);
  }, []);

  /**
   * Cancel an ongoing request
   * @param {string} endpoint - The endpoint to cancel requests for
   */
  const cancelRequest = useCallback((endpoint) => {
    fastagApi.cancelRequest(endpoint);
  }, []);

  // Clean up any pending requests when the component unmounts
  useEffect(() => {
    return () => {
      // Cancel all potential ongoing requests
      cancelRequest('/fastag');
    };
  }, [cancelRequest]);

  return {
    // State
    loading,
    error,
    data,
    
    // Methods
    reset,
    getAllConfigurations,
    getConfigurationById,
    getConfigurationsByPlaza,
    getConfigurationsByLane,
    createConfiguration,
    updateConfiguration,
    deleteConfiguration,
    toggleConfigurationStatus,
    validateConfiguration,
    testConnection,
    exportConfigurations,
    importConfigurations,
    clearCache,
    cancelRequest
  };
};

export default useFastagApi;