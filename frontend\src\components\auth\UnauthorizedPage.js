// frontend/src/components/auth/UnauthorizedPage.js
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ShieldAlert, Home } from 'lucide-react';

/**
 * UnauthorizedPage component displayed when a user tries to access a page they don't have permission for
 *
 * @param {Object} props - Component props
 * @param {string} [props.message] - Custom error message to display
 * @returns {React.ReactNode} - The unauthorized page component
 */
const UnauthorizedPage = ({ message: propMessage = "You don't have permission to access this page" }) => {
  // Check if there's a message in the location state
  const location = useLocation();
  const stateMessage = location.state?.message;

  // Use the message from state if available, otherwise use the prop message
  const message = stateMessage || propMessage;
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-md p-8 max-w-md w-full text-center">
        <div className="flex justify-center mb-6">
          <div className="bg-red-100 p-3 rounded-full">
            <ShieldAlert className="w-12 h-12 text-red-500" />
          </div>
        </div>

        <h1 className="text-2xl font-bold text-gray-800 mb-2">Access Denied</h1>

        <p className="text-gray-600 mb-6">
          {message}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/dashboard"
            className="flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Home className="w-4 h-4" />
            <span>Go to Dashboard</span>
          </Link>

          <button
            onClick={() => window.history.back()}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    </div>
  );
};

// Export both as default and named export for backward compatibility
export { UnauthorizedPage };
export default UnauthorizedPage;
