
// frontend/src/services/api.js
import axios from 'axios';
import toast from 'react-hot-toast';

// Use relative URL in production, fallback to localhost in development
const API_URL = process.env.NODE_ENV === 'production' 
  ? '/api'
  : 'http://localhost:5000/api';

// Create a flag to track if we're already redirecting to avoid multiple redirects
let isRedirecting = false;

const api = axios.create({
  baseURL: API_URL,
});

// Add token to requests if it exists
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
}, (error) => {
  return Promise.reject(error);
});

// Handle response errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    const { response } = error;

    // Handle authentication errors
    if (response && response.status === 401) {
      // Token expired or invalid
      if (window.location.pathname !== '/login' && !isRedirecting) {
        isRedirecting = true;
        
        // Clear auth data
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Show error message
        toast.error('Your session has expired. Please log in again.');

        // Redirect to login page with return URL
        const returnUrl = encodeURIComponent(window.location.pathname);
        window.location.href = `/login?returnUrl=${returnUrl}`;
        
        // Reset the redirecting flag after navigation
        setTimeout(() => {
          isRedirecting = false;
        }, 2000);
      }
    }

    // Handle forbidden errors
    if (response && response.status === 403) {
      toast.error('You do not have permission to perform this action');

      // If not already on the unauthorized page, redirect there
      if (window.location.pathname !== '/unauthorized' && !isRedirecting) {
        isRedirecting = true;
        window.location.href = '/unauthorized';
        
        // Reset the redirecting flag after navigation
        setTimeout(() => {
          isRedirecting = false;
        }, 2000);
      }
    }

    // Handle server errors
    if (response && response.status >= 500) {
      toast.error('Server error. Please try again later or contact support.');
    }

    return Promise.reject(error);
  }
);

export const auth = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register/owner', userData),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (resetData) => api.post('/auth/reset-password', resetData),
  me: () => api.get('/auth/me'),
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    return Promise.resolve();
  }
};

// Add a method to check if the token is valid
export const checkAuth = async () => {
  try {
    const response = await api.get('/auth/me');
    return response.data && response.data.success;
  } catch (error) {
    return false;
  }
};

export default api;