/**
 * ============================================================================
 * # PASS REGISTRATION CONTROLLER
 * ============================================================================
 * This controller handles all CRUD operations for the tbl_Parkwiz_Pass_Reg table,
 * which manages vehicle pass registrations for toll plazas. It provides endpoints to:
 * - Retrieve all or specific pass registrations
 * - Create new pass registrations
 * - Update existing pass registrations
 * - Delete pass registrations
 * - Search pass registrations by various criteria
 *
 * @module controllers/PassRegistrationController
 * @requires mssql
 * @requires ../config/database
 * @requires ../utils/responseHandler
 */

const db = require('../config/database');
const { responseHandler } = require('../utils/responseHandler');

/**
 * Get all pass registrations with filtering based on user role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllPassRegistrations = async (req, res) => {
  try {
    const user = req.user;
    const { page = 1, limit = 10, search = '', plazaCode = '', passType = '' } = req.query;

    const offset = (page - 1) * limit;

    let query = `
      SELECT
        pr.*,
        p.PlazaName,
        c.CompanyName
      FROM tbl_Parkwiz_Pass_Reg pr
      LEFT JOIN Plaza p ON pr.PlazaCode = p.PlazaCode
      LEFT JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE pr.MarkDelete = 0
    `;

    let countQuery = `
      SELECT COUNT(*) as total
      FROM tbl_Parkwiz_Pass_Reg pr
      LEFT JOIN Plaza p ON pr.PlazaCode = p.PlazaCode
      LEFT JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE pr.MarkDelete = 0
    `;

    let queryParams = {};

    // Apply role-based filtering
    if (user.role === 'CompanyAdmin') {
      const companyFilter = ` AND p.CompanyId IN (
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      )`;
      query += companyFilter;
      countQuery += companyFilter;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      const plazaFilter = ` AND p.Id IN (
        SELECT PlazaId FROM UserPlaza
        WHERE UserId = @userId AND IsActive = 1
      )`;
      query += plazaFilter;
      countQuery += plazaFilter;
      queryParams.userId = user.id;
    }

    // Apply search filters
    if (search) {
      const searchFilter = ` AND (
        pr.HolderName LIKE @search OR
        pr.VehicleNo LIKE @search OR
        pr.GIN LIKE @search OR
        pr.ContactNo LIKE @search OR
        pr.EmailID LIKE @search
      )`;
      query += searchFilter;
      countQuery += searchFilter;
      queryParams.search = `%${search}%`;
    }

    if (plazaCode) {
      query += ` AND pr.PlazaCode = @plazaCode`;
      countQuery += ` AND pr.PlazaCode = @plazaCode`;
      queryParams.plazaCode = plazaCode;
    }

    if (passType) {
      query += ` AND pr.PassType = @passType`;
      countQuery += ` AND pr.PassType = @passType`;
      queryParams.passType = passType;
    }

    // Add pagination and ordering
    query += ` ORDER BY pr.IssueDate DESC OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY`;
    queryParams.offset = offset;
    queryParams.limit = parseInt(limit);

    // Execute queries
    const [result, countResult] = await Promise.all([
      db.query(query, queryParams),
      db.query(countQuery, queryParams)
    ]);

    const total = countResult.recordset[0].total;
    const totalPages = Math.ceil(total / limit);

    return responseHandler.success(res, {
      data: result.recordset,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalRecords: total,
        limit: parseInt(limit)
      }
    }, 'Pass registrations retrieved successfully');
  } catch (error) {
    console.error('Error fetching pass registrations:', error);
    return responseHandler.error(res, 'Failed to fetch pass registrations');
  }
};

/**
 * Get pass registration by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getPassRegistrationById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    let query = `
      SELECT
        pr.*,
        p.PlazaName,
        c.CompanyName
      FROM tbl_Parkwiz_Pass_Reg pr
      LEFT JOIN Plaza p ON pr.PlazaCode = p.PlazaCode
      LEFT JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE pr.PassRegID = @id AND pr.MarkDelete = 0
    `;

    let queryParams = { id };

    // Apply role-based filtering
    if (user.role === 'CompanyAdmin') {
      query += ` AND p.CompanyId IN (
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      query += ` AND p.Id IN (
        SELECT PlazaId FROM UserPlaza
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    }

    const result = await db.query(query, queryParams);

    if (result.recordset.length === 0) {
      return responseHandler.notFound(res, 'Pass registration not found');
    }

    return responseHandler.success(res, result.recordset[0], 'Pass registration retrieved successfully');
  } catch (error) {
    console.error('Error fetching pass registration:', error);
    return responseHandler.error(res, 'Failed to fetch pass registration');
  }
};

/**
 * Create a new pass registration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createPassRegistration = async (req, res) => {
  try {
    const {
      PlazaCode, PlazaName, ApplicationID, GIN, HolderName, CompanyName,
      VehicleType, ContactNo, PassType, PassTariffType, EmailID,
      ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
      TagMasterTagID, flgANPRPassEnable, PaymentMode, PaymentType
    } = req.body;

    // Validate required fields
    if (!GIN || !HolderName || !VehicleType || !ContactNo || !PassType || !ContractEndDate || !VehicleNo || !TagMasterTagID) {
      return responseHandler.badRequest(res, 'Required fields missing: GIN, HolderName, VehicleType, ContactNo, PassType, ContractEndDate, VehicleNo, TagMasterTagID');
    }

    const result = await db.query(`
      INSERT INTO tbl_Parkwiz_Pass_Reg (
        PlazaCode, PlazaName, ApplicationID, GIN, IssueDate, HolderName, CompanyName,
        VehicleType, ContactNo, PassType, PassTariffType, EmailID,
        ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
        TagMasterTagID, flgANPRPassEnable, MarkDelete, PaymentMode, PaymentType
      ) VALUES (
        @PlazaCode, @PlazaName, @ApplicationID, @GIN, GETDATE(), @HolderName, @CompanyName,
        @VehicleType, @ContactNo, @PassType, @PassTariffType, @EmailID,
        @ContractEndDate, @VehicleNo, @PasscardNumber, @PaidAmount,
        @TagMasterTagID, @flgANPRPassEnable, 0, @PaymentMode, @PaymentType
      )
    `, {
      PlazaCode: PlazaCode || null,
      PlazaName: PlazaName || null,
      ApplicationID: ApplicationID || null,
      GIN,
      HolderName,
      CompanyName: CompanyName || null,
      VehicleType,
      ContactNo,
      PassType,
      PassTariffType: PassTariffType || null,
      EmailID: EmailID || null,
      ContractEndDate,
      VehicleNo,
      PasscardNumber: PasscardNumber || null,
      PaidAmount: PaidAmount || null,
      TagMasterTagID,
      flgANPRPassEnable: flgANPRPassEnable || 'False',
      PaymentMode: PaymentMode || null,
      PaymentType: PaymentType || null
    });

    return responseHandler.success(res, null, 'Pass registration created successfully', 201);
  } catch (error) {
    console.error('Error creating pass registration:', error);
    return responseHandler.error(res, 'Failed to create pass registration');
  }
};

/**
 * Update an existing pass registration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updatePassRegistration = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      PlazaCode, PlazaName, ApplicationID, GIN, HolderName, CompanyName,
      VehicleType, ContactNo, PassType, PassTariffType, EmailID,
      ContractEndDate, VehicleNo, PasscardNumber, PaidAmount,
      TagMasterTagID, flgANPRPassEnable, PaymentMode, PaymentType
    } = req.body;

    const result = await db.query(`
      UPDATE tbl_Parkwiz_Pass_Reg
      SET
        PlazaCode = @PlazaCode,
        PlazaName = @PlazaName,
        ApplicationID = @ApplicationID,
        GIN = @GIN,
        HolderName = @HolderName,
        CompanyName = @CompanyName,
        VehicleType = @VehicleType,
        ContactNo = @ContactNo,
        PassType = @PassType,
        PassTariffType = @PassTariffType,
        EmailID = @EmailID,
        ContractEndDate = @ContractEndDate,
        VehicleNo = @VehicleNo,
        PasscardNumber = @PasscardNumber,
        PaidAmount = @PaidAmount,
        TagMasterTagID = @TagMasterTagID,
        flgANPRPassEnable = @flgANPRPassEnable,
        PaymentMode = @PaymentMode,
        PaymentType = @PaymentType
      WHERE PassRegID = @id AND MarkDelete = 0
    `, {
      id,
      PlazaCode,
      PlazaName,
      ApplicationID,
      GIN,
      HolderName,
      CompanyName,
      VehicleType,
      ContactNo,
      PassType,
      PassTariffType,
      EmailID,
      ContractEndDate,
      VehicleNo,
      PasscardNumber,
      PaidAmount,
      TagMasterTagID,
      flgANPRPassEnable,
      PaymentMode,
      PaymentType
    });

    if (result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'Pass registration not found');
    }

    return responseHandler.success(res, null, 'Pass registration updated successfully');
  } catch (error) {
    console.error('Error updating pass registration:', error);
    return responseHandler.error(res, 'Failed to update pass registration');
  }
};

/**
 * Delete a pass registration (soft delete)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deletePassRegistration = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(`
      UPDATE tbl_Parkwiz_Pass_Reg
      SET MarkDelete = 1
      WHERE PassRegID = @id AND MarkDelete = 0
    `, { id });

    if (result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'Pass registration not found');
    }

    return responseHandler.success(res, null, 'Pass registration deleted successfully');
  } catch (error) {
    console.error('Error deleting pass registration:', error);
    return responseHandler.error(res, 'Failed to delete pass registration');
  }
};

/**
 * Search pass registrations by vehicle number
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.searchByVehicleNumber = async (req, res) => {
  try {
    const { vehicleNo } = req.params;
    const user = req.user;

    let query = `
      SELECT
        pr.*,
        p.PlazaName,
        c.CompanyName
      FROM tbl_Parkwiz_Pass_Reg pr
      LEFT JOIN Plaza p ON pr.PlazaCode = p.PlazaCode
      LEFT JOIN tblCompanyMaster c ON p.CompanyId = c.Id
      WHERE pr.VehicleNo = @vehicleNo AND pr.MarkDelete = 0
    `;

    let queryParams = { vehicleNo };

    // Apply role-based filtering
    if (user.role === 'CompanyAdmin') {
      query += ` AND p.CompanyId IN (
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      query += ` AND p.Id IN (
        SELECT PlazaId FROM UserPlaza
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    }

    const result = await db.query(query, queryParams);

    return responseHandler.success(res, result.recordset, 'Pass registrations found');
  } catch (error) {
    console.error('Error searching pass registrations:', error);
    return responseHandler.error(res, 'Failed to search pass registrations');
  }
};
