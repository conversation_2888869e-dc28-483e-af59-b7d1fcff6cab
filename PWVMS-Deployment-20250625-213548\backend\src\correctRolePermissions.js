// Script to correct role permissions for CompanyAdmin and PlazaManager
const db = require('./config/database');

async function correctRolePermissions() {
  try {
    // Connect to database
    await db.connect();
    console.log('Database connection successful');

    // Get role IDs
    const rolesQuery = `
      SELECT Id, Name FROM Roles 
      WHERE Name IN ('CompanyAdmin', 'PlazaManager')
    `;
    
    const rolesResult = await db.query(rolesQuery);
    
    if (rolesResult.recordset.length === 0) {
      console.log('Roles not found');
      return;
    }
    
    const roles = {};
    rolesResult.recordset.forEach(role => {
      roles[role.Name] = role.Id;
    });
    
    console.log('Found roles:', roles);

    // 1. Disable CompanyAdmin permissions for Companies (except View)
    await disablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Company Management', 
      'Companies', 
      ['Create', 'Edit', 'Delete']
    );
    
    // 2. Ensure CompanyAdmin has View permission for Companies
    await enablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Company Management', 
      'Companies', 
      ['View']
    );
    
    // 3. Update CompanyAdmin permissions for Plazas
    await enablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Plaza Management', 
      'Plazas', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 4. Update CompanyAdmin permissions for Lanes
    await enablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Lane Management', 
      'Lanes', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 5. Update CompanyAdmin permissions for ANPR
    await enablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Lane Management', 
      'ANPR', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 6. Update CompanyAdmin permissions for Digital Pay
    await enablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Lane Management', 
      'Digital Pay', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 7. Update CompanyAdmin permissions for Fastag
    await enablePermissionsForRole(
      roles['CompanyAdmin'], 
      'Lane Management', 
      'Fastag', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 8. Update PlazaManager permissions for Plazas
    await enablePermissionsForRole(
      roles['PlazaManager'], 
      'Plaza Management', 
      'Plazas', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 9. Update PlazaManager permissions for Lanes
    await enablePermissionsForRole(
      roles['PlazaManager'], 
      'Lane Management', 
      'Lanes', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 10. Update PlazaManager permissions for ANPR
    await enablePermissionsForRole(
      roles['PlazaManager'], 
      'Lane Management', 
      'ANPR', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 11. Update PlazaManager permissions for Digital Pay
    await enablePermissionsForRole(
      roles['PlazaManager'], 
      'Lane Management', 
      'Digital Pay', 
      ['Create', 'Edit', 'Delete', 'View']
    );
    
    // 12. Update PlazaManager permissions for Fastag
    await enablePermissionsForRole(
      roles['PlazaManager'], 
      'Lane Management', 
      'Fastag', 
      ['Create', 'Edit', 'Delete', 'View']
    );

    console.log('Role permissions corrected successfully');
  } catch (error) {
    console.error('Database connection or query error:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('\nDatabase connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

async function enablePermissionsForRole(roleId, moduleName, subModuleName, permissions) {
  try {
    console.log(`Enabling permissions for module: ${moduleName}, submodule: ${subModuleName}, permissions: ${permissions.join(', ')}`);
    
    // Get the SubModulePermissions for the specified module and submodule
    const subModulePermissionsQuery = `
      SELECT smp.Id, sm.Name AS SubModuleName, p.Name AS PermissionName
      FROM SubModulePermissions smp
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE m.Name = @moduleName 
        AND sm.Name = @subModuleName
        AND p.Name IN (${permissions.map(p => `'${p}'`).join(',')})
        AND smp.IsActive = 1
    `;
    
    const subModulePermissionsResult = await db.query(subModulePermissionsQuery, {
      moduleName,
      subModuleName
    });
    
    console.log(`Found ${subModulePermissionsResult.recordset.length} SubModulePermissions for ${subModuleName}`);
    
    // Add these permissions to the role
    for (const permission of subModulePermissionsResult.recordset) {
      console.log(`Processing permission: ${permission.SubModuleName} - ${permission.PermissionName}`);
      
      // Check if the permission already exists
      const existingPermissionQuery = `
        SELECT Id, IsActive FROM RolePermissions
        WHERE RoleId = @roleId AND SubModulePermissionId = @subModulePermissionId
      `;
      
      const existingPermissionResult = await db.query(existingPermissionQuery, {
        roleId,
        subModulePermissionId: permission.Id
      });
      
      if (existingPermissionResult.recordset.length > 0) {
        // Update the existing permission to be active
        const existingPermission = existingPermissionResult.recordset[0];
        
        if (existingPermission.IsActive === 1) {
          console.log(`Permission already exists and is active`);
        } else {
          console.log(`Permission exists but is inactive, updating to be active`);
          
          await db.query(`
            UPDATE RolePermissions
            SET IsActive = 1, ModifiedOn = GETDATE(), ModifiedBy = 1
            WHERE Id = @id
          `, {
            id: existingPermission.Id
          });
        }
      } else {
        // Add the new permission
        console.log(`Adding new permission`);
        
        await db.query(`
          INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
          VALUES (@roleId, @subModulePermissionId, 1, 1, GETDATE())
        `, {
          roleId,
          subModulePermissionId: permission.Id
        });
      }
    }
    
    console.log(`Permissions enabled for ${subModuleName}`);
  } catch (error) {
    console.error(`Error enabling permissions for ${subModuleName}:`, error);
  }
}

async function disablePermissionsForRole(roleId, moduleName, subModuleName, permissions) {
  try {
    console.log(`Disabling permissions for module: ${moduleName}, submodule: ${subModuleName}, permissions: ${permissions.join(', ')}`);
    
    // Get the SubModulePermissions for the specified module and submodule
    const subModulePermissionsQuery = `
      SELECT smp.Id, sm.Name AS SubModuleName, p.Name AS PermissionName
      FROM SubModulePermissions smp
      JOIN SubModules sm ON smp.SubModuleId = sm.Id
      JOIN Modules m ON sm.ModuleId = m.Id
      JOIN Permissions p ON smp.PermissionId = p.Id
      WHERE m.Name = @moduleName 
        AND sm.Name = @subModuleName
        AND p.Name IN (${permissions.map(p => `'${p}'`).join(',')})
        AND smp.IsActive = 1
    `;
    
    const subModulePermissionsResult = await db.query(subModulePermissionsQuery, {
      moduleName,
      subModuleName
    });
    
    console.log(`Found ${subModulePermissionsResult.recordset.length} SubModulePermissions for ${subModuleName}`);
    
    // Disable these permissions for the role
    for (const permission of subModulePermissionsResult.recordset) {
      console.log(`Processing permission: ${permission.SubModuleName} - ${permission.PermissionName}`);
      
      // Check if the permission exists
      const existingPermissionQuery = `
        SELECT Id, IsActive FROM RolePermissions
        WHERE RoleId = @roleId AND SubModulePermissionId = @subModulePermissionId
      `;
      
      const existingPermissionResult = await db.query(existingPermissionQuery, {
        roleId,
        subModulePermissionId: permission.Id
      });
      
      if (existingPermissionResult.recordset.length > 0) {
        const existingPermission = existingPermissionResult.recordset[0];
        
        if (existingPermission.IsActive === 0) {
          console.log(`Permission already exists and is inactive`);
        } else {
          console.log(`Permission exists and is active, updating to be inactive`);
          
          await db.query(`
            UPDATE RolePermissions
            SET IsActive = 0, ModifiedOn = GETDATE(), ModifiedBy = 1
            WHERE Id = @id
          `, {
            id: existingPermission.Id
          });
        }
      } else {
        // Add the permission as inactive
        console.log(`Adding new permission as inactive`);
        
        await db.query(`
          INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
          VALUES (@roleId, @subModulePermissionId, 0, 1, GETDATE())
        `, {
          roleId,
          subModulePermissionId: permission.Id
        });
      }
    }
    
    console.log(`Permissions disabled for ${subModuleName}`);
  } catch (error) {
    console.error(`Error disabling permissions for ${subModuleName}:`, error);
  }
}

// Run the function
correctRolePermissions();
