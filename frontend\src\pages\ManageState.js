import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { stateApi } from '../api/stateApi';
import { countryApi } from '../api/countryApi';
import StateList from '../components/State/StateList';
import StateDialog from '../components/State/StateDialog';
import StateStats from '../components/State/StateStats';

export default function StateManagement() {
  const queryClient = useQueryClient();
  const toast = useToast();
  const [selectedState, setSelectedState] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingState, setEditingState] = useState(null);

  const { data: states, isLoading: statesLoading } = useQuery({
    queryKey: ['states'],
    queryFn: stateApi.getStates,
  });

  const { data: countries, isLoading: countriesLoading } = useQuery({
    queryKey: ['countries'],
    queryFn: countryApi.getCountries,
  });

  const { data: stateStats } = useQuery({
    queryKey: ['stateStats', selectedState],
    queryFn: () => (selectedState ? stateApi.getStateStats(selectedState) : null),
    enabled: !!selectedState,
  });

  const createMutation = useMutation({
    mutationFn: stateApi.createState,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['states'] });
      toast.showCrudSuccess('create', 'State');
      setDialogOpen(false);
    },
    onError: (error) => {
      toast.showCrudError('create', 'State', error.response?.data?.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => stateApi.updateState(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['states'] });
      toast.showCrudSuccess('update', 'State');
      setDialogOpen(false);
      setEditingState(null);
    },
    onError: (error) => {
      toast.showCrudError('update', 'State', error.response?.data?.message);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: stateApi.deleteState,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['states'] });
      toast.showCrudSuccess('delete', 'State');
      if (selectedState) setSelectedState(null);
    },
    onError: (error) => {
      toast.showCrudError('delete', 'State', error.response?.data?.message);
    },
  });

  const handleSubmit = (data) => {
    if (editingState) {
      updateMutation.mutate({ id: editingState.Id, data });
    } else {
      createMutation.mutate(data);
    }
  };

  const handleEdit = (state) => {
    setEditingState(state);
    setDialogOpen(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Are you sure you want to delete this state?')) {
      deleteMutation.mutate(id);
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingState(null);
  };

  if (statesLoading || countriesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">State Management</h1>
          <button
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add State
          </button>
        </div>

        <div className="bg-white rounded-lg shadow">
          <StateList
            states={states || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={setSelectedState}
          />
        </div>

        {selectedState && stateStats && <StateStats stats={stateStats} />}

        {countries && (
          <StateDialog
            isOpen={dialogOpen}
            onClose={handleCloseDialog}
            onSubmit={handleSubmit}
            initialData={
              editingState
                ? {
                    Id: editingState.Id,
                    Name: editingState.Name,
                    CountryId: editingState.CountryId,
                    IsActive: editingState.IsActive
                  }
                : undefined
            }
            title={editingState ? 'Edit State' : 'Add State'}
            countries={countries}
          />
        )}
      </div>
    </div>
  );
}
