// frontend/src/api/userApi.js
import api from '../services/api'; // Shared Axios instance

export const userApi = {
  /**
   * Fetches the list of all users.
   * GET /users
   */
  getUsers: async (filters = {}) => {
    try {
      // Build query string from filters
      const queryParams = new URLSearchParams();
      if (filters.role) queryParams.append('role', filters.role);
      if (filters.company) queryParams.append('company', filters.company);
      if (filters.plaza) queryParams.append('plaza', filters.plaza);
      if (filters.search) queryParams.append('search', filters.search);

      const queryString = queryParams.toString();
      const url = `/users${queryString ? `?${queryString}` : ''}`;

      const response = await api.get(url);

      // Handle different response structures
      if (response.data && response.data.data) {
        return response.data;
      } else if (response.data && response.data.users) {
        return { data: response.data.users };
      } else if (Array.isArray(response.data)) {
        return { data: response.data };
      }

      // Default fallback
      console.warn('Unexpected users API response structure:', response.data);
      return { data: [] };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { data: [] };
    }
  },

  /**
   * Fetches the details of a single user by ID.
   * GET /users/:id
   */
  getUserById: async (id) => {
    const response = await api.get(`/users/${id}`);
    return response.data.user || response.data;
  },

  /**
   * Fetches all available roles.
   * GET /users/roles
   */
  getRoles: async () => {
    try {
      const response = await api.get('/users/roles');

      // Handle different response structures
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && response.data.roles) {
        return response.data.roles;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }

      // Default fallback
      console.warn('Unexpected roles API response structure:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching roles:', error);
      return [];
    }
  },

  /**
   * Fetches companies assigned to a user.
   * GET /users/:id/companies
   */
  getUserCompanies: async (userId) => {
    try {
      const response = await api.get(`/users/${userId}/companies`);

      // Handle different response structures
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && response.data.companies) {
        return response.data.companies;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }

      // Default fallback
      console.warn('Unexpected user companies API response structure:', response.data);
      return [];
    } catch (error) {
      console.error(`Error fetching companies for user ${userId}:`, error);
      return [];
    }
  },

  /**
   * Fetches plazas assigned to a user.
   * GET /users/:id/plazas
   */
  getUserPlazas: async (userId) => {
    try {
      const response = await api.get(`/users/${userId}/plazas`);

      // Handle different response structures
      if (response.data && response.data.data) {
        return response.data.data;
      } else if (response.data && response.data.plazas) {
        return response.data.plazas;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }

      // Default fallback
      console.warn('Unexpected user plazas API response structure:', response.data);
      return [];
    } catch (error) {
      console.error(`Error fetching plazas for user ${userId}:`, error);
      return [];
    }
  },

  /**
   * Creates a new user.
   * POST /users
   */
  createUser: async (userData) => {
    const response = await api.post('/users', userData);
    return response.data.user || response.data;
  },

  /**
   * Updates an existing user.
   * PUT /users/:id
   */
  updateUser: async (id, userData) => {
    const response = await api.put(`/users/${id}`, userData);
    return response.data.user || response.data;
  },

  /**
   * Changes a user's password.
   * PUT /users/:id/change-password
   */
  changePassword: async (id, passwordData) => {
    const response = await api.put(`/users/${id}/change-password`, passwordData);
    return response.data;
  },

  /**
   * Deletes a user.
   * DELETE /users/:id
   */
  deleteUser: async (id) => {
    const response = await api.delete(`/users/${id}`);
    return response.data;
  }
};
