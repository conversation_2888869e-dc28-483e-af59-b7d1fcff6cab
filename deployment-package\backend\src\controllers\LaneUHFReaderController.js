/**
 * ============================================================================
 * # LANE UHF READER CONTROLLER
 * ============================================================================
 * This controller handles all CRUD operations for the tblLaneUHFReaderDetails table,
 * which manages UHF reader configurations for toll plaza lanes. It provides endpoints to:
 * - Retrieve all or specific UHF reader configurations
 * - Create new UHF reader configurations
 * - Update existing UHF reader configurations
 * - Delete UHF reader configurations
 * - Toggle UHF reader status
 *
 * @module controllers/LaneUHFReaderController
 * @requires mssql
 * @requires ../config/database
 * @requires ../utils/responseHandler
 */

const db = require('../config/database');
const responseHandler = require('../utils/responseHandler');

/**
 * Get all UHF reader configurations with filtering based on user role
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getAllUHFReaderConfigurations = async (req, res) => {
  try {
    const user = req.user;
    let query = `
      SELECT
        u.*,
        p.PlazaName,
        c.CompanyName,
        l.LaneNumber as LaneDisplayNumber,
        l.LaneType
      FROM tblLaneUHFReaderDetails u
      LEFT JOIN Plaza p ON u.PlazaID = p.Id
      LEFT JOIN tblCompanyMaster c ON u.CompanyID = c.Id
      LEFT JOIN tblLaneDetails l ON u.LaneID = l.LaneID
      WHERE u.ActiveStatus = '1'
    `;

    let queryParams = {};

    // Apply role-based filtering
    if (user.role === 'CompanyAdmin') {
      query += ` AND u.CompanyID IN (
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      query += ` AND u.PlazaID IN (
        SELECT PlazaId FROM UserPlaza
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    }

    query += ` ORDER BY c.CompanyName, p.PlazaName, u.ReaderLaneNumber`;

    const result = await db.query(query, queryParams);

    return responseHandler.success(res, result.recordset, 'UHF reader configurations retrieved successfully');
  } catch (error) {
    console.error('Error fetching UHF reader configurations:', error);
    return responseHandler.error(res, 'Failed to fetch UHF reader configurations');
  }
};

/**
 * Get UHF reader configuration by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUHFReaderConfigurationById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    let query = `
      SELECT
        u.*,
        p.PlazaName,
        c.CompanyName,
        l.LaneNumber as LaneDisplayNumber,
        l.LaneType
      FROM tblLaneUHFReaderDetails u
      LEFT JOIN Plaza p ON u.PlazaID = p.Id
      LEFT JOIN tblCompanyMaster c ON u.CompanyID = c.Id
      LEFT JOIN tblLaneDetails l ON u.LaneID = l.LaneID
      WHERE u.UHFReaderID = @id AND u.ActiveStatus = '1'
    `;

    let queryParams = { id };

    // Apply role-based filtering
    if (user.role === 'CompanyAdmin') {
      query += ` AND u.CompanyID IN (
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      query += ` AND u.PlazaID IN (
        SELECT PlazaId FROM UserPlaza
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    }

    const result = await db.query(query, queryParams);

    if (result.recordset.length === 0) {
      return responseHandler.notFound(res, 'UHF reader configuration not found');
    }

    return responseHandler.success(res, result.recordset[0], 'UHF reader configuration retrieved successfully');
  } catch (error) {
    console.error('Error fetching UHF reader configuration:', error);
    return responseHandler.error(res, 'Failed to fetch UHF reader configuration');
  }
};

/**
 * Get UHF reader configurations by lane ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getUHFReaderConfigurationsByLane = async (req, res) => {
  try {
    const { laneId } = req.params;
    const user = req.user;

    let query = `
      SELECT
        u.*,
        p.PlazaName,
        c.CompanyName,
        l.LaneNumber as LaneDisplayNumber,
        l.LaneType
      FROM tblLaneUHFReaderDetails u
      LEFT JOIN Plaza p ON u.PlazaID = p.Id
      LEFT JOIN tblCompanyMaster c ON u.CompanyID = c.Id
      LEFT JOIN tblLaneDetails l ON u.LaneID = l.LaneID
      WHERE u.LaneID = @laneId AND u.ActiveStatus = '1'
    `;

    let queryParams = { laneId };

    // Apply role-based filtering
    if (user.role === 'CompanyAdmin') {
      query += ` AND u.CompanyID IN (
        SELECT CompanyId FROM UserCompany
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      query += ` AND u.PlazaID IN (
        SELECT PlazaId FROM UserPlaza
        WHERE UserId = @userId AND IsActive = 1
      )`;
      queryParams.userId = user.id;
    }

    const result = await db.query(query, queryParams);

    return responseHandler.success(res, result.recordset, 'UHF reader configurations retrieved successfully');
  } catch (error) {
    console.error('Error fetching UHF reader configurations by lane:', error);
    return responseHandler.error(res, 'Failed to fetch UHF reader configurations');
  }
};

/**
 * Create a new UHF reader configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createUHFReaderConfiguration = async (req, res) => {
  try {
    const {
      PlazaID,
      CompanyID,
      LaneID,
      ReaderLaneNumber,
      EnableUHFReader,
      ReaderIPAddress,
      ReaderPort,
      IOBoardIPAddress,
      IOBoardPort,
      flgFastag
    } = req.body;

    // Validate required fields
    if (!PlazaID || !CompanyID || !LaneID) {
      return responseHandler.badRequest(res, 'PlazaID, CompanyID, and LaneID are required');
    }

    // Get current user ID for UpdatedBy
    const updatedBy = req.user ? req.user.id.toString() : '1';

    const result = await db.query(`
      INSERT INTO tblLaneUHFReaderDetails (
        PlazaID,
        CompanyID,
        LaneID,
        ReaderLaneNumber,
        EnableUHFReader,
        ReaderIPAddress,
        ReaderPort,
        IOBoardIPAddress,
        IOBoardPort,
        ActiveStatus,
        flgFastag,
        UpdatedDateTime,
        UpdatedBy
      ) VALUES (
        @PlazaID,
        @CompanyID,
        @LaneID,
        @ReaderLaneNumber,
        @EnableUHFReader,
        @ReaderIPAddress,
        @ReaderPort,
        @IOBoardIPAddress,
        @IOBoardPort,
        '1',
        @flgFastag,
        GETDATE(),
        @UpdatedBy
      )
    `, {
      PlazaID,
      CompanyID,
      LaneID,
      ReaderLaneNumber: ReaderLaneNumber || null,
      EnableUHFReader: EnableUHFReader || 'False',
      ReaderIPAddress: ReaderIPAddress || null,
      ReaderPort: ReaderPort || null,
      IOBoardIPAddress: IOBoardIPAddress || null,
      IOBoardPort: IOBoardPort || null,
      flgFastag: flgFastag || 'False',
      UpdatedBy: updatedBy
    });

    return responseHandler.success(res, null, 'UHF reader configuration created successfully', 201);
  } catch (error) {
    console.error('Error creating UHF reader configuration:', error);
    return responseHandler.error(res, 'Failed to create UHF reader configuration');
  }
};

/**
 * Update an existing UHF reader configuration
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateUHFReaderConfiguration = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      PlazaID,
      CompanyID,
      LaneID,
      ReaderLaneNumber,
      EnableUHFReader,
      ReaderIPAddress,
      ReaderPort,
      IOBoardIPAddress,
      IOBoardPort,
      flgFastag
    } = req.body;

    // Get current user ID for UpdatedBy
    const updatedBy = req.user ? req.user.id.toString() : '1';

    const result = await db.query(`
      UPDATE tblLaneUHFReaderDetails
      SET
        PlazaID = @PlazaID,
        CompanyID = @CompanyID,
        LaneID = @LaneID,
        ReaderLaneNumber = @ReaderLaneNumber,
        EnableUHFReader = @EnableUHFReader,
        ReaderIPAddress = @ReaderIPAddress,
        ReaderPort = @ReaderPort,
        IOBoardIPAddress = @IOBoardIPAddress,
        IOBoardPort = @IOBoardPort,
        flgFastag = @flgFastag,
        UpdatedDateTime = GETDATE(),
        UpdatedBy = @UpdatedBy
      WHERE UHFReaderID = @id AND ActiveStatus = '1'
    `, {
      id,
      PlazaID,
      CompanyID,
      LaneID,
      ReaderLaneNumber,
      EnableUHFReader,
      ReaderIPAddress,
      ReaderPort,
      IOBoardIPAddress,
      IOBoardPort,
      flgFastag,
      UpdatedBy: updatedBy
    });

    if (result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'UHF reader configuration not found');
    }

    return responseHandler.success(res, null, 'UHF reader configuration updated successfully');
  } catch (error) {
    console.error('Error updating UHF reader configuration:', error);
    return responseHandler.error(res, 'Failed to update UHF reader configuration');
  }
};

/**
 * Delete a UHF reader configuration (soft delete)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.deleteUHFReaderConfiguration = async (req, res) => {
  try {
    const { id } = req.params;
    const updatedBy = req.user ? req.user.id.toString() : '1';

    const result = await db.query(`
      UPDATE tblLaneUHFReaderDetails
      SET
        ActiveStatus = '0',
        UpdatedDateTime = GETDATE(),
        UpdatedBy = @UpdatedBy
      WHERE UHFReaderID = @id AND ActiveStatus = '1'
    `, {
      id,
      UpdatedBy: updatedBy
    });

    if (result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'UHF reader configuration not found');
    }

    return responseHandler.success(res, null, 'UHF reader configuration deleted successfully');
  } catch (error) {
    console.error('Error deleting UHF reader configuration:', error);
    return responseHandler.error(res, 'Failed to delete UHF reader configuration');
  }
};

/**
 * Toggle UHF reader enable/disable status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.toggleUHFReaderStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { enabled } = req.body;
    const updatedBy = req.user ? req.user.id.toString() : '1';

    const enableStatus = enabled ? 'True' : 'False';

    const result = await db.query(`
      UPDATE tblLaneUHFReaderDetails
      SET
        EnableUHFReader = @enableStatus,
        UpdatedDateTime = GETDATE(),
        UpdatedBy = @UpdatedBy
      WHERE UHFReaderID = @id AND ActiveStatus = '1'
    `, {
      id,
      enableStatus,
      UpdatedBy: updatedBy
    });

    if (result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'UHF reader configuration not found');
    }

    const statusMessage = enabled ? 'enabled' : 'disabled';
    return responseHandler.success(res, null, `UHF reader ${statusMessage} successfully`);
  } catch (error) {
    console.error('Error toggling UHF reader status:', error);
    return responseHandler.error(res, 'Failed to toggle UHF reader status');
  }
};
