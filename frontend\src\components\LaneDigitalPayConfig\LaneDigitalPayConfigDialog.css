/* Styles for the Lane Digital Pay Configuration Dialog */

/* Style for disabled sections */
.toggle-section.disabled {
    opacity: 0.7;
    pointer-events: none;
    }
    
    /* Allow clicking on checkboxes even in disabled sections */
    .toggle-section.disabled input[type=\"checkbox\"] {
    pointer-events: auto;
    opacity: 1;
    }
    
    /* Style for disabled inputs */
    input:disabled, select:disabled, textarea:disabled {
    background-color: #f3f4f6 !important;
    cursor: not-allowed;
    }
    
    /* Style for checkboxes */
    input[type=\"checkbox\"] {
    cursor: pointer;
    }
    
    /* Style for section headers */
    .text-lg.font-medium.text-gray-900.mb-4 {
    display: flex;
    align-items: center;
    }
    
    /* Style for the disabled message */
    .text-sm.text-gray-500.font-normal {
    font-style: italic;
    }
  