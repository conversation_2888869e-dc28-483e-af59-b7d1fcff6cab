require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function verifyPeakHours() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Date range from the log
    const startDateStr = '2025-05-31T18:30:00.000Z';
    const endDateStr = '2025-06-30T18:29:59.999Z';

    // Check peak hours
    const peakHoursResult = await sql.query(`
      SELECT
        DATEPART(HOUR, ExitDateTime) as Hour,
        COUNT(*) as TransactionCount
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${startDateStr}' AND '${endDateStr}'
      GROUP BY DATEPART(HOUR, ExitDateTime)
      ORDER BY Hour
    `);
    
    console.log('Peak Hours Data:');
    console.log(JSON.stringify(peakHoursResult.recordset, null, 2));

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

verifyPeakHours();