# Redis Configuration for PWVMS
# Copy this file to .env and update with your Redis settings

# Redis Connection Settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Redis Advanced Settings
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000
REDIS_RETRY_ATTEMPTS=3
REDIS_RETRY_DELAY=1000

# Cache TTL Settings (in seconds)
CACHE_TTL_DASHBOARD_SUMMARY=300
CACHE_TTL_DASHBOARD_CHARTS=600
CACHE_TTL_USER_SESSION=3600
CACHE_TTL_USER_PERMISSIONS=1800
CACHE_TTL_LIVE_DATA=30
CACHE_TTL_STATIC_DATA=86400

# Rate Limiting Settings
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL=false

# Real-time Features
REALTIME_ENABLED=true
PUBSUB_ENABLED=true
NOTIFICATIONS_ENABLED=true

# Performance Settings
REDIS_POOL_SIZE=10
REDIS_IDLE_TIMEOUT=60000
REDIS_MAX_RETRIES=3

# Security Settings
REDIS_TLS_ENABLED=false
REDIS_AUTH_ENABLED=false
REDIS_KEY_PREFIX=pwvms:

# Monitoring and Logging
REDIS_MONITORING_ENABLED=true
REDIS_LOG_LEVEL=info
CACHE_METRICS_ENABLED=true