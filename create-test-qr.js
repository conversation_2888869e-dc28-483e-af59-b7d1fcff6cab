const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/valet';

async function createTestQRCode() {
  console.log('🔧 Creating Test QR Code for Testing...\n');

  try {
    // First, let's check if we have any plazas
    const plazasResponse = await axios.get('http://localhost:5000/api/plazas');
    console.log('📍 Available Plazas:', plazasResponse.data.data?.length || 0);
    
    if (plazasResponse.data.data && plazasResponse.data.data.length > 0) {
      const plaza = plazasResponse.data.data[0];
      console.log('🏢 Using Plaza:', plaza.PlazaName, '(ID:', plaza.Id + ')');
      
      // Check if we have valet points for this plaza
      const valetPointsResponse = await axios.get(`http://localhost:5000/api/plazavaletpoints?plazaId=${plaza.Id}`);
      console.log('🅿️ Valet Points:', valetPointsResponse.data.data?.length || 0);
      
      if (valetPointsResponse.data.data && valetPointsResponse.data.data.length > 0) {
        const valetPoint = valetPointsResponse.data.data[0];
        console.log('🎯 Using Valet Point:', valetPoint.ValetPointName, '(ID:', valetPoint.Id + ')');
        
        // Now create a QR code
        const qrResponse = await axios.post(`${BASE_URL}/qrcode/generate`, {
          plazaValetPointId: valetPoint.Id,
          qrType: 'VALET_POINT'
        }, {
          headers: {
            'Authorization': 'Bearer test-token', // You might need a valid token
            'Content-Type': 'application/json'
          }
        });
        
        console.log('✅ QR Code Created Successfully!');
        console.log('📱 QR Data:', qrResponse.data.data.qrData);
        console.log('\n🧪 TEST INSTRUCTIONS:');
        console.log('1. Go to http://localhost:3000/valet-customer');
        console.log('2. Click "Start Valet Service"');
        console.log('3. Use Manual Entry and paste this QR data:');
        console.log('   ', qrResponse.data.data.qrData);
        
      } else {
        console.log('❌ No valet points found. Creating a test valet point...');
        
        // Create a test valet point
        const newValetPoint = await axios.post('http://localhost:5000/api/plazavaletpoints', {
          PlazaId: plaza.Id,
          ValetPointName: 'Test Valet Point',
          ParkingFee: 100,
          IsActive: true
        });
        
        console.log('✅ Test Valet Point Created:', newValetPoint.data.data.Id);
        
        // Now create QR code
        const qrResponse = await axios.post(`${BASE_URL}/qrcode/generate`, {
          plazaValetPointId: newValetPoint.data.data.Id,
          qrType: 'VALET_POINT'
        });
        
        console.log('✅ QR Code Created Successfully!');
        console.log('📱 QR Data:', qrResponse.data.data.qrData);
      }
    } else {
      console.log('❌ No plazas found in database. Please create a plaza first.');
    }

  } catch (error) {
    console.log('❌ Error:', error.response?.status || error.code);
    console.log('📄 Error Details:', JSON.stringify(error.response?.data || error.message, null, 2));
    
    // Provide manual test data
    console.log('\n🔧 MANUAL TEST DATA:');
    console.log('If QR creation failed, you can test with this sample QR data:');
    console.log('QR Data: VALET_TEST_' + Date.now());
    console.log('\nNote: This will show "Invalid QR code" but will test the API endpoint.');
  }
}

createTestQRCode();
