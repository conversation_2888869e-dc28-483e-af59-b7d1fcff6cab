// Script to set up modules, submodules, and permissions in the database
const db = require('./config/database');

async function setupModulesAndPermissions() {
  try {
    // Connect to database
    await db.connect();
    console.log('Database connection successful');

    // First, check if Permissions table has the necessary permissions
    const permissionsQuery = `SELECT * FROM Permissions`;
    const permissionsResult = await db.query(permissionsQuery);
    
    if (permissionsResult.recordset.length === 0) {
      console.log('No permissions found in the database. Creating basic permissions...');
      
      // Create basic permissions
      await db.query(`
        INSERT INTO Permissions (Name, Description, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ('View', 'Permission to view resources', 1, 1, GETDATE()),
        ('Create', 'Permission to create resources', 1, 1, GETDATE()),
        ('Edit', 'Permission to edit resources', 1, 1, GETDATE()),
        ('Delete', 'Permission to delete resources', 1, 1, GETDATE()),
        ('Export', 'Permission to export data', 1, 1, GETDATE()),
        ('Import', 'Permission to import data', 1, 1, GETDATE()),
        ('Approve', 'Permission to approve actions', 1, 1, GETDATE())
      `);
      
      console.log('Basic permissions created');
    } else {
      console.log(`Found ${permissionsResult.recordset.length} permissions in the database`);
    }

    // Check if Modules table has entries
    const modulesQuery = `SELECT * FROM Modules`;
    const modulesResult = await db.query(modulesQuery);
    
    if (modulesResult.recordset.length === 0) {
      console.log('No modules found in the database. Creating modules...');
      
      // Create modules
      await db.query(`
        INSERT INTO Modules (Name, Description, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ('Dashboard', 'Dashboard and analytics', 'dashboard', 1, 1, 1, GETDATE()),
        ('User Management', 'Manage users and permissions', 'people', 2, 1, 1, GETDATE()),
        ('Company Management', 'Manage companies', 'business', 3, 1, 1, GETDATE()),
        ('Plaza Management', 'Manage plazas', 'location_city', 4, 1, 1, GETDATE()),
        ('Lane Management', 'Manage lanes', 'linear_scale', 5, 1, 1, GETDATE()),
        ('Configuration', 'System configuration', 'settings', 6, 1, 1, GETDATE()),
        ('Reports', 'Reports and analytics', 'assessment', 7, 1, 1, GETDATE())
      `);
      
      console.log('Modules created');
    } else {
      console.log(`Found ${modulesResult.recordset.length} modules in the database`);
    }

    // Check if SubModules table has entries
    const subModulesQuery = `SELECT * FROM SubModules`;
    const subModulesResult = await db.query(subModulesQuery);
    
    if (subModulesResult.recordset.length === 0) {
      console.log('No submodules found in the database. Creating submodules...');
      
      // Create submodules for Dashboard
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'Dashboard'), 'Dashboard Overview', 'Main dashboard', '/dashboard', 'dashboard', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Dashboard'), 'Analytics', 'Analytics dashboard', '/dashboard/analytics', 'analytics', 2, 1, 1, GETDATE())
      `);
      
      // Create submodules for User Management
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'User Management'), 'Users', 'Manage users', '/users', 'people', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'User Management'), 'Roles', 'Manage roles', '/roles', 'admin_panel_settings', 2, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'User Management'), 'Permissions', 'Manage permissions', '/permissions', 'security', 3, 1, 1, GETDATE())
      `);
      
      // Create submodules for Company Management
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'Company Management'), 'Companies', 'Manage companies', '/companies', 'business', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Company Management'), 'Company Settings', 'Company settings', '/company-settings', 'settings', 2, 1, 1, GETDATE())
      `);
      
      // Create submodules for Plaza Management
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'Plaza Management'), 'Plazas', 'Manage plazas', '/plazas', 'location_city', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Plaza Management'), 'Plaza Settings', 'Plaza settings', '/plaza-settings', 'settings', 2, 1, 1, GETDATE())
      `);
      
      // Create submodules for Lane Management
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'Lanes', 'Manage lanes', '/lanes', 'linear_scale', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'Lane Settings', 'Lane settings', '/lane-settings', 'settings', 2, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'Digital Payment', 'Digital payment settings', '/digital-payment', 'payment', 3, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'FASTag', 'FASTag settings', '/fastag', 'nfc', 4, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'ANPR', 'ANPR settings', '/anpr', 'videocam', 5, 1, 1, GETDATE())
      `);
      
      // Create submodules for Configuration
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'System Settings', 'System settings', '/system-settings', 'settings', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'Countries', 'Manage countries', '/countries', 'public', 2, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'States', 'Manage states', '/states', 'map', 3, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'Cities', 'Manage cities', '/cities', 'location_city', 4, 1, 1, GETDATE())
      `);
      
      // Create submodules for Reports
      await db.query(`
        INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
        VALUES 
        ((SELECT Id FROM Modules WHERE Name = 'Reports'), 'Traffic Reports', 'Traffic reports', '/traffic-reports', 'assessment', 1, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Reports'), 'Revenue Reports', 'Revenue reports', '/revenue-reports', 'attach_money', 2, 1, 1, GETDATE()),
        ((SELECT Id FROM Modules WHERE Name = 'Reports'), 'User Activity', 'User activity reports', '/user-activity', 'history', 3, 1, 1, GETDATE())
      `);
      
      console.log('Submodules created');
    } else {
      console.log(`Found ${subModulesResult.recordset.length} submodules in the database`);
    }

    // Check if SubModulePermissions table has entries
    const subModulePermissionsQuery = `SELECT * FROM SubModulePermissions`;
    const subModulePermissionsResult = await db.query(subModulePermissionsQuery);
    
    if (subModulePermissionsResult.recordset.length === 0) {
      console.log('No submodule permissions found in the database. Creating submodule permissions...');
      
      // Get all submodules
      const allSubModulesQuery = `SELECT Id, Name FROM SubModules WHERE IsActive = 1`;
      const allSubModulesResult = await db.query(allSubModulesQuery);
      
      // Get all permissions
      const allPermissionsQuery = `SELECT Id, Name FROM Permissions WHERE IsActive = 1`;
      const allPermissionsResult = await db.query(allPermissionsQuery);
      
      // For each submodule, add appropriate permissions
      for (const subModule of allSubModulesResult.recordset) {
        // Add View permission to all submodules
        const viewPermission = allPermissionsResult.recordset.find(p => p.Name === 'View');
        await db.query(`
          INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
          VALUES (@subModuleId, @permissionId, 1, 1, GETDATE())
        `, {
          subModuleId: subModule.Id,
          permissionId: viewPermission.Id
        });
        
        // Add Create, Edit, Delete permissions to all submodules except Dashboard and Reports
        if (!['Dashboard Overview', 'Analytics', 'Traffic Reports', 'Revenue Reports', 'User Activity'].includes(subModule.Name)) {
          const createPermission = allPermissionsResult.recordset.find(p => p.Name === 'Create');
          const editPermission = allPermissionsResult.recordset.find(p => p.Name === 'Edit');
          const deletePermission = allPermissionsResult.recordset.find(p => p.Name === 'Delete');
          
          await db.query(`
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES 
            (@subModuleId, @createPermissionId, 1, 1, GETDATE()),
            (@subModuleId, @editPermissionId, 1, 1, GETDATE()),
            (@subModuleId, @deletePermissionId, 1, 1, GETDATE())
          `, {
            subModuleId: subModule.Id,
            createPermissionId: createPermission.Id,
            editPermissionId: editPermission.Id,
            deletePermissionId: deletePermission.Id
          });
        }
        
        // Add Export, Import permissions to Reports submodules
        if (['Traffic Reports', 'Revenue Reports', 'User Activity'].includes(subModule.Name)) {
          const exportPermission = allPermissionsResult.recordset.find(p => p.Name === 'Export');
          const importPermission = allPermissionsResult.recordset.find(p => p.Name === 'Import');
          
          await db.query(`
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES 
            (@subModuleId, @exportPermissionId, 1, 1, GETDATE()),
            (@subModuleId, @importPermissionId, 1, 1, GETDATE())
          `, {
            subModuleId: subModule.Id,
            exportPermissionId: exportPermission.Id,
            importPermissionId: importPermission.Id
          });
        }
      }
      
      console.log('Submodule permissions created');
    } else {
      console.log(`Found ${subModulePermissionsResult.recordset.length} submodule permissions in the database`);
    }

    console.log('Modules and permissions setup complete');
  } catch (error) {
    console.error('Database connection or query error:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('\nDatabase connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the function
setupModulesAndPermissions();
