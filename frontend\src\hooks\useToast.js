// frontend/src/hooks/useToast.js
import { useToast as useToastContext } from '../contexts/ToastContext';

// Custom hook that provides enhanced toast functionality
export const useToast = () => {
  const toast = useToastContext();

  // Enhanced methods with common use cases
  const showCrudSuccess = (action, entity) => {
    const messages = {
      create: `✅ ${entity} created successfully`,
      update: `✅ ${entity} updated successfully`,
      delete: `✅ ${entity} deleted successfully`,
      toggle: `✅ ${entity} status updated successfully`
    };
    
    return toast.showSuccess(messages[action] || `✅ ${action} completed successfully`);
  };

  const showCrudError = (action, entity, error = '') => {
    const messages = {
      create: `❌ Failed to create ${entity}`,
      update: `❌ Failed to update ${entity}`,
      delete: `❌ Failed to delete ${entity}`,
      toggle: `❌ Failed to update ${entity} status`
    };
    
    const message = messages[action] || `❌ ${action} failed`;
    const description = error ? `Error: ${error}` : undefined;
    
    return toast.showError(message, { description });
  };

  const showUnauthorized = (action, module) => {
    const message = `⚠️ You are not authorized to ${action} ${module}`;
    return toast.showWarning(message);
  };

  const showValidationError = (message) => {
    return toast.showError(`⚠️ ${message}`);
  };

  const showNetworkError = () => {
    return toast.showError('🌐 Network error. Please check your connection and try again.');
  };

  const showLoadingError = (entity) => {
    return toast.showError(`❌ Failed to load ${entity}. Please refresh the page.`);
  };

  return {
    ...toast,
    showCrudSuccess,
    showCrudError,
    showUnauthorized,
    showValidationError,
    showNetworkError,
    showLoadingError
  };
};

export default useToast;
