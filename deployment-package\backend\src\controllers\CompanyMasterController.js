// module.exports = new CompanyMasterController();

const db = require('../config/database');
const sql = require('mssql');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Make sure the upload directory exists
const uploadDir = path.join(__dirname, '../../Uploads/Companies');
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// File upload setup
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null,uploadDir );
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ storage: storage });

exports.uploadCompanyFiles = () => {
  return upload.fields([
    { name: 'CompanyLogo', maxCount: 1 }
  ]);
};

// Get all companies
exports.getAllCompanies = async (req, res) => {
  try {
    let query = 'SELECT * FROM tblCompanyMaster WHERE IsActive = 1';
    const queryParams = {};

    // If not SuperAdmin, filter by user's company access
    if (req.user && req.user.role !== 'SuperAdmin') {
      query += ` AND Id IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
      queryParams.userId = req.user.id;
    }

    // Add ordering
    query += ' ORDER BY CompanyName';

    const result = await db.query(query, queryParams);

    // Don't include the full logo URL, just indicate if a logo exists
    const companies = result.recordset.map(company => ({
      ...company
    }));

    res.json({
      success: true,
      companies,
      message: 'Companies retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getAllCompanies controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// Get company by ID
exports.getCompanyById = async (req, res) => {
  const id = req.params.id;
  try {
    let query = 'SELECT * FROM tblCompanyMaster WHERE Id = @Id AND IsActive = 1';
    const queryParams = { Id: id };

    // If not SuperAdmin, check if user has access to this company
    if (req.user && req.user.role !== 'SuperAdmin') {
      query += ` AND Id IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
      queryParams.userId = req.user.id;
    }

    const result = await db.query(query, queryParams);
    const company = result.recordset[0];

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found or you do not have access to this company'
      });
    }

    // We don't need to add a logo URL here, the frontend will use the /api/companies/:id/logo endpoint

    res.json({
      success: true,
      company,
      message: 'Company retrieved successfully'
    });
  } catch (error) {
    console.error('Error in getCompanyById controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};


// Create company
exports.createCompany = async (req, res) => {
  try {
    // Only SuperAdmin can create companies
    if (req.user.role !== 'SuperAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Only SuperAdmin can create new companies'
      });
    }

    const {
      CompanyName,
      AddressId,
      ContactPerson,
      ContactNumber,
      ContactEmail,
      CompanyCode,
      IsActive
    } = req.body;

    // Validate required fields
    if (!CompanyName || !ContactPerson || !ContactEmail) {
      return res.status(400).json({
        success: false,
        message: 'Required fields missing: Company Name, Contact Person, and Contact Email are required'
      });
    }

    // Get current user ID for CreatedBy
    const createdBy = req.user.id;

  //exchanged the previous code With this code:
let logoFilename = null;

if (req.files && req.files.CompanyLogo && req.files.CompanyLogo.length > 0) {
  logoFilename = req.files.CompanyLogo[0].filename;
}

    // Insert company - store just the filename in the database
    // Use a different approach to handle the varbinary conversion
    let insertQuery;
    
    if (logoFilename) {
      insertQuery = `
        DECLARE @BinaryData VARBINARY(MAX);
        SET @BinaryData = CONVERT(VARBINARY(MAX), @CompanyLogo);
        
        INSERT INTO tblCompanyMaster
        (CompanyName, AddressId, ContactPerson, ContactNumber, ContactEmail, CompanyLogo, CompanyCode, IsActive, CreatedBy, CreatedOn)
        VALUES (@CompanyName, @AddressId, @ContactPerson, @ContactNumber, @ContactEmail, @BinaryData, @CompanyCode, @IsActive, @CreatedBy, GETDATE());
        
        SELECT SCOPE_IDENTITY() AS Id;
      `;
    } else {
      insertQuery = `
        INSERT INTO tblCompanyMaster
        (CompanyName, AddressId, ContactPerson, ContactNumber, ContactEmail, CompanyLogo, CompanyCode, IsActive, CreatedBy, CreatedOn)
        VALUES (@CompanyName, @AddressId, @ContactPerson, @ContactNumber, @ContactEmail, NULL, @CompanyCode, @IsActive, @CreatedBy, GETDATE());
        
        SELECT SCOPE_IDENTITY() AS Id;
      `;
    }
    
    const insertParams = {
      CompanyName,
      AddressId: AddressId || null,
      ContactPerson,
      ContactNumber: ContactNumber || null,
      ContactEmail,
      CompanyLogo: logoFilename,
      CompanyCode: CompanyCode || null,
      IsActive: IsActive !== undefined ? IsActive : 1,
      CreatedBy: createdBy
    };
    
    const result = await db.query(insertQuery, insertParams);

    const companyId = result.recordset[0].Id;

    res.status(201).json({
      success: true,
      message: 'Company created successfully',
      companyId,
      hasLogo: !!logoFilename
    });
  } catch (error) {
    console.error('Error in createCompany controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};



// Update company
exports.updateCompany = async (req, res) => {
  try {
    const id = req.params.id;
    const {
      CompanyName,
      AddressId,
      ContactPerson,
      ContactNumber,
      ContactEmail,
      CompanyCode,
      IsActive
    } = req.body;

    // Validate required fields
    if (!CompanyName || !ContactPerson || !ContactEmail) {
      return res.status(400).json({
        success: false,
        message: 'Required fields missing: Company Name, Contact Person, and Contact Email are required'
      });
    }

    // Check if company exists and user has access to it
    let query = 'SELECT * FROM tblCompanyMaster WHERE Id = @Id AND IsActive = 1';
    const queryParams = { Id: id };

    // If not SuperAdmin, check if user has access to this company
    if (req.user && req.user.role !== 'SuperAdmin') {
      query += ` AND Id IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
      queryParams.userId = req.user.id;
    }

    const result = await db.query(query, queryParams);

    const currentCompany = result.recordset[0];
    if (!currentCompany) {
      return res.status(404).json({
        success: false,
        message: 'Company not found or you do not have permission to update it'
      });
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user ? req.user.id : 1;

    //exchanged the previous code With this code:
let logoFilename = null;

if (req.files && req.files.CompanyLogo && req.files.CompanyLogo.length > 0) {
  // If a new logo is uploaded, delete the old one if it exists
  if (currentCompany.CompanyLogo) {
    const oldLogoPath = path.join(__dirname, '../../Uploads/Companies', currentCompany.CompanyLogo);
    if (fs.existsSync(oldLogoPath)) {
      try {
        fs.unlinkSync(oldLogoPath);
      } catch (error) {
        console.error('Error deleting old logo:', error);
      }
    }
  }
  logoFilename = req.files.CompanyLogo[0].filename;
}

    // Build the SQL query dynamically based on whether we have a new logo
    let updateQuery;
    
    if (logoFilename) {
      updateQuery = `
        DECLARE @BinaryData VARBINARY(MAX);
        SET @BinaryData = CONVERT(VARBINARY(MAX), @CompanyLogo);
        
        UPDATE tblCompanyMaster
        SET CompanyName = @CompanyName,
            AddressId = @AddressId,
            ContactPerson = @ContactPerson,
            ContactNumber = @ContactNumber,
            ContactEmail = @ContactEmail,
            CompanyLogo = @BinaryData,
            CompanyCode = @CompanyCode,
            IsActive = @IsActive,
            ModifiedBy = @ModifiedBy,
            ModifiedOn = GETDATE()
        WHERE Id = @Id
      `;
    } else {
      updateQuery = `
        UPDATE tblCompanyMaster
        SET CompanyName = @CompanyName,
            AddressId = @AddressId,
            ContactPerson = @ContactPerson,
            ContactNumber = @ContactNumber,
            ContactEmail = @ContactEmail,
            CompanyCode = @CompanyCode,
            IsActive = @IsActive,
            ModifiedBy = @ModifiedBy,
            ModifiedOn = GETDATE()
        WHERE Id = @Id
      `;
    }
    
    const updateParams = {
      Id: id,
      CompanyName,
      AddressId: AddressId || currentCompany.AddressId,
      ContactPerson,
      ContactNumber: ContactNumber || currentCompany.ContactNumber,
      ContactEmail,
      CompanyLogo: logoFilename,
      CompanyCode: CompanyCode || currentCompany.CompanyCode,
      IsActive: IsActive !== undefined ? IsActive : currentCompany.IsActive,
      ModifiedBy: modifiedBy
    };
    
    await db.query(updateQuery, updateParams);

    res.json({
      success: true,
      message: 'Company updated successfully',
      companyLogo: logoFilename ? `../../Uploads/Companies/${logoFilename}` : null
    });
  } catch (error) {
    console.error('Error in updateCompany controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

// // Delete company
// exports.deleteCompany = async (req, res) => {
//   const id = req.params.id;

//   try {
//     const result = await db.query('SELECT Id, CompanyLogo FROM tblCompanyMaster WHERE Id = @Id', { Id: id });
//     const company = result.recordset[0];

//     if (!company) {
//       return res.status(404).json({ error: 'Company not found' });
//     }

//     // Delete the logo file
//     if (company.CompanyLogo) {
//       const logoPath = path.join(__dirname, '../../Uploads/Companies', company.CompanyLogo);
//       if (fs.existsSync(logoPath)) {
//         fs.unlinkSync(logoPath);
//       }
//     }

//     await db.query('DELETE FROM tblCompanyMaster WHERE Id = @Id', { Id: id });

//     res.json({ message: 'Company deleted successfully' });
//   } catch (error) {
//     res.status(500).json({ error: 'Database error', details: error.message });
//   }
// };


// Delete company
exports.deleteCompany = async (req, res) => {
  try {
    const id = req.params.id;

    // Only SuperAdmin can delete companies
    if (req.user && req.user.role !== 'SuperAdmin') {
      return res.status(403).json({
        success: false,
        message: 'Only SuperAdmin can delete companies'
      });
    }

    // Step 1: Fetch the company
    const result = await db.query('SELECT Id, CompanyLogo FROM tblCompanyMaster WHERE Id = @Id AND IsActive = 1', { Id: id });

    const company = result.recordset[0];

    if (!company) {
      return res.status(404).json({
        success: false,
        message: 'Company not found'
      });
    }

    // Get current user ID for ModifiedBy
    const modifiedBy = req.user ? req.user.id : 1;

    // Step 2: Check if there are any plazas associated with this company
    const plazaResult = await db.query('SELECT COUNT(*) as PlazaCount FROM Plaza WHERE CompanyId = @CompanyId AND IsActive = 1', { CompanyId: id });

    if (plazaResult.recordset[0].PlazaCount > 0) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete company with active plazas. Please delete or reassign all plazas first.'
      });
    }

    // Step 3: Soft delete the company instead of hard delete
    await db.query(`
      UPDATE tblCompanyMaster
      SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE()
      WHERE Id = @Id
    `, {
      Id: id,
      ModifiedBy: modifiedBy
    });

    // Step 4: Deactivate user-company associations
    await db.query(`
      UPDATE UserCompany
      SET IsActive = 0, ModifiedBy = @ModifiedBy, ModifiedOn = GETDATE()
      WHERE CompanyId = @Id
    `, {
      Id: id,
      ModifiedBy: modifiedBy
    });

    res.json({
      success: true,
      message: 'Company deleted successfully'
    });
  } catch (error) {
    console.error('Error in deleteCompany controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};


// Get company logo by ID
exports.getCompanyLogo = async (req, res) => {
  try {
    const id = req.params.id;
    console.log(`[LOGO DEBUG] Fetching logo for company ID: ${id}`);

    // We don't need authentication for logo retrieval as it's a public resource
    // Also removed the IsActive = 1 filter to allow fetching logos for all companies
    const result = await db.query(`
      SELECT Id, CompanyName, 
             CompanyLogo,
             CONVERT(NVARCHAR(MAX), CompanyLogo) AS CompanyLogoText
      FROM tblCompanyMaster 
      WHERE Id = @Id
    `, { Id: id });

    console.log(`[LOGO DEBUG] Query result received`);

    if (!result.recordset || result.recordset.length === 0) {
      console.log(`[LOGO DEBUG] No company found with ID: ${id}`);
      return res.status(404).send('Company not found');
    }

    const company = result.recordset[0];
    const logoData = company.CompanyLogo;
    const logoText = company.CompanyLogoText;

    console.log(`[LOGO DEBUG] Company: ${company.CompanyName}, Logo data exists: ${!!logoData}`);

    if (!logoData) {
      console.log(`[LOGO DEBUG] No logo data found for company ID: ${id}`);
      return res.status(404).send('Logo not found');
    }

    // Try to determine if this is binary image data or a filename
    let isFilename = false;
    try {
      // If it's a filename, it should be a valid UTF-8 string without control characters
      const possibleFilename = logoText.trim();
      if (possibleFilename && 
          possibleFilename.length < 260 && // Max path length
          !possibleFilename.includes('\0') && 
          /^[a-zA-Z0-9\-_\.]+\.[a-zA-Z0-9]+$/.test(possibleFilename)) {
        isFilename = true;
      }
    } catch (e) {
      console.log(`[LOGO DEBUG] Error checking if logo is filename: ${e.message}`);
      // If there's an error, assume it's binary data
      isFilename = false;
    }

    console.log(`[LOGO DEBUG] Logo appears to be a ${isFilename ? 'filename' : 'binary data'}`);

    if (isFilename) {
      // It's a filename, serve the file
      const logoFilename = logoText;
      console.log(`[LOGO DEBUG] Logo filename: ${logoFilename}`);

      // Construct the file path
      const logoPath = path.join(__dirname, '../../Uploads/Companies', logoFilename);
      console.log(`[LOGO DEBUG] Full logo path: ${logoPath}`);

      // Check if file exists
      if (!fs.existsSync(logoPath)) {
        console.log(`[LOGO DEBUG] Logo file does not exist at path: ${logoPath}`);
        return res.status(404).send('Logo file not found');
      }

      // Determine content type based on file extension
      const ext = path.extname(logoFilename).toLowerCase();
      let contentType = 'image/png'; // Default

      if (ext === '.jpg' || ext === '.jpeg') {
        contentType = 'image/jpeg';
      } else if (ext === '.gif') {
        contentType = 'image/gif';
      } else if (ext === '.svg') {
        contentType = 'image/svg+xml';
      }

      console.log(`[LOGO DEBUG] Sending logo file with content type: ${contentType}`);
      res.set('Content-Type', contentType);
      res.sendFile(logoPath);
    } else {
      // It's binary data, send directly
      // Try to determine content type from binary signature
      let contentType = 'image/png'; // Default
      
      if (logoData.length > 2) {
        const signature = logoData.slice(0, 4).toString('hex');
        if (signature.startsWith('ffd8')) {
          contentType = 'image/jpeg';
        } else if (signature.startsWith('89504e47')) {
          contentType = 'image/png';
        } else if (signature.startsWith('47494638')) {
          contentType = 'image/gif';
        } else if (logoData.toString('utf8', 0, 4).includes('SVG')) {
          contentType = 'image/svg+xml';
        }
      }

      console.log(`[LOGO DEBUG] Sending binary logo with content type: ${contentType}`);
      res.set('Content-Type', contentType);
      res.send(logoData);
    }
  } catch (error) {
    console.error('[LOGO DEBUG] Error in getCompanyLogo controller:', error);
    res.status(500).send('Error fetching logo');
  }
};

// Function to get plazas by company ID
exports.getPlazaByCompany = async (req, res) => {
  const companyId = req.params.companyId;

  try {
    // THE CHANGE IS HERE: Removed the "AND IsActive = 1" clause from the query.
    // This will now select all plazas for the company, regardless of their active status.
    let query = `SELECT * FROM Plaza WHERE CompanyId = @CompanyId`;
    const queryParams = { CompanyId: companyId };

    // If not SuperAdmin, check if user has access to this company
    if (req.user && req.user.role !== 'SuperAdmin') {
      if (req.user.role === 'CompanyAdmin') {
        // CompanyAdmin can only see plazas from companies they have access to
        query += ` AND CompanyId IN (SELECT CompanyId FROM UserCompany WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      } else if (req.user.role === 'PlazaManager') {
        // PlazaManager can only see plazas they are assigned to
        query += ` AND Id IN (SELECT PlazaId FROM UserPlaza WHERE UserId = @userId AND IsActive = 1)`;
        queryParams.userId = req.user.id;
      }
    }

    // Add ordering
    query += ' ORDER BY PlazaName';

    const result = await db.query(query, queryParams);
    const plazas = result.recordset;

    // Return the list of plazas (will be an empty array if none are found).
    res.json({
      success: true,
      plazas,
      message: plazas.length > 0 ? 'Plazas retrieved successfully' : 'No plazas found for this company'
    });

  } catch (error) {
    console.error('Error in getPlazaByCompany controller:', error);
    res.status(500).json({
      success: false,
      error: 'Database error',
      message: error.message
    });
  }
};

