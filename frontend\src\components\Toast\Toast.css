/* Toast Container Styles */
.toast-container {
  position: fixed;
  bottom: 32px;
  right: 24px;
  z-index: 9999;
  pointer-events: none;
  max-width: 400px;
  width: 100%;
}

.toast-container > div {
  margin-bottom: 12px;
}

/* Toast Animation Styles */
.toast-enter {
  transform: translateY(100%);
  opacity: 0;
}

.toast-enter-active {
  transform: translateY(0);
  opacity: 1;
  transition: transform 300ms ease-out, opacity 300ms ease-out;
}

.toast-exit {
  transform: translateY(0);
  opacity: 1;
}

.toast-exit-active {
  transform: translateY(100%);
  opacity: 0;
  transition: transform 250ms ease-in, opacity 250ms ease-in;
}

/* Individual Toast Styles */
.toast {
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  min-width: 320px;
  max-width: 400px;
  overflow: hidden;
}

.toast.success {
  border-left: 4px solid #10b981;
}

.toast.error {
  border-left: 4px solid #ef4444;
}

.toast.warning {
  border-left: 4px solid #f59e0b;
}

.toast.info {
  border-left: 4px solid #3b82f6;
}

/* Toast Content */
.toast-content {
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.toast-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  margin-top: 2px;
}

.toast-message {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
  margin: 0 0 4px 0;
  color: #111827;
}

.toast-description {
  font-size: 13px;
  line-height: 1.4;
  color: #6b7280;
  margin: 0;
}

.toast-close {
  flex-shrink: 0;
  background: none;
  border: none;
  color: #9ca3af;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
}

.toast-close:hover {
  color: #6b7280;
  background-color: #f3f4f6;
}

.toast-close:focus {
  outline: none;
  ring: 2px solid #3b82f6;
  ring-offset: 2px;
}

/* Responsive Design */
@media (max-width: 640px) {
  .toast-container {
    top: 16px;
    right: 16px;
    left: 16px;
    max-width: none;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .toast {
    background: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .toast-title {
    color: #f9fafb;
  }
  
  .toast-description {
    color: #d1d5db;
  }
  
  .toast-close {
    color: #9ca3af;
  }
  
  .toast-close:hover {
    color: #d1d5db;
    background-color: #374151;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .toast {
    border-width: 2px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .toast.success {
    border-left-width: 6px;
  }
  
  .toast.error {
    border-left-width: 6px;
  }
  
  .toast.warning {
    border-left-width: 6px;
  }
  
  .toast.info {
    border-left-width: 6px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .toast-enter-active,
  .toast-exit-active {
    transition: opacity 150ms ease;
    transform: none;
  }
  
  .toast-enter {
    opacity: 0;
    transform: none;
  }
  
  .toast-enter-active {
    opacity: 1;
  }
  
  .toast-exit {
    opacity: 1;
  }
  
  .toast-exit-active {
    opacity: 0;
  }

  /* Mobile positioning adjustments */
  .toast-container {
    bottom: 20px;
    right: 16px;
    left: 16px;
    max-width: none;
  }
}
