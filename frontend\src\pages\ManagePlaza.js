import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import PlazaList from '../components/Plaza/PlazaList';
import PlazaDialog from '../components/Plaza/PlazaDialog';
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';
import ModuleFilters from '../components/filters/ModuleFilters';

export default function ManagePlaza() {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();
  const [selectedPlaza, setSelectedPlaza] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingPlaza, setEditingPlaza] = useState(null);

  // Fetch plazas data
  const { data: plazasData, isLoading: plazasLoading } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas,
    onError: (error) => {
      console.error('Error fetching plazas:', error);
      toast.showError(`Failed to fetch plazas: ${error.message}`);
    }
  });

  // Fetch companies data
  const { data: companiesData, isLoading: companiesLoading } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
  });

  // Process raw data - ensure we have arrays
  const rawPlazas = Array.isArray(plazasData?.data) ? plazasData.data :
                   (Array.isArray(plazasData) ? plazasData : []);

  console.log('Processed plazas data:', rawPlazas);

  const rawCompanies = Array.isArray(companiesData) ? companiesData :
                      (Array.isArray(companiesData?.companies) ? companiesData.companies : []);

  // Apply filtering to plazas data using the new module filter hook
  const {
    filteredData: plazas,
    filters,
    setFilters,
    canCreate,
    canEdit,
    canDelete
  } = useModuleFilter({
    data: rawPlazas,
    companies: rawCompanies,
    plazas: rawPlazas,
    companyIdField: 'CompanyId',
    plazaIdField: 'Id',
    module: 'Plazas'
  });

  // Get all available companies for the filter dropdown
  const companies = rawCompanies;

  // No need for notification anymore as the issue is fixed

  const createMutation = useMutation({
    mutationFn: (formData) => plazaApi.createPlaza(formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plazas'] });
      toast.showCrudSuccess('create', 'Plaza');
      setDialogOpen(false);
    },
    onError: (error) => toast.showError(`Failed to create plaza: ${error.message}`),
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, formData }) => plazaApi.updatePlaza(id, formData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plazas'] });
      toast.showCrudSuccess('update', 'Plaza');
      setDialogOpen(false);
      setEditingPlaza(null);
    },
    onError: (error) => toast.showError(`Failed to update plaza: ${error.message}`),
  });

  const deleteMutation = useMutation({
    mutationFn: plazaApi.deletePlaza,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plazas'] });
      toast.showCrudSuccess('delete', 'Plaza');
      if (selectedPlaza) setSelectedPlaza(null);
    },
    onError: (error) => toast.showError(`Failed to delete plaza: ${error.message}`),
  });

  const toggleStatusMutation = useMutation({
    mutationFn: (id) => plazaApi.toggleActiveStatus(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['plazas'] });
      toast.showCrudSuccess('update', 'Plaza status');
    },
    onError: (error) => {
      console.error('Error in toggleStatusMutation:', error);
      toast.showError(`Failed to update plaza status: ${error.message}`);
    },
  });

  const handleSubmit = (data, logo) => {
    const formData = new FormData();

    // Append all form fields to FormData
    Object.keys(data).forEach(key => {
      formData.append(key, data[key]);
    });

    // Append logo file if it exists
    if (logo) {
      formData.append('Logo', logo);
    }

    // Add current user ID for tracking who created/modified the record
    if (user && user.id) {
      if (editingPlaza) {
        // For updates, set ModifiedBy
        formData.append('ModifiedBy', user.id);
      } else {
        // For new records, set CreatedBy
        formData.append('CreatedBy', user.id);
      }
    }

    if (editingPlaza) {
      // Check if user has permission to edit this plaza
      if (canEdit(editingPlaza, 'Plazas')) {
        updateMutation.mutate({ id: editingPlaza.Id, formData });
      } else {
        toast.showError('You do not have permission to edit this plaza');
      }
    } else {
      // Check if user has permission to create plazas
      if (canCreate('Plazas')) {
        createMutation.mutate(formData);
      } else {
        toast.showError('You do not have permission to create plazas');
      }
    }
  };

  const handleEdit = (plaza) => {
    // Check if user has permission to edit this plaza
    if (canEdit(plaza, 'Plazas')) {
      setEditingPlaza(plaza);
      setDialogOpen(true);
    } else {
      toast.showError('You do not have permission to edit this plaza');
    }
  };

  const handleDelete = (id) => {
    // Find the plaza by ID
    const plaza = plazas.find(p => p.Id === id);

    // Check if user has permission to delete this plaza
    if (plaza && canDelete(plaza, 'Plazas')) {
      if (window.confirm('Are you sure you want to delete this plaza?')) {
        deleteMutation.mutate(id);
      }
    } else {
      toast.showError('You do not have permission to delete this plaza');
    }
  };

  const handleToggleStatus = (plaza) => {
    // Check if user has permission to edit this plaza
    if (plaza && canEdit(plaza, 'Plazas')) {
      const statusText = plaza.IsActive ? 'deactivate' : 'activate';
      if (window.confirm(`Are you sure you want to ${statusText} this plaza?`)) {
        // Show a loading toast
        const loadingToast = toast.loading(`${statusText.charAt(0).toUpperCase() + statusText.slice(1)}ing plaza...`);

        toggleStatusMutation.mutate(plaza.Id, {
          onSuccess: () => {
            toast.dismiss(loadingToast);
          },
          onError: () => {
            toast.dismiss(loadingToast);
          }
        });
      }
    } else {
      toast.showError('You do not have permission to change the status of this plaza');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingPlaza(null);
  };

  if (plazasLoading || companiesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Plaza Management</h1>
          <PermissionButton
            requiredModule="Plazas"
            requiredPermissions={["Create"]}
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Plaza
          </PermissionButton>
        </div>

        {/* Filter Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <ModuleFilters
              companies={companies || []}
              filters={filters}
              onFilterChange={setFilters}
              showCompanyFilter={true}
              showPlazaFilter={false}
              loading={plazasLoading || companiesLoading}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <PlazaList
            plazas={plazas || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={setSelectedPlaza}
            onToggleStatus={handleToggleStatus}
          />
        </div>

        {companies && (
          <PlazaDialog
            isOpen={dialogOpen}
            onClose={handleCloseDialog}
            onSubmit={handleSubmit}
            initialData={editingPlaza}
            title={editingPlaza ? 'Edit Plaza' : 'Add Plaza'}
            companies={companies || []}

          />
        )}
      </div>
    </div>
  );
}