-- Update sp_Valet_SMS_Send stored procedure to add CustomerId and CreatedBy parameters
USE ParkwizOps;
GO

-- Drop and recreate the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_<PERSON>t_SMS_Send]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_<PERSON>t_SMS_Send];
GO

CREATE PROCEDURE [dbo].[sp_<PERSON>t_SMS_Send]
    @MobileNumber NVARCHAR(15),
    @Message NVARCHAR(MAX),
    @SMSType NVARCHAR(50),
    @CustomerId INT = NULL,
    @TransactionId DECIMAL = NULL,
    @CreatedBy INT = NULL,
    @SMSId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @MobileNumber IS NULL OR LTRIM(RTRIM(@MobileNumber)) = ''
        BEGIN
            RAISERROR('Mobile number is required', 16, 1);
            RETURN -1;
        END
        
        IF @Message IS NULL OR LTRIM(RTRIM(@Message)) = ''
        BEGIN
            RAISERROR('Message is required', 16, 1);
            RETURN -1;
        END
        
        IF @SMSType IS NULL OR LTRIM(RTRIM(@SMSType)) = ''
        BEGIN
            RAISERROR('SMS type is required', 16, 1);
            RETURN -1;
        END
        
        -- Insert SMS notification
        INSERT INTO SMSNotifications (
            MobileNumber, Message, SMSType, CustomerId, TransactionId, Status, CreatedBy, CreatedOn
        )
        VALUES (
            @MobileNumber, @Message, @SMSType, @CustomerId, @TransactionId, 'PENDING', @CreatedBy, GETDATE()
        );
        
        SET @SMSId = SCOPE_IDENTITY();
        
        -- Return success with SMS details
        SELECT 
            @SMSId AS SMSId,
            @MobileNumber AS MobileNumber,
            @SMSType AS SMSType,
            'SMS notification created successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @SMSId = -1;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_SMS_Send';