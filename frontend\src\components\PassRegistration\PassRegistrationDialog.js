import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { plazaApi } from '../../api/plazaApi';
import { useAuth } from '../../contexts/authContext';

const PassRegistrationDialog = ({ isOpen, onClose, onSubmit, passRegistration, isLoading }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    PlazaCode: '',
    PlazaName: '',
    ApplicationID: '',
    GIN: '',
    HolderName: '',
    CompanyName: '',
    VehicleType: '',
    ContactNo: '',
    PassType: '',
    PassTariffType: '',
    EmailID: '',
    ContractEndDate: '',
    VehicleNo: '',
    PasscardNumber: '',
    PaidAmount: '',
    TagMasterTagID: '',
    flgANPRPassEnable: 'False',
    PaymentMode: '',
    PaymentType: ''
  });

  // Fetch plazas for dropdown
  const { data: plazasData } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas
  });

  // Extract plazas array from the response
  const plazas = plazasData?.data || [];

  useEffect(() => {
    if (passRegistration) {
      setFormData({
        PlazaCode: passRegistration.PlazaCode || '',
        // Handle PlazaName as an array if needed
        PlazaName: Array.isArray(passRegistration.PlazaName) ? 
          passRegistration.PlazaName[0] || '' : passRegistration.PlazaName || '',
        ApplicationID: passRegistration.ApplicationID || '',
        GIN: passRegistration.GIN || '',
        HolderName: passRegistration.HolderName || '',
        CompanyName: passRegistration.CompanyName || '',
        VehicleType: passRegistration.VehicleType || '',
        ContactNo: passRegistration.ContactNo || '',
        PassType: passRegistration.PassType || '',
        PassTariffType: passRegistration.PassTariffType || '',
        EmailID: passRegistration.EmailID || '',
        ContractEndDate: passRegistration.ContractEndDate ?
          new Date(passRegistration.ContractEndDate).toISOString().split('T')[0] : '',
        VehicleNo: passRegistration.VehicleNo || '',
        PasscardNumber: passRegistration.PasscardNumber || '',
        PaidAmount: passRegistration.PaidAmount || '',
        TagMasterTagID: passRegistration.TagMasterTagID || '',
        flgANPRPassEnable: passRegistration.flgANPRPassEnable || 'False',
        PaymentMode: passRegistration.PaymentMode || '',
        PaymentType: passRegistration.PaymentType || ''
      });
    } else {
      // Reset form for new entry
      setFormData({
        PlazaCode: '',
        PlazaName: '',
        ApplicationID: '',
        GIN: '',
        HolderName: '',
        CompanyName: '',
        VehicleType: '',
        ContactNo: '',
        PassType: '',
        PassTariffType: '',
        EmailID: '',
        ContractEndDate: '',
        VehicleNo: '',
        PasscardNumber: '',
        PaidAmount: '',
        TagMasterTagID: '',
        flgANPRPassEnable: 'False',
        PaymentMode: '',
        PaymentType: ''
      });
    }
  }, [passRegistration]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePlazaChange = (e) => {
    const selectedPlaza = plazas.find(plaza => plaza.PlazaCode === e.target.value);
    
    // Generate a GIN based on the plaza code if it's a new registration
    let gin = formData.GIN;
    if (!passRegistration && selectedPlaza) {
      // Format: PlazaCode + sequential number (e.g., "SC001" for South City plaza)
      gin = selectedPlaza.PlazaCode + '001';
    }
    
    // Log the selected plaza for debugging
    console.log('Selected plaza:', selectedPlaza);
    
    setFormData(prev => ({
      ...prev,
      PlazaCode: e.target.value,
      // Store PlazaName as a string, not an array
      PlazaName: selectedPlaza ? selectedPlaza.PlazaName : '',
      GIN: gin
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  // Vehicle types
  const vehicleTypes = [
    'Car', 'Bus', 'Truck', 'Motorcycle', 'Auto Rickshaw', 'Tempo', 'Mini Bus', 'Heavy Vehicle'
  ];

  // Pass types
  const passTypes = [
    'Monthly', 'Quarterly', 'Half Yearly', 'Yearly', 'Daily', 'Weekly'
  ];

  // Payment modes
  const paymentModes = [
    'Cash', 'Card', 'UPI', 'Net Banking', 'Cheque', 'DD'
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">
            {passRegistration ? 'Edit Pass Registration' : 'Add Pass Registration'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Plaza and Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Plaza *
              </label>
              <select
                value={formData.PlazaCode}
                onChange={handlePlazaChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Plaza</option>
                {plazas.map((plaza) => (
                  <option key={plaza.PlazaCode} value={plaza.PlazaCode}>
                    {plaza.PlazaName}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                GIN (Gate Identification Number) *
              </label>
              <div className="relative">
                <input
                  type="text"
                  name="GIN"
                  value={formData.GIN}
                  onChange={handleInputChange}
                  required
                  maxLength={20}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter GIN"
                />
                {!passRegistration && formData.PlazaCode && (
                  <div className="text-xs text-gray-500 mt-1">
                    Auto-generated based on selected plaza. You can modify if needed.
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Holder Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Holder Name *
              </label>
              <input
                type="text"
                name="HolderName"
                value={formData.HolderName}
                onChange={handleInputChange}
                required
                maxLength={25}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter holder name"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Company Name
              </label>
              <input
                type="text"
                name="CompanyName"
                value={formData.CompanyName}
                onChange={handleInputChange}
                maxLength={50}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter company name"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contact Number *
              </label>
              <input
                type="tel"
                name="ContactNo"
                value={formData.ContactNo}
                onChange={handleInputChange}
                required
                maxLength={21}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter contact number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email ID
              </label>
              <input
                type="email"
                name="EmailID"
                value={formData.EmailID}
                onChange={handleInputChange}
                maxLength={50}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter email address"
              />
            </div>
          </div>

          {/* Vehicle Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Vehicle Number *
              </label>
              <input
                type="text"
                name="VehicleNo"
                value={formData.VehicleNo}
                onChange={handleInputChange}
                required
                maxLength={15}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter vehicle number"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Vehicle Type *
              </label>
              <select
                name="VehicleType"
                value={formData.VehicleType}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Vehicle Type</option>
                {vehicleTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Pass Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pass Type *
              </label>
              <select
                name="PassType"
                value={formData.PassType}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Pass Type</option>
                {passTypes.map((type) => (
                  <option key={type} value={type}>
                    {type}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Contract End Date *
              </label>
              <input
                type="date"
                name="ContractEndDate"
                value={formData.ContractEndDate}
                onChange={handleInputChange}
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Paid Amount
              </label>
              <input
                type="number"
                name="PaidAmount"
                value={formData.PaidAmount}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter amount"
              />
            </div>
          </div>

          {/* Tag and Additional Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tag Master Tag ID *
              </label>
              <input
                type="text"
                name="TagMasterTagID"
                value={formData.TagMasterTagID}
                onChange={handleInputChange}
                required
                maxLength={24}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter tag ID"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Passcard Number
              </label>
              <input
                type="text"
                name="PasscardNumber"
                value={formData.PasscardNumber}
                onChange={handleInputChange}
                maxLength={50}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter passcard number"
              />
            </div>
          </div>

          {/* Payment and Configuration */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Mode
              </label>
              <select
                name="PaymentMode"
                value={formData.PaymentMode}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select Payment Mode</option>
                {paymentModes.map((mode) => (
                  <option key={mode} value={mode}>
                    {mode}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Type
              </label>
              <input
                type="text"
                name="PaymentType"
                value={formData.PaymentType}
                onChange={handleInputChange}
                maxLength={50}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter payment type"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ANPR Pass Enable
              </label>
              <select
                name="flgANPRPassEnable"
                value={formData.flgANPRPassEnable}
                onChange={handleInputChange}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="False">Disabled</option>
                <option value="True">Enabled</option>
              </select>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Saving...' : (passRegistration ? 'Update' : 'Create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PassRegistrationDialog;
