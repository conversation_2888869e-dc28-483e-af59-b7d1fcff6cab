import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { laneApi } from '../api/laneApi';
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import LaneList from '../components/Lane/LaneList';
import LaneDialog from '../components/Lane/LaneDialog';
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';
import ModuleFilters from '../components/filters/ModuleFilters';
import { isStatusActive } from '../utils/statusUtils';

export default function ManageLane() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingLane, setEditingLane] = useState(null);
  const [selectedLane, setSelectedLane] = useState(null);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();

  // Create a local state to manage lanes for immediate UI updates
  const [lanes, setLanes] = useState([]);

  // Fetch lanes data
  const { data: fetchedLanes, isLoading: lanesLoading } = useQuery({
    queryKey: ['lanes'],
    queryFn: laneApi.getAllLanes,
  });

  // Update local state when fetched data changes
  useEffect(() => {
    if (fetchedLanes) {
      setLanes(fetchedLanes);
    }
  }, [fetchedLanes]);

  // Fetch plazas for dropdown
  const { data: plazas, isLoading: plazasLoading } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas,
    select: (data) => {
      // Handle the specific structure: {success: true, data: Array(2)}
      if (data && data.success === true && Array.isArray(data.data)) {
        return data.data;
      }

      // Handle other possible data structures
      if (data && Array.isArray(data)) {
        return data;
      } else if (data && data.plazas && Array.isArray(data.plazas)) {
        return data.plazas;
      } else if (data && typeof data === 'object') {
        // If it's an object but not an array, try to extract values
        const plazaArray = Object.values(data).filter(item =>
          item && typeof item === 'object' && (item.Id || item.id) && (item.PlazaName || item.plazaName || item.name)
        );
        if (plazaArray.length > 0) {
          return plazaArray;
        }
      }

      // Default fallback
      return [];
    }
  });

  // Fetch companies for dropdown
  const { data: companies, isLoading: companiesLoading } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => {
      return Array.isArray(data) ? data : [];
    }
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: laneApi.createLane,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lanes'] });
      toast.showCrudSuccess('create', 'Lane');
      setDialogOpen(false);
    },
    onError: (error) => {
      console.error('Lane creation error:', error);
      console.error('Error response:', error.response?.data);
      
      // Check if it's a validation error
      if (error.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = validationErrors.map(err => `${err.field}: ${err.error}`).join('\n');
        toast.showError(`Validation failed:\n${errorMessages}`);
      } else {
        toast.showError(`Failed to create lane: ${error.message}`);
      }
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => laneApi.updateLane(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lanes'] });
      toast.showCrudSuccess('update', 'Lane');
      setDialogOpen(false);
      setEditingLane(null);
    },
    onError: (error) => {
      console.error('Lane update error:', error);
      console.error('Error response:', error.response?.data);
      
      // Check if it's a validation error
      if (error.response?.data?.validationErrors) {
        const validationErrors = error.response.data.validationErrors;
        const errorMessages = validationErrors.map(err => `${err.field}: ${err.error}`).join('\n');
        toast.showError(`Validation failed:\n${errorMessages}`);
      } else {
        toast.showError(`Failed to update lane: ${error.message}`);
      }
    },
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: laneApi.deleteLane,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lanes'] });
      toast.showCrudSuccess('delete', 'Lane');
      if (selectedLane) setSelectedLane(null);
    },
    onError: (error) => toast.showError(`Failed to delete lane: ${error.message}`),
  });

  // Toggle status mutation with force refresh
  const toggleStatusMutation = useMutation({
    mutationFn: (laneId) => {
      console.log('Toggle status mutation called with laneId:', laneId);
      return laneApi.toggleLaneStatus(laneId, user?.id || 'admin');
    },
    onSuccess: (data) => {
      console.log('Toggle status mutation success:', data);

      // Show success toast immediately
      toast.showSuccess(`Lane ${data.newStatus ? 'activated' : 'deactivated'} successfully`);

      // Update the lane in the local state immediately
      setLanes(prevLanes => {
        if (!prevLanes) return prevLanes;

        return prevLanes.map(lane => {
          if (lane.LaneID === data.laneId) {
            // Update the ActiveStatus with the new value
            return {
              ...lane,
              ActiveStatus: data.newStatusValue // Use the actual string value from the response
            };
          }
          return lane;
        });
      });

      // Force a complete refresh of the lanes data
      queryClient.invalidateQueries({ queryKey: ['lanes'] });

      // Force refetch the data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['lanes'] });
      }, 500);
    },
    onError: (error) => {
      console.error('Toggle status mutation error:', error);
      toast.showError(`Failed to update lane status: ${error.message || 'Unknown error'}`);
    },
  });

  // Ensure lanes data is an array before filtering
  const lanesData = Array.isArray(lanes) ? lanes : [];

  // Process plazas data
  const processedPlazas = Array.isArray(plazas) ? plazas : [];

  // Process companies data
  const processedCompanies = Array.isArray(companies) ? companies : [];

  // Apply filtering to lanes data using the new module filter hook
  const {
    filteredData: filteredLanes,
    filters,
    setFilters,
    canCreate,
    canEdit,
    canDelete
  } = useModuleFilter({
    data: lanesData,
    companies: processedCompanies,
    plazas: processedPlazas,
    lanes: lanesData,
    companyIdField: 'CompanyID',
    plazaIdField: 'PlazaID',
    laneIdField: 'LaneID',
    module: 'Lanes'
  });

  const handleSubmit = (data) => {
    // Add current user ID for tracking who created/modified the record
    const dataWithUser = {
      ...data,
      UpdatedBy: user?.id || 'admin'
    };

    if (editingLane) {
      // Check if user has permission to edit this lane
      if (canEdit(editingLane, 'Lanes')) {
        updateMutation.mutate({ id: editingLane.LaneID, data: dataWithUser });
      } else {
        toast.showError('You do not have permission to edit this lane');
      }
    } else {
      // Check if user has permission to create lanes
      if (canCreate('Lanes')) {
        createMutation.mutate(dataWithUser);
      } else {
        toast.showError('You do not have permission to create lanes');
      }
    }
  };

  const handleEdit = (lane) => {
    // Check if user has permission to edit this lane
    if (canEdit(lane, 'Lanes')) {
      setEditingLane(lane);
      setDialogOpen(true);
    } else {
      toast.showError('You do not have permission to edit this lane');
    }
  };

  const handleDelete = (id) => {
    // Find the lane by ID
    const lane = lanes?.find(l => l.LaneID === id);

    // Check if user has permission to delete this lane
    if (lane && canDelete(lane, 'Lanes')) {
      if (window.confirm('Are you sure you want to delete this lane?')) {
        deleteMutation.mutate(id);
      }
    } else {
      toast.showError('You do not have permission to delete this lane');
    }
  };

  const handleToggleStatus = (lane) => {
    if (!lane || !lane.LaneID) {
      toast.showError('Cannot toggle status: Invalid lane data');
      return;
    }

    // Check if user has permission to edit this lane
    if (canEdit(lane, 'Lanes')) {
      console.log('Toggling status for lane:', lane);

      // Show a confirmation dialog
      if (window.confirm(`Are you sure you want to ${isStatusActive(lane.ActiveStatus) ? 'deactivate' : 'activate'} this lane?`)) {
        // Pass the lane ID directly
        toggleStatusMutation.mutate(lane.LaneID);
      }
    } else {
      toast.showError('You do not have permission to change the status of this lane');
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingLane(null);
  };

  if (lanesLoading || plazasLoading || companiesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Lane Management</h1>
          <PermissionButton
            requiredModule="Lanes"
            requiredPermissions={["Create"]}
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Lane
          </PermissionButton>
        </div>

        {/* Filter Section */}
        <div className="bg-white rounded-lg shadow p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 className="text-lg font-semibold text-gray-700 flex items-center">
              <Filter className="w-5 h-5 mr-2 text-blue-600" />
              Filters
            </h2>
            <ModuleFilters
              companies={processedCompanies || []}
              plazas={processedPlazas || []}
              filters={filters}
              onFilterChange={setFilters}
              showCompanyFilter={true}
              showPlazaFilter={true}
              loading={lanesLoading || plazasLoading || companiesLoading}
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow">
          <LaneList
            lanes={filteredLanes || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={setSelectedLane}
            onToggleStatus={handleToggleStatus}
          />
        </div>

        {plazas && companies && (
          <LaneDialog
            isOpen={dialogOpen}
            onClose={handleCloseDialog}
            onSubmit={handleSubmit}
            initialData={editingLane}
            title={editingLane ? 'Edit Lane' : 'Add Lane'}
            plazas={plazas}
            companies={companies}
          />
        )}
      </div>
    </div>
  );
}
