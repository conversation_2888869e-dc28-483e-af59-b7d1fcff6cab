import React from 'react';

export function RecentActivities({ activities }) {
  return (
    <div className="space-y-4">
      {activities.map((activity, index) => (
        <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div className={`w-2 h-2 rounded-full ${activity.color}`} />
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-900">{activity.title}</p>
            <p className="text-xs text-gray-600">{activity.time}</p>
          </div>
          <span className="text-sm text-gray-600">{activity.value}</span>
        </div>
      ))}
    </div>
  );
}
