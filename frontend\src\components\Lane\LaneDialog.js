import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';
import { plazaApi } from '../../api/plazaApi';
import { useQuery } from '@tanstack/react-query';

/**
 * ===============================================================================
 * # Lane Dialog Component
 * ===============================================================================
 *
 * A comprehensive modal dialog for creating and editing lane configurations.
 * This component handles all the form fields required for lane management,
 * including validation and submission.
 *
 * Features:
 * - Form validation for required fields
 * - Dynamic initialization from existing lane data
 * - Support for all lane configuration parameters
 * - Responsive layout with field grouping
 * - Error messaging for invalid inputs
 *
 * @param {boolean} isOpen - Whether the dialog is visible
 * @param {Function} onClose - Callback when dialog is closed
 * @param {Function} onSubmit - Callback when form is submitted with valid data
 * @param {Object} initialData - Existing lane data for editing (null for new lanes)
 * @param {string} title - Dialog title (e.g., "Add Lane" or "Edit Lane")
 * @param {Array} plazas - List of available plazas for selection
 * @param {Array} companies - List of available companies for selection
 * @returns {JSX.Element|null} The rendered dialog or null if closed
 */
export default function LaneDialog({ isOpen, onClose, onSubmit, initialData, title, plazas: allPlazas, companies }) {
  const { user } = useAuth();
  
  // State to track the selected company's plazas
  const [companyPlazas, setCompanyPlazas] = useState([]);
  
  // -------------------------------------------------------------------------------
  // State Management
  // -------------------------------------------------------------------------------

  /**
   * Form data state containing all lane configuration fields
   * This comprehensive state object maps directly to the tblLaneDetails table structure
   */
  const [formData, setFormData] = useState({
    // Core lane identification and configuration
    PlazaID: '',            // Plaza where lane is located
    CompanyID: '',          // Company that owns/operates the lane
    LaneNumber: '',         // Unique number identifying the lane
    LaneType: 'Entry',      // Type of lane (Entry/Exit)
    LaneDetails: '',        // Additional details about the lane
    TypeCode: '',           // Code indicating lane type
    DataForPrint: '',       // Data to be printed on receipts
    LaneIP: '',             // IP address of the lane controller
    VehicleType: '',        // Type of vehicle the lane serves
    VehicleType2: '',       // Secondary vehicle type
    UpdatedBy: 'admin',     // User who last updated the lane (default: admin)
    ActiveStatus: true,     // Whether the lane is active (default: true)

    // Display and interface configuration
    iDisplayComport: '',    // COM port for display
    DisplayPole: false,     // Display pole configuration
    CashDrawer: false,      // Cash drawer configuration

    // Lane operation configuration
    MultipleExit: false,    // Whether multiple exits are allowed
    Antipassback: false,    // Anti-passback configuration
    HFPasscard: false,      // High-frequency pass card configuration
    HFPassPort: '',         // High-frequency pass port configuration
    CoinReaderPort: '',     // Port for coin reader
    APS_Exit: false,        // Automated parking system exit configuration

    // Feature flags
    flgKioskCamera: false,  // Kiosk camera enabled flag
    flgReceiptPrint: false, // Receipt printing enabled flag
    flgGKeyDetails: false,  // G-key details flag
    flgCKeyCard: false,     // C-key card flag
    flgP4S: false,          // P4S feature flag
    LOTFee: 0,              // LOT fee configuration
    flgPasscard: false,     // Pass card enabled flag

    // Payment gateway configuration
    PGTID: '',              // Payment gateway ID
    pgActivationKey: '',    // Payment gateway activation key
    Passcard_Reader_Type: '', // Type of pass card reader
    PayTmPG: false,         // PayTm payment gateway configuration

    // Camera configuration
    FlgLPRCamera: false,    // License plate recognition camera flag
    LPRCamIP: '',           // LPR camera IP address
    LPRCamID: '',           // LPR camera ID
    LPRCamPass: '',         // LPR camera password

    // Additional configuration
    iGraceMinute: 0,        // Grace period in minutes
    flgPaperSensor: false,  // Paper sensor flag
    PGSLevel: '',           // PGS level configuration
    PrinterMake: '',        // Printer manufacturer
    BarcodeType: '',        // Type of barcode used
    PrinterPort: '',        // Printer port configuration

    // Payment configuration
    sPaytmWallet: '',       // PayTm wallet configuration
    sPaytmMID: '',          // PayTm merchant ID
    sPaytmKey: '',          // PayTm key
    fRecyclerStatus: false, // Recycler status
    sSMSKey: '',            // SMS key for notifications

    // Additional lane configuration
    flgCCUpdateEx: false,   // CC update exchange flag
    LaneNumber2: '',        // Secondary lane number
    flgSubLane: false,      // Sub-lane flag
    RecyclerType: ''        // Type of recycler
  });

  /**
   * Validation errors state
   * Tracks validation errors for form fields
   */
  const [errors, setErrors] = useState({});

  // -------------------------------------------------------------------------------
  // Lifecycle Effects
  // -------------------------------------------------------------------------------

  /**
   * Initialize form data when initialData changes or dialog opens
   * Populates form with existing lane data for editing, or resets for new lane
   */
  // Fetch plazas for the selected company
  const { data: fetchedCompanyPlazas, isLoading: plazasLoading, error: plazasError } = useQuery({
    queryKey: ['plazasByCompany', formData.CompanyID],
    queryFn: async () => {
      try {
        // Log the API call
        console.log(`Calling API: /companies/${formData.CompanyID}/plazas`);
        
        // Make the API call
        const data = await plazaApi.getPlazasByCompany(formData.CompanyID);
        
        // Log the response
        console.log(`API response for company ${formData.CompanyID} plazas:`, data);
        
        return data;
      } catch (error) {
        console.error(`API call failed for company ${formData.CompanyID} plazas:`, error);
        throw error;
      }
    },
    enabled: !!formData.CompanyID, // Only run the query if a company is selected
    onSuccess: (data) => {
      console.log('Setting company plazas state with:', data);
      setCompanyPlazas(data || []);
    },
    onError: (error) => {
      console.error('Error fetching plazas for company:', error);
    }
  });
  
  // Debug when company ID changes
  useEffect(() => {
    if (formData.CompanyID) {
      console.log('Company ID selected:', formData.CompanyID);
    }
  }, [formData.CompanyID]);
  
  // Debug the plazas and companies data
  useEffect(() => {
    if (allPlazas && companies) {
      console.log('All plazas data in LaneDialog:', allPlazas);
      console.log('Companies data in LaneDialog:', companies);
      
      // Check the structure of the first plaza and company
      if (Array.isArray(allPlazas) && allPlazas.length > 0) {
        console.log('First plaza structure:', allPlazas[0]);
        console.log('Plaza company ID type:', typeof allPlazas[0].CompanyID);
      }
      
      if (Array.isArray(companies) && companies.length > 0) {
        console.log('First company structure:', companies[0]);
        console.log('Company ID type:', typeof companies[0].Id);
      }
    }
  }, [allPlazas, companies]);

  useEffect(() => {
    if (initialData) {
      // Populate form with existing lane data, using fallbacks for missing values
      setFormData({
        // Core lane identification and configuration
        PlazaID: initialData.PlazaID || '',
        CompanyID: initialData.CompanyID || '',
        LaneNumber: initialData.LaneNumber || '',
        LaneType: initialData.LaneType || 'Entry',
        LaneDetails: initialData.LaneDetails || '',
        TypeCode: initialData.TypeCode || '',
        DataForPrint: initialData.DataForPrint || '',
        LaneIP: initialData.LaneIP || '',
        VehicleType: initialData.VehicleType || '',
        VehicleType2: initialData.VehicleType2 || '',
        UpdatedBy: initialData.UpdatedBy || 'admin', // Use existing value or default to 'admin'
        ActiveStatus: initialData.ActiveStatus !== undefined ? initialData.ActiveStatus : true,

        // Display and interface configuration
        iDisplayComport: initialData.iDisplayComport || '',
        DisplayPole: initialData.DisplayPole || false,
        CashDrawer: initialData.CashDrawer || false,

        // Lane operation configuration
        MultipleExit: initialData.MultipleExit || false,
        Antipassback: initialData.Antipassback || false,
        HFPasscard: initialData.HFPasscard || false,
        HFPassPort: initialData.HFPassPort || '',
        CoinReaderPort: initialData.CoinReaderPort || '',
        APS_Exit: initialData.APS_Exit || false,

        // Feature flags
        flgKioskCamera: initialData.flgKioskCamera || false,
        flgReceiptPrint: initialData.flgReceiptPrint || false,
        flgGKeyDetails: initialData.flgGKeyDetails || false,
        flgCKeyCard: initialData.flgCKeyCard || false,
        flgP4S: initialData.flgP4S || false,
        LOTFee: initialData.LOTFee || 0,
        flgPasscard: initialData.flgPasscard || false,

        // Payment gateway configuration
        PGTID: initialData.PGTID || '',
        pgActivationKey: initialData.pgActivationKey || '',
        Passcard_Reader_Type: initialData.Passcard_Reader_Type || '',
        PayTmPG: initialData.PayTmPG || false,

        // Camera configuration
        FlgLPRCamera: initialData.FlgLPRCamera || false,
        LPRCamIP: initialData.LPRCamIP || '',
        LPRCamID: initialData.LPRCamID || '',
        LPRCamPass: initialData.LPRCamPass || '',

        // Additional configuration
        iGraceMinute: initialData.iGraceMinute || 0,
        flgPaperSensor: initialData.flgPaperSensor || false,
        PGSLevel: initialData.PGSLevel || '',
        PrinterMake: initialData.PrinterMake || '',
        BarcodeType: initialData.BarcodeType || '',
        PrinterPort: initialData.PrinterPort || '',

        // Payment configuration
        sPaytmWallet: initialData.sPaytmWallet || '',
        sPaytmMID: initialData.sPaytmMID || '',
        sPaytmKey: initialData.sPaytmKey || '',
        fRecyclerStatus: initialData.fRecyclerStatus || false,
        sSMSKey: initialData.sSMSKey || '',

        // Additional lane configuration
        flgCCUpdateEx: initialData.flgCCUpdateEx || false,
        LaneNumber2: initialData.LaneNumber2 || '',
        flgSubLane: initialData.flgSubLane || false,
        RecyclerType: initialData.RecyclerType || ''
      });
    } else {
      // Reset form for new lane
      resetForm();
    }
  }, [initialData, isOpen]); // Re-run when initialData or isOpen changes

  // -------------------------------------------------------------------------------
  // Form Handling Functions
  // -------------------------------------------------------------------------------

  /**
   * Reset form to default values
   * Clears all form fields and validation errors
   */
  const resetForm = () => {
    setFormData({
      // Core lane identification and configuration
      PlazaID: '',
      CompanyID: '',
      LaneNumber: '',
      LaneType: 'Entry',
      LaneDetails: '',
      TypeCode: '',
      DataForPrint: '',
      LaneIP: '',
      VehicleType: '',
      VehicleType2: '',
      UpdatedBy: 'admin', // Default value for UpdatedBy
      ActiveStatus: true,

      // Display and interface configuration
      iDisplayComport: '',
      DisplayPole: false,
      CashDrawer: false,

      // Lane operation configuration
      MultipleExit: false,
      Antipassback: false,
      HFPasscard: false,
      HFPassPort: '',
      CoinReaderPort: '',
      APS_Exit: false,

      // Feature flags
      flgKioskCamera: false,
      flgReceiptPrint: false,
      flgGKeyDetails: false,
      flgCKeyCard: false,
      flgP4S: false,
      LOTFee: 0,
      flgPasscard: false,

      // Payment gateway configuration
      PGTID: '',
      pgActivationKey: '',
      Passcard_Reader_Type: '',
      PayTmPG: false,

      // Camera configuration
      FlgLPRCamera: false,
      LPRCamIP: '',
      LPRCamID: '',
      LPRCamPass: '',

      // Additional configuration
      iGraceMinute: 0,
      flgPaperSensor: false,
      PGSLevel: '',
      PrinterMake: '',
      BarcodeType: '',
      PrinterPort: '',

      // Payment configuration
      sPaytmWallet: '',
      sPaytmMID: '',
      sPaytmKey: '',
      fRecyclerStatus: false,
      sSMSKey: '',

      // Additional lane configuration
      flgCCUpdateEx: false,
      LaneNumber2: '',
      flgSubLane: false,
      RecyclerType: ''
    });

    // Clear all validation errors
    setErrors({});
  };

  /**
   * Handle form field changes
   * Updates form state and clears validation errors for the changed field
   *
   * @param {Object} e - Event object from input change
   */
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // If company is changing, reset the plaza selection
    if (name === 'CompanyID') {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value,
        // Reset PlazaID when company changes
        PlazaID: ''
      }));
    } else {
      // Normal update for other fields
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }

    // Clear error for this field if any
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  /**
   * Handle numeric input changes
   * Ensures numeric values are stored as numbers, not strings
   *
   * @param {Object} e - Event object from input change
   */
  const handleNumberChange = (e) => {
    const { name, value } = e.target;

    // Convert empty string to 0, otherwise parse as number
    setFormData(prev => ({
      ...prev,
      [name]: value === '' ? 0 : Number(value)
    }));
  };

  /**
   * Validate form data
   * Checks required fields and sets validation errors
   *
   * @returns {boolean} True if form is valid, false otherwise
   */
  const validateForm = () => {
    const newErrors = {};

    // Check required fields
    if (!formData.PlazaID) newErrors.PlazaID = 'Plaza is required';
    if (!formData.CompanyID) newErrors.CompanyID = 'Company is required';
    if (!formData.LaneNumber) newErrors.LaneNumber = 'Lane number is required';
    if (!formData.LaneType) newErrors.LaneType = 'Lane type is required';
    if (!formData.UpdatedBy) newErrors.UpdatedBy = 'Updated By is required';

    // Update errors state
    setErrors(newErrors);

    // Form is valid if there are no errors
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Handle form submission
   * Validates form and calls onSubmit callback if valid
   *
   * @param {Object} e - Form submit event
   */
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Add current user ID for tracking who created/modified the record
      const dataToSubmit = {
        ...formData,
        UpdatedBy: user?.id || formData.UpdatedBy || 'admin'
      };

      // Form is valid, submit data to parent component
      onSubmit(dataToSubmit);
    }
  };

  // Don't render anything if dialog is closed
  if (!isOpen) return null;

  return (
    // ===============================================================================
    // ## MODAL CONTAINER
    // ===============================================================================
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-auto">
        {/* -------------------------------------------------------------------------------
         * Modal Header
         * Contains title and close button
         * ------------------------------------------------------------------------------- */}
        <div className="flex justify-between items-center px-6 py-4 border-b">
          <h2 className="text-lg font-medium">{title}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
            aria-label="Close dialog"
          >
            <X size={20} />
          </button>
        </div>



        {/* =============================================================================== */}
        {/* ## FORM SECTION */}
        {/* =============================================================================== */}
        <form onSubmit={handleSubmit} className="p-6">
          {/* -------------------------------------------------------------------------------
           * Core Lane Information Fields
           * Contains required fields and primary configuration options
           * ------------------------------------------------------------------------------- */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Company Selection - Required Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company*
              </label>
              <select
                name="CompanyID"
                value={formData.CompanyID}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.CompanyID ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select Company</option>
                {(() => {
                  // Handle different possible data structures
                  let companyItems = companies;

                  // If companies is not an array but has a companies property that is an array
                  if (companies && !Array.isArray(companies) && companies.companies && Array.isArray(companies.companies)) {
                    companyItems = companies.companies;
                  }

                  // If companies is not an array but has a data property that is an array
                  if (companies && !Array.isArray(companies) && companies.data && Array.isArray(companies.data)) {
                    companyItems = companies.data;
                  }

                  if (!Array.isArray(companyItems)) {
                    return <option value="">No companies available</option>;
                  }

                  return companyItems.map((company) => {
                    // Check if company has the required properties
                    const id = company.Id || company.id || company.CompanyID || company.companyID;
                    const name = company.CompanyName || company.companyName || company.name;

                    if (id && name) {
                      console.log('Company option:', id, name);
                      return (
                        <option key={id} value={id}>
                          {name}
                        </option>
                      );
                    }
                    return null;
                  });
                })()}
              </select>
              {errors.CompanyID && (
                <p className="mt-1 text-sm text-red-500">{errors.CompanyID}</p>
              )}
            </div>

            {/* Plaza Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plaza*
              </label>
              <select
                name="PlazaID"
                value={formData.PlazaID}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.PlazaID ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={!formData.CompanyID} // Disable if no company is selected
              >
                <option value="">
                  {!formData.CompanyID 
                    ? "Select a company first" 
                    : "Select Plaza"}
                </option>
                {(() => {
                  // If no company is selected, don't show any plazas
                  if (!formData.CompanyID) {
                    return null;
                  }

                  // Show loading state while fetching plazas
                  if (plazasLoading) {
                    return <option value="">Loading plazas...</option>;
                  }

                  // If there was an error fetching plazas, show an error message
                  if (plazasError) {
                    console.error('Error fetching plazas:', plazasError);
                    return <option value="">Error loading plazas</option>;
                  }

                  // Use the company-specific plazas fetched from the API
                  if (fetchedCompanyPlazas) {
                    // Handle different response structures
                    let plazaItems = [];
                    
                    if (Array.isArray(fetchedCompanyPlazas)) {
                      plazaItems = fetchedCompanyPlazas;
                    } else if (fetchedCompanyPlazas.plazas && Array.isArray(fetchedCompanyPlazas.plazas)) {
                      plazaItems = fetchedCompanyPlazas.plazas;
                    } else if (fetchedCompanyPlazas.data && Array.isArray(fetchedCompanyPlazas.data)) {
                      plazaItems = fetchedCompanyPlazas.data;
                    }
                    
                    if (plazaItems.length > 0) {
                      console.log('Rendering plaza options:', plazaItems);
                      
                      return plazaItems.map((plaza) => {
                        // Check if plaza has the required properties
                        const id = plaza.Id || plaza.id || plaza.PlazaID || plaza.plazaID;
                        const name = plaza.PlazaName || plaza.plazaName || plaza.name;
                        
                        console.log('Plaza option:', id, name);

                        if (id && name) {
                          return (
                            <option key={id} value={id}>
                              {name}
                            </option>
                          );
                        }
                        return null;
                      });
                    }
                  }

                  // If no company-specific plazas are found, show a message
                  return <option value="">No plazas available for this company</option>;
                })()}
              </select>
              {errors.PlazaID && (
                <p className="mt-1 text-sm text-red-500">{errors.PlazaID}</p>
              )}
            </div>

            {/* Lane Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Lane Number*
              </label>
              <input
                type="text"
                name="LaneNumber"
                value={formData.LaneNumber}
                onChange={handleChange}
                maxLength={2}
                placeholder="Max 2 characters (e.g., 01)"
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.LaneNumber ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.LaneNumber && (
                <p className="mt-1 text-sm text-red-500">{errors.LaneNumber}</p>
              )}
            </div>

            {/* Lane Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Lane Type*
              </label>
              <select
                name="LaneType"
                value={formData.LaneType}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.LaneType ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="Entry">Entry</option>
                <option value="Exit">Exit</option>
              </select>
              {errors.LaneType && (
                <p className="mt-1 text-sm text-red-500">{errors.LaneType}</p>
              )}
            </div>

            {/* Updated By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Updated By*
              </label>
              <input
                type="text"
                name="UpdatedBy"
                value={formData.UpdatedBy}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.UpdatedBy ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.UpdatedBy && (
                <p className="mt-1 text-sm text-red-500">{errors.UpdatedBy}</p>
              )}
            </div>

            {/* Lane Details */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Lane Details
              </label>
              <input
                type="text"
                name="LaneDetails"
                value={formData.LaneDetails}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Type Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Type Code
              </label>
              <input
                type="text"
                name="TypeCode"
                value={formData.TypeCode}
                onChange={handleChange}
                maxLength={2}
                placeholder="Max 2 characters (e.g., 01)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Lane IP */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Lane IP
              </label>
              <input
                type="text"
                name="LaneIP"
                value={formData.LaneIP}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Vehicle Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vehicle Type
              </label>
              <input
                type="text"
                name="VehicleType"
                value={formData.VehicleType}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Vehicle Type 2 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Vehicle Type 2
              </label>
              <input
                type="text"
                name="VehicleType2"
                value={formData.VehicleType2}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* LPR Camera IP */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LPR Camera IP
              </label>
              <input
                type="text"
                name="LPRCamIP"
                value={formData.LPRCamIP}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* LPR Camera ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LPR Camera ID
              </label>
              <input
                type="text"
                name="LPRCamID"
                value={formData.LPRCamID}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* LPR Camera Password */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                LPR Camera Password
              </label>
              <input
                type="password"
                name="LPRCamPass"
                value={formData.LPRCamPass}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Grace Minutes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Grace Minutes
              </label>
              <input
                type="number"
                name="iGraceMinute"
                value={formData.iGraceMinute}
                onChange={handleNumberChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          {/* =============================================================================== */}
          {/* ## LANE CONFIGURATION SECTION */}
          {/* =============================================================================== */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Lane Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="activeStatus"
                  name="ActiveStatus"
                  checked={formData.ActiveStatus}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="activeStatus" className="ml-2 text-sm font-medium text-gray-700">
                  Active Status
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="displayPole"
                  name="DisplayPole"
                  checked={formData.DisplayPole}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="displayPole" className="ml-2 text-sm font-medium text-gray-700">
                  Display Pole
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="cashDrawer"
                  name="CashDrawer"
                  checked={formData.CashDrawer}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="cashDrawer" className="ml-2 text-sm font-medium text-gray-700">
                  Cash Drawer
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="multipleExit"
                  name="MultipleExit"
                  checked={formData.MultipleExit}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="multipleExit" className="ml-2 text-sm font-medium text-gray-700">
                  Multiple Exit
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="antipassback"
                  name="Antipassback"
                  checked={formData.Antipassback}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="antipassback" className="ml-2 text-sm font-medium text-gray-700">
                  Antipassback
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="hfPasscard"
                  name="HFPasscard"
                  checked={formData.HFPasscard}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="hfPasscard" className="ml-2 text-sm font-medium text-gray-700">
                  HF Passcard
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="flgLPRCamera"
                  name="FlgLPRCamera"
                  checked={formData.FlgLPRCamera}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="flgLPRCamera" className="ml-2 text-sm font-medium text-gray-700">
                  LPR Camera
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="flgKioskCamera"
                  name="flgKioskCamera"
                  checked={formData.flgKioskCamera}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="flgKioskCamera" className="ml-2 text-sm font-medium text-gray-700">
                  Kiosk Camera
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="flgReceiptPrint"
                  name="flgReceiptPrint"
                  checked={formData.flgReceiptPrint}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="flgReceiptPrint" className="ml-2 text-sm font-medium text-gray-700">
                  Receipt Print
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="flgPaperSensor"
                  name="flgPaperSensor"
                  checked={formData.flgPaperSensor}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="flgPaperSensor" className="ml-2 text-sm font-medium text-gray-700">
                  Paper Sensor
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="flgSubLane"
                  name="flgSubLane"
                  checked={formData.flgSubLane}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="flgSubLane" className="ml-2 text-sm font-medium text-gray-700">
                  Sub Lane
                </label>
              </div>
            </div>
          </div>

          {/* =============================================================================== */}
          {/* ## PAYMENT CONFIGURATION SECTION */}
          {/* =============================================================================== */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  LOT Fee
                </label>
                <input
                  type="number"
                  name="LOTFee"
                  value={formData.LOTFee}
                  onChange={handleNumberChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PG Transaction ID
                </label>
                <input
                  type="text"
                  name="PGTID"
                  value={formData.PGTID}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PG Activation Key
                </label>
                <input
                  type="text"
                  name="pgActivationKey"
                  value={formData.pgActivationKey}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Paytm Wallet
                </label>
                <input
                  type="text"
                  name="sPaytmWallet"
                  value={formData.sPaytmWallet}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Paytm MID
                </label>
                <input
                  type="text"
                  name="sPaytmMID"
                  value={formData.sPaytmMID}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Paytm Key
                </label>
                <input
                  type="text"
                  name="sPaytmKey"
                  value={formData.sPaytmKey}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="payTmPG"
                  name="PayTmPG"
                  checked={formData.PayTmPG}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="payTmPG" className="ml-2 text-sm font-medium text-gray-700">
                  PayTm PG
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="fRecyclerStatus"
                  name="fRecyclerStatus"
                  checked={formData.fRecyclerStatus}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="fRecyclerStatus" className="ml-2 text-sm font-medium text-gray-700">
                  Recycler Status
                </label>
              </div>
            </div>
          </div>

          {/* -------------------------------------------------------------------------------
           * Form Action Buttons
           * Contains cancel and save buttons
           * ------------------------------------------------------------------------------- */}
          <div className="mt-8 flex justify-end space-x-3">
            {/* Cancel Button */}
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>

            {/* Submit Button - Shows "Update" or "Create" based on whether editing or creating */}
            <PermissionButton
              type="submit"
              className="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              requiredModule="Lanes"
              requiredPermissions={initialData ? ["Edit"] : ["Create"]}
              companyId={initialData?.CompanyID}
              plazaId={initialData?.PlazaID}
            >
              {initialData ? 'Update' : 'Create'}
            </PermissionButton>
          </div>
        </form>

      </div>
    </div>
  );
}
