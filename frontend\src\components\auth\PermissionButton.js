// frontend/src/components/auth/PermissionButton.js
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/authContext';
import toast from 'react-hot-toast';

/**
 * PermissionButton component that conditionally renders a button based on user permissions
 *
 * @param {Object} props - Component props
 * @param {string[]} [props.requiredPermissions] - Array of permissions required to render the button
 * @param {string} [props.requiredModule] - Module name for permission check
 * @param {string[]} [props.allowedRoles] - Array of roles allowed to render the button
 * @param {number} [props.companyId] - Company ID to check access for
 * @param {number} [props.plazaId] - Plaza ID to check access for
 * @param {React.ReactNode} [props.children] - Button content
 * @param {Function} [props.onClick] - Button click handler
 * @param {string} [props.className] - Additional CSS classes
 * @param {boolean} [props.disabled] - Whether the button is disabled
 * @param {string} [props.type] - Button type (button, submit, reset)
 * @param {Object} [props.rest] - Additional props to pass to the button
 * @returns {React.ReactNode} - The button or null
 */
const PermissionButton = ({
  requiredPermissions = [],
  requiredModule,
  allowedRoles = [],
  companyId,
  plazaId,
  children,
  onClick,
  className = '',
  disabled = false,
  type = 'button',
  ...rest
}) => {
  // Always call hooks at the top level
  const navigate = useNavigate();
  const {
    user,
    hasPermission,
    hasRole,
    hasCompanyAccess,
    hasPlazaAccess,
    isSuperAdmin
  } = useAuth();

  // If no user is logged in, don't render the button
  if (!user) {
    return null;
  }

  // SuperAdmin can access everything
  if (isSuperAdmin()) {
    return (
      <button
        type={type}
        className={className}
        onClick={onClick}
        disabled={disabled}
        {...rest}
      >
        {children}
      </button>
    );
  }

  // Check role-based access if specified
  if (allowedRoles.length > 0 && !allowedRoles.some(role => hasRole(role))) {
    return null;
  }

  // Check permission-based access if specified
  if (requiredModule && requiredPermissions.length > 0) {
    // Special case for CompanyAdmin viewing Companies actions
    const isCompanyAdminViewingCompanies =
      hasRole('CompanyAdmin') &&
      requiredModule === 'Companies' &&
      (requiredPermissions.includes('Edit') || requiredPermissions.includes('Delete'));

    // Special case for CompanyAdmin viewing Digital Pay actions
    const isCompanyAdminViewingDigitalPay =
      hasRole('CompanyAdmin') &&
      (requiredModule === 'Digital Pay' || requiredModule === 'DigitalPayment') &&
      (requiredPermissions.includes('Edit') || requiredPermissions.includes('Delete') || requiredPermissions.includes('Create'));

    // Special case for CompanyAdmin viewing Users actions
    const isCompanyAdminViewingUsers =
      hasRole('CompanyAdmin') &&
      requiredModule === 'Users' &&
      (requiredPermissions.includes('View') || requiredPermissions.includes('Edit') ||
       requiredPermissions.includes('Delete') || requiredPermissions.includes('Create'));

    // Special case for PlazaManager viewing Users actions - show buttons but redirect to unauthorized
    const isPlazaManagerViewingUsers =
      hasRole('PlazaManager') &&
      requiredModule === 'Users' &&
      (requiredPermissions.includes('Edit') || requiredPermissions.includes('Delete') || requiredPermissions.includes('Create'));

    // Special case for PlazaManager viewing ALL other modules - show buttons but redirect to unauthorized
    const isPlazaManagerViewingOtherModules =
      hasRole('PlazaManager') &&
      ['Companies', 'Plazas', 'Lanes', 'ANPR', 'DigitalPayment', 'Digital Pay', 'Fastag'].includes(requiredModule) &&
      (requiredPermissions.includes('Edit') || requiredPermissions.includes('Delete') || requiredPermissions.includes('Create'));

    // For CompanyAdmin viewing Companies, show the buttons that redirect to unauthorized page when clicked
    if (isCompanyAdminViewingCompanies) {
      return (
        <button
          type={type}
          className={className}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            // Redirect to the unauthorized page with a custom message
            const action = requiredPermissions[0].toLowerCase();
            const message = `Company Admins can view companies but cannot ${action} them`;
            navigate('/unauthorized', { state: { message } });
          }}
          {...rest}
        >
          {children}
        </button>
      );
    }

    // For CompanyAdmin viewing Digital Pay, enable the buttons
    if (isCompanyAdminViewingDigitalPay) {
      console.log('CompanyAdmin has access to Digital Pay action:', requiredPermissions);
      return (
        <button
          type={type}
          className={className}
          onClick={onClick}
          disabled={disabled}
          {...rest}
        >
          {children}
        </button>
      );
    }

    // For CompanyAdmin viewing Users, enable the buttons
    if (isCompanyAdminViewingUsers) {
      console.log('CompanyAdmin has access to Users action:', requiredPermissions);
      return (
        <button
          type={type}
          className={className}
          onClick={onClick}
          disabled={disabled}
          {...rest}
        >
          {children}
        </button>
      );
    }

    // For PlazaManager viewing Users, show the buttons that redirect to unauthorized page when clicked
    if (isPlazaManagerViewingUsers) {
      return (
        <button
          type={type}
          className={className}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            // Redirect to the unauthorized page with a custom message
            const action = requiredPermissions[0].toLowerCase();
            const message = `Plaza Managers can view users but cannot ${action} them`;
            navigate('/unauthorized', { state: { message } });
          }}
          {...rest}
        >
          {children}
        </button>
      );
    }

    // For PlazaManager viewing other modules, show the buttons that redirect to unauthorized page when clicked
    if (isPlazaManagerViewingOtherModules) {
      return (
        <button
          type={type}
          className={className}
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();

            // Redirect to the unauthorized page with a custom message
            const action = requiredPermissions[0].toLowerCase();
            const moduleName = requiredModule.toLowerCase().replace(/([A-Z])/g, ' $1').trim();
            const message = `Plaza Managers can view ${moduleName} but cannot ${action} them`;
            navigate('/unauthorized', { state: { message } });
          }}
          {...rest}
        >
          {children}
        </button>
      );
    }

    // Normal permission check for other cases
    const hasRequiredPermission = requiredPermissions.some(
      permission => hasPermission(requiredModule, permission)
    );

    if (!hasRequiredPermission) {
      return null;
    }
  }

  // Check company access if specified
  if (companyId && !hasCompanyAccess(companyId)) {
    return null;
  }

  // Check plaza access if specified
  if (plazaId && !hasPlazaAccess(plazaId)) {
    return null;
  }

  // User is authorized
  return (
    <button
      type={type}
      className={className}
      onClick={onClick}
      disabled={disabled}
      {...rest}
    >
      {children}
    </button>
  );
};

// Export both as default and named export for backward compatibility
export { PermissionButton };
export default PermissionButton;
