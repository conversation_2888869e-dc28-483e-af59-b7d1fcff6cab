# Direct PowerShell command to get plaza assignments using .NET SqlClient
# This doesn't require SQL Server PowerShell modules

param(
    [string]$Server = "parkwizvms.database.windows.net",
    [string]$Database = "ParkwizOps", 
    [string]$Username = "hparkwiz",
    [string]$Password = "Parkwiz@2020"
)

Write-Host "=== PLAZA ASSIGNMENTS REPORT (Direct SQL) ===" -ForegroundColor Cyan

# Build connection string
$connectionString = "Server=$Server;Database=$Database;User Id=$Username;Password=$Password;Encrypt=true;TrustServerCertificate=true;Connection Timeout=30;"

try {
    # Load System.Data.SqlClient
    Add-Type -AssemblyName System.Data
    
    # Create connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to database: $Database" -ForegroundColor Green
    
    # Main query for plaza assignments
    $query = @"
    SELECT 
        u.Id as UserId,
        u.Userna<PERSON>,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as <PERSON><PERSON><PERSON>,
        p.Id as <PERSON><PERSON><PERSON>,
        p.<PERSON>,
        p.<PERSON>,
        p.<PERSON> as Plaza<PERSON><PERSON><PERSON><PERSON>,
        p.<PERSON><PERSON>umber as PlazaContactNumber,
        c.CompanyName,
        up.IsActive as AssignmentActive,
        up.CreatedOn as AssignedOn
    FROM Users u
    INNER JOIN UserPlaza up ON u.Id = up.UserId
    INNER JOIN Plaza p ON up.PlazaId = p.Id
    INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE u.IsActive = 1 AND p.IsActive = 1
    ORDER BY u.Username, p.PlazaName
"@
    
    # Execute query
    $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    
    if ($dataTable.Rows.Count -gt 0) {
        Write-Host "`nPLAZA ASSIGNMENTS FOUND:" -ForegroundColor Green
        Write-Host "Total assignments: $($dataTable.Rows.Count)" -ForegroundColor Cyan
        
        # Group by user
        $userGroups = $dataTable.Rows | Group-Object Username
        
        foreach ($userGroup in $userGroups) {
            $user = $userGroup.Group[0]
            Write-Host "`n--- USER: $($user.Username) ($($user.FirstName) $($user.LastName)) ---" -ForegroundColor Magenta
            Write-Host "Role: $($user.RoleName)" -ForegroundColor White
            Write-Host "Email: $($user.Email)" -ForegroundColor White
            Write-Host "Assigned Plazas:" -ForegroundColor Yellow
            
            foreach ($assignment in $userGroup.Group) {
                Write-Host "  • $($assignment.PlazaName) (Code: $($assignment.PlazaCode))" -ForegroundColor White
                Write-Host "    Company: $($assignment.CompanyName)" -ForegroundColor Gray
                Write-Host "    Contact: $($assignment.PlazaContactPerson) - $($assignment.PlazaContactNumber)" -ForegroundColor Gray
                Write-Host "    Assigned On: $($assignment.AssignedOn)" -ForegroundColor Gray
                Write-Host ""
            }
        }
        
        # Summary by role
        Write-Host "`n=== SUMMARY BY ROLE ===" -ForegroundColor Cyan
        $roleSummary = $dataTable.Rows | Group-Object RoleName
        foreach ($roleGroup in $roleSummary) {
            $uniqueUsers = ($roleGroup.Group | Select-Object -Property UserId -Unique).Count
            Write-Host "$($roleGroup.Name): $uniqueUsers users with $($roleGroup.Count) total plaza assignments" -ForegroundColor White
        }
        
    } else {
        Write-Host "No plaza assignments found." -ForegroundColor Red
    }
    
    # Query for users without plaza assignments
    $usersWithoutPlazasQuery = @"
    SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName
    FROM Users u
    LEFT JOIN UserPlaza up ON u.Id = up.UserId
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE u.IsActive = 1 AND up.UserId IS NULL
    ORDER BY u.Username
"@
    
    $command2 = New-Object System.Data.SqlClient.SqlCommand($usersWithoutPlazasQuery, $connection)
    $adapter2 = New-Object System.Data.SqlClient.SqlDataAdapter($command2)
    $dataTable2 = New-Object System.Data.DataTable
    $adapter2.Fill($dataTable2)
    
    Write-Host "`n=== USERS WITHOUT PLAZA ASSIGNMENTS ===" -ForegroundColor Cyan
    if ($dataTable2.Rows.Count -gt 0) {
        Write-Host "Found $($dataTable2.Rows.Count) users without plaza assignments:" -ForegroundColor Yellow
        foreach ($user in $dataTable2.Rows) {
            Write-Host "  • $($user.Username) ($($user.FirstName) $($user.LastName)) - $($user.RoleName) - $($user.Email)" -ForegroundColor White
        }
    } else {
        Write-Host "All active users have plaza assignments." -ForegroundColor Green
    }
    
    $connection.Close()
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host "`n=== REPORT COMPLETE ===" -ForegroundColor Cyan