-- =============================================
-- Valet System Module and Permissions Setup
-- Create comprehensive valet module structure
-- =============================================

USE ParkwizOps;
GO

PRINT 'Starting Valet System Module Setup...';
PRINT '====================================';

-- =============================================
-- Step 1: Create Valet Management Module
-- =============================================
DECLARE @ValetModuleId INT;

IF NOT EXISTS (SELECT 1 FROM Modules WHERE Name = 'Valet Management')
BEGIN
    PRINT 'Creating Valet Management module...';
    
    INSERT INTO Modules (Name, Description, Icon, IsActive, CreatedBy, CreatedOn)
    VALUES ('Valet Management', 'Comprehensive valet parking management system', 'local_parking', 1, 1, GETDATE());
    
    SET @ValetModuleId = SCOPE_IDENTITY();
    PRINT '✅ Valet Management module created with ID: ' + CAST(@ValetModuleId AS NVARCHAR(10));
END
ELSE
BEGIN
    SELECT @ValetModuleId = Id FROM Modules WHERE Name = 'Valet Management';
    PRINT '⚠️ Valet Management module already exists with ID: ' + CAST(@ValetModuleId AS NVARCHAR(10));
END

-- =============================================
-- Step 2: Create Valet SubModules
-- =============================================
DECLARE @SubModules TABLE (
    Name NVARCHAR(100),
    Icon NVARCHAR(50)
);

INSERT INTO @SubModules VALUES
('Customer Management', 'people'),
('Driver Management', 'drive_eta'),
('Transaction Management', 'receipt_long'),
('Payment Management', 'payment'),
('Vehicle Tracking', 'my_location'),
('QR Code Management', 'qr_code'),
('Controller Dashboard', 'dashboard'),
('Valet Reports', 'assessment');

DECLARE @SubModuleName NVARCHAR(100), @SubModuleIcon NVARCHAR(50);
DECLARE submodule_cursor CURSOR FOR
SELECT Name, Icon FROM @SubModules;

OPEN submodule_cursor;
FETCH NEXT FROM submodule_cursor INTO @SubModuleName, @SubModuleIcon;

WHILE @@FETCH_STATUS = 0
BEGIN
    IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = @SubModuleName AND ModuleId = @ValetModuleId)
    BEGIN
        INSERT INTO SubModules (ModuleId, Name, Icon, IsActive, CreatedBy, CreatedOn)
        VALUES (@ValetModuleId, @SubModuleName, @SubModuleIcon, 1, 1, GETDATE());

        PRINT '✅ SubModule created: ' + @SubModuleName;
    END
    ELSE
    BEGIN
        PRINT '⚠️ SubModule already exists: ' + @SubModuleName;
    END

    FETCH NEXT FROM submodule_cursor INTO @SubModuleName, @SubModuleIcon;
END

CLOSE submodule_cursor;
DEALLOCATE submodule_cursor;

-- =============================================
-- Step 3: Create SubModule Permissions
-- =============================================
PRINT 'Creating SubModule Permissions...';

DECLARE @PermissionId INT;
DECLARE @SubModuleId INT;

-- Get all permission IDs
DECLARE @ViewPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'View');
DECLARE @CreatePermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Create');
DECLARE @EditPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Edit');
DECLARE @DeletePermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Delete');

-- Create permissions for each submodule
DECLARE submodule_permission_cursor CURSOR FOR 
SELECT Id, Name FROM SubModules WHERE ModuleId = @ValetModuleId;

OPEN submodule_permission_cursor;
FETCH NEXT FROM submodule_permission_cursor INTO @SubModuleId, @SubModuleName;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- Create View permission
    IF NOT EXISTS (SELECT 1 FROM SubModulePermissions WHERE SubModuleId = @SubModuleId AND PermissionId = @ViewPermissionId)
    BEGIN
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SubModuleId, @ViewPermissionId, 1, 1, GETDATE());
    END
    
    -- Create Create permission
    IF NOT EXISTS (SELECT 1 FROM SubModulePermissions WHERE SubModuleId = @SubModuleId AND PermissionId = @CreatePermissionId)
    BEGIN
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SubModuleId, @CreatePermissionId, 1, 1, GETDATE());
    END
    
    -- Create Edit permission
    IF NOT EXISTS (SELECT 1 FROM SubModulePermissions WHERE SubModuleId = @SubModuleId AND PermissionId = @EditPermissionId)
    BEGIN
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SubModuleId, @EditPermissionId, 1, 1, GETDATE());
    END
    
    -- Create Delete permission
    IF NOT EXISTS (SELECT 1 FROM SubModulePermissions WHERE SubModuleId = @SubModuleId AND PermissionId = @DeletePermissionId)
    BEGIN
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@SubModuleId, @DeletePermissionId, 1, 1, GETDATE());
    END
    
    PRINT '✅ Permissions created for: ' + @SubModuleName;
    
    FETCH NEXT FROM submodule_permission_cursor INTO @SubModuleId, @SubModuleName;
END

CLOSE submodule_permission_cursor;
DEALLOCATE submodule_permission_cursor;

-- =============================================
-- Step 4: Assign Permissions to Roles
-- =============================================
PRINT 'Assigning permissions to roles...';

-- Get role IDs
DECLARE @SuperAdminRoleId INT = (SELECT Id FROM Roles WHERE Name = 'SuperAdmin');
DECLARE @CompanyAdminRoleId INT = (SELECT Id FROM Roles WHERE Name = 'CompanyAdmin');
DECLARE @PlazaManagerRoleId INT = (SELECT Id FROM Roles WHERE Name = 'PlazaManager');

-- Create ValetController and ValetDriver roles if they don't exist
DECLARE @ValetControllerRoleId INT;
DECLARE @ValetDriverRoleId INT;

IF NOT EXISTS (SELECT 1 FROM Roles WHERE Name = 'ValetController')
BEGIN
    INSERT INTO Roles (Name, IsActive, CreatedBy, CreatedOn)
    VALUES ('ValetController', 1, 1, GETDATE());

    SET @ValetControllerRoleId = SCOPE_IDENTITY();
    PRINT '✅ ValetController role created with ID: ' + CAST(@ValetControllerRoleId AS NVARCHAR(10));
END
ELSE
BEGIN
    SELECT @ValetControllerRoleId = Id FROM Roles WHERE Name = 'ValetController';
    PRINT '⚠️ ValetController role already exists with ID: ' + CAST(@ValetControllerRoleId AS NVARCHAR(10));
END

IF NOT EXISTS (SELECT 1 FROM Roles WHERE Name = 'ValetDriver')
BEGIN
    INSERT INTO Roles (Name, IsActive, CreatedBy, CreatedOn)
    VALUES ('ValetDriver', 1, 1, GETDATE());

    SET @ValetDriverRoleId = SCOPE_IDENTITY();
    PRINT '✅ ValetDriver role created with ID: ' + CAST(@ValetDriverRoleId AS NVARCHAR(10));
END
ELSE
BEGIN
    SELECT @ValetDriverRoleId = Id FROM Roles WHERE Name = 'ValetDriver';
    PRINT '⚠️ ValetDriver role already exists with ID: ' + CAST(@ValetDriverRoleId AS NVARCHAR(10));
END

-- Assign full permissions to SuperAdmin
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT @SuperAdminRoleId, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
WHERE sm.ModuleId = @ValetModuleId
AND NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = @SuperAdminRoleId AND SubModulePermissionId = smp.Id);

PRINT '✅ Full permissions assigned to SuperAdmin';

-- Assign appropriate permissions to CompanyAdmin
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT @CompanyAdminRoleId, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
INNER JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.ModuleId = @ValetModuleId
AND sm.Name NOT IN ('QR Code Management') -- CompanyAdmin cannot manage QR codes
AND NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = @CompanyAdminRoleId AND SubModulePermissionId = smp.Id);

PRINT '✅ Appropriate permissions assigned to CompanyAdmin';

-- Assign appropriate permissions to PlazaManager
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT @PlazaManagerRoleId, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
INNER JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.ModuleId = @ValetModuleId
AND sm.Name NOT IN ('QR Code Management', 'Payment Management') -- PlazaManager cannot manage QR codes or payments
AND NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = @PlazaManagerRoleId AND SubModulePermissionId = smp.Id);

PRINT '✅ Appropriate permissions assigned to PlazaManager';

-- Assign controller dashboard permissions to ValetController
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT @ValetControllerRoleId, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
INNER JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.ModuleId = @ValetModuleId
AND sm.Name IN ('Controller Dashboard', 'Transaction Management', 'Vehicle Tracking', 'Customer Management')
AND p.Name IN ('View', 'Edit') -- ValetController can view and edit but not create/delete
AND NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = @ValetControllerRoleId AND SubModulePermissionId = smp.Id);

PRINT '✅ Controller permissions assigned to ValetController';

-- Assign limited permissions to ValetDriver
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT @ValetDriverRoleId, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
INNER JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.ModuleId = @ValetModuleId
AND sm.Name IN ('Vehicle Tracking', 'Transaction Management')
AND p.Name IN ('View', 'Edit') -- ValetDriver can only view and update vehicle status
AND NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = @ValetDriverRoleId AND SubModulePermissionId = smp.Id);

PRINT '✅ Limited permissions assigned to ValetDriver';

-- =============================================
-- Step 5: Display Summary
-- =============================================
PRINT '';
PRINT '=== VALET SYSTEM MODULE SETUP SUMMARY ===';
PRINT '';

SELECT 
    m.Name as ModuleName,
    sm.Name as SubModuleName,
    COUNT(smp.Id) as TotalPermissions
FROM Modules m
INNER JOIN SubModules sm ON m.Id = sm.ModuleId
INNER JOIN SubModulePermissions smp ON sm.Id = smp.SubModuleId
WHERE m.Name = 'Valet Management'
GROUP BY m.Name, sm.Name
ORDER BY sm.Name;

PRINT '';
PRINT '=== ROLE PERMISSIONS SUMMARY ===';
PRINT '';

SELECT 
    r.Name as RoleName,
    COUNT(rp.Id) as ValetPermissions
FROM Roles r
INNER JOIN RolePermissions rp ON r.Id = rp.RoleId
INNER JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
INNER JOIN SubModules sm ON smp.SubModuleId = sm.Id
INNER JOIN Modules m ON sm.ModuleId = m.Id
WHERE m.Name = 'Valet Management'
AND rp.IsActive = 1
GROUP BY r.Name
ORDER BY COUNT(rp.Id) DESC;

PRINT '';
PRINT '✅ Valet System Module Setup Completed Successfully!';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Update frontend navigation to include Valet Management';
PRINT '2. Use submodule names in PermissionGuard components';
PRINT '3. Restart backend server to refresh permissions';
PRINT '4. Test role-based access for valet modules';
PRINT '';
