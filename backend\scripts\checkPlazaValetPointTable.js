const sql = require('mssql');
require('dotenv').config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT) || 30000,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT) || 30000,
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 30000,
  }
};

async function checkPlazaValetPointTable() {
  let pool;
  
  try {
    console.log('🔍 Checking PlazaValetPoint table and stored procedures...');
    
    pool = await sql.connect(dbConfig);
    console.log('✅ Connected to database successfully!');
    
    // Check if PlazaValetPoint table exists
    console.log('\n📋 Checking PlazaValetPoint table...');
    const tableCheck = await pool.request().query(`
      SELECT COUNT(*) as TableExists
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'PlazaValetPoint'
    `);
    
    const tableExists = tableCheck.recordset[0].TableExists > 0;
    console.log(`Table exists: ${tableExists}`);
    
    if (!tableExists) {
      console.log('❌ PlazaValetPoint table does not exist!');
      console.log('🔧 Creating PlazaValetPoint table...');
      
      await pool.request().query(`
        CREATE TABLE [dbo].[PlazaValetPoint] (
          [Id] INT IDENTITY(1,1) PRIMARY KEY,
          [PlazaId] INT NOT NULL,
          [ValetPointName] NVARCHAR(500) NOT NULL,
          [IsActive] BIT NOT NULL DEFAULT 1,
          [CreatedBy] INT NOT NULL,
          [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
          [ModifiedBy] INT NULL,
          [ModifiedOn] DATETIME NULL,
          [CompanyId] DECIMAL(18,0) NOT NULL,
          [Latitude] DECIMAL(8,6) NULL,
          [Longitude] DECIMAL(9,6) NULL
        );
      `);
      
      console.log('✅ PlazaValetPoint table created successfully!');
    } else {
      console.log('✅ PlazaValetPoint table exists');
      
      // Check table structure
      const columns = await pool.request().query(`
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          CHARACTER_MAXIMUM_LENGTH,
          NUMERIC_PRECISION,
          NUMERIC_SCALE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'PlazaValetPoint'
        ORDER BY ORDINAL_POSITION
      `);
      
      console.log('\n📊 Table structure:');
      columns.recordset.forEach(col => {
        console.log(`- ${col.COLUMN_NAME}: ${col.DATA_TYPE}${col.CHARACTER_MAXIMUM_LENGTH ? `(${col.CHARACTER_MAXIMUM_LENGTH})` : ''}${col.NUMERIC_PRECISION ? `(${col.NUMERIC_PRECISION},${col.NUMERIC_SCALE})` : ''} - Nullable: ${col.IS_NULLABLE}`);
      });
    }
    
    // Check if stored procedures exist
    console.log('\n🔧 Checking stored procedures...');
    const spCheck = await pool.request().query(`
      SELECT 
        ROUTINE_NAME,
        ROUTINE_TYPE
      FROM INFORMATION_SCHEMA.ROUTINES 
      WHERE ROUTINE_NAME LIKE '%PlazaValetPoint%'
      ORDER BY ROUTINE_NAME
    `);
    
    console.log(`Found ${spCheck.recordset.length} stored procedures:`);
    spCheck.recordset.forEach(sp => {
      console.log(`- ${sp.ROUTINE_NAME} (${sp.ROUTINE_TYPE})`);
    });
    
    if (spCheck.recordset.length === 0) {
      console.log('❌ No PlazaValetPoint stored procedures found!');
      console.log('💡 You need to run the valet_point.sql script to create the stored procedures.');
    }
    
    // Test a simple query if table exists
    if (tableExists) {
      console.log('\n🧪 Testing table access...');
      const testQuery = await pool.request().query(`
        SELECT COUNT(*) as RecordCount FROM [dbo].[PlazaValetPoint]
      `);
      console.log(`Current records in table: ${testQuery.recordset[0].RecordCount}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
    
    if (error.message.includes('Invalid object name')) {
      console.error('💡 The PlazaValetPoint table does not exist. Please create it first.');
    } else if (error.message.includes('Could not find stored procedure')) {
      console.error('💡 The stored procedures do not exist. Please run the valet_point.sql script.');
    }
  } finally {
    if (pool) {
      await pool.close();
      console.log('\n🔌 Database connection closed');
    }
  }
}

checkPlazaValetPointTable();
