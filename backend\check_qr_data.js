const sql = require('mssql');
require('dotenv').config();

const config = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_DATABASE,
  options: {
    encrypt: true,
    trustServerCertificate: false
  }
};

async function checkQRData() {
  try {
    console.log('Connecting to database...');
    await sql.connect(config);
    console.log('Connected successfully');

    // Check what QR codes exist
    console.log('\n=== All QR Codes ===');
    const allQRs = await sql.query`SELECT TOP 10 * FROM ValetQRCodes ORDER BY CreatedOn DESC`;
    console.log('QR Codes found:', allQRs.recordset.length);
    allQRs.recordset.forEach(qr => {
      console.log(`- ID: ${qr.Id}, Data: ${qr.QRCodeData}, Active: ${qr.IsActive}, PlazaId: ${qr.PlazaId}, ValetPointId: ${qr.PlazaValetPointId}`);
    });

    // Check specific QR code
    const testQR = '1_1751953664734_41ea95a1';
    const fullURL = `http://localhost:3000/valet-customer?qr=${testQR}`;
    
    console.log(`\n=== Checking QR: ${testQR} ===`);
    const qrCheck1 = await sql.query`SELECT * FROM ValetQRCodes WHERE QRCodeData = ${testQR}`;
    console.log('Direct parameter match:', qrCheck1.recordset.length);
    
    console.log(`\n=== Checking QR: ${fullURL} ===`);
    const qrCheck2 = await sql.query`SELECT * FROM ValetQRCodes WHERE QRCodeData = ${fullURL}`;
    console.log('Full URL match:', qrCheck2.recordset.length);
    if (qrCheck2.recordset.length > 0) {
      console.log('Found QR:', qrCheck2.recordset[0]);
    }

    // Check plaza valet points
    console.log('\n=== Plaza Valet Points ===');
    const valetPoints = await sql.query`SELECT TOP 10 * FROM PlazaValetPoint ORDER BY Id DESC`;
    console.log('Valet Points found:', valetPoints.recordset.length);
    valetPoints.recordset.forEach(vp => {
      console.log(`- ID: ${vp.Id}, Name: ${vp.ValetPointName}, Active: ${vp.IsActive}, PlazaId: ${vp.PlazaId}`);
    });

    // Check plazas
    console.log('\n=== Plazas ===');
    const plazas = await sql.query`SELECT TOP 10 Id, PlazaName, IsActive, CompanyId FROM Plaza ORDER BY Id DESC`;
    console.log('Plazas found:', plazas.recordset.length);
    plazas.recordset.forEach(p => {
      console.log(`- ID: ${p.Id}, Name: ${p.PlazaName}, Active: ${p.IsActive}, CompanyId: ${p.CompanyId}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await sql.close();
    console.log('\nDatabase connection closed');
  }
}

checkQRData();
