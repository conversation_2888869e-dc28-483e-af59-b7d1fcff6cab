const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000';
const CREDENTIALS = {
  username: 'superadmin',
  password: 'Admin@123'
};

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function testPermissionManagement() {
  let token = null;
  
  try {
    log('🚀 Starting Permission Management Test Suite', 'blue');
    log('=' .repeat(60), 'blue');

    // Test 1: Server Health Check
    log('\n1. Testing Server Health...', 'yellow');
    try {
      const healthResponse = await axios.get(`${BASE_URL}/health-check`);
      log(`✅ Server is healthy: ${healthResponse.data.message}`, 'green');
      log(`   Database: ${healthResponse.data.database.name}`, 'green');
      log(`   Redis: ${healthResponse.data.redis.status}`, 'green');
    } catch (error) {
      log(`❌ Server health check failed: ${error.message}`, 'red');
      return;
    }

    // Test 2: Authentication
    log('\n2. Testing Authentication...', 'yellow');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, CREDENTIALS);
      if (loginResponse.data.success && loginResponse.data.data.token) {
        token = loginResponse.data.data.token;
        log(`✅ Login successful`, 'green');
        log(`   User: ${loginResponse.data.data.user.username}`, 'green');
        log(`   Role: ${loginResponse.data.data.user.role}`, 'green');
        log(`   Token: ${token.substring(0, 20)}...`, 'green');
      } else {
        log(`❌ Login failed: Invalid response structure`, 'red');
        return;
      }
    } catch (error) {
      log(`❌ Login failed: ${error.response?.data?.message || error.message}`, 'red');
      return;
    }

    // Test 3: Permission Management Routes (without auth)
    log('\n3. Testing Permission Management Routes (No Auth)...', 'yellow');
    
    // Test basic route
    try {
      const testResponse = await axios.get(`${BASE_URL}/api/permission-management/test`);
      log(`✅ Test route: ${testResponse.data.message}`, 'green');
    } catch (error) {
      log(`❌ Test route failed: ${error.response?.data?.message || error.message}`, 'red');
    }

    // Test debug route
    try {
      const debugResponse = await axios.get(`${BASE_URL}/api/permission-management/debug`);
      log(`✅ Debug route: ${debugResponse.data.message}`, 'green');
      log(`   Active Modules: ${debugResponse.data.activeModules}`, 'green');
    } catch (error) {
      log(`❌ Debug route failed: ${error.response?.data?.message || error.message}`, 'red');
    }

    // Test 4: Permission Management Routes (with auth)
    log('\n4. Testing Permission Management Routes (With Auth)...', 'yellow');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test modules tree
    try {
      log('   Testing modules-tree endpoint...', 'blue');
      const modulesResponse = await axios.get(`${BASE_URL}/api/permission-management/modules-tree`, { headers });
      
      if (modulesResponse.data.success) {
        log(`✅ Modules tree retrieved successfully`, 'green');
        log(`   Modules count: ${modulesResponse.data.data.length}`, 'green');
        
        if (modulesResponse.data.data.length > 0) {
          log(`   First module: ${modulesResponse.data.data[0].name}`, 'green');
          log(`   Submodules: ${modulesResponse.data.data[0].subModules.length}`, 'green');
          
          // Show first few modules
          log('\n   📋 Modules Summary:', 'blue');
          modulesResponse.data.data.slice(0, 3).forEach(module => {
            log(`     - ${module.name}: ${module.subModules.length} submodules`, 'green');
            module.subModules.slice(0, 2).forEach(subModule => {
              log(`       └─ ${subModule.name}: ${subModule.permissions.length} permissions`, 'green');
            });
          });
        } else {
          log(`❌ No modules returned (this is the main issue!)`, 'red');
        }
      } else {
        log(`❌ Modules tree failed: ${modulesResponse.data.message}`, 'red');
      }
    } catch (error) {
      log(`❌ Modules tree failed: ${error.response?.data?.message || error.message}`, 'red');
      if (error.response?.status === 403) {
        log(`   This might be an authorization issue`, 'yellow');
      }
    }

    // Test roles
    try {
      log('\n   Testing roles endpoint...', 'blue');
      const rolesResponse = await axios.get(`${BASE_URL}/api/permission-management/roles`, { headers });
      
      if (rolesResponse.data.success) {
        log(`✅ Roles retrieved successfully`, 'green');
        log(`   Roles count: ${rolesResponse.data.data.length}`, 'green');
        
        if (rolesResponse.data.data.length > 0) {
          log('\n   👥 Roles:', 'blue');
          rolesResponse.data.data.forEach(role => {
            log(`     - ${role.Name} (ID: ${role.Id})`, 'green');
          });
        }
      } else {
        log(`❌ Roles failed: ${rolesResponse.data.message}`, 'red');
      }
    } catch (error) {
      log(`❌ Roles failed: ${error.response?.data?.message || error.message}`, 'red');
    }

    // Test permission matrix
    try {
      log('\n   Testing permission matrix endpoint...', 'blue');
      const matrixResponse = await axios.get(`${BASE_URL}/api/permission-management/matrix`, { headers });
      
      if (matrixResponse.data.success) {
        log(`✅ Permission matrix retrieved successfully`, 'green');
        log(`   Matrix entries: ${matrixResponse.data.data.length}`, 'green');
      } else {
        log(`❌ Permission matrix failed: ${matrixResponse.data.message}`, 'red');
      }
    } catch (error) {
      log(`❌ Permission matrix failed: ${error.response?.data?.message || error.message}`, 'red');
    }

    // Test 5: Frontend Access Test
    log('\n5. Testing Frontend Access...', 'yellow');
    try {
      // Test if we can access the frontend permission management page
      const frontendResponse = await axios.get(`${BASE_URL}/dashboard/manage-permissions`, {
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
      });
      
      if (frontendResponse.status === 200) {
        log(`✅ Frontend route accessible`, 'green');
      }
    } catch (error) {
      if (error.response?.status === 404) {
        log(`❌ Frontend route not found (404)`, 'red');
      } else {
        log(`⚠️  Frontend test inconclusive: ${error.message}`, 'yellow');
      }
    }

    // Test 6: Direct Controller Test
    log('\n6. Testing Direct Controller Logic...', 'yellow');
    try {
      // This would require running the controller logic directly
      log(`ℹ️  Direct controller test would require server restart to see debug logs`, 'blue');
      log(`   Check the backend console for debug messages from the controller`, 'blue');
    } catch (error) {
      log(`❌ Direct controller test failed: ${error.message}`, 'red');
    }

    // Summary
    log('\n' + '=' .repeat(60), 'blue');
    log('📊 TEST SUMMARY', 'blue');
    log('=' .repeat(60), 'blue');
    
    log('\n🔍 DIAGNOSIS:', 'yellow');
    log('Based on the test results, the issue appears to be:', 'yellow');
    log('1. ✅ Server is running and healthy', 'green');
    log('2. ✅ Authentication works correctly', 'green');
    log('3. ✅ Routes are accessible', 'green');
    log('4. ❌ Controller returns empty data (main issue)', 'red');
    
    log('\n💡 POSSIBLE CAUSES:', 'yellow');
    log('• Controller changes not loaded (server restart needed)', 'yellow');
    log('• Database query issue (check IsActive values)', 'yellow');
    log('• Data transformation logic error', 'yellow');
    log('• Authentication middleware interfering', 'yellow');
    
    log('\n🔧 RECOMMENDED FIXES:', 'yellow');
    log('1. Restart the backend server completely', 'yellow');
    log('2. Check backend console logs for errors', 'yellow');
    log('3. Verify database IsActive column values', 'yellow');
    log('4. Test controller logic in isolation', 'yellow');

  } catch (error) {
    log(`❌ Test suite failed: ${error.message}`, 'red');
  }
}

// Run the test
if (require.main === module) {
  testPermissionManagement().then(() => {
    log('\n🏁 Test suite completed', 'blue');
    process.exit(0);
  }).catch(error => {
    log(`❌ Test suite crashed: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { testPermissionManagement };