// backend/src/getSampleData.js
require('dotenv').config();
const db = require('./config/database');

async function getSampleData(tableName, limit = 5) {
  try {
    // Query to get sample data
    const query = `
      SELECT TOP ${limit} * 
      FROM ${tableName}
      ORDER BY PakringDataID DESC;
    `;
    
    const result = await db.query(query);
    
    console.log(`Sample data from ${tableName} (${limit} rows):`);
    console.log('======================');
    
    if (result.recordset.length === 0) {
      console.log(`No data found in table ${tableName}.`);
    } else {
      // Print the data in a readable format
      result.recordset.forEach((row, index) => {
        console.log(`\nRow ${index + 1}:`);
        Object.entries(row).forEach(([key, value]) => {
          // Format the value for better readability
          let displayValue = value;
          if (value instanceof Date) {
            displayValue = value.toISOString();
          } else if (value === null) {
            displayValue = 'NULL';
          }
          console.log(`  ${key}: ${displayValue}`);
        });
      });
      
      console.log(`\nTotal rows retrieved: ${result.recordset.length}`);
    }
    
    // Close the connection
    await db.closePool();
  } catch (error) {
    console.error('Error getting sample data:', error.message);
  }
}

// Execute the function with the table name
getSampleData('dbo.tblParkwiz_Parking_Data');