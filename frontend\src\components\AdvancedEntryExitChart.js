import React, { useEffect, useRef, useState } from "react";
import * as d3 from "d3";
import { useTheme } from "../contexts/themeContext";

/**
 * AdvancedEntryExitChart Component
 * 
 * A sophisticated D3.js chart component for displaying hourly entry/exit flow analysis
 * with interactive features, animations, and dual y-axes for vehicle count and net flow.
 * 
 * Features:
 * - Interactive line/area charts with hover effects
 * - Animated transitions and gradient fills
 * - Dual y-axes for entries/exits and net flow
 * - Peak indicators and detailed insights
 * - Theme support and responsive design
 */
export function AdvancedEntryExitChart({
  data = [],
  title = "Hourly Entry/Exit Flow Analysis",
  height = 400,
  showAnimation = true
}) {
  const svgRef = useRef(null);
  const [hoveredData, setHoveredData] = useState(null);
  const { theme } = useTheme();

  // Use provided data from API
  const chartData = data || [];

  useEffect(() => {
    if (!svgRef.current || !chartData || chartData.length === 0) {
      // Clear any existing chart if no data
      if (svgRef.current) {
        const svg = d3.select(svgRef.current);
        svg.selectAll("*").remove();
      }
      return;
    }

    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    // Get container dimensions
    const containerWidth = svgRef.current.clientWidth;
    const margin = { top: 40, right: 80, bottom: 60, left: 60 };
    const width = containerWidth - margin.left - margin.right;
    const chartHeight = height - margin.top - margin.bottom;

    const g = svg.append("g").attr("transform", `translate(${margin.left},${margin.top})`);

    // Theme colors
    const getThemeColors = () => {
      if (theme === 'dark') {
        return {
          background: '#1f2937',
          text: '#9ca3af',
          grid: '#374151',
          entry: '#10b981',
          exit: '#f59e0b',
          net: '#8b5cf6'
        };
      } else if (theme === 'saffron') {
        return {
          background: '#fef3c7',
          text: '#92400e',
          grid: '#fbbf24',
          entry: '#059669',
          exit: '#dc2626',
          net: '#7c3aed'
        };
      } else {
        return {
          background: '#ffffff',
          text: '#6b7280',
          grid: '#e5e7eb',
          entry: '#10b981',
          exit: '#f59e0b',
          net: '#8b5cf6'
        };
      }
    };

    const colors = getThemeColors();

    // Create gradients
    const defs = svg.append("defs");

    // Entry gradient (green)
    const entryGradient = defs
      .append("linearGradient")
      .attr("id", "entryGradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0)
      .attr("y1", chartHeight)
      .attr("x2", 0)
      .attr("y2", 0);

    entryGradient.append("stop").attr("offset", "0%").attr("stop-color", colors.entry).attr("stop-opacity", 0.1);
    entryGradient.append("stop").attr("offset", "100%").attr("stop-color", colors.entry).attr("stop-opacity", 0.8);

    // Exit gradient (orange)
    const exitGradient = defs
      .append("linearGradient")
      .attr("id", "exitGradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0)
      .attr("y1", chartHeight)
      .attr("x2", 0)
      .attr("y2", 0);

    exitGradient.append("stop").attr("offset", "0%").attr("stop-color", colors.exit).attr("stop-opacity", 0.1);
    exitGradient.append("stop").attr("offset", "100%").attr("stop-color", colors.exit).attr("stop-opacity", 0.8);

    // Scales
    const xScale = d3
      .scaleBand()
      .domain(chartData.map((d) => d.time))
      .range([0, width])
      .padding(0.1);

    const maxValue = d3.max(chartData, (d) => Math.max(d.entries, d.exits)) || 0;
    const yScale = d3
      .scaleLinear()
      .domain([0, maxValue * 1.1])
      .range([chartHeight, 0]);

    const netScale = d3
      .scaleLinear()
      .domain(d3.extent(chartData, (d) => d.net))
      .range([chartHeight, 0]);

    // Create axes
    const xAxis = d3.axisBottom(xScale);
    const yAxis = d3.axisLeft(yScale);
    const netAxis = d3.axisRight(netScale);

    // Add grid lines
    g.append("g")
      .attr("class", "grid")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(d3.axisBottom(xScale).tickSize(-chartHeight).tickFormat(""))
      .selectAll("line")
      .style("stroke", colors.grid)
      .style("stroke-opacity", 0.3);

    g.append("g")
      .attr("class", "grid")
      .call(d3.axisLeft(yScale).tickSize(-width).tickFormat(""))
      .selectAll("line")
      .style("stroke", colors.grid)
      .style("stroke-opacity", 0.3);

    // Add axes
    g.append("g")
      .attr("class", "x-axis")
      .attr("transform", `translate(0,${chartHeight})`)
      .call(xAxis)
      .selectAll("text")
      .style("fill", colors.text)
      .style("font-size", "12px");

    g.append("g")
      .attr("class", "y-axis")
      .call(yAxis)
      .selectAll("text")
      .style("fill", colors.text)
      .style("font-size", "12px");

    g.append("g")
      .attr("class", "net-axis")
      .attr("transform", `translate(${width},0)`)
      .call(netAxis)
      .selectAll("text")
      .style("fill", colors.text)
      .style("font-size", "12px");

    // Add axis labels
    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", 0 - margin.left)
      .attr("x", 0 - chartHeight / 2)
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .style("fill", colors.text)
      .style("font-size", "14px")
      .text("Vehicle Count");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("y", width + margin.right - 10)
      .attr("x", 0 - chartHeight / 2)
      .attr("dy", "1em")
      .style("text-anchor", "middle")
      .style("fill", colors.text)
      .style("font-size", "14px")
      .text("Net Flow");

    // Create line generators
    const entryLine = d3
      .line()
      .x((d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .y((d) => yScale(d.entries))
      .curve(d3.curveCardinal);

    const exitLine = d3
      .line()
      .x((d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .y((d) => yScale(d.exits))
      .curve(d3.curveCardinal);

    const netLine = d3
      .line()
      .x((d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .y((d) => netScale(d.net))
      .curve(d3.curveCardinal);

    // Create area generators for fill
    const entryArea = d3
      .area()
      .x((d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .y0(chartHeight)
      .y1((d) => yScale(d.entries))
      .curve(d3.curveCardinal);

    const exitArea = d3
      .area()
      .x((d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .y0(chartHeight)
      .y1((d) => yScale(d.exits))
      .curve(d3.curveCardinal);

    // Add areas with animation
    if (showAnimation) {
      g.append("path")
        .datum(chartData)
        .attr("class", "entry-area")
        .attr("fill", "url(#entryGradient)")
        .attr("d", entryArea)
        .style("opacity", 0)
        .transition()
        .duration(1000)
        .style("opacity", 0.6);

      g.append("path")
        .datum(chartData)
        .attr("class", "exit-area")
        .attr("fill", "url(#exitGradient)")
        .attr("d", exitArea)
        .style("opacity", 0)
        .transition()
        .duration(1000)
        .delay(200)
        .style("opacity", 0.6);
    } else {
      g.append("path")
        .datum(chartData)
        .attr("class", "entry-area")
        .attr("fill", "url(#entryGradient)")
        .attr("d", entryArea)
        .style("opacity", 0.6);

      g.append("path")
        .datum(chartData)
        .attr("class", "exit-area")
        .attr("fill", "url(#exitGradient)")
        .attr("d", exitArea)
        .style("opacity", 0.6);
    }

    // Add lines with animation
    const entryPath = g
      .append("path")
      .datum(chartData)
      .attr("class", "entry-line")
      .attr("fill", "none")
      .attr("stroke", colors.entry)
      .attr("stroke-width", 3)
      .attr("d", entryLine);

    const exitPath = g
      .append("path")
      .datum(chartData)
      .attr("class", "exit-line")
      .attr("fill", "none")
      .attr("stroke", colors.exit)
      .attr("stroke-width", 3)
      .attr("d", exitLine);

    const netPath = g
      .append("path")
      .datum(chartData)
      .attr("class", "net-line")
      .attr("fill", "none")
      .attr("stroke", colors.net)
      .attr("stroke-width", 2)
      .attr("stroke-dasharray", "5,5")
      .attr("d", netLine);

    // Animate lines if animation is enabled
    if (showAnimation) {
      const totalEntryLength = entryPath.node()?.getTotalLength() || 0;
      const totalExitLength = exitPath.node()?.getTotalLength() || 0;
      const totalNetLength = netPath.node()?.getTotalLength() || 0;

      entryPath
        .attr("stroke-dasharray", `${totalEntryLength} ${totalEntryLength}`)
        .attr("stroke-dashoffset", totalEntryLength)
        .transition()
        .duration(1500)
        .ease(d3.easeLinear)
        .attr("stroke-dashoffset", 0);

      exitPath
        .attr("stroke-dasharray", `${totalExitLength} ${totalExitLength}`)
        .attr("stroke-dashoffset", totalExitLength)
        .transition()
        .duration(1500)
        .delay(300)
        .ease(d3.easeLinear)
        .attr("stroke-dashoffset", 0);

      netPath
        .attr("stroke-dasharray", `${totalNetLength} ${totalNetLength}`)
        .attr("stroke-dashoffset", totalNetLength)
        .transition()
        .duration(1500)
        .delay(600)
        .ease(d3.easeLinear)
        .attr("stroke-dashoffset", 0);
    }

    // Add interactive dots
    const entryDots = g
      .selectAll(".entry-dot")
      .data(chartData)
      .enter()
      .append("circle")
      .attr("class", "entry-dot")
      .attr("cx", (d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .attr("cy", (d) => yScale(d.entries))
      .attr("r", showAnimation ? 0 : 4)
      .attr("fill", colors.entry)
      .attr("stroke", "#ffffff")
      .attr("stroke-width", 2)
      .style("cursor", "pointer");

    const exitDots = g
      .selectAll(".exit-dot")
      .data(chartData)
      .enter()
      .append("circle")
      .attr("class", "exit-dot")
      .attr("cx", (d) => (xScale(d.time) || 0) + xScale.bandwidth() / 2)
      .attr("cy", (d) => yScale(d.exits))
      .attr("r", showAnimation ? 0 : 4)
      .attr("fill", colors.exit)
      .attr("stroke", "#ffffff")
      .attr("stroke-width", 2)
      .style("cursor", "pointer");

    // Animate dots if animation is enabled
    if (showAnimation) {
      entryDots
        .transition()
        .duration(800)
        .delay((_, i) => i * 50)
        .attr("r", 4);

      exitDots
        .transition()
        .duration(800)
        .delay((_, i) => i * 50 + 200)
        .attr("r", 4);
    }

    // Add hover effects
    const handleMouseOver = (event, d) => {
      setHoveredData(d);

      // Highlight dots
      d3.selectAll(".entry-dot, .exit-dot")
        .transition()
        .duration(200)
        .attr("r", (dotData) => (dotData.hour === d.hour ? 6 : 4))
        .attr("stroke-width", (dotData) => (dotData.hour === d.hour ? 3 : 2));
    };

    const handleMouseOut = () => {
      setHoveredData(null);

      // Reset dots
      d3.selectAll(".entry-dot, .exit-dot")
        .transition()
        .duration(200)
        .attr("r", 4)
        .attr("stroke-width", 2);
    };

    // Add event listeners
    entryDots.on("mouseover", handleMouseOver).on("mouseout", handleMouseOut);
    exitDots.on("mouseover", handleMouseOver).on("mouseout", handleMouseOut);

    // Add peak indicators
    const peakEntry = chartData.reduce((prev, current) => (prev.entries > current.entries ? prev : current));
    const peakExit = chartData.reduce((prev, current) => (prev.exits > current.exits ? prev : current));

    // Peak entry indicator
    g.append("text")
      .attr("x", (xScale(peakEntry.time) || 0) + xScale.bandwidth() / 2)
      .attr("y", yScale(peakEntry.entries) - 15)
      .attr("text-anchor", "middle")
      .style("fill", colors.entry)
      .style("font-size", "16px")
      .text("🔥")
      .style("opacity", showAnimation ? 0 : 1);

    // Peak exit indicator
    g.append("text")
      .attr("x", (xScale(peakExit.time) || 0) + xScale.bandwidth() / 2)
      .attr("y", yScale(peakExit.exits) - 15)
      .attr("text-anchor", "middle")
      .style("fill", colors.exit)
      .style("font-size", "16px")
      .text("🔥")
      .style("opacity", showAnimation ? 0 : 1);

    // Animate peak indicators if animation is enabled
    if (showAnimation) {
      g.selectAll("text:last-of-type")
        .transition()
        .duration(800)
        .delay(2000)
        .style("opacity", 1);
    }

    // Add legend
    const legend = g
      .append("g")
      .attr("class", "legend")
      .attr("transform", `translate(${width - 150}, 20)`);

    const legendData = [
      { label: "Entries", color: colors.entry },
      { label: "Exits", color: colors.exit },
      { label: "Net Flow", color: colors.net },
    ];

    const legendItems = legend
      .selectAll(".legend-item")
      .data(legendData)
      .enter()
      .append("g")
      .attr("class", "legend-item")
      .attr("transform", (d, i) => `translate(0, ${i * 20})`);

    legendItems
      .append("line")
      .attr("x1", 0)
      .attr("x2", 15)
      .attr("y1", 0)
      .attr("y2", 0)
      .attr("stroke", (d) => d.color)
      .attr("stroke-width", 3)
      .attr("stroke-dasharray", (d) => (d.label === "Net Flow" ? "5,5" : "none"));

    legendItems
      .append("text")
      .attr("x", 20)
      .attr("y", 0)
      .attr("dy", "0.35em")
      .style("fill", colors.text)
      .style("font-size", "12px")
      .text((d) => d.label);

  }, [chartData, theme, height, showAnimation]);

  // Calculate insights - handle empty data
  const peakEntry = chartData.length > 0 ? chartData.reduce((prev, current) => (prev.entries > current.entries ? prev : current)) : { time: "N/A", entries: 0 };
  const peakExit = chartData.length > 0 ? chartData.reduce((prev, current) => (prev.exits > current.exits ? prev : current)) : { time: "N/A", exits: 0 };
  const positiveNetFlowHours = chartData.filter(d => d.net > 0);

  return (
    <div className="bg-white rounded-lg shadow-lg p-4 md:p-6 border border-amber-100">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2 sm:mb-0">
          {title}
        </h3>
        {hoveredData && (
          <div className="text-sm font-normal text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 px-3 py-1 rounded-lg">
            <span className="font-medium">{hoveredData.time}:</span> {hoveredData.entries} entries, {hoveredData.exits} exits
            <span className={`ml-2 font-semibold ${hoveredData.net >= 0 ? "text-green-600" : "text-red-600"}`}>
              (Net: {hoveredData.net >= 0 ? "+" : ""}{hoveredData.net})
            </span>
          </div>
        )}
      </div>

      {chartData.length === 0 ? (
        <div className="w-full bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg p-8 text-center">
          <div className="text-gray-500 dark:text-gray-400">
            <div className="text-4xl mb-4">📊</div>
            <div className="text-lg font-medium mb-2">No Entry/Exit Data Available</div>
            <div className="text-sm">Data will appear here when vehicle transactions are recorded</div>
          </div>
        </div>
      ) : (
        <div className="w-full overflow-x-auto bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg p-2">
          <svg
            ref={svgRef}
            width="100%"
            height={height}
            className="w-full"
            style={{ minWidth: "800px" }}
          />
        </div>
      )}

      {/* Insights Panel */}
      <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="font-medium text-green-700 dark:text-green-400">Peak Entry</span>
          </div>
          <div className="text-green-600 dark:text-green-300 font-bold">
            {peakEntry.time} - {peakEntry.entries} vehicles
          </div>
        </div>

        <div className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-lg border border-orange-200 dark:border-orange-800">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span className="font-medium text-orange-700 dark:text-orange-400">Peak Exit</span>
          </div>
          <div className="text-orange-600 dark:text-orange-300 font-bold">
            {peakExit.time} - {peakExit.exits} vehicles
          </div>
        </div>

        <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span className="font-medium text-purple-700 dark:text-purple-400">Net Flow</span>
          </div>
          <div className="text-purple-600 dark:text-purple-300 font-bold">
            Positive: {positiveNetFlowHours.length} hours
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span><strong>Entry Flow:</strong> Vehicle arrival patterns</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span><strong>Exit Flow:</strong> Vehicle departure patterns</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span><strong>Net Flow:</strong> Occupancy change analysis</span>
          </div>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          💡 <strong>Tip:</strong> Hover over data points for detailed information • 🔥 indicates peak hours
        </div>
      </div>
    </div>
  );
}
