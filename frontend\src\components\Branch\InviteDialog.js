import React from 'react';
import { useForm } from 'react-hook-form';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

export default function InviteDialog({
  isOpen,
  onClose,
  onSubmit,
  branchName,
}) {
  const { register, handleSubmit, formState: { errors } } = useForm();

  if (!isOpen) return null;

  const onFormSubmit = async (data) => {
    try {
      if (onSubmit) {
        // Use the parent-provided callback to handle invitation logic
        await onSubmit({
          email: data.email,
          branchName,
        });

        toast.success('Invitation sent successfully!');
        onClose(); // Close the dialog on success
      } else {
        toast.error('onSubmit callback is not provided');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to send invitation');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>

        {/* Dialog Title */}
        <h2 className="text-2xl font-bold mb-4 text-[#000000]">
          Invite Branch Head
        </h2>

        <p className="text-[#727272] mb-4">
          Send an invitation to manage {branchName}
        </p>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Email Field */}
          <div>
            <label className="block text-sm font-medium text-[#727272] mb-1">
              Email Address
            </label>
            <input
              type="email"
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address',
                },
              })}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
            />
            {errors.email && (
              <span className="text-red-500 text-sm">{errors.email.message}</span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-[#727272] hover:text-[#000000] transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#9AC8CE] text-white rounded hover:bg-[#7BA9AF] transition-colors focus:ring-2 focus:ring-offset-2 focus:ring-[#9AC8CE]"
            >
              Send Invitation
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
