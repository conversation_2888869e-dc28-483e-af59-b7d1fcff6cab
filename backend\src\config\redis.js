// backend/src/config/redis.js
const Redis = require('ioredis');
const dotenv = require('dotenv');

dotenv.config();

/**
 * ===============================================================================
 * # Redis Configuration
 * ===============================================================================
 * 
 * Centralized Redis configuration with connection pooling, retry logic,
 * and environment-based settings for PWVMS caching layer.
 */

// Redis configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB || '0'),
  
  // Connection settings
  connectTimeout: 10000,
  lazyConnect: true,
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxLoadingTimeout: 5000,
  
  // Retry strategy
  retryStrategy: (times) => {
    const delay = Math.min(times * 50, 2000);
    console.log(`Redis retry attempt ${times}, delay: ${delay}ms`);
    return delay;
  },
  
  // Connection pool settings
  family: 4,
  keepAlive: true,
  
  // Key prefix for PWVMS
  keyPrefix: 'pwvms:',
};

// Create Redis instances
let redisClient = null;
let redisSubscriber = null;
let redisPublisher = null;

/**
 * Initialize Redis connections
 */
const initializeRedis = async () => {
  try {
    // Main Redis client for general operations
    redisClient = new Redis(redisConfig);
    
    // Separate clients for pub/sub operations
    redisSubscriber = new Redis({
      ...redisConfig,
      keyPrefix: '', // No prefix for pub/sub
    });
    
    redisPublisher = new Redis({
      ...redisConfig,
      keyPrefix: '', // No prefix for pub/sub
    });

    // Event handlers for main client
    redisClient.on('connect', () => {
      console.log('✅ Redis client connected successfully');
    });

    redisClient.on('ready', () => {
      console.log('✅ Redis client ready for operations');
    });

    redisClient.on('error', (error) => {
      console.error('❌ Redis client error:', error.message);
    });

    redisClient.on('close', () => {
      console.log('⚠️ Redis client connection closed');
    });

    // Test connection
    await redisClient.ping();
    console.log('🚀 Redis connection established successfully');

    return { redisClient, redisSubscriber, redisPublisher };
  } catch (error) {
    console.error('❌ Failed to initialize Redis:', error.message);
    throw error;
  }
};

/**
 * Get Redis client instance
 */
const getRedisClient = () => {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call initializeRedis() first.');
  }
  return redisClient;
};

/**
 * Get Redis subscriber instance
 */
const getRedisSubscriber = () => {
  if (!redisSubscriber) {
    throw new Error('Redis subscriber not initialized. Call initializeRedis() first.');
  }
  return redisSubscriber;
};

/**
 * Get Redis publisher instance
 */
const getRedisPublisher = () => {
  if (!redisPublisher) {
    throw new Error('Redis publisher not initialized. Call initializeRedis() first.');
  }
  return redisPublisher;
};

/**
 * Close all Redis connections
 */
const closeRedisConnections = async () => {
  try {
    const promises = [];
    
    if (redisClient) {
      promises.push(redisClient.quit());
    }
    
    if (redisSubscriber) {
      promises.push(redisSubscriber.quit());
    }
    
    if (redisPublisher) {
      promises.push(redisPublisher.quit());
    }
    
    await Promise.all(promises);
    console.log('✅ All Redis connections closed successfully');
  } catch (error) {
    console.error('❌ Error closing Redis connections:', error.message);
    throw error;
  }
};

/**
 * Redis key generators for consistent naming
 */
const RedisKeys = {
  // Dashboard cache keys
  DASHBOARD_SUMMARY: (userId, role, filters) => 
    `dashboard:summary:${role}:${userId}:${JSON.stringify(filters)}`,
  
  DASHBOARD_REVENUE_BY_PAYMENT: (userId, role, filters) => 
    `dashboard:revenue:payment:${role}:${userId}:${JSON.stringify(filters)}`,
  
  DASHBOARD_PEAK_HOURS: (userId, role, filters) => 
    `dashboard:peak:${role}:${userId}:${JSON.stringify(filters)}`,
  
  DASHBOARD_RECENT_TRANSACTIONS: (userId, role, filters) => 
    `dashboard:transactions:${role}:${userId}:${JSON.stringify(filters)}`,
  
  // User session keys
  USER_SESSION: (userId) => `session:user:${userId}`,
  USER_PERMISSIONS: (userId) => `permissions:user:${userId}`,
  USER_COMPANIES: (userId) => `companies:user:${userId}`,
  USER_PLAZAS: (userId) => `plazas:user:${userId}`,
  
  // Real-time data keys
  LIVE_PARKING_DATA: (plazaCode) => `live:parking:${plazaCode}`,
  LANE_STATUS: (plazaId) => `lane:status:${plazaId}`,
  PLAZA_OCCUPANCY: (plazaCode) => `occupancy:${plazaCode}`,
  
  // Rate limiting keys
  RATE_LIMIT: (ip, endpoint) => `ratelimit:${ip}:${endpoint}`,
  
  // Notification keys
  USER_NOTIFICATIONS: (userId) => `notifications:${userId}`,
  SYSTEM_ALERTS: () => 'alerts:system',
  
  // Cache invalidation keys
  CACHE_TAGS: {
    DASHBOARD: 'tag:dashboard',
    USER_DATA: 'tag:userdata',
    PARKING_DATA: 'tag:parkingdata',
    SYSTEM_CONFIG: 'tag:systemconfig',
  }
};

/**
 * Cache TTL constants (in seconds)
 */
const CacheTTL = {
  DASHBOARD_SUMMARY: 300,      // 5 minutes
  DASHBOARD_CHARTS: 600,       // 10 minutes
  USER_SESSION: 3600,          // 1 hour
  USER_PERMISSIONS: 1800,      // 30 minutes
  LIVE_DATA: 30,               // 30 seconds
  STATIC_DATA: 86400,          // 24 hours
  RATE_LIMIT: 3600,            // 1 hour
  NOTIFICATIONS: 7200,         // 2 hours
};

module.exports = {
  initializeRedis,
  getRedisClient,
  getRedisSubscriber,
  getRedisPublisher,
  closeRedisConnections,
  RedisKeys,
  CacheTTL,
  redisConfig
};