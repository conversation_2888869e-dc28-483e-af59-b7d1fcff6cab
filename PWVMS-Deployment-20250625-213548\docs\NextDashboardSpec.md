# Next.js Advanced Dashboard Project Specification for ParkWiz

## Project Overview

Create a high-performance, visually stunning analytics dashboard using Next.js 14+ with App Router. This dashboard will visualize parking management data from the ParkWiz system with real-time updates, interactive charts, and predictive analytics. The dashboard should handle large datasets efficiently and provide an exceptional user experience.

## Technology Stack

- **Framework**: Next.js 14+ with App Router
- **UI Library**: Material UI v5 or Shadcn UI
- **State Management**: React Query / TanStack Query for server state
- **Visualization Libraries**:
  - Recharts for standard charts
  - Nivo for advanced visualizations
  - Apache ECharts (via echarts-for-react) for high-performance large datasets
  - react-table v7 or TanStack Table for data tables
- **Authentication**: JWT token-based (compatible with existing backend)
- **Styling**: Tailwind CSS with custom theme
- **Data Fetching**: SWR or React Query with optimistic updates
- **TypeScript**: Strict mode for type safety

## Project Structure

```
parkwiz-dashboard/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   │   └── page.tsx
│   │   └── layout.tsx
│   ├── dashboard/
│   │   ├── bookings/
│   │   │   └── page.tsx
│   │   ├── occupancy/
│   │   │   └── page.tsx
│   │   ├── revenue/
│   │   │   └── page.tsx
│   │   ├── predictions/
│   │   │   └── page.tsx
│   │   ├── settings/
│   │   │   └── page.tsx
│   │   └── page.tsx
│   ├── api/
│   │   └── proxy/
│   │       └── [...path]/
│   │           └── route.ts
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   └── AuthGuard.tsx
│   ├── dashboard/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── DashboardLayout.tsx
│   ├── charts/
│   │   ├── RevenueChart.tsx
│   │   ├── OccupancyChart.tsx
│   │   ├── BookingTrendsChart.tsx
│   │   ├── HeatmapChart.tsx
│   │   ├── PredictiveChart.tsx
│   │   └── GaugeChart.tsx
│   ├── tables/
│   │   ├── BookingsTable.tsx
│   │   ├── RevenueTable.tsx
│   │   ├── OccupancyTable.tsx
│   │   └── TableWrapper.tsx
│   ├── ui/
│   │   ├── MetricCard.tsx
│   │   ├── DateRangePicker.tsx
│   │   ├── FilterBar.tsx
│   │   ├── ExportButton.tsx
│   │   ├── ThemeToggle.tsx
│   │   └── LoadingState.tsx
│   └── maps/
│       ├── ParkingMap.tsx
│       └── HeatMap.tsx
├── lib/
│   ├── api/
│   │   ├── client.ts
│   │   ├── endpoints.ts
│   │   └── hooks/
│   │       ├── useBookings.ts
│   │       ├── useRevenue.ts
│   │       ├── useOccupancy.ts
│   │       └── usePredictions.ts
│   ├── auth/
│   │   ├── authContext.tsx
│   │   └── authUtils.ts
│   ├── utils/
│   │   ├── dateUtils.ts
│   │   ├── formatters.ts
│   │   ├── dataTransformers.ts
│   │   └── exportUtils.ts
│   └── types/
│       ├── booking.types.ts
│       ├── revenue.types.ts
│       ├── occupancy.types.ts
│       └── user.types.ts
├── public/
│   ├── images/
│   ├── icons/
│   └── favicon.ico
├── styles/
│   ├── globals.css
│   └── theme.ts
├── middleware.ts
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
└── package.json
```

## Key Features & Requirements

### 1. Dashboard Overview Page

Create a comprehensive dashboard overview with:

- **Summary Metrics Cards**:
  - Total Revenue: Current period vs previous period
  - Total Transactions: Count with trend indicator
  - Vehicle Count: Number of unique vehicles with trend indicator
  - Average Duration: Parking duration with trend indicator

- **Revenue Chart**:
  - Line chart showing daily/weekly/monthly revenue
  - Ability to toggle between time periods
  - Comparison with previous period
  - Annotations for special events/holidays

- **Occupancy Visualization**:
  - Heat map showing parking lot occupancy by time of day
  - Color gradient indicating occupancy levels
  - Drill-down capability to view specific lots

- **Recent Transactions Table**:
  - Sortable, filterable table with pagination
  - Columns: Ticket No, Plaza Name, Vehicle Number, Entry Time, Exit Time, Duration, Amount, Payment Mode
  - Quick actions: View details, Export

- **Peak Hours Analysis**:
  - Bar chart showing transaction volume by hour of day
  - Highlight peak hours
  - Comparison with previous periods

### 2. Detailed Revenue Analysis Page

- **Revenue Breakdown**:
  - Bar chart showing revenue by plaza
  - Pie chart showing revenue by payment method (CASH, UPI, etc.)
  - Line chart showing revenue trends over time

- **Revenue Metrics**:
  - Average revenue per transaction
  - Revenue by time of day (morning, afternoon, evening, night)
  - Revenue by day of week
  - Top performing plazas

- **Revenue Table**:
  - Detailed table with all revenue transactions
  - Advanced filtering and sorting
  - Export to CSV/Excel functionality
  - Columns: Plaza Name, Date, Transaction Count, Total Revenue, Avg Revenue/Transaction, Payment Method

### 3. Occupancy Analysis Page

- **Occupancy Heatmap**:
  - Interactive heatmap showing occupancy by hour and day
  - Ability to filter by plaza
  - Color scale indicating occupancy percentage

- **Occupancy Trends**:
  - Line chart showing occupancy trends over time
  - Comparison with historical data
  - Peak hours identification

- **Plaza Map**:
  - Visual map of plazas with real-time occupancy
  - Color-coded indicators
  - Click to drill down into specific plaza details

- **Occupancy Metrics**:
  - Average occupancy duration
  - Turn-over rate
  - Peak occupancy times
  - Least utilized times

### 4. Bookings Management Page

- **Bookings Table**:
  - Comprehensive table with all booking details
  - Advanced filtering by date, plaza, vehicle type, payment mode
  - Pagination with selectable page size
  - Columns: Ticket No, Plaza Name, Vehicle Number, Entry Time, Exit Time, Duration, Amount, Payment Mode, Status

- **Booking Trends**:
  - Line chart showing booking volume over time
  - Bar chart showing bookings by plaza
  - Pie chart showing vehicle type distribution (Two Wheeler, Four Wheeler)

- **Vehicle Analysis**:
  - Repeat vehicle analysis
  - Average booking duration by vehicle type
  - Payment method breakdown (CASH, UPI, etc.)

### 5. Predictive Analytics Page

- **Occupancy Prediction**:
  - Line chart with forecasted occupancy for next 30 days
  - Confidence intervals
  - Comparison with historical data

- **Revenue Forecast**:
  - Projected revenue for upcoming periods
  - Seasonal adjustments
  - What-if scenario modeling

- **Anomaly Detection**:
  - Highlighting unusual patterns in bookings or occupancy
  - Alert thresholds configuration
  - Historical anomalies analysis

### 6. Settings & Configuration Page

- **User Preferences**:
  - Theme selection (light/dark)
  - Default dashboard view
  - Date format preferences

- **Notification Settings**:
  - Alert thresholds configuration
  - Email notification preferences
  - Dashboard alert settings

- **Data Export Options**:
  - Scheduled reports configuration
  - Export format preferences
  - Data retention settings

## Data Structure

Based on the existing database table `tblParkwiz_Parking_Data`, the dashboard will work with the following data structure:

### Parking Transaction Data
```typescript
interface ParkingTransaction {
  PakringDataID: number;
  PlazaCode: string;
  PlazaName: string;
  JournalID: number;
  EncodedData: string;
  TicketNo: string;
  TransactionCode: string;
  CardID: string;
  VehicleType: string; // "Two Wheeler", "Four Wheeler"
  TrnsType: string;
  ExitTType: string;
  EntryDateTime: string; // ISO date string
  EntryLane: string;
  ExitDateTime: string; // ISO date string
  ExitLane: string;
  ParkedDuration: number; // in minutes
  iCGSTFee: number;
  iSGSTFee: number;
  iTotalGSTFee: number;
  ParkingFee: number;
  UpdatedBy: string;
  TReportID: number;
  ExitHours: number;
  flgGhostT: string;
  flgAutoPay: string;
  dtExpressExitDateTime: string; // ISO date string
  flgExpressExit: string;
  RapidExitLane: string;
  PaymentType: string; // "OFLN", "ONLN"
  PaymentMode: string; // "CASH", "UPI", etc.
  GatewayProvider: string;
  VehicleNumber: string;
  sPRN: string;
  GuestEmailID: string;
  ExitVehicleNumber: string;
  FasTagVehicleNumber: string;
  GraceExitFromT1: string;
}
```

### Summary Metrics
```typescript
interface DashboardSummary {
  totalRevenue: number;
  transactionCount: number;
  vehicleCount: number;
  avgDuration: number;
  revenueTrend: number; // percentage change
  transactionTrend: number; // percentage change
  vehicleTrend: number; // percentage change
  durationTrend: number; // percentage change
}
```

### Revenue by Payment Method
```typescript
interface PaymentMethodRevenue {
  paymentMode: string;
  totalRevenue: number;
  transactionCount: number;
}
```

### Peak Hours Data
```typescript
interface PeakHourData {
  hour: number; // 0-23
  count: number;
}
```

### Revenue by Plaza
```typescript
interface PlazaRevenue {
  PlazaName: string;
  totalRevenue: number;
  transactionCount: number;
}
```

### Lane Status
```typescript
interface LaneStatus {
  LaneID: number;
  LaneNumber: string;
  LaneType: string;
  ActiveStatus: number;
  Status: string; // "Active" or "Inactive"
  TransactionsLast24Hours: number;
}
```

## API Integration

The dashboard should connect to the existing backend API. Here are the expected endpoints based on the existing DashboardController:

```typescript
// Expected API endpoints
const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  REFRESH_TOKEN: '/auth/refresh',
  
  // Dashboard
  DASHBOARD_SUMMARY: '/dashboard/summary',
  REVENUE_BY_PAYMENT_METHOD: '/dashboard/revenue-by-payment-method',
  RECENT_TRANSACTIONS: '/dashboard/recent-transactions',
  PEAK_HOURS_DATA: '/dashboard/peak-hours',
  REVENUE_BY_PLAZA: '/dashboard/revenue-by-plaza',
  LANE_STATUS: '/dashboard/lane-status',
  
  // Additional endpoints to be implemented on the backend
  TRANSACTIONS: '/transactions',
  TRANSACTION_DETAIL: (id: string) => `/transactions/${id}`,
  OCCUPANCY_CURRENT: '/occupancy/current',
  OCCUPANCY_HISTORY: '/occupancy/history',
  OCCUPANCY_BY_PLAZA: '/occupancy/by-plaza',
  OCCUPANCY_HEATMAP: '/occupancy/heatmap',
  
  // Predictions (to be implemented)
  PREDICT_OCCUPANCY: '/predictions/occupancy',
  PREDICT_REVENUE: '/predictions/revenue',
  ANOMALY_DETECTION: '/predictions/anomalies',
  
  // Settings
  USER_SETTINGS: '/settings/user',
  NOTIFICATION_SETTINGS: '/settings/notifications',
};
```

## Query Parameters

The dashboard should support the following query parameters for API requests:

- **dateRange**: 'today', 'yesterday', 'week', 'month', 'year'
- **companyId**: Filter by company ID
- **plazaId**: Filter by plaza ID
- **laneId**: Filter by lane ID
- **limit**: Limit the number of results (for pagination)
- **page**: Page number (for pagination)
- **sortBy**: Field to sort by
- **sortOrder**: 'asc' or 'desc'
- **vehicleType**: Filter by vehicle type
- **paymentMode**: Filter by payment mode

## Performance Requirements

1. **Large Dataset Handling**:
   - Must efficiently render charts with 10,000+ data points
   - Implement data virtualization for large tables
   - Use windowing techniques for long lists

2. **Rendering Optimization**:
   - Memoize expensive components with React.memo
   - Implement code-splitting for dashboard sections
   - Optimize bundle size with dynamic imports

3. **Data Loading**:
   - Implement staggered loading for dashboard components
   - Show skeleton loaders during data fetching
   - Cache API responses appropriately

4. **Real-time Updates**:
   - Implement efficient polling or WebSocket connections
   - Update only changed data points
   - Batch updates to minimize re-renders

## Visual Design Requirements

1. **Modern UI**:
   - Clean, minimalist design with ample white space
   - Consistent color scheme based on brand colors
   - Responsive design for all screen sizes

2. **Chart Design**:
   - Consistent styling across all visualizations
   - Clear labels and legends
   - Interactive tooltips with detailed information
   - Animations for transitions (but disable for large datasets)

3. **Accessibility**:
   - WCAG 2.1 AA compliance
   - Keyboard navigation support
   - Screen reader compatibility
   - Sufficient color contrast

4. **Responsive Behavior**:
   - Optimized layouts for desktop, tablet, and mobile
   - Collapsible sidebar on smaller screens
   - Stacked charts and tables on mobile
   - Touch-friendly controls

## Authentication & Security

1. **JWT Authentication**:
   - Implement token-based authentication
   - Handle token refresh automatically
   - Secure storage of tokens
   - Automatic logout on token expiration

2. **Role-Based Access**:
   - Support for different user roles (SuperAdmin, CompanyAdmin, PlazaManager)
   - Conditional rendering based on permissions
   - API request authorization

3. **Security Best Practices**:
   - CSRF protection
   - XSS prevention
   - Input validation
   - Secure HTTP headers

## Additional Features

1. **Export Functionality**:
   - Export charts as PNG/PDF
   - Export tables as CSV/Excel
   - Scheduled report generation

2. **Filtering & Customization**:
   - Advanced filtering for all data views
   - Customizable dashboard layouts
   - Savable filter presets

3. **Notifications & Alerts**:
   - Real-time alerts for anomalies
   - Configurable thresholds
   - In-app notification center

4. **Theme Support**:
   - Light and dark mode
   - Custom theme configuration
   - High contrast mode for accessibility

## Deployment Considerations

The dashboard will be deployed separately from the existing React application. It should be:

1. **Standalone Application**:
   - Deployable to Vercel, Netlify, or custom hosting
   - Configurable API base URL for different environments

2. **Integration Options**:
   - Ability to be embedded via iframe in the existing app
   - Direct link from the existing app
   - Shared authentication with the main application

3. **Performance Monitoring**:
   - Integration with analytics tools
   - Error tracking
   - Performance metrics collection

## Development Guidelines

1. **Code Quality**:
   - Follow TypeScript best practices
   - Implement comprehensive unit tests
   - Use ESLint and Prettier for code formatting
   - Document components with JSDoc

2. **State Management**:
   - Use React Query for server state
   - Implement context for global UI state
   - Keep component state local when possible

3. **Component Structure**:
   - Create reusable, composable components
   - Implement proper prop validation
   - Use custom hooks for shared logic

4. **Documentation**:
   - Document all API integrations
   - Create component documentation
   - Include setup instructions

## Example Implementation for Key Components

### 1. Dashboard Summary Component

```tsx
// components/dashboard/DashboardSummary.tsx
import { useQuery } from 'react-query';
import { Grid } from '@mui/material';
import MetricCard from '../ui/MetricCard';
import { fetchDashboardSummary } from '../../lib/api/hooks/useDashboard';
import { formatCurrency, formatDuration } from '../../lib/utils/formatters';

interface DashboardSummaryProps {
  dateRange: string;
  plazaId?: string;
  companyId?: string;
}

export default function DashboardSummary({ dateRange, plazaId, companyId }: DashboardSummaryProps) {
  const { data, isLoading, error } = useQuery(
    ['dashboardSummary', dateRange, plazaId, companyId],
    () => fetchDashboardSummary({ dateRange, plazaId, companyId }),
    { staleTime: 5 * 60 * 1000 } // 5 minutes
  );

  if (isLoading) return <div>Loading summary metrics...</div>;
  if (error) return <div>Error loading summary metrics</div>;

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Total Revenue"
          value={formatCurrency(data.totalRevenue)}
          trend={data.revenueTrend}
          icon="revenue"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Transactions"
          value={data.transactionCount.toLocaleString()}
          trend={data.transactionTrend}
          icon="transactions"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Unique Vehicles"
          value={data.vehicleCount.toLocaleString()}
          trend={data.vehicleTrend}
          icon="vehicles"
        />
      </Grid>
      <Grid item xs={12} md={3}>
        <MetricCard
          title="Avg Duration"
          value={formatDuration(data.avgDuration)}
          trend={data.durationTrend}
          icon="duration"
        />
      </Grid>
    </Grid>
  );
}
```

### 2. Revenue Chart Component

```tsx
// components/charts/RevenueChart.tsx
import { useMemo } from 'react';
import { ResponsiveLine } from '@nivo/line';
import { useTheme } from '@mui/material/styles';
import { useQuery } from 'react-query';
import { fetchRevenueData } from '../../lib/api/hooks/useRevenue';
import { formatCurrency } from '../../lib/utils/formatters';

interface RevenueChartProps {
  dateRange: string;
  plazaId?: string;
  companyId?: string;
}

export default function RevenueChart({ dateRange, plazaId, companyId }: RevenueChartProps) {
  const theme = useTheme();
  
  const { data, isLoading, error } = useQuery(
    ['revenueData', dateRange, plazaId, companyId],
    () => fetchRevenueData({ dateRange, plazaId, companyId }),
    { staleTime: 5 * 60 * 1000 } // 5 minutes
  );

  const chartData = useMemo(() => {
    if (!data) return [];
    
    return [
      {
        id: 'revenue',
        data: data.map(item => ({
          x: new Date(item.date),
          y: item.totalRevenue
        }))
      }
    ];
  }, [data]);

  if (isLoading) return <div>Loading revenue chart...</div>;
  if (error) return <div>Error loading revenue data</div>;

  return (
    <div style={{ height: 400 }}>
      <ResponsiveLine
        data={chartData}
        margin={{ top: 20, right: 20, bottom: 50, left: 60 }}
        xScale={{ 
          type: 'time',
          format: '%Y-%m-%d',
          useUTC: false,
          precision: 'day'
        }}
        xFormat="time:%Y-%m-%d"
        yScale={{ 
          type: 'linear', 
          min: 'auto', 
          max: 'auto', 
          stacked: false, 
          reverse: false 
        }}
        curve="natural"
        axisTop={null}
        axisRight={null}
        axisBottom={{
          format: '%b %d',
          tickValues: 'every 2 days',
          legend: 'Date',
          legendOffset: 36,
          legendPosition: 'middle'
        }}
        axisLeft={{
          legend: 'Revenue',
          legendOffset: -40,
          legendPosition: 'middle',
          format: value => formatCurrency(value)
        }}
        enableGridX={false}
        colors={{ scheme: 'category10' }}
        lineWidth={3}
        pointSize={10}
        pointColor={{ theme: 'background' }}
        pointBorderWidth={2}
        pointBorderColor={{ from: 'serieColor' }}
        pointLabelYOffset={-12}
        enableArea={true}
        areaOpacity={0.15}
        useMesh={true}
        legends={[
          {
            anchor: 'bottom-right',
            direction: 'column',
            justify: false,
            translateX: 0,
            translateY: 0,
            itemsSpacing: 0,
            itemDirection: 'left-to-right',
            itemWidth: 80,
            itemHeight: 20,
            itemOpacity: 0.75,
            symbolSize: 12,
            symbolShape: 'circle',
            symbolBorderColor: 'rgba(0, 0, 0, .5)',
            effects: [
              {
                on: 'hover',
                style: {
                  itemBackground: 'rgba(0, 0, 0, .03)',
                  itemOpacity: 1
                }
              }
            ]
          }
        ]}
        theme={{
          axis: {
            domain: {
              line: {
                stroke: theme.palette.divider,
              },
            },
            ticks: {
              line: {
                stroke: theme.palette.divider,
                strokeWidth: 1,
              },
              text: {
                fill: theme.palette.text.secondary,
              },
            },
          },
          grid: {
            line: {
              stroke: theme.palette.divider,
              strokeWidth: 1,
            },
          },
          legends: {
            text: {
              fill: theme.palette.text.primary,
            },
          },
          tooltip: {
            container: {
              background: theme.palette.background.paper,
              color: theme.palette.text.primary,
              fontSize: 12,
              borderRadius: 4,
              boxShadow: theme.shadows[3],
              padding: 8,
            },
          },
        }}
      />
    </div>
  );
}
```

### 3. Transactions Table Component

```tsx
// components/tables/TransactionsTable.tsx
import { useState } from 'react';
import { useQuery } from 'react-query';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import { VisibilityOutlined, GetAppOutlined } from '@mui/icons-material';
import { fetchTransactions } from '../../lib/api/hooks/useTransactions';
import { formatCurrency, formatDateTime, formatDuration } from '../../lib/utils/formatters';

interface TransactionsTableProps {
  dateRange: string;
  plazaId?: string;
  companyId?: string;
  limit?: number;
}

export default function TransactionsTable({ dateRange, plazaId, companyId, limit = 10 }: TransactionsTableProps) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(limit);

  const { data, isLoading, error } = useQuery(
    ['transactions', dateRange, plazaId, companyId, page, rowsPerPage],
    () => fetchTransactions({ dateRange, plazaId, companyId, page, limit: rowsPerPage }),
    { keepPreviousData: true }
  );

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  if (isLoading) return <div>Loading transactions...</div>;
  if (error) return <div>Error loading transactions</div>;

  return (
    <Paper elevation={0} sx={{ width: '100%', overflow: 'hidden' }}>
      <TableContainer sx={{ maxHeight: 440 }}>
        <Table stickyHeader aria-label="transactions table">
          <TableHead>
            <TableRow>
              <TableCell>Ticket No</TableCell>
              <TableCell>Plaza</TableCell>
              <TableCell>Vehicle</TableCell>
              <TableCell>Entry Time</TableCell>
              <TableCell>Exit Time</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell align="right">Amount</TableCell>
              <TableCell>Payment</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {data.transactions.map((transaction) => (
              <TableRow hover key={transaction.PakringDataID}>
                <TableCell>{transaction.TicketNo}</TableCell>
                <TableCell>{transaction.PlazaName}</TableCell>
                <TableCell>{transaction.VehicleNumber !== 'NA' ? transaction.VehicleNumber : '-'}</TableCell>
                <TableCell>{formatDateTime(transaction.EntryDateTime)}</TableCell>
                <TableCell>{formatDateTime(transaction.ExitDateTime)}</TableCell>
                <TableCell>{formatDuration(transaction.ParkedDuration)}</TableCell>
                <TableCell align="right">{formatCurrency(transaction.ParkingFee + transaction.iTotalGSTFee)}</TableCell>
                <TableCell>{transaction.PaymentMode}</TableCell>
                <TableCell align="center">
                  <Tooltip title="View Details">
                    <IconButton size="small">
                      <VisibilityOutlined fontSize="small" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Export">
                    <IconButton size="small">
                      <GetAppOutlined fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={data.totalCount}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
}
```

This specification provides a comprehensive blueprint for building a high-performance, visually appealing dashboard with Next.js that integrates with your existing ParkWiz database and API structure. The senior frontend developer should have all the information needed to create an exceptional dashboard experience that can handle large datasets while providing beautiful visualizations and analytics.