import React from 'react';
import { getResponsiveTextSize, formatPercentage } from '../utils/formatters';

/**
 * Enhanced Dashboard Card Component
 * 
 * Supports both single metric and multi-metric display
 * - Single metric: Traditional card with title, value, trend, and icon
 * - Multi-metric: Card with multiple related metrics in a structured layout
 * - Enhanced single: Single metric with additional summary metrics below
 */
export function EnhancedDashboardCard({ 
  title, 
  value, 
  trend, 
  icon: Icon, 
  color,
  metrics, // Array of metrics for multi-metric cards
  summaryMetrics, // Array of summary metrics for enhanced single cards
  type = 'single' // 'single', 'multi', or 'enhanced-single'
}) {
  // Single metric card (existing functionality)
  if (type === 'single') {
    const textSizeClass = getResponsiveTextSize(value);
    
    return (
      <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 hover:shadow-xl hover:border-amber-200 transition-all duration-300 transform hover:-translate-y-1">
        <div className="flex items-start justify-between gap-3">
          {/* Left side - Content */}
          <div className="flex-1 min-w-0">
            <p className="text-xs sm:text-sm text-gray-600 mb-1 truncate" title={title}>
              {title}
            </p>
            <h3 
              className={`${textSizeClass} font-bold text-gray-900 break-words leading-tight`}
              title={value}
            >
              {value}
            </h3>
            {trend !== undefined && !isNaN(trend) && (
              <div className="flex items-center mt-1 sm:mt-2">
                <span className={`inline-flex items-center text-xs sm:text-sm font-medium ${
                  trend >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  <svg 
                    className={`w-3 h-3 mr-1 ${trend >= 0 ? 'rotate-0' : 'rotate-180'}`} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path 
                      fillRule="evenodd" 
                      d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" 
                      clipRule="evenodd" 
                    />
                  </svg>
                  {formatPercentage(Math.abs(trend))}
                </span>
              </div>
            )}
          </div>
          
          {/* Right side - Icon */}
          <div className={`flex-shrink-0 p-2 sm:p-3 rounded-lg ${color} shadow-sm`}>
            <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
        </div>
      </div>
    );
  }

  // Enhanced single metric card with summary metrics
  if (type === 'enhanced-single') {
    const textSizeClass = getResponsiveTextSize(value);
    
    return (
      <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 hover:shadow-xl hover:border-amber-200 transition-all duration-300 transform hover:-translate-y-1">
        {/* Header with title and icon */}
        <div className="flex items-start justify-between gap-3 mb-4">
          <div className="flex-1 min-w-0">
            <p className="text-xs sm:text-sm text-gray-600 mb-1 truncate" title={title}>
              {title}
            </p>
            <h3 
              className={`${textSizeClass} font-bold text-gray-900 break-words leading-tight`}
              title={value}
            >
              {value}
            </h3>
            {trend !== undefined && !isNaN(trend) && (
              <div className="flex items-center mt-1 sm:mt-2">
                <span className={`inline-flex items-center text-xs sm:text-sm font-medium ${
                  trend >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  <svg 
                    className={`w-3 h-3 mr-1 ${trend >= 0 ? 'rotate-0' : 'rotate-180'}`} 
                    fill="currentColor" 
                    viewBox="0 0 20 20"
                  >
                    <path 
                      fillRule="evenodd" 
                      d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" 
                      clipRule="evenodd" 
                    />
                  </svg>
                  {formatPercentage(Math.abs(trend))}
                </span>
              </div>
            )}
          </div>
          
          {/* Right side - Icon */}
          <div className={`flex-shrink-0 p-2 sm:p-3 rounded-lg ${color} shadow-sm`}>
            <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
          </div>
        </div>

        {/* Summary Metrics */}
        {summaryMetrics && summaryMetrics.length > 0 && (
          <div className="border-t border-amber-100 pt-4">
            <div className="grid grid-cols-3 gap-3">
              {summaryMetrics.map((metric, index) => (
                <div key={index} className="text-center p-2 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg border border-amber-100 hover:border-amber-200 transition-colors duration-200">
                  <p className="text-xs text-gray-500 mb-1 truncate" title={metric.label}>
                    {metric.label}
                  </p>
                  <p 
                    className="text-sm sm:text-base font-semibold text-gray-900 truncate" 
                    title={metric.value}
                  >
                    {metric.value}
                  </p>
                  {metric.trend !== undefined && !isNaN(metric.trend) && (
                    <div className="flex items-center justify-center mt-1">
                      <span className={`inline-flex items-center text-xs font-medium ${
                        metric.trend >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        <svg 
                          className={`w-2 h-2 mr-1 ${metric.trend >= 0 ? 'rotate-0' : 'rotate-180'}`} 
                          fill="currentColor" 
                          viewBox="0 0 20 20"
                        >
                          <path 
                            fillRule="evenodd" 
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" 
                            clipRule="evenodd" 
                          />
                        </svg>
                        {formatPercentage(Math.abs(metric.trend))}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Multi-metric card (enhanced functionality)
  return (
    <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 hover:shadow-xl hover:border-amber-200 transition-all duration-300 transform hover:-translate-y-1">
      {/* Header with title and icon */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm sm:text-base font-semibold text-gray-900 truncate" title={title}>
          {title}
        </h3>
        <div className={`flex-shrink-0 p-2 rounded-lg ${color} shadow-sm`}>
          <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
        </div>
      </div>

      {/* Main Revenue Metric - Highlighted */}
      {metrics && metrics.length > 0 && metrics[0].isMain && (
        <div className="mb-4 p-3 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg border border-amber-100">
          <div className="text-center">
            <p className="text-xs text-gray-500 mb-1">Total Revenue</p>
            <p className="text-lg sm:text-xl font-bold text-gray-900">
              {metrics[0].value}
            </p>
            {metrics[0].trend !== undefined && !isNaN(metrics[0].trend) && (
              <div className="flex items-center justify-center mt-1">
                <span className={`inline-flex items-center text-xs font-medium ${
                  metrics[0].trend >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  <svg
                    className={`w-3 h-3 mr-1 ${metrics[0].trend >= 0 ? 'rotate-0' : 'rotate-180'}`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {formatPercentage(Math.abs(metrics[0].trend))}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Vehicle Count Metrics Grid */}
      <div className="grid grid-cols-3 gap-2 sm:gap-3">
        {metrics && metrics.slice(1).map((metric, index) => {
          // Define colors for each metric type
          const getMetricColor = (label) => {
            switch(label.toLowerCase()) {
              case 'entry':
                return 'border-green-200 bg-green-50 hover:border-green-300';
              case 'exit':
                return 'border-blue-200 bg-blue-50 hover:border-blue-300';
              case 'remaining':
                return 'border-orange-200 bg-orange-50 hover:border-orange-300';
              default:
                return 'border-gray-200 bg-gray-50 hover:border-gray-300';
            }
          };

          return (
            <div key={index + 1} className={`text-center p-2 rounded-lg border transition-colors duration-200 ${getMetricColor(metric.label)}`}>
              <p className="text-xs text-gray-500 mb-1 truncate" title={metric.label}>
                {metric.label}
              </p>
              <p 
                className="text-sm sm:text-base font-bold text-gray-900 truncate" 
                title={metric.value}
              >
                {metric.value}
              </p>
              {metric.trend !== undefined && !isNaN(metric.trend) && (
                <div className="flex items-center justify-center mt-1">
                  <span className={`inline-flex items-center text-xs font-medium ${
                    metric.trend >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <svg 
                      className={`w-2 h-2 mr-1 ${metric.trend >= 0 ? 'rotate-0' : 'rotate-180'}`} 
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                    {formatPercentage(Math.abs(metric.trend))}
                  </span>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Vehicle Flow Summary with proper color coding */}
      {metrics && metrics.length >= 4 && (
        <div className="mt-4 pt-3 border-t border-amber-100">
          <div className="flex items-center justify-between text-xs text-gray-600">
            <span className="font-medium">Vehicle Flow Status</span>
            <div className="flex items-center space-x-3">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                <span>Entry</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-1"></div>
                <span>Exit</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-orange-400 rounded-full mr-1"></div>
                <span>Remaining</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Enhanced Dashboard Card Skeleton Loader
 * Supports single, enhanced-single, and multi-metric card skeletons
 */
export function EnhancedDashboardCardSkeleton({ type = 'single' }) {
  if (type === 'single') {
    return (
      <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 animate-pulse">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
          </div>
          <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }

  if (type === 'enhanced-single') {
    return (
      <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 animate-pulse">
        {/* Header skeleton */}
        <div className="flex items-start justify-between gap-3 mb-4">
          <div className="flex-1 min-w-0">
            <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-6 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/4"></div>
          </div>
          <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
        </div>

        {/* Summary metrics skeleton */}
        <div className="border-t border-amber-100 pt-4">
          <div className="grid grid-cols-3 gap-3">
            {[1, 2, 3].map((item) => (
              <div key={item} className="text-center p-2 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg border border-amber-100">
                <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg border border-amber-100 p-4 sm:p-6 animate-pulse">
      {/* Header skeleton */}
      <div className="flex items-center justify-between mb-4">
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
      </div>

      {/* Main revenue skeleton */}
      <div className="mb-4 p-3 bg-gradient-to-br from-amber-50 to-yellow-50 rounded-lg border border-amber-100">
        <div className="text-center">
          <div className="h-3 bg-gray-200 rounded w-1/3 mx-auto mb-1"></div>
          <div className="h-6 bg-gray-200 rounded w-2/3 mx-auto mb-1"></div>
          <div className="h-2 bg-gray-200 rounded w-1/4 mx-auto"></div>
        </div>
      </div>

      {/* Vehicle metrics grid skeleton */}
      <div className="grid grid-cols-3 gap-2 sm:gap-3">
        {[
          { bg: 'bg-green-50', border: 'border-green-200' },
          { bg: 'bg-blue-50', border: 'border-blue-200' },
          { bg: 'bg-orange-50', border: 'border-orange-200' }
        ].map((style, index) => (
          <div key={index} className={`text-center p-2 rounded-lg border ${style.bg} ${style.border}`}>
            <div className="h-3 bg-gray-200 rounded w-full mb-1"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-1"></div>
            <div className="h-2 bg-gray-200 rounded w-1/2 mx-auto"></div>
          </div>
        ))}
      </div>

      {/* Flow summary skeleton */}
      <div className="mt-4 pt-3 border-t border-amber-100">
        <div className="flex items-center justify-between">
          <div className="h-3 bg-gray-200 rounded w-1/3"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    </div>
  );
}