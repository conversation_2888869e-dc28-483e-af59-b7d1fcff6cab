// backend/src/services/CacheStrategyService.js
const redisService = require('./RedisService');
const cron = require('node-cron');

/**
 * ===============================================================================
 * # Advanced Cache Strategy Service
 * ===============================================================================
 * 
 * Implements intelligent caching strategies for PWVMS:
 * - Cache warming
 * - Cache invalidation patterns
 * - Performance optimization
 * - Memory management
 * - Analytics and monitoring
 */

class CacheStrategyService {
  constructor() {
    this.cacheHitStats = new Map();
    this.cacheMissStats = new Map();
    this.cacheInvalidationStats = new Map();
    this.isMonitoring = false;
  }

  /**
   * ===============================================================================
   * ## CACHE WARMING STRATEGIES
   * ===============================================================================
   */

  /**
   * Warm dashboard cache for active users
   */
  async warmDashboardCache() {
    try {
      console.log('🔥 Starting dashboard cache warming...');
      
      // Get active users from the last 24 hours
      const activeUsers = await this.getActiveUsers();
      
      let warmedCount = 0;
      
      for (const user of activeUsers) {
        try {
          // Warm common dashboard queries
          const commonFilters = [
            { dateRange: 'today' },
            { dateRange: 'yesterday' },
            { dateRange: 'week' },
            { dateRange: 'month' }
          ];

          for (const filters of commonFilters) {
            // Simulate dashboard requests to warm cache
            await this.warmUserDashboard(user.id, user.role, filters);
            warmedCount++;
          }
          
          // Add small delay to prevent overwhelming the database
          await this.sleep(100);
          
        } catch (error) {
          console.error(`Failed to warm cache for user ${user.id}:`, error.message);
        }
      }
      
      console.log(`✅ Dashboard cache warming completed. Warmed ${warmedCount} cache entries.`);
      return { success: true, warmedCount };
      
    } catch (error) {
      console.error('❌ Dashboard cache warming failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Warm user-specific dashboard cache
   */
  async warmUserDashboard(userId, role, filters) {
    try {
      // Check if cache already exists
      const existingCache = await redisService.getDashboardSummary(userId, role, filters);
      
      if (!existingCache) {
        // Cache doesn't exist, we would need to make actual API calls here
        // For now, we'll create placeholder data
        const placeholderData = {
          summary: {
            totalRevenue: 0,
            transactionCount: 0,
            vehicleCount: 0,
            avgDuration: 0
          },
          warmed: true,
          timestamp: new Date().toISOString()
        };
        
        await redisService.cacheDashboardSummary(userId, role, filters, placeholderData);
      }
      
      return true;
    } catch (error) {
      console.error(`Failed to warm user dashboard cache:`, error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## CACHE INVALIDATION STRATEGIES
   * ===============================================================================
   */

  /**
   * Smart cache invalidation based on data changes
   */
  async invalidateCacheOnDataChange(changeType, affectedEntities) {
    try {
      console.log(`🔄 Smart cache invalidation triggered: ${changeType}`);
      
      const invalidationStrategies = {
        'PARKING_TRANSACTION': async (entities) => {
          // Invalidate dashboard cache for affected plazas/companies
          await this.invalidateDashboardCacheByPlaza(entities.plazaCode);
          await this.invalidateLiveParkingData(entities.plazaCode);
        },
        
        'USER_PERMISSION_CHANGE': async (entities) => {
          // Invalidate user-specific cache
          await redisService.invalidateUserCache(entities.userId);
          await redisService.invalidateDashboardCache(entities.userId, entities.role);
        },
        
        'PLAZA_CONFIG_CHANGE': async (entities) => {
          // Invalidate plaza-related cache
          await this.invalidatePlazaRelatedCache(entities.plazaId);
        },
        
        'SYSTEM_CONFIG_CHANGE': async (entities) => {
          // Invalidate system-wide cache
          await redisService.invalidateAllDashboardCache();
        }
      };

      const strategy = invalidationStrategies[changeType];
      if (strategy) {
        await strategy(affectedEntities);
        
        // Track invalidation stats
        this.trackCacheInvalidation(changeType);
        
        console.log(`✅ Cache invalidation completed for: ${changeType}`);
      }
      
      return true;
    } catch (error) {
      console.error(`❌ Cache invalidation failed:`, error);
      return false;
    }
  }

  /**
   * Invalidate dashboard cache by plaza
   */
  async invalidateDashboardCacheByPlaza(plazaCode) {
    try {
      // This would require getting all users who have access to this plaza
      // For now, we'll invalidate all dashboard cache
      await redisService.invalidateAllDashboardCache();
      return true;
    } catch (error) {
      console.error(`Failed to invalidate dashboard cache for plaza ${plazaCode}:`, error);
      return false;
    }
  }

  /**
   * Invalidate live parking data
   */
  async invalidateLiveParkingData(plazaCode) {
    try {
      const key = `live:parking:${plazaCode}`;
      await redisService.del(key);
      return true;
    } catch (error) {
      console.error(`Failed to invalidate live parking data for plaza ${plazaCode}:`, error);
      return false;
    }
  }

  /**
   * ===============================================================================
   * ## CACHE PERFORMANCE OPTIMIZATION
   * ===============================================================================
   */

  /**
   * Optimize cache based on usage patterns
   */
  async optimizeCache() {
    try {
      console.log('⚡ Starting cache optimization...');
      
      // Get cache statistics
      const stats = await this.getCacheStatistics();
      
      // Identify frequently accessed but expired cache
      const frequentlyAccessed = this.identifyFrequentlyAccessedCache(stats);
      
      // Pre-warm frequently accessed cache
      for (const cacheKey of frequentlyAccessed) {
        await this.prewarmSpecificCache(cacheKey);
      }
      
      // Clean up rarely used cache
      await this.cleanupRarelyUsedCache(stats);
      
      console.log('✅ Cache optimization completed');
      return { success: true };
      
    } catch (error) {
      console.error('❌ Cache optimization failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * ===============================================================================
   * ## CACHE MONITORING AND ANALYTICS
   * ===============================================================================
   */

  /**
   * Start cache monitoring
   */
  startCacheMonitoring() {
    if (this.isMonitoring) {
      console.log('⚠️ Cache monitoring already running');
      return;
    }

    this.isMonitoring = true;
    console.log('📊 Starting cache monitoring...');

    // Monitor cache hit/miss rates every minute
    cron.schedule('* * * * *', async () => {
      await this.collectCacheMetrics();
    });

    // Generate cache reports every hour
    cron.schedule('0 * * * *', async () => {
      await this.generateCacheReport();
    });

    // Optimize cache every 6 hours
    cron.schedule('0 */6 * * *', async () => {
      await this.optimizeCache();
    });

    // Warm cache every day at 6 AM
    cron.schedule('0 6 * * *', async () => {
      await this.warmDashboardCache();
    });

    console.log('✅ Cache monitoring started');
  }

  /**
   * Collect cache metrics
   */
  async collectCacheMetrics() {
    try {
      const redisInfo = await redisService.getRedisInfo();
      
      if (redisInfo.connected) {
        const metrics = {
          timestamp: new Date().toISOString(),
          hitRate: this.calculateHitRate(),
          missRate: this.calculateMissRate(),
          memoryUsage: this.extractMemoryUsage(redisInfo.memory),
          keyCount: this.extractKeyCount(redisInfo.keyspace),
          connectionCount: this.extractConnectionCount(redisInfo.info)
        };

        // Store metrics in Redis for historical analysis
        const metricsKey = `metrics:cache:${Date.now()}`;
        await redisService.set(metricsKey, metrics, 86400); // Keep for 24 hours
        
        return metrics;
      }
      
    } catch (error) {
      console.error('Failed to collect cache metrics:', error);
    }
  }

  /**
   * Generate cache performance report
   */
  async generateCacheReport() {
    try {
      const report = {
        timestamp: new Date().toISOString(),
        period: 'last_hour',
        hitRate: this.calculateHitRate(),
        missRate: this.calculateMissRate(),
        topHitKeys: this.getTopHitKeys(10),
        topMissKeys: this.getTopMissKeys(10),
        invalidationCount: this.getTotalInvalidations(),
        recommendations: this.generateRecommendations()
      };

      console.log('📊 Cache Performance Report:', JSON.stringify(report, null, 2));
      
      // Store report
      const reportKey = `report:cache:${Date.now()}`;
      await redisService.set(reportKey, report, 86400 * 7); // Keep for 7 days
      
      return report;
    } catch (error) {
      console.error('Failed to generate cache report:', error);
    }
  }

  /**
   * ===============================================================================
   * ## UTILITY METHODS
   * ===============================================================================
   */

  /**
   * Track cache hit
   */
  trackCacheHit(key) {
    const count = this.cacheHitStats.get(key) || 0;
    this.cacheHitStats.set(key, count + 1);
  }

  /**
   * Track cache miss
   */
  trackCacheMiss(key) {
    const count = this.cacheMissStats.get(key) || 0;
    this.cacheMissStats.set(key, count + 1);
  }

  /**
   * Track cache invalidation
   */
  trackCacheInvalidation(type) {
    const count = this.cacheInvalidationStats.get(type) || 0;
    this.cacheInvalidationStats.set(type, count + 1);
  }

  /**
   * Calculate hit rate
   */
  calculateHitRate() {
    const totalHits = Array.from(this.cacheHitStats.values()).reduce((sum, count) => sum + count, 0);
    const totalMisses = Array.from(this.cacheMissStats.values()).reduce((sum, count) => sum + count, 0);
    const total = totalHits + totalMisses;
    
    return total > 0 ? ((totalHits / total) * 100).toFixed(2) : 0;
  }

  /**
   * Calculate miss rate
   */
  calculateMissRate() {
    return (100 - parseFloat(this.calculateHitRate())).toFixed(2);
  }

  /**
   * Get top hit keys
   */
  getTopHitKeys(limit = 10) {
    return Array.from(this.cacheHitStats.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([key, count]) => ({ key, hits: count }));
  }

  /**
   * Get top miss keys
   */
  getTopMissKeys(limit = 10) {
    return Array.from(this.cacheMissStats.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([key, count]) => ({ key, misses: count }));
  }

  /**
   * Generate optimization recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    const hitRate = parseFloat(this.calculateHitRate());
    
    if (hitRate < 70) {
      recommendations.push('Consider increasing cache TTL for frequently accessed data');
    }
    
    if (hitRate > 95) {
      recommendations.push('Cache performance is excellent - consider reducing TTL to save memory');
    }
    
    const topMissKeys = this.getTopMissKeys(5);
    if (topMissKeys.length > 0) {
      recommendations.push(`Consider pre-warming cache for: ${topMissKeys.map(k => k.key).join(', ')}`);
    }
    
    return recommendations;
  }

  /**
   * Get active users (placeholder implementation)
   */
  async getActiveUsers() {
    // This would typically query the database for users active in the last 24 hours
    // For now, return empty array
    return [];
  }

  /**
   * Sleep utility
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Extract memory usage from Redis info
   */
  extractMemoryUsage(memoryInfo) {
    // Parse Redis memory info string
    const lines = memoryInfo.split('\r\n');
    const memoryData = {};
    
    lines.forEach(line => {
      const [key, value] = line.split(':');
      if (key && value) {
        memoryData[key] = value;
      }
    });
    
    return {
      used: memoryData.used_memory_human || 'N/A',
      peak: memoryData.used_memory_peak_human || 'N/A',
      rss: memoryData.used_memory_rss_human || 'N/A'
    };
  }

  /**
   * Extract key count from Redis keyspace info
   */
  extractKeyCount(keyspaceInfo) {
    const lines = keyspaceInfo.split('\r\n');
    let totalKeys = 0;
    
    lines.forEach(line => {
      const match = line.match(/keys=(\d+)/);
      if (match) {
        totalKeys += parseInt(match[1]);
      }
    });
    
    return totalKeys;
  }

  /**
   * Extract connection count from Redis info
   */
  extractConnectionCount(info) {
    const match = info.match(/connected_clients:(\d+)/);
    return match ? parseInt(match[1]) : 0;
  }
}

// Create singleton instance
const cacheStrategyService = new CacheStrategyService();

module.exports = cacheStrategyService;