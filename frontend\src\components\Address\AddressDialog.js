"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../../components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "../../components/ui/form"
import { Input } from "../../components/ui/input"
import { Switch } from "../../components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { toast } from "../../components/ui/use-toast"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../../components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "../../components/ui/card"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { countryApi } from "../../api/countryApi"
import { stateApi } from "../../api/stateApi"
// import type { Address } from "./address-list"

const formSchema = z.object({
  countryId: z.string().min(1, { message: "Please select a country" }),
  stateId: z.string().min(1, { message: "Please select a state" }),
  firstName: z.string().min(2, { message: "First name must be at least 2 characters" }),
  lastName: z.string().min(2, { message: "Last name must be at least 2 characters" }),
  mobile: z.string().min(10, { message: "Please enter a valid mobile number" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  pincode: z.string().min(5, { message: "Please enter a valid pincode" }),
  addressLine1: z.string().min(5, { message: "Address line 1 is required" }),
  addressLine2: z.string().optional(),
  landmarkCity: z.string().min(2, { message: "City/Landmark is required" }),
  latitude: z.string().optional(),
  longitude: z.string().optional(),
  isLatLongRequired: z.boolean().default(false),
  isActive: z.boolean().default(true),
})

// Define the form values type based on the schema
// type AddressFormValues = z.infer<typeof formSchema>

// Default values for the form
const defaultValues = {
  countryId: "",
  stateId: "",
  firstName: "",
  lastName: "",
  mobile: "",
  email: "",
  pincode: "",
  addressLine1: "",
  addressLine2: "",
  landmarkCity: "",
  latitude: "",
  longitude: "",
  isLatLongRequired: false,
  isActive: true,
}

function AddressDialog({
  open,
  onOpenChange,
  initialData,
  onSave,
}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedCountryId, setSelectedCountryId] = useState(initialData?.countryId || "")
  const [activeTab, setActiveTab] = useState("personal")
  const [countries, setCountries] = useState([])
  const [states, setStates] = useState([])
  const [loading, setLoading] = useState(false)

  // Convert initialData to form values
  const getInitialFormValues = () => {
    if (!initialData) return defaultValues

    return {
      countryId: initialData.countryId || "",
      stateId: initialData.stateId || "",
      firstName: initialData.firstName || "",
      lastName: initialData.lastName || "",
      mobile: initialData.mobile || "",
      email: initialData.email || "",
      pincode: initialData.pincode || "",
      addressLine1: initialData.addressLine1 || "",
      addressLine2: initialData.addressLine2 || "",
      landmarkCity: initialData.landmarkCity || "",
      latitude: initialData.latitude || "",
      longitude: initialData.longitude || "",
      isLatLongRequired: initialData.isLatLongRequired || false,
      isActive: initialData.isActive !== undefined ? initialData.isActive : true,
    }
  }

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: getInitialFormValues(),
  })

  // Fetch countries on component mount
  useEffect(() => {
    async function fetchCountries() {
      try {
        setLoading(true)
        const countriesData = await countryApi.getCountries()
        setCountries(countriesData)
      } catch (error) {
        console.error("Failed to fetch countries:", error)
        toast({
          title: "Error",
          description: "Failed to load countries. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchCountries()
  }, [])

  // Fetch states when a country is selected
  useEffect(() => {
    async function fetchStates() {
      if (!selectedCountryId) {
        setStates([])
        return
      }

      try {
        setLoading(true)
        const statesData = await stateApi.getStates()
        // Filter states by the selected country
        const filteredStates = statesData.filter(state => state.countryId === selectedCountryId)
        setStates(filteredStates)
      } catch (error) {
        console.error("Failed to fetch states:", error)
        toast({
          title: "Error",
          description: "Failed to load states. Please try again.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStates()
  }, [selectedCountryId])

  // Update form when initialData changes
  useEffect(() => {
    if (initialData) {
      const formValues = getInitialFormValues()
      Object.entries(formValues).forEach(([key, value]) => {
        form.setValue(key, value)
      })

      setSelectedCountryId(initialData.countryId || "")
    } else {
      form.reset(defaultValues)
      setSelectedCountryId("")
    }
  }, [initialData, form])

  function onSubmit(data) {
    // You would typically send this data to your API
    console.log(data)

    toast({
      title: "Address saved successfully",
      description: `Address for ${data.firstName} ${data.lastName} has been ${initialData ? "updated" : "created"}.`,
    })

    // Call onSave callback
    if (onSave) {
      onSave()
    }

    // Close the dialog
    if (onOpenChange) {
      onOpenChange(false)
    } else {
      setIsDialogOpen(false)
    }
  }

  const handleOpenChange = (open) => {
    if (onOpenChange) {
      onOpenChange(open)
    } else {
      setIsDialogOpen(open)
    }
  }

  const isControlled = open !== undefined && onOpenChange !== undefined

  // Filter states based on selected country (fallback in case API filtering isn't working)
  const filteredStates = states.filter((state) => state.countryId === selectedCountryId)

  return (
    <Dialog open={isControlled ? open : isDialogOpen} onOpenChange={handleOpenChange}>
      {!isControlled && (
        <DialogTrigger asChild>
          <Button variant="outline">Add Address</Button>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{initialData ? "Edit Address" : "Add New Address"}</DialogTitle>
          <DialogDescription>
            Fill in the details to {initialData ? "update" : "create"} an address record.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="personal" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="personal">Personal Info</TabsTrigger>
            <TabsTrigger value="address">Address Details</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-6">
              <TabsContent value="personal">
                <Card>
                  <CardHeader>
                    <CardTitle>Personal Information</CardTitle>
                    <CardDescription>Enter the contact details for this address.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input placeholder="John" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Doe" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="mobile"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mobile</FormLabel>
                            <FormControl>
                              <Input placeholder="+****************" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" onClick={() => handleOpenChange(false)}>
                      Cancel
                    </Button>
                    <Button type="button" onClick={() => setActiveTab("address")}>
                      Next
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
              <TabsContent value="address">
                <Card>
                  <CardHeader>
                    <CardTitle>Address Details</CardTitle>
                    <CardDescription>Enter the location and address information.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="countryId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Country</FormLabel>
                            <Select
                              onValueChange={(value) => {
                                field.onChange(value)
                                setSelectedCountryId(value)
                                // Reset stateId when country changes
                                form.setValue("stateId", "")
                              }}
                              value={field.value}
                              disabled={loading}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a country" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {loading ? (
                                  <SelectItem value="loading" disabled>
                                    Loading...
                                  </SelectItem>
                                ) : (
                                  countries.map((country) => (
                                    <SelectItem key={country.id} value={country.id}>
                                      {country.name}
                                    </SelectItem>
                                  ))
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="stateId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>State</FormLabel>
                            <Select 
                              onValueChange={field.onChange} 
                              value={field.value}
                              disabled={loading || !selectedCountryId}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select a state" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {loading ? (
                                  <SelectItem value="loading" disabled>
                                    Loading...
                                  </SelectItem>
                                ) : selectedCountryId ? (
                                  filteredStates.length > 0 ? (
                                    filteredStates.map((state) => (
                                      <SelectItem key={state.id} value={state.id}>
                                        {state.name}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="none" disabled>
                                      No states available
                                    </SelectItem>
                                  )
                                ) : (
                                  <SelectItem value="select-country" disabled>
                                    Select a country first
                                  </SelectItem>
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4">
                      <FormField
                        control={form.control}
                        name="addressLine1"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address Line 1</FormLabel>
                            <FormControl>
                              <Input placeholder="123 Main St" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="addressLine2"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address Line 2</FormLabel>
                            <FormControl>
                              <Input placeholder="Apt 4B" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="landmarkCity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City/Landmark</FormLabel>
                            <FormControl>
                              <Input placeholder="San Francisco" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="pincode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Pincode/Zip</FormLabel>
                            <FormControl>
                              <Input placeholder="94103" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" type="button" onClick={() => setActiveTab("personal")}>
                      Back
                    </Button>
                    <Button type="button" onClick={() => setActiveTab("settings")}>
                      Next
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
              <TabsContent value="settings">
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Settings</CardTitle>
                    <CardDescription>Configure geolocation and status settings.</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="latitude"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Latitude</FormLabel>
                            <FormControl>
                              <Input placeholder="37.7749" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="longitude"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Longitude</FormLabel>
                            <FormControl>
                              <Input placeholder="-122.4194" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="isLatLongRequired"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Require Lat/Long</FormLabel>
                              <FormDescription>Require latitude and longitude for this address</FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="isActive"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                            <div className="space-y-0.5">
                              <FormLabel>Active Status</FormLabel>
                              <FormDescription>Set the address as active or inactive</FormDescription>
                            </div>
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" type="button" onClick={() => setActiveTab("address")}>
                      Back
                    </Button>
                    <Button type="submit">Save Address</Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </form>
          </Form>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

export { AddressDialog }