const db = require('../../config/database');
const sql = require('mssql');

/**
 * Valet Payment Controller
 * Handles payment processing, gateway integration, and payment status management for valet system
 */

// Get payment options for a plaza
exports.getPaymentOptions = async (req, res) => {
  try {
    const { plazaId } = req.params;

    if (!plazaId) {
      return res.status(400).json({
        success: false,
        message: 'Plaza ID is required'
      });
    }

    try {
      // Try to get payment options using stored procedure
      const result = await db.query('EXEC sp_Valet_Payment_GetOptions @plazaId', { plazaId });
      
      const paymentOptions = result.recordset[0] || {};
      
      res.json({
        success: true,
        message: 'Payment options retrieved successfully',
        data: {
          plazaId: parseInt(plazaId),
          razorPayEnabled: paymentOptions.RazorPayEnabled || false,
          phonePeEnabled: paymentOptions.PhonePeEnabled || false,
          cashEnabled: true, // Cash is always enabled
          upiEnabled: paymentOptions.RazorPayEnabled || paymentOptions.PhonePeEnabled || false,
          cardEnabled: paymentOptions.RazorPayEnabled || false,
          netBankingEnabled: paymentOptions.RazorPayEnabled || false,
          valetCharges: paymentOptions.ValetCharges || 0
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const paymentConfigQuery = `
        SELECT 
          CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as RazorPayEnabled,
          CASE WHEN pp.Id IS NOT NULL AND pp.IsActive = 1 THEN 1 ELSE 0 END as PhonePeEnabled,
          ISNULL(p.ValetCharges, 0) as ValetCharges
        FROM Plaza p
        LEFT JOIN PlazaRazorPayConfiguration rp ON p.Id = rp.PlazaId AND rp.IsActive = 1
        LEFT JOIN PlazaPhonePeConfiguration pp ON p.Id = pp.PlazaId AND pp.IsActive = 1
        WHERE p.Id = @plazaId AND p.IsActive = 1
      `;

      const configResult = await db.query(paymentConfigQuery, { plazaId });
      
      if (configResult.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Plaza not found or inactive'
        });
      }

      const config = configResult.recordset[0];
      
      res.json({
        success: true,
        message: 'Payment options retrieved successfully',
        data: {
          plazaId: parseInt(plazaId),
          razorPayEnabled: config.RazorPayEnabled,
          phonePeEnabled: config.PhonePeEnabled,
          cashEnabled: true,
          upiEnabled: config.RazorPayEnabled || config.PhonePeEnabled,
          cardEnabled: config.RazorPayEnabled,
          netBankingEnabled: config.RazorPayEnabled,
          valetCharges: config.ValetCharges
        }
      });
    }

  } catch (error) {
    console.error('Error in getPaymentOptions:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? 'Failed to retrieve payment options' 
        : error.message
    });
  }
};

// Initiate payment process
exports.initiatePayment = async (req, res) => {
  try {
    const { 
      transactionId, 
      paymentMethod, 
      amount, 
      customerId,
      customerName,
      customerEmail,
      customerMobile 
    } = req.body;

    // Validate required fields
    if (!transactionId || !paymentMethod || !amount || !customerId) {
      return res.status(400).json({
        success: false,
        message: 'Transaction ID, payment method, amount, and customer ID are required'
      });
    }

    // Validate payment method
    const validPaymentMethods = ['RAZORPAY', 'PHONEPE', 'CASH'];
    if (!validPaymentMethods.includes(paymentMethod.toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment method. Supported methods: RAZORPAY, PHONEPE, CASH'
      });
    }

    // Validate amount
    if (amount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Amount must be greater than 0'
      });
    }

    try {
      // Try to initiate payment using stored procedure
      const result = await db.query(`
        DECLARE @PaymentId INT, @OrderId NVARCHAR(100);
        EXEC sp_Valet_Payment_Initiate
          @TransactionId = @transactionId,
          @PaymentMethod = @paymentMethod,
          @Amount = @amount,
          @CustomerId = @customerId,
          @CustomerName = @customerName,
          @CustomerEmail = @customerEmail,
          @CustomerMobile = @customerMobile,
          @CreatedBy = @customerId,
          @PaymentId = @PaymentId OUTPUT,
          @OrderId = @OrderId OUTPUT;
        SELECT @PaymentId as PaymentId, @OrderId as OrderId;
      `, {
        transactionId,
        paymentMethod: paymentMethod.toUpperCase(),
        amount,
        customerId,
        customerName: customerName || null,
        customerEmail: customerEmail || null,
        customerMobile: customerMobile || null
      });

      const paymentData = result.recordset[0];

      // Handle different payment methods
      let response = {
        success: true,
        message: 'Payment initiated successfully',
        data: {
          paymentId: paymentData.PaymentId,
          orderId: paymentData.OrderId,
          transactionId,
          paymentMethod: paymentMethod.toUpperCase(),
          amount,
          status: 'INITIATED'
        }
      };

      // For cash payments, mark as pending
      if (paymentMethod.toUpperCase() === 'CASH') {
        response.data.status = 'PENDING_CASH';
        response.data.instructions = 'Please visit the valet desk to complete cash payment';
      } else {
        // For gateway payments, we'll need to integrate with actual payment services
        response.data.gatewayUrl = `/api/valet/payment/gateway/${paymentMethod.toLowerCase()}/${paymentData.PaymentId}`;
      }

      res.json(response);

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      // Generate order ID
      const orderId = `VALET_${Date.now()}_${transactionId}`;
      
      const insertPaymentQuery = `
        INSERT INTO PaymentGatewayTransactions (
          TransactionId, GatewayType, GatewayOrderId, Amount, 
          Currency, PaymentMethod, Status, CreatedOn
        )
        OUTPUT INSERTED.Id
        VALUES (
          @transactionId, @paymentMethod, @orderId, @amount,
          'INR', @paymentMethod, 'PENDING', GETDATE()
        )
      `;

      const paymentResult = await db.query(insertPaymentQuery, {
        transactionId,
        paymentMethod: paymentMethod.toUpperCase(),
        orderId,
        amount
      });

      const paymentId = paymentResult.recordset[0].Id;

      res.json({
        success: true,
        message: 'Payment initiated successfully',
        data: {
          paymentId,
          orderId,
          transactionId,
          paymentMethod: paymentMethod.toUpperCase(),
          amount,
          status: paymentMethod.toUpperCase() === 'CASH' ? 'PENDING_CASH' : 'INITIATED',
          gatewayUrl: paymentMethod.toUpperCase() !== 'CASH' 
            ? `/api/valet/payment/gateway/${paymentMethod.toLowerCase()}/${paymentId}`
            : null,
          instructions: paymentMethod.toUpperCase() === 'CASH' 
            ? 'Please visit the valet desk to complete cash payment'
            : null
        }
      });
    }

  } catch (error) {
    console.error('Error in initiatePayment:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production' 
        ? 'Failed to initiate payment' 
        : error.message
    });
  }
};

// Get payment status
exports.getPaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;

    if (!paymentId) {
      return res.status(400).json({
        success: false,
        message: 'Payment ID is required'
      });
    }

    try {
      // Try to get payment status using stored procedure
      const result = await db.query('EXEC sp_Valet_Payment_GetStatus @paymentId', { paymentId });
      
      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Payment not found'
        });
      }

      const payment = result.recordset[0];
      
      res.json({
        success: true,
        message: 'Payment status retrieved successfully',
        data: {
          paymentId: payment.Id,
          transactionId: payment.TransactionId,
          orderId: payment.GatewayOrderId,
          amount: payment.Amount,
          paymentMethod: payment.PaymentMethod,
          status: payment.Status,
          gatewayTransactionId: payment.GatewayTransactionId,
          createdOn: payment.CreatedOn,
          updatedOn: payment.UpdatedOn
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);
      
      const paymentQuery = `
        SELECT Id, TransactionId, GatewayOrderId, Amount, PaymentMethod, 
               Status, GatewayTransactionId, CreatedOn, UpdatedOn
        FROM PaymentGatewayTransactions
        WHERE Id = @paymentId
      `;

      const result = await db.query(paymentQuery, { paymentId });
      
      if (result.recordset.length === 0) {
        return res.status(404).json({
          success: false,
          message: 'Payment not found'
        });
      }

      const payment = result.recordset[0];
      
      res.json({
        success: true,
        message: 'Payment status retrieved successfully',
        data: {
          paymentId: payment.Id,
          transactionId: payment.TransactionId,
          orderId: payment.GatewayOrderId,
          amount: payment.Amount,
          paymentMethod: payment.PaymentMethod,
          status: payment.Status,
          gatewayTransactionId: payment.GatewayTransactionId,
          createdOn: payment.CreatedOn,
          updatedOn: payment.UpdatedOn
        }
      });
    }

  } catch (error) {
    console.error('Error in getPaymentStatus:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to retrieve payment status'
        : error.message
    });
  }
};

// Update payment status (for gateway callbacks)
exports.updatePaymentStatus = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const {
      status,
      gatewayTransactionId,
      gatewayResponse,
      updatedBy
    } = req.body;

    if (!paymentId || !status) {
      return res.status(400).json({
        success: false,
        message: 'Payment ID and status are required'
      });
    }

    // Validate status
    const validStatuses = ['SUCCESS', 'FAILED', 'PENDING', 'CANCELLED'];
    if (!validStatuses.includes(status.toUpperCase())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid payment status'
      });
    }

    try {
      // Try to update payment status using stored procedure
      const result = await db.query(`
        EXEC sp_Valet_Payment_UpdateStatus
          @PaymentId = @paymentId,
          @Status = @status,
          @GatewayTransactionId = @gatewayTransactionId,
          @GatewayResponse = @gatewayResponse,
          @UpdatedBy = @updatedBy
      `, {
        paymentId,
        status: status.toUpperCase(),
        gatewayTransactionId: gatewayTransactionId || null,
        gatewayResponse: gatewayResponse ? JSON.stringify(gatewayResponse) : null,
        updatedBy: updatedBy || 1
      });

      // If payment is successful, update the main transaction status
      if (status.toUpperCase() === 'SUCCESS') {
        // Get transaction ID from payment
        const paymentQuery = `SELECT TransactionId FROM PaymentGatewayTransactions WHERE Id = @paymentId`;
        const paymentResult = await db.query(paymentQuery, { paymentId });

        if (paymentResult.recordset.length > 0) {
          const transactionId = paymentResult.recordset[0].TransactionId;

          // Update transaction status to PAYMENT_COMPLETED
          await db.query(`
            UPDATE ParkingTransactions
            SET TransactionStatus = 2 -- PAYMENT_COMPLETED
            WHERE Id = @transactionId
          `, { transactionId });
        }
      }

      res.json({
        success: true,
        message: 'Payment status updated successfully',
        data: {
          paymentId: parseInt(paymentId),
          status: status.toUpperCase(),
          updatedOn: new Date()
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      const updateQuery = `
        UPDATE PaymentGatewayTransactions
        SET Status = @status,
            GatewayTransactionId = @gatewayTransactionId,
            GatewayResponse = @gatewayResponse,
            UpdatedOn = GETDATE()
        WHERE Id = @paymentId
      `;

      await db.query(updateQuery, {
        paymentId,
        status: status.toUpperCase(),
        gatewayTransactionId: gatewayTransactionId || null,
        gatewayResponse: gatewayResponse ? JSON.stringify(gatewayResponse) : null
      });

      res.json({
        success: true,
        message: 'Payment status updated successfully',
        data: {
          paymentId: parseInt(paymentId),
          status: status.toUpperCase(),
          updatedOn: new Date()
        }
      });
    }

  } catch (error) {
    console.error('Error in updatePaymentStatus:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to update payment status'
        : error.message
    });
  }
};

// Accept cash payment (for valet controllers)
exports.acceptCashPayment = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { acceptedBy, remarks } = req.body;

    if (!paymentId || !acceptedBy) {
      return res.status(400).json({
        success: false,
        message: 'Payment ID and acceptedBy are required'
      });
    }

    try {
      // Try to accept cash payment using stored procedure
      const result = await db.query(`
        EXEC sp_Valet_Payment_AcceptCash
          @PaymentId = @paymentId,
          @AcceptedBy = @acceptedBy,
          @Remarks = @remarks
      `, {
        paymentId,
        acceptedBy,
        remarks: remarks || null
      });

      // Get updated payment details
      const paymentQuery = `SELECT TransactionId FROM PaymentGatewayTransactions WHERE Id = @paymentId`;
      const paymentResult = await db.query(paymentQuery, { paymentId });

      if (paymentResult.recordset.length > 0) {
        const transactionId = paymentResult.recordset[0].TransactionId;

        // Update transaction status to PAYMENT_COMPLETED
        await db.query(`
          UPDATE ParkingTransactions
          SET TransactionStatus = 2 -- PAYMENT_COMPLETED
          WHERE Id = @transactionId
        `, { transactionId });
      }

      res.json({
        success: true,
        message: 'Cash payment accepted successfully',
        data: {
          paymentId: parseInt(paymentId),
          status: 'SUCCESS',
          acceptedBy,
          acceptedOn: new Date(),
          remarks
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      const updateQuery = `
        UPDATE PaymentGatewayTransactions
        SET Status = 'SUCCESS',
            GatewayResponse = @gatewayResponse,
            UpdatedOn = GETDATE()
        WHERE Id = @paymentId AND PaymentMethod = 'CASH'
      `;

      const gatewayResponse = JSON.stringify({
        acceptedBy,
        acceptedOn: new Date(),
        remarks: remarks || null,
        paymentType: 'CASH'
      });

      await db.query(updateQuery, { paymentId, gatewayResponse });

      res.json({
        success: true,
        message: 'Cash payment accepted successfully',
        data: {
          paymentId: parseInt(paymentId),
          status: 'SUCCESS',
          acceptedBy,
          acceptedOn: new Date(),
          remarks
        }
      });
    }

  } catch (error) {
    console.error('Error in acceptCashPayment:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to accept cash payment'
        : error.message
    });
  }
};

// Get pending cash payments (for valet controllers)
exports.getPendingCashPayments = async (req, res) => {
  try {
    const { plazaId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    if (!plazaId) {
      return res.status(400).json({
        success: false,
        message: 'Plaza ID is required'
      });
    }

    const offset = (page - 1) * limit;

    try {
      // Try to get pending cash payments using stored procedure
      const result = await db.query(`
        EXEC sp_Valet_Payment_GetPendingCash
          @PlazaId = @plazaId,
          @PageNumber = @page,
          @PageSize = @limit
      `, {
        plazaId,
        page: parseInt(page),
        limit: parseInt(limit)
      });

      const payments = result.recordset;

      res.json({
        success: true,
        message: 'Pending cash payments retrieved successfully',
        data: {
          payments,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: payments.length > 0 ? payments[0].TotalCount || payments.length : 0
          }
        }
      });

    } catch (error) {
      // Fall back to direct query
      console.warn('Stored procedure failed, using direct query:', error.message);

      const paymentsQuery = `
        SELECT
          pgt.Id as PaymentId,
          pgt.TransactionId,
          pgt.GatewayOrderId,
          pgt.Amount,
          pgt.Status,
          pgt.CreatedOn,
          pt.PNR,
          pt.ParkingPin,
          c.Name as CustomerName,
          c.MobileNumber,
          cv.VehicleNumber
        FROM PaymentGatewayTransactions pgt
        JOIN ParkingTransactions pt ON pgt.TransactionId = pt.Id
        JOIN Customer c ON pt.CustomerId = c.Id
        LEFT JOIN CustomerVehicle cv ON pt.CustomerVehicleId = cv.Id
        JOIN PlazaValetPoint pvp ON pt.PlazaValetPointId = pvp.Id
        WHERE pvp.PlazaId = @plazaId
          AND pgt.PaymentMethod = 'CASH'
          AND pgt.Status = 'PENDING_CASH'
        ORDER BY pgt.CreatedOn DESC
        OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
      `;

      const result = await db.query(paymentsQuery, { plazaId, offset, limit });

      res.json({
        success: true,
        message: 'Pending cash payments retrieved successfully',
        data: {
          payments: result.recordset,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: result.recordset.length
          }
        }
      });
    }

  } catch (error) {
    console.error('Error in getPendingCashPayments:', error);
    res.status(500).json({
      success: false,
      message: process.env.NODE_ENV === 'production'
        ? 'Failed to retrieve pending cash payments'
        : error.message
    });
  }
};
