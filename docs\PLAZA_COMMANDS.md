# Plaza Assignment Commands

## Database Credentials (from .env file)
- **Server**: parkwizvms.database.windows.net
- **Database**: ParkwizOps
- **Username**: hparkwiz
- **Password**: Parkwiz@2020

## PowerShell Commands to Check Plaza Assignments

### 1. Quick One-Liner (using Node.js)
```powershell
cd d:/PWVMS/backend && node ../get-plaza-assignments-simple.js
```

### 2. Direct PowerShell Command (no dependencies)
```powershell
cd d:/PWVMS && .\get-plaza-assignments-direct.ps1
```

### 3. PowerShell with SQL Server Module (if available)
```powershell
cd d:/PWVMS && .\get-plaza-assignments.ps1
```

### 4. Simple PowerShell Wrapper
```powershell
cd d:/PWVMS && .\plaza-assignments-oneliner.ps1
```

## Current Plaza Assignments (as of last check)

### Users WITH Plaza Assignments:
- **accr** (Ambuja CCRP) - PlazaManager
  - Email: <EMAIL>
  - Assigned Plaza: AMBUJA CITY CENTRE RAIPUR (Code: 102)
  - Company: AMBUJA REALTY DEVELOPMENT LIMITED
  - Contact: Priyakant Parida - 8340557100
  - Assigned On: 06/27/2025 14:13:26

### Users WITHOUT Plaza Assignments:
- **company admin** (company admin) - CompanyAdmin - <EMAIL>
- **shubodh** (shubodh singh) - PlazaManager - <EMAIL>  
- **system** (hi hello) - SuperAdmin - <EMAIL>

## Summary by Role:
- **PlazaManager**: 1 user with 1 total plaza assignment
- **CompanyAdmin**: 1 user without plaza assignment
- **SuperAdmin**: 1 user without plaza assignment

## Database Tables Involved:
- **Users**: Contains user information and credentials
- **UserPlaza**: Maps users to plazas (junction table)
- **Plaza**: Contains plaza information
- **tblCompanyMaster**: Contains company information
- **Roles**: Contains role definitions

## Quick SQL Query (if you want to run directly in SQL Server):
```sql
SELECT 
    u.Username,
    u.FirstName + ' ' + u.LastName as FullName,
    u.Email,
    r.Name as RoleName,
    p.PlazaName,
    p.PlazaCode,
    c.CompanyName
FROM Users u
INNER JOIN UserPlaza up ON u.Id = up.UserId
INNER JOIN Plaza p ON up.PlazaId = p.Id
INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
LEFT JOIN Roles r ON u.RoleId = r.Id
WHERE u.IsActive = 1 AND p.IsActive = 1
ORDER BY u.Username;
```