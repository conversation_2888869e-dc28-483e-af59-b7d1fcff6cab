// frontend/src/services/realtimeService.js
import axios from 'axios';

/**
 * ===============================================================================
 * # Frontend Real-time Service
 * ===============================================================================
 * 
 * Handles real-time communication with the Redis-enhanced backend.
 * Provides WebSocket-like functionality for live updates.
 */

class RealtimeService {
  constructor() {
    this.eventSource = null;
    this.listeners = new Map();
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
  }

  /**
   * ===============================================================================
   * ## CONNECTION MANAGEMENT
   * ===============================================================================
   */

  /**
   * Initialize real-time connection
   */
  async initialize(token) {
    try {
      // For now, we'll use polling instead of WebSocket
      // In a full implementation, you'd set up WebSocket or Server-Sent Events
      this.token = token;
      this.isConnected = true;
      
      console.log('✅ Real-time service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize real-time service:', error);
      return false;
    }
  }

  /**
   * Start polling for real-time updates
   */
  startPolling(interval = 30000) { // 30 seconds
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    this.pollingInterval = setInterval(async () => {
      await this.fetchRealtimeUpdates();
    }, interval);

    console.log(`🔄 Started polling for real-time updates every ${interval}ms`);
  }

  /**
   * Stop polling
   */
  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      console.log('⏹️ Stopped polling for real-time updates');
    }
  }

  /**
   * ===============================================================================
   * ## EVENT HANDLING
   * ===============================================================================
   */

  /**
   * Subscribe to real-time events
   */
  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    
    this.listeners.get(eventType).push(callback);
    console.log(`📡 Subscribed to ${eventType} events`);
  }

  /**
   * Unsubscribe from events
   */
  unsubscribe(eventType, callback) {
    if (this.listeners.has(eventType)) {
      const callbacks = this.listeners.get(eventType);
      const index = callbacks.indexOf(callback);
      
      if (index > -1) {
        callbacks.splice(index, 1);
        console.log(`📡 Unsubscribed from ${eventType} events`);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  emit(eventType, data) {
    if (this.listeners.has(eventType)) {
      const callbacks = this.listeners.get(eventType);
      callbacks.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in event callback for ${eventType}:`, error);
        }
      });
    }
  }

  /**
   * ===============================================================================
   * ## DATA FETCHING
   * ===============================================================================
   */

  /**
   * Fetch real-time dashboard updates
   */
  async fetchRealtimeUpdates() {
    try {
      const response = await axios.get('/api/dashboard/enhanced/realtime', {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (response.data.success) {
        this.emit('DASHBOARD_UPDATE', response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch real-time updates:', error);
    }
  }

  /**
   * Fetch live parking data for a plaza
   */
  async fetchLiveParkingData(plazaCode) {
    try {
      const response = await axios.get(`/api/dashboard/enhanced/live-parking/${plazaCode}`, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (response.data.success) {
        this.emit('LIVE_PARKING_UPDATE', {
          plazaCode,
          data: response.data.data
        });
        
        return response.data.data;
      }
    } catch (error) {
      console.error(`Failed to fetch live parking data for ${plazaCode}:`, error);
      return null;
    }
  }

  /**
   * ===============================================================================
   * ## CACHE MANAGEMENT
   * ===============================================================================
   */

  /**
   * Clear dashboard cache
   */
  async clearDashboardCache() {
    try {
      const response = await axios.post('/api/dashboard/enhanced/cache/clear', {}, {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (response.data.success) {
        console.log('✅ Dashboard cache cleared successfully');
        this.emit('CACHE_CLEARED', { type: 'dashboard' });
        return true;
      }
    } catch (error) {
      console.error('Failed to clear dashboard cache:', error);
      return false;
    }
  }

  /**
   * Get cache status
   */
  async getCacheStatus() {
    try {
      const response = await axios.get('/api/dashboard/enhanced/cache/status', {
        headers: {
          'Authorization': `Bearer ${this.token}`
        }
      });

      if (response.data.success) {
        return response.data.data;
      }
    } catch (error) {
      console.error('Failed to get cache status:', error);
      return null;
    }
  }

  /**
   * ===============================================================================
   * ## UTILITY METHODS
   * ===============================================================================
   */

  /**
   * Check if service is connected
   */
  isServiceConnected() {
    return this.isConnected;
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      listeners: Array.from(this.listeners.keys())
    };
  }

  /**
   * Cleanup and disconnect
   */
  disconnect() {
    this.stopPolling();
    this.listeners.clear();
    this.isConnected = false;
    console.log('🔌 Real-time service disconnected');
  }
}

// Create singleton instance
const realtimeService = new RealtimeService();

export default realtimeService;