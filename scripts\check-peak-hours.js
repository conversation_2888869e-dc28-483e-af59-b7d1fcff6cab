require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function checkPeakHours() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Check peak hours for June 2025
    const juneHoursResult = await sql.query(`
      SELECT 
        DATEPART(HOUR, ExitDateTime) as Hour, 
        COUNT(*) as TransactionCount 
      FROM tblParkwiz_Parking_Data 
      WHERE ExitDateTime >= '2025-06-01' 
        AND ExitDateTime <= '2025-06-30'
      GROUP BY DATEPART(HOUR, ExitDateTime)
      ORDER BY Hour
    `);
    
    console.log('Peak hours for June 2025:');
    console.log(JSON.stringify(juneHoursResult.recordset, null, 2));

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkPeakHours();