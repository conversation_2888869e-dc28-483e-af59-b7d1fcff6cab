import api from '../services/api'; // Using the shared axios instance

export const addressApi = {
  /**
   * Fetches the list of all addresses.
   * GET /addresses
   */
  getAddresses: async () => {
    const response = await api.get('/addresses');
    return response.data.addresses;
  },

  /**
   * Fetches the details of a single address by its ID.
   * GET /addresses/:id
   */
  getAddressById: async (id) => {
    const response = await api.get(`/addresses/${id}`);
    return response.data;
  },

  /**
   * Creates a new address.
   * POST /addresses
   */
  createAddress: async (data) => {
    const response = await api.post('/addresses', data);
    return response.data.address;
  },

  /**
   * Updates an address by ID.
   * PUT /addresses/:id
   */
  updateAddress: async (id, data) => {
    const response = await api.put(`/addresses/${id}`, data);
    return response.data.address;
  },

  /**
   * Deletes an address by ID.
   * DELETE /addresses/:id
   */
  deleteAddress: async (id) => {
    await api.delete(`/addresses/${id}`);
  },

  /**
   * Fetches addresses by user ID.
   * GET /addresses/user/:userId
   */
  getAddressesByUser: async (userId) => {
    const response = await api.get(`/addresses/user/${userId}`);
    return response.data.addresses;
  },

  /**
   * Sets an address as the default for a user.
   * PUT /addresses/:id/default
   */
  setDefaultAddress: async (id, userId) => {
    const response = await api.put(`/addresses/${id}/default`, { userId });
    return response.data;
  },

  /**
   * Validates an address with external service.
   * POST /addresses/validate
   */
  validateAddress: async (addressData) => {
    const response = await api.post('/addresses/validate', addressData);
    return response.data.validationResult;
  },

  /**
   * Geocodes an address to get latitude and longitude.
   * POST /addresses/geocode
   */
  geocodeAddress: async (addressData) => {
    const response = await api.post('/addresses/geocode', addressData);
    return response.data.coordinates;
  },

  /**
   * Fetches addresses within a certain radius of coordinates.
   * GET /addresses/nearby
   */
  getNearbyAddresses: async (latitude, longitude, radius) => {
    const response = await api.get('/addresses/nearby', {
      params: { latitude, longitude, radius }
    });
    return response.data.addresses;
  },

  /**
   * Batch creates multiple addresses.
   * POST /addresses/batch
   */
  batchCreateAddresses: async (addressesData) => {
    const response = await api.post('/addresses/batch', { addresses: addressesData });
    return response.data.addresses;
  },

  /**
   * Batch updates multiple addresses.
   * PUT /addresses/batch
   */
  batchUpdateAddresses: async (addressesData) => {
    const response = await api.put('/addresses/batch', { addresses: addressesData });
    return response.data.addresses;
  }
};

export default addressApi;