// frontend/src/hooks/usePermissionFilter.js
import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../contexts/authContext';
import { filterDataByUserAccess } from '../utils/permissionFilters';

/**
 * Custom hook for filtering data based on user permissions
 *
 * @param {Array} data - The data array to filter
 * @param {Object} options - Filter options
 * @param {string} options.companyIdField - The field name for company ID in the data objects
 * @param {string} options.plazaIdField - The field name for plaza ID in the data objects
 * @returns {Object} - The filtered data and helper functions
 */
const usePermissionFilter = (data, options = {}) => {
  const { user } = useAuth();

  // Extract options values to use as dependencies
  const {
    companyIdField = 'CompanyId',
    plazaIdField = 'PlazaId',
    includeInactive = false
  } = options;

  // Use useMemo instead of useState + useEffect to prevent infinite loops
  const filteredData = useMemo(() => {
    if (data && Array.isArray(data) && user) {
      return filterDataByUserAccess(data, user, {
        companyIdField,
        plazaIdField,
        includeInactive
      });
    }
    return [];
  }, [data, user, companyIdField, plazaIdField, includeInactive]);

  // Function to check if user can create new items
  const canCreate = useCallback((module) => {
    if (!user || !user.permissions) return false;

    // SuperAdmin can create anything
    if (user.role === 'SuperAdmin') return true;

    // Special case for CompanyAdmin and Digital Pay
    if (user.role === 'CompanyAdmin' && (module === 'Digital Pay' || module === 'DigitalPayment')) {
      return true;
    }

    // Special case for CompanyAdmin and Users
    if (user.role === 'CompanyAdmin' && module === 'Users') {
      console.log('CompanyAdmin has special permission to create users');
      return true;
    }

    // Check if user has Create permission for the module
    return user.permissions[module]?.includes('Create') || false;
  }, [user]);

  // Function to check if user can edit a specific item
  const canEdit = useCallback((item, module) => {
    if (!user || !user.permissions || !item) return false;

    // SuperAdmin can edit anything
    if (user.role === 'SuperAdmin') return true;

    // Special case for CompanyAdmin and Digital Pay
    if (user.role === 'CompanyAdmin' && (module === 'Digital Pay' || module === 'DigitalPayment')) {
      // CompanyAdmin can edit Digital Pay items for their companies
      if (user.companies) {
        const companyIds = user.companies.map(company => company.Id);
        return companyIds.includes(parseInt(item[companyIdField], 10));
      }
      return false;
    }

    // Special case for CompanyAdmin and Users
    if (user.role === 'CompanyAdmin' && module === 'Users') {
      console.log('CompanyAdmin has special permission to edit users');
      // Don't allow editing SuperAdmin users
      if (item.RoleName === 'SuperAdmin') {
        return false;
      }
      return true;
    }

    // Special case for CompanyAdmin and Companies - never allow editing companies
    if (user.role === 'CompanyAdmin' && module === 'Companies') {
      console.log('CompanyAdmin is not allowed to edit companies');
      return false;
    }

    // Check if user has Edit permission for the module
    const hasPermission = user.permissions[module]?.includes('Edit') || false;
    if (!hasPermission) return false;

    // CompanyAdmin can only edit items for their companies (except Companies module)
    if (user.role === 'CompanyAdmin' && user.companies) {
      const companyIds = user.companies.map(company => company.Id);
      return companyIds.includes(parseInt(item[companyIdField], 10));
    }

    // PlazaManager can only edit items for their plazas
    if (user.role === 'PlazaManager' && user.plazas) {
      const plazaIds = user.plazas.map(plaza => plaza.Id);
      return plazaIds.includes(parseInt(item[plazaIdField], 10));
    }

    return false;
  }, [user, companyIdField, plazaIdField]);

  // Function to check if user can delete a specific item
  const canDelete = useCallback((item, module) => {
    if (!user || !user.permissions || !item) return false;

    // SuperAdmin can delete anything
    if (user.role === 'SuperAdmin') return true;

    // Special case for CompanyAdmin and Digital Pay
    if (user.role === 'CompanyAdmin' && (module === 'Digital Pay' || module === 'DigitalPayment')) {
      // CompanyAdmin can delete Digital Pay items for their companies
      if (user.companies) {
        const companyIds = user.companies.map(company => company.Id);
        return companyIds.includes(parseInt(item[companyIdField], 10));
      }
      return false;
    }

    // Special case for CompanyAdmin and Users
    if (user.role === 'CompanyAdmin' && module === 'Users') {
      console.log('CompanyAdmin has special permission to delete users');
      // Don't allow deleting SuperAdmin users
      if (item.RoleName === 'SuperAdmin') {
        return false;
      }
      return true;
    }

    // Special case for CompanyAdmin and Companies - never allow deleting companies
    if (user.role === 'CompanyAdmin' && module === 'Companies') {
      console.log('CompanyAdmin is not allowed to delete companies');
      return false;
    }

    // Check if user has Delete permission for the module
    const hasPermission = user.permissions[module]?.includes('Delete') || false;
    if (!hasPermission) return false;

    // CompanyAdmin can only delete items for their companies (except Companies module)
    if (user.role === 'CompanyAdmin' && user.companies) {
      const companyIds = user.companies.map(company => company.Id);
      return companyIds.includes(parseInt(item[companyIdField], 10));
    }

    // PlazaManager can only delete items for their plazas
    if (user.role === 'PlazaManager' && user.plazas) {
      const plazaIds = user.plazas.map(plaza => plaza.Id);
      return plazaIds.includes(parseInt(item[plazaIdField], 10));
    }

    return false;
  }, [user, companyIdField, plazaIdField]);

  return {
    filteredData,
    canCreate,
    canEdit,
    canDelete
  };
};

export default usePermissionFilter;
