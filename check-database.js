const sql = require('mssql');

async function checkDatabase() {
  try {
    const config = {
      user: 'hparkwiz',
      password: 'Parkwiz@2020',
      server: 'parkwizvms.database.windows.net',
      database: 'ParkwizOps',
      port: 1433,
      options: {
        encrypt: true,
        trustServerCertificate: true
      },
      requestTimeout: 30000,
      connectionTimeout: 30000
    };

    console.log('🔍 Connecting to database...');
    const pool = await sql.connect(config);
    
    console.log('✅ Connected! Checking tables...');
    
    // Check if permission-related tables exist
    const tablesResult = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE' 
      AND (TABLE_NAME LIKE '%Module%' OR TABLE_NAME LIKE '%Permission%' OR TABLE_NAME LIKE '%Role%')
      ORDER BY TABLE_NAME
    `);
    
    console.log('📋 Permission-related tables:');
    tablesResult.recordset.forEach(table => {
      console.log('  -', table.TABLE_NAME);
    });
    
    // Check all tables to see what's available
    const allTablesResult = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    
    console.log('\n📋 All tables in database:');
    allTablesResult.recordset.forEach(table => {
      console.log('  -', table.TABLE_NAME);
    });
    
    // Check if Modules table exists and get its structure
    try {
      const modulesStructure = await pool.request().query(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Modules'
        ORDER BY ORDINAL_POSITION
      `);
      
      if (modulesStructure.recordset.length > 0) {
        console.log('\n🏗️ Modules table structure:');
        modulesStructure.recordset.forEach(col => {
          console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
        });
        
        // Check sample data
        const sampleData = await pool.request().query('SELECT TOP 5 * FROM Modules');
        console.log('\n📊 Sample Modules data:');
        sampleData.recordset.forEach(row => {
          console.log(`  - ID: ${row.Id}, Name: ${row.Name}, Active: ${row.IsActive}`);
        });
      } else {
        console.log('\n❌ Modules table not found');
      }
      
    } catch (error) {
      console.log('\n❌ Error checking Modules table:', error.message);
    }
    
    // Check if there are any stored procedures related to permissions
    try {
      const proceduresResult = await pool.request().query(`
        SELECT ROUTINE_NAME, ROUTINE_TYPE
        FROM INFORMATION_SCHEMA.ROUTINES 
        WHERE ROUTINE_TYPE = 'PROCEDURE'
        AND (ROUTINE_NAME LIKE '%Module%' OR ROUTINE_NAME LIKE '%Permission%' OR ROUTINE_NAME LIKE '%Role%')
        ORDER BY ROUTINE_NAME
      `);
      
      if (proceduresResult.recordset.length > 0) {
        console.log('\n🔧 Permission-related stored procedures:');
        proceduresResult.recordset.forEach(proc => {
          console.log(`  - ${proc.ROUTINE_NAME} (${proc.ROUTINE_TYPE})`);
        });
      } else {
        console.log('\n📝 No permission-related stored procedures found');
      }
      
    } catch (error) {
      console.log('\n❌ Error checking stored procedures:', error.message);
    }
    
    await pool.close();
    console.log('\n✅ Database check completed');
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  }
}

checkDatabase().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('❌ Script error:', error.message);
  process.exit(1);
});