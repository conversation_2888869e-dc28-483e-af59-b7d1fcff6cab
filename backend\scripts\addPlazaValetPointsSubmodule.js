const sql = require('mssql');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT) || 1433,
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT) || 30000,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT) || 30000,
  },
  pool: {
    max: parseInt(process.env.DB_POOL_MAX) || 10,
    min: parseInt(process.env.DB_POOL_MIN) || 0,
    idleTimeoutMillis: parseInt(process.env.DB_POOL_IDLE_TIMEOUT) || 30000,
  }
};

async function addPlazaValetPointsSubmodule() {
  let pool;
  
  try {
    console.log('Connecting to database...');
    console.log(`Server: ${dbConfig.server}`);
    console.log(`Database: ${dbConfig.database}`);
    console.log(`User: ${dbConfig.user}`);
    
    // Create connection pool
    pool = await sql.connect(dbConfig);
    console.log('Connected to database successfully!');
    
    // Read the SQL script
    const sqlScriptPath = path.join(__dirname, '../database/add_plaza_valet_points_submodule.sql');
    console.log(`Reading SQL script from: ${sqlScriptPath}`);
    
    if (!fs.existsSync(sqlScriptPath)) {
      throw new Error(`SQL script not found at: ${sqlScriptPath}`);
    }
    
    const sqlScript = fs.readFileSync(sqlScriptPath, 'utf8');
    console.log('SQL script loaded successfully');
    
    // Split the script by GO statements and execute each batch
    const batches = sqlScript.split(/^\s*GO\s*$/gim).filter(batch => batch.trim());
    
    console.log(`Executing ${batches.length} SQL batches...`);
    
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i].trim();
      if (batch) {
        console.log(`\nExecuting batch ${i + 1}/${batches.length}...`);
        try {
          const result = await pool.request().query(batch);
          
          // Log any messages from SQL Server
          if (result.recordset && result.recordset.length > 0) {
            console.log('Query results:', result.recordset);
          }
          
          console.log(`Batch ${i + 1} executed successfully`);
        } catch (batchError) {
          console.error(`Error in batch ${i + 1}:`, batchError.message);
          throw batchError;
        }
      }
    }
    
    console.log('\n✅ PlazaValetPoints submodule added successfully!');
    console.log('\nNext steps:');
    console.log('1. Restart your backend server');
    console.log('2. The "Valet Points" menu item should now be visible in the sidebar');
    console.log('3. Users with appropriate permissions can access the Plaza Valet Points management');
    
  } catch (error) {
    console.error('❌ Error adding PlazaValetPoints submodule:', error);
    
    if (error.code === 'ELOGIN') {
      console.error('Database login failed. Please check your credentials in .env file');
    } else if (error.code === 'ETIMEOUT') {
      console.error('Database connection timeout. Please check your network connection');
    } else if (error.code === 'ENOTFOUND') {
      console.error('Database server not found. Please check the server address');
    }
    
    process.exit(1);
  } finally {
    if (pool) {
      try {
        await pool.close();
        console.log('Database connection closed');
      } catch (closeError) {
        console.error('Error closing database connection:', closeError);
      }
    }
  }
}

// Run the script
console.log('🚀 Starting PlazaValetPoints submodule setup...');
console.log('This script will add the PlazaValetPoints submodule to the database');
console.log('and assign appropriate permissions to all roles.\n');

addPlazaValetPointsSubmodule();
