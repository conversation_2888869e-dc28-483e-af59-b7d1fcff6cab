// <PERSON>ript to activate a Digital Pay configuration
const db = require('../config/database');

async function activateDigitalPayConfig() {
  try {
    // Connect to database
    await db.connect();
    console.log('Database connection successful');

    // Get the first inactive configuration
    const inactiveConfigQuery = `
      SELECT TOP 1
        c.ConfigLaneID,
        c.PlazaID,
        c.LaneID,
        c.ActiveStatus,
        p.PlazaName,
        l.LaneNumber
      FROM tblLaneDigitalPayConfiguration c
      LEFT JOIN Plaza p ON c.PlazaID = p.Id
      LEFT JOIN tblLaneDetails l ON c.LaneID = l.LaneID
      WHERE c.ActiveStatus = 'N'
      ORDER BY c.ConfigLaneID DESC
    `;
    
    const inactiveConfigResult = await db.query(inactiveConfigQuery);
    
    if (inactiveConfigResult.recordset.length > 0) {
      const config = inactiveConfigResult.recordset[0];
      console.log(`Found inactive configuration: ID ${config.ConfigLaneID}, Plaza: ${config.PlazaName}, Lane: ${config.LaneNumber}`);
      
      // Activate the configuration
      const activateQuery = `
        UPDATE tblLaneDigitalPayConfiguration
        SET ActiveStatus = 'Y',
            UpdatedBy = 'admin',
            UpdatedDateTime = GETDATE()
        WHERE ConfigLaneID = @configId
      `;
      
      await db.query(activateQuery, {
        configId: config.ConfigLaneID
      });
      
      console.log(`Activated configuration ID: ${config.ConfigLaneID}`);
      
      // Verify the update
      const verifyQuery = `
        SELECT ConfigLaneID, ActiveStatus
        FROM tblLaneDigitalPayConfiguration
        WHERE ConfigLaneID = @configId
      `;
      
      const verifyResult = await db.query(verifyQuery, {
        configId: config.ConfigLaneID
      });
      
      if (verifyResult.recordset.length > 0) {
        console.log(`Verification: Configuration ID ${config.ConfigLaneID} now has ActiveStatus = ${verifyResult.recordset[0].ActiveStatus}`);
      }
    } else {
      console.log('No inactive configurations found');
    }

    console.log('\nDigital Pay configuration activation complete');
  } catch (error) {
    console.error('Database connection or query error:', error);
  } finally {
    // Close the database connection
    try {
      await db.close();
      console.log('\nDatabase connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
  }
}

// Run the function
activateDigitalPayConfig();
