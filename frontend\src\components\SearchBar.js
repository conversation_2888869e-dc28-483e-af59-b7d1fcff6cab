import React from 'react';
import { Search } from 'lucide-react';

export default function SearchBar({ value, onChange, placeholder }) {
  return (
    <div className="relative">
      <Search 
        size={20} 
        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#727272]"
      />
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder || "Search..."}
        className="w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
      />
    </div>
  );
}
