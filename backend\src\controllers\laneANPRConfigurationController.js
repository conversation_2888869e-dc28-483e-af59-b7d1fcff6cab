const db = require('../config/database'); // Assuming db.js handles connection and parameterization
const { response<PERSON>and<PERSON> } = require('../Utils/ResponseHandler');

// Helper function to normalize boolean-like values to '1' or '0'
const normalizeBoolean = (value) => {
  // Handle SQL Server bit values (which might come as numbers)
  if (value === 1 || value === true || value === '1' || value === 'Y' || value === 'true') {
    return '1';
  }

  if (value === 0 || value === false || value === '0' || value === 'N' || value === 'false' || value === null || value === undefined) {
    return '0';
  }

  // For any other case, try to parse the value
  try {
    // If it's a string that can be parsed as a number
    const numValue = Number(value);
    if (!isNaN(numValue)) {
      return numValue > 0 ? '1' : '0';
    }

    // If it's a string that might be a boolean
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase().trim();
      if (lowerValue === 'true' || lowerValue === 'yes' || lowerValue === 'y') {
        return '1';
      }
      if (lowerValue === 'false' || lowerValue === 'no' || lowerValue === 'n') {
        return '0';
      }
    }
  } catch (e) {
    console.error(`Error parsing value ${value}:`, e);
  }

  return '0'; // Default to '0' if undefined or unexpected value
};

// Helper function to convert normalized '1'/'0' back to DB format if needed (e.g., 'Y'/'N')
// Adjust this based on the actual database column type and expected values
const denormalizeBooleanForDB = (value) => {
  // First normalize the value to ensure it's in a consistent format
  const normalizedValue = normalizeBoolean(value);

  // For SQL Server bit columns, we should use 1/0 (numeric)
  // This is the most likely format for SQL Server bit columns
  return normalizedValue === '1' ? 1 : 0;

  // Alternative formats based on database requirements:
  // return normalizedValue === '1' ? '1' : '0'; // String '1'/'0'
  // return normalizedValue === '1' ? 'Y' : 'N'; // String 'Y'/'N'
  // return normalizedValue === '1' ? true : false; // Boolean true/false
};

// Get all ANPR configurations with related details
exports.getAllANPRConfigurations = async (req, res) => {
  try {
    // Get user information from the request
    const user = req.user;

    // Base query with joins for related data
    let baseQuery = `
      SELECT
        anpr.*,
        p.PlazaName,
        cm.CompanyName,
        l.LaneNumber AS LaneNumberDetail
      FROM tblLaneANPRConfiguration anpr
      LEFT JOIN Plaza p ON anpr.PlazaID = p.Id
      LEFT JOIN tblCompanyMaster cm ON anpr.CompanyID = cm.Id
      LEFT JOIN tblLaneDetails l ON anpr.LaneID = l.LaneID
      WHERE anpr.ActiveStatus = 1
    `;

    const queryParams = {};

    // Apply filtering based on user role
    if (user.role === 'SuperAdmin') {
      // SuperAdmin can see all configurations
      // No additional filters needed
    } else if (user.role === 'CompanyAdmin') {
      // CompanyAdmin can only see configurations for their companies
      baseQuery += `
        AND anpr.CompanyID IN (
          SELECT CompanyId FROM UserCompany
          WHERE UserId = @userId AND IsActive = 1
        )
      `;
      queryParams.userId = user.id;
    } else if (user.role === 'PlazaManager') {
      // PlazaManager can only see configurations for their plazas
      baseQuery += `
        AND anpr.PlazaID IN (
          SELECT PlazaId FROM UserPlaza
          WHERE UserId = @userId AND IsActive = 1
        )
      `;
      queryParams.userId = user.id;
    }

    // Add ordering
    baseQuery += ` ORDER BY anpr.ANPRID DESC`;

    // Execute the query
    const result = await db.query(baseQuery, queryParams);
    const rows = result.recordset;

    // Normalize boolean-like fields
    const normalizedRows = rows.map(row => {
      return {
        ...row,
        flgEnableANPR: normalizeBoolean(row.flgEnableANPR),
        ActiveStatus: normalizeBoolean(row.ActiveStatus),
        AllowBlacklistedVehicle: normalizeBoolean(row.AllowBlacklistedVehicle)
      };
    });

    return responseHandler.success(res, normalizedRows, 'ANPR configurations retrieved successfully');
  } catch (error) {
    console.error('Error fetching ANPR configurations:', error);
    return responseHandler.error(res, error.message);
  }
};

// Get a single ANPR configuration by ID with related details
exports.getANPRConfigurationById = async (req, res) => {
  const { id } = req.params;
  try {
    // Get user information from the request
    const user = req.user;

    // Base query to get the ANPR configuration with related data
    let query = `
      SELECT
        anpr.*,
        p.PlazaName,
        cm.CompanyName,
        l.LaneNumber AS LaneNumberDetail
      FROM tblLaneANPRConfiguration anpr
      LEFT JOIN Plaza p ON anpr.PlazaID = p.Id
      LEFT JOIN tblCompanyMaster cm ON anpr.CompanyID = cm.Id
      LEFT JOIN tblLaneDetails l ON anpr.LaneID = l.LaneID
      WHERE anpr.ANPRID = @id AND anpr.ActiveStatus = 1
    `;

    const result = await db.query(query, { id });

    if (result.recordset.length === 0) {
      return responseHandler.notFound(res, 'ANPR configuration not found');
    }

    const anprConfig = result.recordset[0];

    // Check if user has access to this configuration based on role
    if (user.role !== 'SuperAdmin') {
      if (user.role === 'CompanyAdmin') {
        // Check if the configuration belongs to one of the user's companies
        const companyAccessQuery = `
          SELECT 1
          FROM UserCompany
          WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
        `;
        const companyAccessResult = await db.query(companyAccessQuery, {
          userId: user.id,
          companyId: anprConfig.CompanyID
        });

        if (companyAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this ANPR configuration');
        }
      } else if (user.role === 'PlazaManager') {
        // Check if the configuration belongs to one of the user's plazas
        const plazaAccessQuery = `
          SELECT 1
          FROM UserPlaza
          WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
        `;
        const plazaAccessResult = await db.query(plazaAccessQuery, {
          userId: user.id,
          plazaId: anprConfig.PlazaID
        });

        if (plazaAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have access to this ANPR configuration');
        }
      }
    }

    // Normalize boolean-like fields
    const normalizedRow = {
      ...anprConfig,
      flgEnableANPR: normalizeBoolean(anprConfig.flgEnableANPR),
      ActiveStatus: normalizeBoolean(anprConfig.ActiveStatus),
      AllowBlacklistedVehicle: normalizeBoolean(anprConfig.AllowBlacklistedVehicle)
    };

    return responseHandler.success(res, normalizedRow, 'ANPR configuration retrieved successfully');
  } catch (error) {
    console.error(`Error fetching ANPR configuration with ID ${id}:`, error);
    return responseHandler.error(res, error.message);
  }
};

// Create a new ANPR configuration
exports.createANPRConfiguration = async (req, res) => {
  const {
    PlazaID,
    CompanyID,
    LaneID,
    PMSLaneNumber,
    flgEnableANPR, // Expecting '1' or '0' from frontend
    ANPROrgID,
    ANPRLaneID,
    ANPRPublicKey,
    ANPRPrivateKey,
    ANPRSource,
    ANPRAPIURL,
    ANPRAPIURL2,
    ActiveStatus, // Expecting '1' or '0' from frontend
    AllowBlacklistedVehicle, // Expecting '1' or '0' from frontend
    ANPRVendor
  } = req.body;

  // Basic validation
  if (!PlazaID || !CompanyID || !LaneID) {
    return responseHandler.badRequest(res, 'PlazaID, CompanyID, and LaneID are required');
  }

  try {
    // Get user information from the request
    const user = req.user;

    // Check if user has access to create configuration for this company/plaza
    if (user.role !== 'SuperAdmin') {
      if (user.role === 'CompanyAdmin') {
        // Check if the user has access to the company
        const companyAccessQuery = `
          SELECT 1
          FROM UserCompany
          WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
        `;
        const companyAccessResult = await db.query(companyAccessQuery, {
          userId: user.id,
          companyId: CompanyID
        });

        if (companyAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have permission to create ANPR configuration for this company');
        }
      } else if (user.role === 'PlazaManager') {
        // Check if the user has access to the plaza
        const plazaAccessQuery = `
          SELECT 1
          FROM UserPlaza
          WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
        `;
        const plazaAccessResult = await db.query(plazaAccessQuery, {
          userId: user.id,
          plazaId: PlazaID
        });

        if (plazaAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have permission to create ANPR configuration for this plaza');
        }
      }
    }

    // Convert normalized boolean values back to the format expected by the database
    const dbFlgEnableANPR = denormalizeBooleanForDB(flgEnableANPR);
    const dbActiveStatus = denormalizeBooleanForDB(ActiveStatus);
    const dbAllowBlacklistedVehicle = denormalizeBooleanForDB(AllowBlacklistedVehicle);

    // Use the authenticated user's ID for UpdatedBy
    const UpdatedBy = user.id;

    const query = `
      INSERT INTO tblLaneANPRConfiguration (
        PlazaID, CompanyID, LaneID, PMSLaneNumber, flgEnableANPR, ANPROrgID,
        ANPRLaneID, ANPRPublicKey, ANPRPrivateKey, ANPRSource, ANPRAPIURL,
        ANPRAPIURL2, ActiveStatus, UpdateDateTime, UpdatedBy,
        AllowBlacklistedVehicle, ANPRVendor
      ) VALUES (@PlazaID, @CompanyID, @LaneID, @PMSLaneNumber, @dbFlgEnableANPR, @ANPROrgID, @ANPRLaneID, @ANPRPublicKey, @ANPRPrivateKey, @ANPRSource, @ANPRAPIURL, @ANPRAPIURL2, @dbActiveStatus, GETDATE(), @UpdatedBy, @dbAllowBlacklistedVehicle, @ANPRVendor)
    `;

    const params = {
      PlazaID, CompanyID, LaneID, PMSLaneNumber, dbFlgEnableANPR, ANPROrgID,
      ANPRLaneID, ANPRPublicKey, ANPRPrivateKey, ANPRSource, ANPRAPIURL,
      ANPRAPIURL2, dbActiveStatus, UpdatedBy,
      dbAllowBlacklistedVehicle, ANPRVendor
    };

    const result = await db.query(query, params);

    return responseHandler.created(res, { ANPRID: result.insertId }, 'ANPR configuration created successfully');
  } catch (error) {
    console.error('Error creating ANPR configuration:', error);
    return responseHandler.error(res, error.message);
  }
};

// Update an existing ANPR configuration
exports.updateANPRConfiguration = async (req, res) => {
  const { id } = req.params;
  const {
    PlazaID,
    CompanyID,
    LaneID,
    PMSLaneNumber,
    flgEnableANPR, // Expecting '1' or '0' from frontend
    ANPROrgID,
    ANPRLaneID,
    ANPRPublicKey,
    ANPRPrivateKey,
    ANPRSource,
    ANPRAPIURL,
    ANPRAPIURL2,
    ActiveStatus, // Expecting '1' or '0' from frontend
    AllowBlacklistedVehicle, // Expecting '1' or '0' from frontend
    ANPRVendor
  } = req.body;

  try {
    // Get user information from the request
    const user = req.user;

    // First, check if the ANPR configuration exists
    const checkQuery = `
      SELECT anpr.*, p.CompanyId as PlazaCompanyId
      FROM tblLaneANPRConfiguration anpr
      LEFT JOIN Plaza p ON anpr.PlazaID = p.Id
      WHERE anpr.ANPRID = @id
    `;

    const checkResult = await db.query(checkQuery, { id });

    if (checkResult.recordset.length === 0) {
      return responseHandler.notFound(res, 'ANPR configuration not found');
    }

    const existingConfig = checkResult.recordset[0];

    // Check if user has access to update this configuration
    if (user.role !== 'SuperAdmin') {
      if (user.role === 'CompanyAdmin') {
        // Check if the configuration belongs to one of the user's companies
        const companyAccessQuery = `
          SELECT 1
          FROM UserCompany
          WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
        `;
        const companyAccessResult = await db.query(companyAccessQuery, {
          userId: user.id,
          companyId: existingConfig.CompanyID
        });

        if (companyAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have permission to update this ANPR configuration');
        }

        // If updating to a different company, check if user has access to the new company
        if (CompanyID && CompanyID !== existingConfig.CompanyID) {
          const newCompanyAccessResult = await db.query(companyAccessQuery, {
            userId: user.id,
            companyId: CompanyID
          });

          if (newCompanyAccessResult.recordset.length === 0) {
            return responseHandler.forbidden(res, 'You do not have permission to update to the specified company');
          }
        }
      } else if (user.role === 'PlazaManager') {
        // Check if the configuration belongs to one of the user's plazas
        const plazaAccessQuery = `
          SELECT 1
          FROM UserPlaza
          WHERE UserId = @userId AND PlazaId = @plazaId AND IsActive = 1
        `;
        const plazaAccessResult = await db.query(plazaAccessQuery, {
          userId: user.id,
          plazaId: existingConfig.PlazaID
        });

        if (plazaAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have permission to update this ANPR configuration');
        }

        // If updating to a different plaza, check if user has access to the new plaza
        if (PlazaID && PlazaID !== existingConfig.PlazaID) {
          const newPlazaAccessResult = await db.query(plazaAccessQuery, {
            userId: user.id,
            plazaId: PlazaID
          });

          if (newPlazaAccessResult.recordset.length === 0) {
            return responseHandler.forbidden(res, 'You do not have permission to update to the specified plaza');
          }
        }
      }
    }

    // Convert normalized boolean values back to the format expected by the database
    const dbFlgEnableANPR = denormalizeBooleanForDB(flgEnableANPR);
    const dbActiveStatus = denormalizeBooleanForDB(ActiveStatus);
    const dbAllowBlacklistedVehicle = denormalizeBooleanForDB(AllowBlacklistedVehicle);

    // Use the authenticated user's ID for UpdatedBy
    const UpdatedBy = user.id;

    const query = `
      UPDATE tblLaneANPRConfiguration SET
        PlazaID = @PlazaID, CompanyID = @CompanyID, LaneID = @LaneID, PMSLaneNumber = @PMSLaneNumber, flgEnableANPR = @dbFlgEnableANPR,
        ANPROrgID = @ANPROrgID, ANPRLaneID = @ANPRLaneID, ANPRPublicKey = @ANPRPublicKey, ANPRPrivateKey = @ANPRPrivateKey,
        ANPRSource = @ANPRSource, ANPRAPIURL = @ANPRAPIURL, ANPRAPIURL2 = @ANPRAPIURL2, ActiveStatus = @dbActiveStatus,
        UpdateDateTime = GETDATE(), UpdatedBy = @UpdatedBy, AllowBlacklistedVehicle = @dbAllowBlacklistedVehicle, ANPRVendor = @ANPRVendor
      WHERE ANPRID = @id
    `;

    const params = {
      PlazaID, CompanyID, LaneID, PMSLaneNumber, dbFlgEnableANPR,
      ANPROrgID, ANPRLaneID, ANPRPublicKey, ANPRPrivateKey,
      ANPRSource, ANPRAPIURL, ANPRAPIURL2, dbActiveStatus,
      UpdatedBy, dbAllowBlacklistedVehicle, ANPRVendor,
      id
    };

    const result = await db.query(query, params);

    if (result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'ANPR configuration not found or no changes made');
    }

    // Fetch the updated record to return it
    const updatedResult = await db.query(`
      SELECT
        anpr.*,
        p.PlazaName,
        cm.CompanyName,
        l.LaneNumber AS LaneNumberDetail
      FROM tblLaneANPRConfiguration anpr
      LEFT JOIN Plaza p ON anpr.PlazaID = p.Id
      LEFT JOIN tblCompanyMaster cm ON anpr.CompanyID = cm.Id
      LEFT JOIN tblLaneDetails l ON anpr.LaneID = l.LaneID
      WHERE anpr.ANPRID = @id
    `, { id });

    if (updatedResult.recordset.length === 0) {
      return responseHandler.notFound(res, 'Updated ANPR configuration could not be retrieved');
    }

    // Normalize the updated record before sending
    const normalizedUpdatedRow = {
      ...updatedResult.recordset[0],
      flgEnableANPR: normalizeBoolean(updatedResult.recordset[0].flgEnableANPR),
      ActiveStatus: normalizeBoolean(updatedResult.recordset[0].ActiveStatus),
      AllowBlacklistedVehicle: normalizeBoolean(updatedResult.recordset[0].AllowBlacklistedVehicle)
    };

    return responseHandler.success(res, normalizedUpdatedRow, 'ANPR configuration updated successfully');
  } catch (error) {
    console.error(`Error updating ANPR configuration with ID ${id}:`, error);
    return responseHandler.error(res, error.message);
  }
};

// Delete an ANPR configuration (Soft delete - setting ActiveStatus to 0)
exports.deleteANPRConfiguration = async (req, res) => {
  const { id } = req.params;

  if (!id) {
    return responseHandler.badRequest(res, 'ANPR Configuration ID is required for deletion');
  }

  try {
    // Get user information from the request
    const user = req.user;

    // First, check if the ANPR configuration exists
    const checkQuery = `
      SELECT anpr.*, p.CompanyId as PlazaCompanyId
      FROM tblLaneANPRConfiguration anpr
      LEFT JOIN Plaza p ON anpr.PlazaID = p.Id
      WHERE anpr.ANPRID = @id
    `;

    const checkResult = await db.query(checkQuery, { id });

    if (checkResult.recordset.length === 0) {
      return responseHandler.notFound(res, 'ANPR configuration not found');
    }

    const existingConfig = checkResult.recordset[0];

    // Check if user has access to delete this configuration
    if (user.role !== 'SuperAdmin') {
      if (user.role === 'CompanyAdmin') {
        // Check if the configuration belongs to one of the user's companies
        const companyAccessQuery = `
          SELECT 1
          FROM UserCompany
          WHERE UserId = @userId AND CompanyId = @companyId AND IsActive = 1
        `;
        const companyAccessResult = await db.query(companyAccessQuery, {
          userId: user.id,
          companyId: existingConfig.CompanyID
        });

        if (companyAccessResult.recordset.length === 0) {
          return responseHandler.forbidden(res, 'You do not have permission to delete this ANPR configuration');
        }
      } else if (user.role === 'PlazaManager') {
        // PlazaManager should not be able to delete configurations
        return responseHandler.forbidden(res, 'Plaza Managers do not have permission to delete ANPR configurations');
      }
    }

    // Use the authenticated user's ID for UpdatedBy
    const UpdatedBy = user.id;

    // Soft Delete - set ActiveStatus to 0
    // This will ensure the record is not shown on the frontend
    const query = `
      UPDATE tblLaneANPRConfiguration SET
        ActiveStatus = 0,
        UpdateDateTime = GETDATE(),
        UpdatedBy = @UpdatedBy
      WHERE ANPRID = @id
    `;

    const result = await db.query(query, { UpdatedBy, id });

    if (result.rowsAffected && result.rowsAffected[0] === 0) {
      return responseHandler.notFound(res, 'ANPR configuration not found');
    }

    return responseHandler.success(res, null, 'ANPR configuration deleted successfully');
  } catch (error) {
    console.error(`Error deleting ANPR configuration with ID ${id}:`, error);
    return responseHandler.error(res, error.message);
  }
};