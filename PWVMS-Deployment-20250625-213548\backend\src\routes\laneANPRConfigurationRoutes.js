const express = require('express');
const router = express.Router();
const laneANPRController = require('../controllers/laneANPRConfigurationController');
const auth = require('../middleware/auth');

/**
 * @route   GET /api/anpr-configs
 * @desc    Get all ANPR configurations
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/', auth(['View']), laneANPRController.getAllANPRConfigurations);

/**
 * @route   GET /api/anpr-configs/:id
 * @desc    Get a single ANPR configuration by ID
 * @access  Private (SuperAdmin, CompanyAdmin, PlazaManager)
 */
router.get('/:id', auth(['View']), laneANPRController.getANPRConfigurationById);

/**
 * @route   POST /api/anpr-configs
 * @desc    Create a new ANPR configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.post('/', auth(['Create']), laneANPRController.createANPRConfiguration);

/**
 * @route   PUT /api/anpr-configs/:id
 * @desc    Update an existing ANPR configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.put('/:id', auth(['Edit']), laneANPRController.updateANPRConfiguration);

/**
 * @route   DELETE /api/anpr-configs/:id
 * @desc    Delete an ANPR configuration
 * @access  Private (SuperAdmin, CompanyAdmin)
 */
router.delete('/:id', auth(['Delete']), laneANPRController.deleteANPRConfiguration);

module.exports = router;