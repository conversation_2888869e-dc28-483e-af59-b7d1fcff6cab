const express = require('express');
const router = express.Router();
const permissionController = require('../controllers/PermissionManagementController');
const auth = require('../middleware/auth');

console.log('🔍 Permission Management Routes loaded');

/**
 * Permission Management Routes
 * All routes require authentication (except test endpoints)
 */

// Test endpoint WITHOUT authentication to verify basic functionality
router.get('/test-no-auth', (req, res) => {
  console.log('🔍 Test-no-auth endpoint hit');
  res.json({
    success: true,
    message: 'Permission management routes are working (no auth)',
    timestamp: new Date().toISOString()
  });
});

// Simple database test WITHOUT authentication
router.get('/test-db-no-auth', async (req, res) => {
  console.log('🔍 Testing database without auth...');
  
  try {
    const db = require('../config/database');
    const result = await db.query('SELECT TOP 1 Id, Name FROM Modules');
    
    res.json({
      success: true,
      message: 'Database test successful (no auth)',
      data: result.recordset[0]
    });
    
  } catch (error) {
    console.error('❌ Database test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Apply authentication middleware to all other routes
router.use(auth());

// Test endpoint to verify routes are working
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Permission management routes are working',
    user: req.user?.username || 'Unknown'
  });
});

// Get complete modules tree with submodules and permissions
router.get('/modules-tree', permissionController.getModulesTree);

// Get all roles
router.get('/roles', permissionController.getRoles);

// Get permissions for a specific role
router.get('/roles/:roleId/permissions', permissionController.getRolePermissions);

// Update permissions for a specific role
router.put('/roles/:roleId/permissions', permissionController.updateRolePermissions);

// Create new role
router.post('/roles', permissionController.createRole);

// Update role
router.put('/roles/:roleId', permissionController.updateRole);

// Delete role (soft delete)
router.delete('/roles/:roleId', permissionController.deleteRole);

// Get permission matrix for all roles
router.get('/matrix', permissionController.getPermissionMatrix);

module.exports = router;