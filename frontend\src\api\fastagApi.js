import api from '../services/api'; // Shared Axios instance

/**
 * Fastag API Service
 * Provides methods for interacting with the Fastag configuration endpoints
 * Includes advanced features like request caching, error handling, and request cancellation
 */
export const fastagApi = {
  // Cache for storing responses to reduce redundant network requests
  _cache: {
    data: {},
    timestamp: {},
    ttl: 60000, // Cache time-to-live in milliseconds (1 minute)
  },

  // Request cancellation tokens
  _cancelTokens: {},

  /**
   * Clears the cache for a specific endpoint or all endpoints
   * @param {string} endpoint - Optional endpoint to clear cache for
   */
  clearCache: (endpoint) => {
    if (endpoint) {
      delete fastagApi._cache.data[endpoint];
      delete fastagApi._cache.timestamp[endpoint];
    } else {
      fastagApi._cache.data = {};
      fastagApi._cache.timestamp = {};
    }
  },

  /**
   * Cancels an ongoing request for a specific endpoint
   * @param {string} endpoint - The endpoint to cancel requests for
   */
  cancelRequest: (endpoint) => {
    if (fastagApi._cancelTokens[endpoint]) {
      fastagApi._cancelTokens[endpoint]();
      delete fastagApi._cancelTokens[endpoint];
    }
  },

  /**
   * Fetches the list of all Fastag configurations with optional filtering
   * GET /fastag
   * @param {Object} options - Optional parameters for filtering, pagination, etc.
   * @param {boolean} useCache - Whether to use cached data if available
   * @returns {Promise<Array>} List of Fastag configurations
   */
  getAllConfigurations: async (options = {}, useCache = true) => {
    const endpoint = '/fastag';
    const cacheKey = `${endpoint}${options ? JSON.stringify(options) : ''}`;
    
    // Check cache if enabled
    if (useCache && 
        fastagApi._cache.data[cacheKey] && 
        (Date.now() - fastagApi._cache.timestamp[cacheKey] < fastagApi._cache.ttl)) {
      return fastagApi._cache.data[cacheKey];
    }

    try {
      // Create a cancel token
      const CancelToken = api.CancelToken;
      const source = CancelToken.source();
      fastagApi._cancelTokens[endpoint] = source.cancel;

      // Add cache-busting parameter
      const timestamp = new Date().getTime();
      const queryParams = new URLSearchParams(options);
      queryParams.append('_', timestamp);

      const response = await api.get(`${endpoint}?${queryParams.toString()}`, {
        cancelToken: source.token
      });

      // Cache the response
      fastagApi._cache.data[cacheKey] = response.data.data;
      fastagApi._cache.timestamp[cacheKey] = Date.now();

      return response.data.data;
    } catch (error) {
      if (api.isCancel(error)) {
        console.log('Request canceled:', error.message);
      } else {
        console.error('Error fetching Fastag configurations:', error);
        throw error;
      }
    } finally {
      delete fastagApi._cancelTokens[endpoint];
    }
  },

  /**
   * Fetches the details of a single Fastag configuration by its ID
   * GET /fastag/:id
   * @param {string|number} id - The ID of the Fastag configuration
   * @param {boolean} useCache - Whether to use cached data if available
   * @returns {Promise<Object>} Fastag configuration details
   */
  getConfigurationById: async (id, useCache = true) => {
    const endpoint = `/fastag/${id}`;
    
    // Check cache if enabled
    if (useCache && 
        fastagApi._cache.data[endpoint] && 
        (Date.now() - fastagApi._cache.timestamp[endpoint] < fastagApi._cache.ttl)) {
      return fastagApi._cache.data[endpoint];
    }

    try {
      // Create a cancel token
      const CancelToken = api.CancelToken;
      const source = CancelToken.source();
      fastagApi._cancelTokens[endpoint] = source.cancel;

      const response = await api.get(endpoint, {
        cancelToken: source.token
      });

      // Cache the response
      fastagApi._cache.data[endpoint] = response.data.data;
      fastagApi._cache.timestamp[endpoint] = Date.now();

      return response.data.data;
    } catch (error) {
      if (api.isCancel(error)) {
        console.log('Request canceled:', error.message);
      } else {
        console.error(`Error fetching Fastag configuration with ID ${id}:`, error);
        throw error;
      }
    } finally {
      delete fastagApi._cancelTokens[endpoint];
    }
  },

  /**
   * Fetches Fastag configurations by plaza ID
   * GET /fastag/plaza/:plazaId
   * @param {string|number} plazaId - The ID of the plaza
   * @param {boolean} useCache - Whether to use cached data if available
   * @returns {Promise<Array>} List of Fastag configurations for the plaza
   */
  getConfigurationsByPlaza: async (plazaId, useCache = true) => {
    const endpoint = `/fastag/plaza/${plazaId}`;
    
    // Check cache if enabled
    if (useCache && 
        fastagApi._cache.data[endpoint] && 
        (Date.now() - fastagApi._cache.timestamp[endpoint] < fastagApi._cache.ttl)) {
      return fastagApi._cache.data[endpoint];
    }

    try {
      // Create a cancel token
      const CancelToken = api.CancelToken;
      const source = CancelToken.source();
      fastagApi._cancelTokens[endpoint] = source.cancel;

      const response = await api.get(endpoint, {
        cancelToken: source.token
      });

      // Cache the response
      fastagApi._cache.data[endpoint] = response.data.data;
      fastagApi._cache.timestamp[endpoint] = Date.now();

      return response.data.data;
    } catch (error) {
      if (api.isCancel(error)) {
        console.log('Request canceled:', error.message);
      } else {
        console.error(`Error fetching Fastag configurations for plaza ${plazaId}:`, error);
        throw error;
      }
    } finally {
      delete fastagApi._cancelTokens[endpoint];
    }
  },

  /**
   * Fetches Fastag configurations by lane ID
   * GET /fastag/lane/:laneId
   * @param {string|number} laneId - The ID of the lane
   * @param {boolean} useCache - Whether to use cached data if available
   * @returns {Promise<Array>} List of Fastag configurations for the lane
   */
  getConfigurationsByLane: async (laneId, useCache = true) => {
    const endpoint = `/fastag/lane/${laneId}`;
    
    // Check cache if enabled
    if (useCache && 
        fastagApi._cache.data[endpoint] && 
        (Date.now() - fastagApi._cache.timestamp[endpoint] < fastagApi._cache.ttl)) {
      return fastagApi._cache.data[endpoint];
    }

    try {
      // Create a cancel token
      const CancelToken = api.CancelToken;
      const source = CancelToken.source();
      fastagApi._cancelTokens[endpoint] = source.cancel;

      const response = await api.get(endpoint, {
        cancelToken: source.token
      });

      // Cache the response
      fastagApi._cache.data[endpoint] = response.data.data;
      fastagApi._cache.timestamp[endpoint] = Date.now();

      return response.data.data;
    } catch (error) {
      if (api.isCancel(error)) {
        console.log('Request canceled:', error.message);
      } else {
        console.error(`Error fetching Fastag configurations for lane ${laneId}:`, error);
        throw error;
      }
    } finally {
      delete fastagApi._cancelTokens[endpoint];
    }
  },

  /**
   * Creates a new Fastag configuration
   * POST /fastag
   * @param {Object} data - The Fastag configuration data
   * @returns {Promise<Object>} Created Fastag configuration
   */
  createConfiguration: async (data) => {
    try {
      const response = await api.post('/fastag', data);
      
      // Clear relevant caches after creating
      fastagApi.clearCache('/fastag');
      if (data.PlazaID) {
        fastagApi.clearCache(`/fastag/plaza/${data.PlazaID}`);
      }
      if (data.LaneID) {
        fastagApi.clearCache(`/fastag/lane/${data.LaneID}`);
      }
      
      return response.data;
    } catch (error) {
      console.error('Error creating Fastag configuration:', error);
      throw error;
    }
  },

  /**
   * Updates a Fastag configuration by ID
   * PUT /fastag/:id
   * @param {string|number} id - The ID of the Fastag configuration
   * @param {Object} data - The updated Fastag configuration data
   * @returns {Promise<Object>} Updated Fastag configuration
   */
  updateConfiguration: async (id, data) => {
    try {
      const response = await api.put(`/fastag/${id}`, data);
      
      // Clear relevant caches after updating
      fastagApi.clearCache(`/fastag/${id}`);
      fastagApi.clearCache('/fastag');
      if (data.PlazaID) {
        fastagApi.clearCache(`/fastag/plaza/${data.PlazaID}`);
      }
      if (data.LaneID) {
        fastagApi.clearCache(`/fastag/lane/${data.LaneID}`);
      }
      
      return response.data;
    } catch (error) {
      console.error(`Error updating Fastag configuration with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Deletes a Fastag configuration by ID
   * DELETE /fastag/:id
   * @param {string|number} id - The ID of the Fastag configuration
   * @returns {Promise<void>}
   */
  deleteConfiguration: async (id) => {
    try {
      // First get the configuration to know which caches to clear
      const config = await fastagApi.getConfigurationById(id, false);
      
      await api.delete(`/fastag/${id}`);
      
      // Clear relevant caches after deleting
      fastagApi.clearCache(`/fastag/${id}`);
      fastagApi.clearCache('/fastag');
      if (config && config.PlazaID) {
        fastagApi.clearCache(`/fastag/plaza/${config.PlazaID}`);
      }
      if (config && config.LaneID) {
        fastagApi.clearCache(`/fastag/lane/${config.LaneID}`);
      }
    } catch (error) {
      console.error(`Error deleting Fastag configuration with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Bulk create or update multiple Fastag configurations
   * POST /fastag/bulk
   * @param {Array<Object>} data - Array of Fastag configuration data
   * @returns {Promise<Object>} Result of the bulk operation
   */
  bulkCreateOrUpdate: async (data) => {
    try {
      const response = await api.post('/fastag/bulk', data);
      
      // Clear all caches after bulk operation
      fastagApi.clearCache();
      
      return response.data;
    } catch (error) {
      console.error('Error performing bulk Fastag configuration operation:', error);
      throw error;
    }
  },

  /**
   * Toggles the active status of a Fastag configuration
   * PATCH /fastag/:id/toggle-status
   * @param {string|number} id - The ID of the Fastag configuration
   * @param {string} updatedBy - The user who is updating the status
   * @returns {Promise<Object>} Updated Fastag configuration
   */
  toggleConfigurationStatus: async (id, updatedBy = 'admin') => {
    try {
      const response = await api.patch(`/fastag/${id}/toggle-status`, { UpdatedBy: updatedBy });
      
      // Clear relevant caches after toggling status
      fastagApi.clearCache(`/fastag/${id}`);
      fastagApi.clearCache('/fastag');
      
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for Fastag configuration with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Validates a Fastag configuration before saving
   * POST /fastag/validate
   * @param {Object} data - The Fastag configuration data to validate
   * @returns {Promise<Object>} Validation result
   */
  validateConfiguration: async (data) => {
    try {
      const response = await api.post('/fastag/validate', data);
      return response.data;
    } catch (error) {
      console.error('Error validating Fastag configuration:', error);
      throw error;
    }
  },

  /**
   * Tests the connection to a Fastag API endpoint
   * POST /fastag/test-connection
   * @param {Object} connectionData - The connection data to test
   * @returns {Promise<Object>} Connection test result
   */
  testConnection: async (connectionData) => {
    try {
      const response = await api.post('/fastag/test-connection', connectionData);
      return response.data;
    } catch (error) {
      console.error('Error testing Fastag API connection:', error);
      throw error;
    }
  },

  /**
   * Exports Fastag configurations to CSV or Excel format
   * GET /fastag/export
   * @param {string} format - The export format ('csv' or 'excel')
   * @param {Object} filters - Optional filters to apply before exporting
   * @returns {Promise<Blob>} The exported file as a Blob
   */
  exportConfigurations: async (format = 'csv', filters = {}) => {
    try {
      const queryParams = new URLSearchParams(filters);
      queryParams.append('format', format);
      
      const response = await api.get(`/fastag/export?${queryParams.toString()}`, {
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error) {
      console.error('Error exporting Fastag configurations:', error);
      throw error;
    }
  },

  /**
   * Imports Fastag configurations from a file
   * POST /fastag/import
   * @param {File} file - The file to import
   * @returns {Promise<Object>} Import result
   */
  importConfigurations: async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await api.post('/fastag/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      // Clear all caches after import
      fastagApi.clearCache();
      
      return response.data;
    } catch (error) {
      console.error('Error importing Fastag configurations:', error);
      throw error;
    }
  }
};

export default fastagApi;