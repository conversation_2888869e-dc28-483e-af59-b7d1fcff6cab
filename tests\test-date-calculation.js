// Copy of the calculateDateRange function from the dashboard controller
function calculateDateRange(dateRange) {
  // Use current date as the reference date
  const referenceDate = new Date();
  let startDate, endDate;
  
  // Check if dateRange is a specific date in YYYY-MM-DD format
  if (dateRange && dateRange.match(/^\d{4}-\d{2}-\d{2}$/)) {
    // It's a specific date
    startDate = new Date(dateRange + 'T00:00:00.000Z');
    endDate = new Date(dateRange + 'T23:59:59.999Z');
    
    console.log(`Using specific date: ${dateRange}`);
  } else {
    // It's a predefined range
    switch(dateRange) {
      case 'today':
        // For today, use current date
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        // For yesterday, use current date - 1 day
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setDate(endDate.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        // For week, use the last 7 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 6);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'month':
        // For month, use the last 30 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 29);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'year':
        // For year, use the last 365 days
        startDate = new Date(referenceDate);
        startDate.setDate(startDate.getDate() - 364);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
        break;
      default:
        // Default to today
        startDate = new Date(referenceDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(referenceDate);
        endDate.setHours(23, 59, 59, 999);
    }
  
  return { startDate, endDate };
}

// Test the date calculation for different date ranges
function testDateCalculation() {
  console.log('Testing date calculation for different date ranges:');
  console.log('Current date:', new Date().toISOString());
  
  const dateRanges = ['today', 'yesterday', 'week', 'month', 'year'];
  
  dateRanges.forEach(range => {
    const { startDate, endDate } = calculateDateRange(range);
    console.log(`\nDate range for '${range}':`);
    console.log('- Start date:', startDate.toISOString());
    console.log('- End date:', endDate.toISOString());
    console.log('- Duration in days:', (endDate - startDate) / (1000 * 60 * 60 * 24));
  });
  
  // Test specific dates
  const specificDates = ['2025-06-21', '2023-01-15', '2024-12-31'];
  
  specificDates.forEach(date => {
    const { startDate, endDate } = calculateDateRange(date);
    console.log(`\nDate range for specific date '${date}':`);
    console.log('- Start date:', startDate.toISOString());
    console.log('- End date:', endDate.toISOString());
    console.log('- Duration in days:', (endDate - startDate) / (1000 * 60 * 60 * 24));
  });
}

testDateCalculation();