import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Plus } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { companyApi } from '../api/companyApi';
import CompanyList from '../components/Company/CompanyList';
import CompanyDialog from '../components/Company/CompanyDialog';
import { useAuth } from '../contexts/authContext';
import usePermissionFilter from '../hooks/usePermissionFilter';
import { PermissionButton } from '../components/auth/PermissionButton';

export default function CompanyManagement() {
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user, hasPermission } = useAuth();
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCompany, setEditingCompany] = useState(null);

  // Pagination state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Sorting state
  const [sortConfig, setSortConfig] = useState({ key: 'CompanyName', direction: 'asc' });

  // Filter state
  const [filters, setFilters] = useState({});

  // Get companies data
  const { data, isLoading: companiesLoading } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
  });

  // Make sure we have the correct data structure
  // If the API returns the data directly rather than in a specific shape:
  const rawCompanies = Array.isArray(data) ? data : (data?.companies || []);

  // Apply permission filtering to companies data
  const { filteredData: companies, canCreate, canEdit, canDelete } = usePermissionFilter(
    rawCompanies,
    { companyIdField: 'Id' }
  );

  const totalCompanies = companies.length;

  const createMutation = useMutation({
    mutationFn: companyApi.createCompany,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast.showCrudSuccess('create', 'Company');
      setDialogOpen(false);
    },
    onError: (error) => {
      toast.showCrudError('create', 'Company', error.response?.data?.message);
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => companyApi.updateCompany(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast.showCrudSuccess('update', 'Company');
      setDialogOpen(false);
      setEditingCompany(null);
    },
    onError: (error) => {
      toast.showCrudError('update', 'Company', error.response?.data?.message);
    },
  });

  const deleteMutation = useMutation({
    mutationFn: companyApi.deleteCompany,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['companies'] });
      toast.showCrudSuccess('delete', 'Company');
      if (selectedCompany) setSelectedCompany(null);
    },
    onError: (error) => {
      toast.showCrudError('delete', 'Company', error.response?.data?.message);
    },
  });

  // Simplified handlers for CompanyList props
  const handleSort = (newSortConfig) => {
    setSortConfig(newSortConfig);
  };

  const handleFilter = (newFilters) => {
    setFilters(newFilters);
    setPage(1);
  };

  const handleClearFilters = () => {
    setFilters({});
    setPage(1);
  };

  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize) => {
    setPageSize(newPageSize);
    setPage(1);
  };

  const handleSubmit = (data) => {
    if (editingCompany) {
      // Check if user has permission to edit this company
      if (canEdit(editingCompany, 'Companies')) {
        updateMutation.mutate({ id: editingCompany.Id, data });
      } else {
        toast.showError('You do not have permission to edit this company');
      }
    } else {
      // Check if user has permission to create companies
      if (canCreate('Companies')) {
        createMutation.mutate(data);
      } else {
        toast.showError('You do not have permission to create companies');
      }
    }
  };

  const navigate = useNavigate();

  const handleEdit = (company) => {
    // Check if user has permission to edit this company
    if (canEdit(company, 'Companies')) {
      setEditingCompany(company);
      setDialogOpen(true);
    } else {
      // Check if user is CompanyAdmin
      if (user && user.role === 'CompanyAdmin') {
        // Redirect to unauthorized page with custom message
        navigate('/unauthorized', {
          state: { message: 'Company Admins can view companies but cannot edit them' }
        });
      } else {
        toast.showError('You do not have permission to edit this company');
      }
    }
  };

  const handleDelete = (id) => {
    // Find the company by ID
    const company = companies.find(c => c.Id === id);

    // Check if user has permission to delete this company
    if (company && canDelete(company, 'Companies')) {
      if (window.confirm('Are you sure you want to delete this company?')) {
        deleteMutation.mutate(id);
      }
    } else {
      // Check if user is CompanyAdmin
      if (user && user.role === 'CompanyAdmin') {
        // Redirect to unauthorized page with custom message
        navigate('/unauthorized', {
          state: { message: 'Company Admins can view companies but cannot delete them' }
        });
      } else {
        toast.showError('You do not have permission to delete this company');
      }
    }
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingCompany(null);
  };

  // Client-side pagination, sorting, and filtering
  // This is a fallback in case your API doesn't support these operations
  let displayedCompanies = [...companies];

  // Apply filters
  if (filters.search) {
    const searchTerm = filters.search.toLowerCase();
    displayedCompanies = displayedCompanies.filter(company =>
      company.CompanyName?.toLowerCase().includes(searchTerm) ||
      company.CompanyCode?.toLowerCase().includes(searchTerm) ||
      company.ContactPerson?.toLowerCase().includes(searchTerm) ||
      company.ContactEmail?.toLowerCase().includes(searchTerm)
    );
  }

  if (filters.IsActive !== undefined) {
    displayedCompanies = displayedCompanies.filter(company =>
      company.IsActive === filters.IsActive
    );
  }

  // Apply sorting
  if (sortConfig.key) {
    displayedCompanies.sort((a, b) => {
      if (a[sortConfig.key] < b[sortConfig.key]) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (a[sortConfig.key] > b[sortConfig.key]) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  // Apply pagination
  const paginatedCompanies = displayedCompanies.slice(
    (page - 1) * pageSize,
    page * pageSize
  );

  if (companiesLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Company Management</h1>
          <PermissionButton
            requiredModule="Companies"
            requiredPermissions={["Create"]}
            onClick={() => setDialogOpen(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Company
          </PermissionButton>
        </div>

        <CompanyList
          companies={paginatedCompanies}
          totalCompanies={displayedCompanies.length}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSelect={setSelectedCompany}
          loading={companiesLoading}
          page={page}
          pageSize={pageSize}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          sortConfig={sortConfig}
          onSort={handleSort}
          filters={filters}
          onFilter={handleFilter}
          onClearFilters={handleClearFilters}
        />

        <CompanyDialog
          isOpen={dialogOpen}
          onClose={handleCloseDialog}
          onSubmit={handleSubmit}
          initialData={
            editingCompany
              ? {
                  Id: editingCompany.Id,
                  CompanyName: editingCompany.CompanyName,
                  CompanyCode: editingCompany.CompanyCode,
                  ContactPerson: editingCompany.ContactPerson,
                  ContactNumber: editingCompany.ContactNumber,
                  ContactEmail: editingCompany.ContactEmail,
                  Address: editingCompany.Address,
                  IsActive: editingCompany.IsActive
                }
              : undefined
          }
          title={editingCompany ? 'Edit Company' : 'Add Company'}
        />
      </div>
    </div>
  );
}