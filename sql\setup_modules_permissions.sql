-- Setup Modules, SubModules, and Permissions for RBAC system

-- Create Modules table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Modules')
BEGIN
    CREATE TABLE Modules (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        Name NVARCHAR(100) NOT NULL,
        Description NVARCHAR(255),
        Icon NVARCHAR(50),
        DisplayOrder INT,
        IsActive BIT DEFAULT 1,
        CreatedBy INT,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedOn DATETIME
    );
    PRINT 'Modules table created successfully';
END
ELSE
BEGIN
    PRINT 'Modules table already exists';
END

-- Create SubModules table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'SubModules')
BEGIN
    CREATE TABLE SubModules (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        ModuleId INT FOREIGN KEY REFERENCES Modules(Id),
        Name NVARCHAR(100) NOT NULL,
        Description NVARCHAR(255),
        Route NVARCHAR(100),
        Icon NVARCHAR(50),
        DisplayOrder INT,
        IsActive BIT DEFAULT 1,
        CreatedBy INT,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT,
        ModifiedOn DATETIME
    );
    PRINT 'SubModules table created successfully';
END
ELSE
BEGIN
    PRINT 'SubModules table already exists';
END

-- Insert Modules
IF NOT EXISTS (SELECT 1 FROM Modules WHERE Name = 'Dashboard')
BEGIN
    INSERT INTO Modules (Name, Description, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ('Dashboard', 'Dashboard and analytics', 'dashboard', 1, 1, 1, GETDATE()),
    ('User Management', 'Manage users and permissions', 'people', 2, 1, 1, GETDATE()),
    ('Company Management', 'Manage companies', 'business', 3, 1, 1, GETDATE()),
    ('Plaza Management', 'Manage plazas', 'location_city', 4, 1, 1, GETDATE()),
    ('Lane Management', 'Manage lanes', 'linear_scale', 5, 1, 1, GETDATE()),
    ('Configuration', 'System configuration', 'settings', 6, 1, 1, GETDATE()),
    ('Reports', 'Reports and analytics', 'assessment', 7, 1, 1, GETDATE());
    PRINT 'Modules inserted successfully';
END
ELSE
BEGIN
    PRINT 'Modules already exist';
END

-- Insert SubModules
IF NOT EXISTS (SELECT 1 FROM SubModules WHERE Name = 'Dashboard Overview')
BEGIN
    -- Dashboard SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'Dashboard'), 'Dashboard Overview', 'Main dashboard', '/dashboard', 'dashboard', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Dashboard'), 'Analytics', 'Analytics dashboard', '/dashboard/analytics', 'analytics', 2, 1, 1, GETDATE());

    -- User Management SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'User Management'), 'Users', 'Manage users', '/users', 'people', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'User Management'), 'Roles', 'Manage roles', '/roles', 'admin_panel_settings', 2, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'User Management'), 'Permissions', 'Manage permissions', '/permissions', 'security', 3, 1, 1, GETDATE());

    -- Company Management SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'Company Management'), 'Companies', 'Manage companies', '/companies', 'business', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Company Management'), 'Company Settings', 'Company settings', '/company-settings', 'settings', 2, 1, 1, GETDATE());

    -- Plaza Management SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'Plaza Management'), 'Plazas', 'Manage plazas', '/plazas', 'location_city', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Plaza Management'), 'Plaza Settings', 'Plaza settings', '/plaza-settings', 'settings', 2, 1, 1, GETDATE());

    -- Lane Management SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'Lanes', 'Manage lanes', '/lanes', 'linear_scale', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'Lane Settings', 'Lane settings', '/lane-settings', 'settings', 2, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'Digital Payment', 'Digital payment settings', '/digital-payment', 'payment', 3, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'FASTag', 'FASTag settings', '/fastag', 'nfc', 4, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Lane Management'), 'ANPR', 'ANPR settings', '/anpr', 'videocam', 5, 1, 1, GETDATE());

    -- Configuration SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'System Settings', 'System settings', '/system-settings', 'settings', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'Countries', 'Manage countries', '/countries', 'public', 2, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'States', 'Manage states', '/states', 'map', 3, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Configuration'), 'Cities', 'Manage cities', '/cities', 'location_city', 4, 1, 1, GETDATE());

    -- Reports SubModules
    INSERT INTO SubModules (ModuleId, Name, Description, Route, Icon, DisplayOrder, IsActive, CreatedBy, CreatedOn)
    VALUES 
    ((SELECT Id FROM Modules WHERE Name = 'Reports'), 'Traffic Reports', 'Traffic reports', '/traffic-reports', 'assessment', 1, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Reports'), 'Revenue Reports', 'Revenue reports', '/revenue-reports', 'attach_money', 2, 1, 1, GETDATE()),
    ((SELECT Id FROM Modules WHERE Name = 'Reports'), 'User Activity', 'User activity reports', '/user-activity', 'history', 3, 1, 1, GETDATE());

    PRINT 'SubModules inserted successfully';
END
ELSE
BEGIN
    PRINT 'SubModules already exist';
END

-- Link SubModules with Permissions
IF NOT EXISTS (SELECT 1 FROM SubModulePermissions WHERE SubModuleId = (SELECT Id FROM SubModules WHERE Name = 'Users') AND PermissionId = (SELECT Id FROM Permissions WHERE Name = 'View'))
BEGIN
    -- For each SubModule, add all relevant permissions
    DECLARE @subModuleId INT
    DECLARE @viewPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'View')
    DECLARE @createPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Create')
    DECLARE @editPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Edit')
    DECLARE @deletePermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Delete')
    DECLARE @exportPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Export')
    DECLARE @importPermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Import')
    DECLARE @approvePermissionId INT = (SELECT Id FROM Permissions WHERE Name = 'Approve')

    -- Cursor to iterate through all SubModules
    DECLARE submodule_cursor CURSOR FOR 
    SELECT Id FROM SubModules WHERE IsActive = 1

    OPEN submodule_cursor
    FETCH NEXT FROM submodule_cursor INTO @subModuleId

    WHILE @@FETCH_STATUS = 0
    BEGIN
        -- Add View permission to all SubModules
        INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
        VALUES (@subModuleId, @viewPermissionId, 1, 1, GETDATE())

        -- Add Create, Edit, Delete permissions to all SubModules except Dashboard and Reports
        IF EXISTS (SELECT 1 FROM SubModules sm 
                  JOIN Modules m ON sm.ModuleId = m.Id 
                  WHERE sm.Id = @subModuleId AND m.Name NOT IN ('Dashboard', 'Reports'))
        BEGIN
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES 
            (@subModuleId, @createPermissionId, 1, 1, GETDATE()),
            (@subModuleId, @editPermissionId, 1, 1, GETDATE()),
            (@subModuleId, @deletePermissionId, 1, 1, GETDATE())
        END

        -- Add Export, Import permissions to Reports SubModules
        IF EXISTS (SELECT 1 FROM SubModules sm 
                  JOIN Modules m ON sm.ModuleId = m.Id 
                  WHERE sm.Id = @subModuleId AND m.Name = 'Reports')
        BEGIN
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES 
            (@subModuleId, @exportPermissionId, 1, 1, GETDATE()),
            (@subModuleId, @importPermissionId, 1, 1, GETDATE())
        END

        -- Add Approve permission to specific SubModules that need approval workflows
        IF EXISTS (SELECT 1 FROM SubModules 
                  WHERE Id = @subModuleId AND Name IN ('Users', 'Companies', 'Plazas', 'Lanes'))
        BEGIN
            INSERT INTO SubModulePermissions (SubModuleId, PermissionId, IsActive, CreatedBy, CreatedOn)
            VALUES (@subModuleId, @approvePermissionId, 1, 1, GETDATE())
        END

        FETCH NEXT FROM submodule_cursor INTO @subModuleId
    END

    CLOSE submodule_cursor
    DEALLOCATE submodule_cursor

    PRINT 'SubModulePermissions inserted successfully';
END
ELSE
BEGIN
    PRINT 'SubModulePermissions already exist';
END
