# ParkwizOps Permission System Documentation

## Overview

This document provides a comprehensive overview of the permission system implemented in the ParkwizOps application. The permission system is designed to control access to various features and functionalities based on user roles. This hierarchical approach ensures that users only have access to the features necessary for their responsibilities.

## System Architecture

The permission system is built on four key database tables:

1. **Roles**: Defines the user roles in the system
2. **SubModules**: Represents individual features or pages in the application
3. **SubModulePermissions**: Links permissions to specific submodules
4. **RolePermissions**: Associates roles with specific submodule permissions

## User Roles

The system has three primary roles, each with different levels of access:

| Role | Description | Permission Level |
|------|-------------|-----------------|
| SuperAdmin | System administrators with complete access | Highest |
| CompanyAdmin | Company-level administrators | Medium |
| PlazaManager | Plaza-level managers | Limited |

## Permission Types

The system implements six types of permissions:

| Permission | Description |
|------------|-------------|
| View | Ability to see data and information |
| Create | Ability to add new records |
| Edit | Ability to modify existing records |
| Delete | Ability to remove records |
| Export | Ability to export data |
| Import | Ability to import data |

## Module Structure

The application is organized into 9 primary modules, each containing multiple submodules:

1. **Dashboard**
2. **User Management**
3. **Company Management**
4. **Plaza Management**
   - Plazas (/manage-plazas)
   - Plaza Settings (/plaza-settings)
5. **Lane Management**
6. **Configuration**
7. **Reports**
   - Traffic Reports (/traffic-reports)
   - Revenue Reports (/revenue-reports)
   - User Activity (/user-activity)
8. **Transactions**
9. **Monitoring**

## Detailed Role Permissions

### SuperAdmin Role

SuperAdmin has complete access to all modules and features in the system.

**Total Permissions**: 107
- View: 27
- Create: 26
- Edit: 26
- Delete: 26
- Export: 1
- Import: 1

#### Module Access:

1. **Dashboard (2 submodules, 8 permissions)**
   - Dashboard Overview: View, Create, Edit, Delete
   - Analytics: View, Create, Edit, Delete

2. **User Management (3 submodules, 12 permissions)**
   - Users: View, Create, Edit, Delete
   - Roles: View, Create, Edit, Delete
   - Permissions: View, Create, Edit, Delete

3. **Company Management (2 submodules, 8 permissions)**
   - Companies: View, Create, Edit, Delete
   - Company Settings: View, Create, Edit, Delete

4. **Plaza Management (2 submodules, 8 permissions)**
   - Plazas: View, Create, Edit, Delete
   - Plaza Settings: View, Create, Edit, Delete

5. **Lane Management (4 submodules, 16 permissions)**
   - Lanes: View, Create, Edit, Delete
   - Lane Settings: View, Create, Edit, Delete
   - UHF Reader: View, Create, Edit, Delete
   - Pass Registration: View, Create, Edit, Delete

6. **Configuration (7 submodules, 27 permissions)**
   - System Settings: View, Create, Edit, Delete
   - Email Templates: View, Create, Edit, Delete
   - SMS Templates: View, Create, Edit, Delete
   - URL Mapping: View, Create, Edit, Delete
   - SMTP Configuration: View, Create, Edit, Delete
   - Countries: View, Create, Edit, Delete
   - States: View, Create, Edit, Delete, Export, Import

7. **Reports (3 submodules, 12 permissions)**
   - Traffic Reports: View, Create, Edit, Delete
   - Revenue Reports: View, Create, Edit, Delete
   - User Activity: View, Create, Edit, Delete

8. **Transactions (3 submodules, 12 permissions)**
   - Payment Transactions: View, Create, Edit, Delete
   - Vehicle Entries: View, Create, Edit, Delete
   - Transaction History: View, Create, Edit, Delete

9. **Monitoring (3 submodules, 12 permissions)**
   - Lane Status: View, Create, Edit, Delete
   - System Health: View, Create, Edit, Delete
   - Alerts: View, Create, Edit, Delete

### CompanyAdmin Role

CompanyAdmin has access to most modules except Dashboard and Plaza Management.

**Total Permissions**: 91
- View: 23
- Create: 22
- Edit: 22
- Delete: 22
- Export: 1
- Import: 1

#### Module Access:

1. **User Management (3 submodules, 12 permissions)**
   - Users: View, Create, Edit, Delete
   - Roles: View, Create, Edit, Delete
   - Permissions: View, Create, Edit, Delete

2. **Company Management (2 submodules, 8 permissions)**
   - Companies: View, Create, Edit, Delete
   - Company Settings: View, Create, Edit, Delete

3. **Lane Management (4 submodules, 16 permissions)**
   - Lanes: View, Create, Edit, Delete
   - Lane Settings: View, Create, Edit, Delete
   - UHF Reader: View, Create, Edit, Delete
   - Pass Registration: View, Create, Edit, Delete

4. **Configuration (7 submodules, 27 permissions)**
   - System Settings: View, Create, Edit, Delete
   - Email Templates: View, Create, Edit, Delete
   - SMS Templates: View, Create, Edit, Delete
   - URL Mapping: View, Create, Edit, Delete
   - SMTP Configuration: View, Create, Edit, Delete
   - Countries: View, Create, Edit, Delete
   - States: View, Create, Edit, Delete, Export, Import

5. **Reports (1 submodule, 4 permissions)**
   - Traffic Reports: View, Create, Edit, Delete
   - Note: CompanyAdmin does not have access to Revenue Reports or User Activity

6. **Transactions (3 submodules, 12 permissions)**
   - Payment Transactions: View, Create, Edit, Delete
   - Vehicle Entries: View, Create, Edit, Delete
   - Transaction History: View, Create, Edit, Delete

7. **Monitoring (3 submodules, 12 permissions)**
   - Lane Status: View, Create, Edit, Delete
   - System Health: View, Create, Edit, Delete
   - Alerts: View, Create, Edit, Delete

### PlazaManager Role

PlazaManager has limited access focused on day-to-day operations.

**Total Permissions**: 36
- View: 12
- Create: 7
- Edit: 8
- Delete: 8
- Export: 1
- Import: 0

#### Module Access:

1. **Configuration (7 submodules, 17 permissions)**
   - System Settings: View
   - Email Templates: View, Create, Edit, Delete
   - SMS Templates: View, Create, Edit, Delete
   - URL Mapping: View
   - SMTP Configuration: View
   - Countries: View
   - States: View, Export

2. **Lane Management (4 submodules, 15 permissions)**
   - Lanes: View, Create, Edit, Delete
   - Lane Settings: View, Create, Edit, Delete
   - UHF Reader: View, Create, Edit, Delete
   - Pass Registration: View, Create, Edit, Delete

3. **Reports (1 submodule, 4 permissions)**
   - Traffic Reports: View, Create, Edit, Delete

## Permission Comparison by Module

| Module | SuperAdmin | CompanyAdmin | PlazaManager |
|--------|------------|--------------|--------------|
| Dashboard | Full Access | No Access | No Access |
| User Management | Full Access | Full Access | No Access |
| Company Management | Full Access | Full Access | No Access |
| Plaza Management | Full Access<br>(Plazas, Plaza Settings) | No Access | No Access |
| Lane Management | Full Access | Full Access | Full Access (with limitations) |
| Configuration | Full Access | Full Access | Limited Access |
| Reports | Full Access<br>(Traffic Reports, Revenue Reports, User Activity) | Limited Access<br>(Traffic Reports only) | Limited Access<br>(Traffic Reports only) |
| Transactions | Full Access | Full Access | No Access |
| Monitoring | Full Access | Full Access | No Access |

## Permission Matrix by SubModule

| SubModule | SuperAdmin | CompanyAdmin | PlazaManager |
|-----------|------------|--------------|--------------|
| **Dashboard Module** |
| Dashboard Overview | CRUD | - | - |
| Analytics | CRUD | - | - |
| **User Management Module** |
| Users | CRUD | CRUD | - |
| Roles | CRUD | CRUD | - |
| Permissions | CRUD | CRUD | - |
| **Company Management Module** |
| Companies | CRUD | CRUD | - |
| Company Settings | CRUD | CRUD | - |
| **Plaza Management Module** |
| Plazas | CRUD | - | - |
| Plaza Settings | CRUD | - | - |
| **Lane Management Module** |
| Lanes | CRUD | CRUD | CRUD |
| Lane Settings | CRUD | CRUD | CRUD |
| UHF Reader | CRUD | CRUD | CRUD |
| Pass Registration | CRUD | CRUD | CRUD |
| **Configuration Module** |
| System Settings | CRUD | CRUD | View |
| Email Templates | CRUD | CRUD | CRUD |
| SMS Templates | CRUD | CRUD | CRUD |
| URL Mapping | CRUD | CRUD | View |
| SMTP Configuration | CRUD | CRUD | View |
| Countries | CRUD | CRUD | View |
| States | CRUD+EI | CRUD+EI | View+E |
| **Reports Module** |
| Traffic Reports | CRUD | CRUD | CRUD |
| Revenue Reports | CRUD | - | - |
| User Activity | CRUD | - | - |
| **Transactions Module** |
| Payment Transactions | CRUD | CRUD | - |
| Vehicle Entries | CRUD | CRUD | - |
| Transaction History | CRUD | CRUD | - |
| **Monitoring Module** |
| Lane Status | CRUD | CRUD | - |
| System Health | CRUD | CRUD | - |
| Alerts | CRUD | CRUD | - |

*Legend: C=Create, R=Read, U=Update, D=Delete, E=Export, I=Import*

## Business Logic and Access Control

### SuperAdmin
- **Purpose**: System-wide administration and oversight
- **Scope**: All aspects of the system
- **Key Responsibilities**:
  - User and role management
  - System configuration
  - Company and plaza setup
  - Comprehensive reporting
  - System monitoring
  - Transaction oversight

### CompanyAdmin
- **Purpose**: Company-level administration
- **Scope**: Limited to company-specific operations
- **Key Responsibilities**:
  - User management within the company
  - Lane configuration and management
  - Transaction processing and monitoring
  - Basic reporting
  - Company-specific settings

### PlazaManager
- **Purpose**: Day-to-day plaza operations
- **Scope**: Limited to operational tasks
- **Key Responsibilities**:
  - Lane operations
  - Basic configuration
  - Traffic reporting
  - Pass registration and management

## Implementation Details

The permission system is implemented through database relationships:

1. Each role is assigned specific permissions through the RolePermissions table
2. Each permission is tied to a specific action on a specific submodule
3. When a user logs in, their role determines which permissions they have
4. The application UI and backend API enforce these permissions

## Security Considerations

- Permissions are enforced at both the UI and API levels
- API endpoints validate permissions before processing requests
- UI elements are conditionally rendered based on user permissions
- Unauthorized access attempts are logged for security monitoring

## Conclusion

The ParkwizOps permission system provides a robust and flexible framework for controlling access to system features. By assigning users to appropriate roles, administrators can ensure that each user has exactly the access they need to perform their job functions, while maintaining system security and data integrity.

This hierarchical approach to permissions allows for clear separation of responsibilities and helps prevent unauthorized access to sensitive features and data.