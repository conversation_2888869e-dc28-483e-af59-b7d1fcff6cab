const crypto = require('crypto');
const axios = require('axios');
const db = require('../../config/database');

/**
 * PhonePe Service for Valet System
 * Handles PhonePe payment gateway integration, payment initiation, status check, and webhooks
 */

class PhonePeService {
  constructor() {
    this.baseURL = process.env.PHONEPE_BASE_URL || 'https://api.phonepe.com/apis/hermes';
    this.merchantId = process.env.PHONEPE_MERCHANT_ID;
    this.saltKey = process.env.PHONEPE_SALT_KEY;
    this.saltIndex = process.env.PHONEPE_SALT_INDEX || '1';
    this.environment = process.env.PHONEPE_ENVIRONMENT || 'SANDBOX'; // SANDBOX or PRODUCTION
    
    if (!this.merchantId || !this.saltKey) {
      console.warn('PhonePe credentials not configured. Payment processing will be disabled.');
    }

    // Set base URL based on environment
    if (this.environment === 'PRODUCTION') {
      this.baseURL = 'https://api.phonepe.com/apis/hermes';
    } else {
      this.baseURL = 'https://api-preprod.phonepe.com/apis/pg-sandbox';
    }
  }

  /**
   * Create PhonePe payment request
   */
  async createPaymentRequest(paymentData) {
    try {
      if (!this.merchantId || !this.saltKey) {
        throw new Error('PhonePe credentials not configured');
      }

      const {
        amount,
        merchantTransactionId,
        merchantUserId,
        redirectUrl,
        redirectMode = 'POST',
        callbackUrl,
        mobileNumber,
        paymentInstrument = {
          type: 'PAY_PAGE'
        }
      } = paymentData;

      // Validate amount (PhonePe expects amount in paise)
      const amountInPaise = Math.round(amount * 100);
      
      if (amountInPaise < 100) {
        throw new Error('Minimum amount should be ₹1.00');
      }

      const payload = {
        merchantId: this.merchantId,
        merchantTransactionId,
        merchantUserId,
        amount: amountInPaise,
        redirectUrl,
        redirectMode,
        callbackUrl,
        mobileNumber,
        paymentInstrument
      };

      // Create base64 encoded payload
      const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');
      
      // Create checksum
      const checksum = this.generateChecksum(base64Payload);

      const requestData = {
        request: base64Payload
      };

      const headers = {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'accept': 'application/json'
      };

      const response = await axios.post(
        `${this.baseURL}/pg/v1/pay`,
        requestData,
        {
          headers,
          timeout: 30000
        }
      );

      if (response.data.success) {
        return {
          success: true,
          data: {
            merchantTransactionId,
            transactionId: response.data.data.merchantTransactionId,
            instrumentResponse: response.data.data.instrumentResponse,
            redirectInfo: response.data.data.instrumentResponse.redirectInfo
          }
        };
      } else {
        return {
          success: false,
          error: response.data.message || 'Payment request failed',
          code: response.data.code
        };
      }

    } catch (error) {
      console.error('PhonePe payment request error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        code: error.response?.data?.code || 'PHONEPE_ERROR'
      };
    }
  }

  /**
   * Check payment status
   */
  async checkPaymentStatus(merchantTransactionId) {
    try {
      if (!this.merchantId || !this.saltKey) {
        throw new Error('PhonePe credentials not configured');
      }

      const endpoint = `/pg/v1/status/${this.merchantId}/${merchantTransactionId}`;
      const checksum = this.generateChecksum(endpoint);

      const headers = {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'X-MERCHANT-ID': this.merchantId,
        'accept': 'application/json'
      };

      const response = await axios.get(
        `${this.baseURL}${endpoint}`,
        {
          headers,
          timeout: 30000
        }
      );

      if (response.data.success) {
        const paymentData = response.data.data;
        
        return {
          success: true,
          data: {
            merchantTransactionId: paymentData.merchantTransactionId,
            transactionId: paymentData.transactionId,
            amount: paymentData.amount,
            state: paymentData.state,
            responseCode: paymentData.responseCode,
            paymentInstrument: paymentData.paymentInstrument
          }
        };
      } else {
        return {
          success: false,
          error: response.data.message || 'Status check failed',
          code: response.data.code
        };
      }

    } catch (error) {
      console.error('PhonePe status check error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        code: error.response?.data?.code || 'PHONEPE_ERROR'
      };
    }
  }

  /**
   * Verify callback/webhook data
   */
  verifyCallback(callbackData, receivedChecksum) {
    try {
      if (!this.saltKey) {
        throw new Error('PhonePe salt key not configured');
      }

      // Decode base64 response
      const decodedResponse = Buffer.from(callbackData, 'base64').toString('utf-8');
      const responseData = JSON.parse(decodedResponse);

      // Generate checksum for verification
      const expectedChecksum = this.generateChecksum(callbackData);

      const isChecksumValid = expectedChecksum === receivedChecksum;

      return {
        success: isChecksumValid,
        data: responseData,
        error: isChecksumValid ? null : 'Invalid callback checksum'
      };

    } catch (error) {
      console.error('PhonePe callback verification error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process callback/webhook
   */
  async processCallback(callbackData) {
    try {
      const { data } = callbackData;
      
      if (!data) {
        throw new Error('Invalid callback data');
      }

      const {
        merchantTransactionId,
        transactionId,
        amount,
        state,
        responseCode,
        paymentInstrument
      } = data;

      // Update payment status in database based on state
      let status = 'PENDING';
      
      switch (state) {
        case 'COMPLETED':
          status = 'SUCCESS';
          break;
        case 'FAILED':
          status = 'FAILED';
          break;
        case 'PENDING':
          status = 'PENDING';
          break;
        default:
          status = 'UNKNOWN';
      }

      // Update payment in database
      await db.query(`
        UPDATE PaymentGatewayTransactions
        SET Status = @status,
            GatewayTransactionId = @transactionId,
            GatewayResponse = @response,
            UpdatedOn = GETDATE()
        WHERE GatewayOrderId = @merchantTransactionId
      `, {
        status,
        transactionId,
        merchantTransactionId,
        response: JSON.stringify(data)
      });

      // If payment is successful, update main transaction
      if (status === 'SUCCESS') {
        const transactionQuery = `
          SELECT pt.Id as TransactionId
          FROM PaymentGatewayTransactions pgt
          JOIN ParkingTransactions pt ON pgt.TransactionId = pt.Id
          WHERE pgt.GatewayOrderId = @merchantTransactionId
        `;
        
        const transactionResult = await db.query(transactionQuery, { merchantTransactionId });
        
        if (transactionResult.recordset.length > 0) {
          const transactionId = transactionResult.recordset[0].TransactionId;
          
          await db.query(`
            UPDATE ParkingTransactions
            SET Status = 'PAYMENT_COMPLETED',
                ModifiedOn = GETDATE()
            WHERE Id = @transactionId
          `, { transactionId });
        }
      }

      return {
        success: true,
        message: 'Callback processed successfully',
        data: {
          merchantTransactionId,
          transactionId,
          amount,
          state,
          status
        }
      };

    } catch (error) {
      console.error('PhonePe callback processing error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Generate checksum for PhonePe API
   */
  generateChecksum(payload) {
    try {
      const string = payload + '/pg/v1/pay' + this.saltKey;
      const sha256 = crypto.createHash('sha256').update(string).digest('hex');
      return sha256 + '###' + this.saltIndex;
    } catch (error) {
      console.error('Checksum generation error:', error);
      throw new Error('Failed to generate checksum');
    }
  }

  /**
   * Generate checksum for status check
   */
  generateStatusChecksum(endpoint) {
    try {
      const string = endpoint + this.saltKey;
      const sha256 = crypto.createHash('sha256').update(string).digest('hex');
      return sha256 + '###' + this.saltIndex;
    } catch (error) {
      console.error('Status checksum generation error:', error);
      throw new Error('Failed to generate status checksum');
    }
  }

  /**
   * Get plaza PhonePe configuration
   */
  async getPlazaConfiguration(plazaId) {
    try {
      const configQuery = `
        SELECT MerchantId, SaltKey, SaltIndex, Environment, IsActive
        FROM PlazaPhonePeConfiguration
        WHERE PlazaId = @plazaId AND IsActive = 1
      `;

      const result = await db.query(configQuery, { plazaId });
      
      if (result.recordset.length === 0) {
        return {
          success: false,
          error: 'PhonePe not configured for this plaza'
        };
      }

      const config = result.recordset[0];
      
      return {
        success: true,
        data: {
          merchantId: config.MerchantId,
          saltKey: config.SaltKey,
          saltIndex: config.SaltIndex,
          environment: config.Environment,
          isActive: config.IsActive
        }
      };

    } catch (error) {
      console.error('Get plaza PhonePe configuration error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Refund payment (if supported)
   */
  async refundPayment(refundData) {
    try {
      if (!this.merchantId || !this.saltKey) {
        throw new Error('PhonePe credentials not configured');
      }

      const {
        merchantTransactionId,
        originalTransactionId,
        amount,
        callbackUrl
      } = refundData;

      const payload = {
        merchantId: this.merchantId,
        merchantTransactionId,
        originalTransactionId,
        amount: Math.round(amount * 100), // Convert to paise
        callbackUrl
      };

      const base64Payload = Buffer.from(JSON.stringify(payload)).toString('base64');
      const checksum = this.generateChecksum(base64Payload);

      const requestData = {
        request: base64Payload
      };

      const headers = {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'accept': 'application/json'
      };

      const response = await axios.post(
        `${this.baseURL}/pg/v1/refund`,
        requestData,
        {
          headers,
          timeout: 30000
        }
      );

      if (response.data.success) {
        return {
          success: true,
          data: {
            merchantTransactionId,
            transactionId: response.data.data.transactionId,
            state: response.data.data.state,
            responseCode: response.data.data.responseCode
          }
        };
      } else {
        return {
          success: false,
          error: response.data.message || 'Refund request failed',
          code: response.data.code
        };
      }

    } catch (error) {
      console.error('PhonePe refund error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.message || error.message,
        code: error.response?.data?.code || 'PHONEPE_ERROR'
      };
    }
  }
}

module.exports = new PhonePeService();
