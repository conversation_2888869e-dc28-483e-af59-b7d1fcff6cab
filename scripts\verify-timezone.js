// scripts/verify-timezone.js
/**
 * ===============================================================================
 * # Timezone Verification Script
 * ===============================================================================
 * 
 * This script shows you exactly what times are being used and proves
 * that 00:30 UTC = 6:00 AM IST (your operational day start time)
 */

console.log('🌍 TIMEZONE VERIFICATION FOR OPERATIONAL DAY');
console.log('='.repeat(60));

// Your log shows these times
const logStartUTC = '2025-07-01T00:30:00.000Z';
const logEndUTC = '2025-07-02T00:29:59.999Z';

console.log('\n📋 FROM YOUR LOGS:');
console.log(`   Start UTC: ${logStartUTC}`);
console.log(`   End UTC:   ${logEndUTC}`);

// Convert to IST (UTC + 5:30)
const startDate = new Date(logStartUTC);
const endDate = new Date(logEndUTC);

// Add 5.5 hours (5 hours 30 minutes) to convert UTC to IST
const startIST = new Date(startDate.getTime() + (5.5 * 60 * 60 * 1000));
const endIST = new Date(endDate.getTime() + (5.5 * 60 * 60 * 1000));

console.log('\n🇮🇳 CONVERTED TO IST:');
console.log(`   Start IST: ${startIST.toLocaleString('en-IN', {
  year: 'numeric',
  month: '2-digit', 
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false
})} (${startIST.toLocaleTimeString('en-IN', { hour12: true })})`);

console.log(`   End IST:   ${endIST.toLocaleString('en-IN', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit', 
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false
})} (${endIST.toLocaleTimeString('en-IN', { hour12: true })})`);

console.log('\n⏰ TIME BREAKDOWN:');
console.log(`   Start Time: ${startIST.getHours()}:${startIST.getMinutes().toString().padStart(2, '0')} (${startIST.getHours() === 6 ? '✅ 6:00 AM' : '❌ Not 6:00 AM'})`);
console.log(`   End Time:   ${endIST.getHours()}:${endIST.getMinutes().toString().padStart(2, '0')} (${endIST.getHours() === 5 && endIST.getMinutes() === 59 ? '✅ 5:59 AM' : '❌ Not 5:59 AM'})`);

console.log('\n📊 OPERATIONAL DAY VERIFICATION:');
const durationHours = (endDate - startDate) / (1000 * 60 * 60);
console.log(`   Duration: ${durationHours} hours (${durationHours === 24 ? '✅ Exactly 24 hours' : '❌ Not 24 hours'})`);

console.log('\n🎯 CONCLUSION:');
if (startIST.getHours() === 6 && startIST.getMinutes() === 0 && 
    endIST.getHours() === 5 && endIST.getMinutes() === 59) {
  console.log('   ✅ PERFECT! Your operational day is correctly set to 6:00 AM - 5:59 AM IST');
  console.log('   ✅ The UTC times in logs (00:30 - 00:29) are correct for IST timezone');
} else {
  console.log('   ❌ Something is wrong with the operational day calculation');
}

console.log('\n💡 WHY YOU SEE 00:30 in LOGS:');
console.log('   • Your server stores times in UTC (Universal Coordinated Time)');
console.log('   • India is UTC+5:30 (5 hours 30 minutes ahead of UTC)');
console.log('   • 6:00 AM IST = 00:30 UTC (midnight + 30 minutes)');
console.log('   • 5:59 AM IST = 00:29 UTC (midnight + 29 minutes)');

console.log('\n🚀 YOUR SYSTEM IS WORKING CORRECTLY!');
console.log('   The times in your logs are UTC, but they represent the correct IST operational hours.');