const axios = require('axios');

async function testPermissionManagementAPI() {
  console.log('🔍 Testing New Permission Management API\n');

  try {
    // Step 1: Login
    console.log('1. 🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'superadmin',
      password: 'Admin@123'
    });
    
    const token = loginResponse.data.data.token;
    const headers = { 'Authorization': `Bearer ${token}` };
    console.log('✅ Login successful\n');

    // Step 2: Test the test endpoint
    console.log('2. 🧪 Testing test endpoint...');
    try {
      const testResponse = await axios.get('http://localhost:5000/api/permission-management/test', {
        headers,
        timeout: 10000
      });
      
      console.log('✅ Test endpoint successful');
      console.log(`   Message: ${testResponse.data.message}`);
      console.log(`   User: ${testResponse.data.user}\n`);
      
    } catch (error) {
      console.log(`❌ Test endpoint failed: ${error.response?.data?.message || error.message}\n`);
    }

    // Step 3: Test modules-tree endpoint
    console.log('3. 🌳 Testing modules-tree endpoint...');
    try {
      const modulesResponse = await axios.get('http://localhost:5000/api/permission-management/modules-tree', {
        headers,
        timeout: 15000
      });
      
      console.log('✅ Modules-tree endpoint successful');
      console.log(`   Success: ${modulesResponse.data.success}`);
      console.log(`   Modules count: ${modulesResponse.data.data.length}`);
      console.log(`   Message: ${modulesResponse.data.message}`);
      
      if (modulesResponse.data.data.length > 0) {
        const firstModule = modulesResponse.data.data[0];
        console.log(`   First module: ${firstModule.name}`);
        console.log(`   Submodules: ${firstModule.subModules.length}`);
        
        if (firstModule.subModules.length > 0) {
          const firstSubModule = firstModule.subModules[0];
          console.log(`   First submodule: ${firstSubModule.name}`);
          console.log(`   Permissions: ${firstSubModule.permissions.length}`);
        }
      }
      console.log('');
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('❌ Modules-tree request timed out\n');
      } else {
        console.log(`❌ Modules-tree request failed: ${error.response?.data?.message || error.message}\n`);
      }
    }

    // Step 4: Test roles endpoint
    console.log('4. 👥 Testing roles endpoint...');
    try {
      const rolesResponse = await axios.get('http://localhost:5000/api/permission-management/roles', {
        headers,
        timeout: 10000
      });
      
      console.log('✅ Roles endpoint successful');
      console.log(`   Success: ${rolesResponse.data.success}`);
      console.log(`   Roles count: ${rolesResponse.data.data.length}`);
      console.log(`   Message: ${rolesResponse.data.message}`);
      
      if (rolesResponse.data.data.length > 0) {
        const firstRole = rolesResponse.data.data[0];
        console.log(`   First role: ${firstRole.Name} (ID: ${firstRole.Id})`);
        
        // Step 5: Test role permissions endpoint
        console.log('\n5. 🔑 Testing role permissions endpoint...');
        try {
          const rolePermissionsResponse = await axios.get(
            `http://localhost:5000/api/permission-management/roles/${firstRole.Id}/permissions`, 
            {
              headers,
              timeout: 10000
            }
          );
          
          console.log('✅ Role permissions endpoint successful');
          console.log(`   Success: ${rolePermissionsResponse.data.success}`);
          console.log(`   Permissions count: ${rolePermissionsResponse.data.data.length}`);
          console.log(`   Message: ${rolePermissionsResponse.data.message}`);
          
        } catch (error) {
          console.log(`❌ Role permissions request failed: ${error.response?.data?.message || error.message}`);
        }
      }
      console.log('');
      
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        console.log('❌ Roles request timed out\n');
      } else {
        console.log(`❌ Roles request failed: ${error.response?.data?.message || error.message}\n`);
      }
    }

    // Step 6: Test creating a new role
    console.log('6. ➕ Testing create role endpoint...');
    try {
      const newRoleData = {
        name: `Test Role ${Date.now()}`,
        description: 'Test role created by API test'
      };
      
      const createRoleResponse = await axios.post('http://localhost:5000/api/permission-management/roles', 
        newRoleData, 
        {
          headers,
          timeout: 10000
        }
      );
      
      console.log('✅ Create role endpoint successful');
      console.log(`   Success: ${createRoleResponse.data.success}`);
      console.log(`   New role: ${createRoleResponse.data.data.Name} (ID: ${createRoleResponse.data.data.Id})`);
      console.log(`   Message: ${createRoleResponse.data.message}\n`);
      
    } catch (error) {
      console.log(`❌ Create role request failed: ${error.response?.data?.message || error.message}\n`);
    }

    console.log('🎉 Permission Management API test completed successfully!');

  } catch (error) {
    console.log(`❌ Test failed: ${error.message}`);
  }
}

testPermissionManagementAPI().then(() => {
  console.log('\n🏁 All tests completed');
  process.exit(0);
}).catch(error => {
  console.log(`❌ Test crashed: ${error.message}`);
  process.exit(1);
});