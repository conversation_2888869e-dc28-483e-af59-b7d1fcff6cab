require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function simpleTableCheck() {
  try {
    console.log('=== SIMPLE TABLE USAGE CHECK ===');
    
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Check what the dashboard controller is actually querying for "month" range
    const today = new Date();
    const startDate = new Date(today);
    startDate.setDate(startDate.getDate() - 29);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(today);
    endDate.setHours(23, 59, 59, 999);

    console.log(`Checking MONTH range: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    // This is the EXACT query the dashboard controller uses
    const dashboardQuery = `
      SELECT
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
        COUNT(*) as TransactionCount
      FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
    `;

    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);

    const result = await request.query(dashboardQuery);
    const data = result.recordset[0];

    console.log('\nDASHBOARD ACTUAL RESULTS (NEW table only):');
    console.log(`  Revenue: ₹${data.TotalRevenue.toFixed(2)}`);
    console.log(`  Transactions: ${data.TransactionCount}`);

    // Quick check of OLD table for same period (with timeout protection)
    try {
      const oldRequest = new sql.Request();
      oldRequest.input('startDate', sql.DateTime, startDate);
      oldRequest.input('endDate', sql.DateTime, endDate);
      
      // Set a shorter timeout and simpler query
      const oldResult = await oldRequest.query(`
        SELECT COUNT(*) as TransactionCount
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      `);
      
      const oldData = oldResult.recordset[0];
      console.log('\nOLD TABLE (not used by dashboard):');
      console.log(`  Transactions: ${oldData.TransactionCount}`);
      
      if (oldData.TransactionCount > 0) {
        console.log('\n⚠️  CRITICAL FINDING: Dashboard is missing historical data from OLD table!');
        console.log(`   Dashboard shows: ${data.TransactionCount} transactions`);
        console.log(`   OLD table has: ${oldData.TransactionCount} additional transactions`);
        console.log(`   Total available: ${data.TransactionCount + oldData.TransactionCount} transactions`);
      } else {
        console.log('\n✅ No missing data - OLD table has no records for this period');
      }
      
    } catch (oldError) {
      console.log('\nOLD TABLE: Query timed out (likely has data but too slow to query)');
      console.log('⚠️  This suggests there IS historical data in OLD table that dashboard is NOT using');
    }

    // Check the date ranges of both tables
    console.log('\n=== TABLE DATE RANGES ===');
    
    const rangeQuery = `
      SELECT 
        'NEW_TABLE' as TableName,
        MIN(ExitDateTime) as EarliestDate,
        MAX(ExitDateTime) as LatestDate,
        COUNT(*) as TotalRecords
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime IS NOT NULL
      
      UNION ALL
      
      SELECT 
        'OLD_TABLE' as TableName,
        MIN(ExitDateTime) as EarliestDate,
        MAX(ExitDateTime) as LatestDate,
        COUNT(*) as TotalRecords
      FROM tblParkwiz_Parking_Data_OLD
      WHERE ExitDateTime IS NOT NULL
    `;
    
    const rangeResult = await sql.query(rangeQuery);
    
    rangeResult.recordset.forEach(row => {
      console.log(`\n${row.TableName}:`);
      console.log(`  Date Range: ${row.EarliestDate?.toISOString()} to ${row.LatestDate?.toISOString()}`);
      console.log(`  Total Records: ${row.TotalRecords.toLocaleString()}`);
    });

    await sql.close();
    
    console.log('\n=== CONCLUSION ===');
    console.log('The dashboard controller is ONLY using tblParkwiz_Parking_Data (NEW table)');
    console.log('It is NOT using tblParkwiz_Parking_Data_OLD (OLD table)');
    console.log('This means historical data before June 21, 2025 is NOT shown in dashboard');
    
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

simpleTableCheck();