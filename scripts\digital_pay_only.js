// Script to create only Digital Pay configurations
const sql = require('mssql');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from backend/.env
dotenv.config({ path: path.join(__dirname, 'backend', '.env') });

// Database configuration
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '1433'),
  options: {
    encrypt: process.env.DB_ENCRYPT === 'true',
    trustServerCertificate: process.env.DB_TRUST_CERT === 'true',
    enableArithAbort: true,
    connectionTimeout: parseInt(process.env.DB_TIMEOUT || '30000'),
    requestTimeout: parseInt(process.env.DB_REQUEST_TIMEOUT || '30000')
  }
};

// Execute a SQL query with proper error handling
async function executeQuery(pool, description, query) {
  try {
    console.log(`Executing: ${description}...`);
    await pool.request().query(query);
    console.log(`Success: ${description}`);
    return true;
  } catch (err) {
    console.error(`Error in "${description}":`, err.message);
    console.error('Query:', query);
    return false;
  }
}

async function createDigitalPayConfigs() {
  let pool;
  try {
    console.log('Connecting to database...');
    pool = await sql.connect(dbConfig);
    console.log('Connected to database successfully');

    // Clear existing digital pay configurations
    console.log('Clearing existing digital pay configurations...');
    await executeQuery(pool, "Delete digital pay configurations", `DELETE FROM tblLaneDigitalPayConfiguration WHERE 1=1`);
    
    // Get lane IDs
    console.log('Fetching lane IDs...');
    const lanesResult = await pool.request().query(`
      SELECT LaneID, PlazaID, CompanyID, LaneNumber FROM tblLaneDetails
    `);
    
    // Create Digital Pay configurations
    console.log('Creating Digital Pay configurations...');
    for (const lane of lanesResult.recordset) {
      const laneNumber = lane.LaneNumber.trim();
      
      await executeQuery(pool, `Create Digital Pay config for lane ${laneNumber} in plaza ${lane.PlazaID}`, `
        INSERT INTO tblLaneDigitalPayConfiguration (
          PlazaID, CompanyID, LaneID, LaneNumber, EnableCardPayment,
          EnableUPIPhonePe, EnableSendSMS, CardReaderMID,
          PhonePeMID, PhonePeKeyID, PhonePeIndexID,
          ActiveStatus, UpdatedDateTime, UpdatedBy, AllowBlacklistedVehicle,
          CardReaderPGProvider, CardReaderPort, CardReaderEnvType, 
          CardReaderUserName, CardReaderPassword, CardReaderAppKey,
          CardPayAPIURL, CardReaderDeviceID, UPIPGProvider, PhonePeAPI,
          SMSSenderName, SMSAPI, CardPaymentDeviceModel
        )
        VALUES (
          ${lane.PlazaID}, 
          ${lane.CompanyID}, 
          ${lane.LaneID}, 
          '${laneNumber}', 
          'Y',
          'Y', 
          'Y', 
          'MERCH${lane.LaneID}',
          'PPMERCH${lane.LaneID}', 
          'PPKEY${lane.LaneID}', 
          '1',
          'Y', 
          GETDATE(), 
          '1', 
          'N',
          'Provider1', 
          '1', 
          'Test', 
          'user1', 
          'pass1', 
          'appkey1',
          'https://api.payment.com/v1', 
          'DEV${lane.LaneID}', 
          'PhonePe', 
          'https://api.phonepe.com/v1',
          'PWIZ', 
          'https://api.sms.com/v1', 
          'Model1'
        )
      `);
    }
    
    console.log('Digital Pay configurations created successfully!');
  } catch (err) {
    console.error('Error creating Digital Pay configurations:', err);
  } finally {
    if (pool) {
      await pool.close();
      console.log('Database connection closed');
    }
  }
}

createDigitalPayConfigs();