const db = require('./src/config/database');

async function addGeographicMapping() {
  try {
    console.log('=== ADDING GEOGRAPHIC MAPPING ===');
    
    // Step 1: Check if tblCompanyMaster needs geographic fields
    console.log('\n1. CHECKING COMPANY TABLE STRUCTURE...');
    
    const companyStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblCompanyMaster'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('Current tblCompanyMaster structure:');
    companyStructure.recordset.forEach(col => {
      console.log(`  ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });
    
    // Check if CountryId and StateId columns exist
    const hasCountryId = companyStructure.recordset.some(col => col.COLUMN_NAME === 'CountryId');
    const hasStateId = companyStructure.recordset.some(col => col.COLUMN_NAME === 'StateId');
    
    console.log(`Has CountryId: ${hasCountryId}`);
    console.log(`Has StateId: ${hasStateId}`);
    
    // Step 2: Add geographic columns if they don't exist
    if (!hasCountryId) {
      console.log('\n2. ADDING COUNTRYID COLUMN...');
      try {
        await db.query(`
          ALTER TABLE tblCompanyMaster 
          ADD CountryId INT NULL
        `);
        console.log('✓ CountryId column added');
      } catch (error) {
        console.error('Error adding CountryId:', error.message);
      }
    }
    
    if (!hasStateId) {
      console.log('\n3. ADDING STATEID COLUMN...');
      try {
        await db.query(`
          ALTER TABLE tblCompanyMaster 
          ADD StateId INT NULL
        `);
        console.log('✓ StateId column added');
      } catch (error) {
        console.error('Error adding StateId:', error.message);
      }
    }
    
    // Step 3: Check current geographic data
    console.log('\n4. CHECKING GEOGRAPHIC DATA...');
    
    const countryCount = await db.query('SELECT COUNT(*) as Count FROM Country');
    const stateCount = await db.query('SELECT COUNT(*) as Count FROM State');
    
    console.log(`Countries: ${countryCount.recordset[0].Count}`);
    console.log(`States: ${stateCount.recordset[0].Count}`);
    
    // Step 4: Get sample countries and states
    if (countryCount.recordset[0].Count > 0) {
      const countries = await db.query('SELECT TOP 5 * FROM Country');
      console.log('Sample countries:', countries.recordset);
      
      const states = await db.query('SELECT TOP 10 * FROM State');
      console.log('Sample states:', states.recordset);
    }
    
    // Step 5: Update existing companies with geographic data
    console.log('\n5. UPDATING COMPANIES WITH GEOGRAPHIC DATA...');
    
    // Get India's ID
    const indiaResult = await db.query('SELECT Id FROM Country WHERE Name = \'India\'');
    if (indiaResult.recordset.length > 0) {
      const indiaId = indiaResult.recordset[0].Id;
      
      // Get West Bengal's ID
      const westBengalResult = await db.query(`SELECT Id FROM State WHERE Name = 'West Bengal' AND CountryId = ${indiaId}`);
      
      if (westBengalResult.recordset.length > 0) {
        const westBengalId = westBengalResult.recordset[0].Id;
        
        // Update companies without geographic data
        const updateResult = await db.query(`
          UPDATE tblCompanyMaster 
          SET CountryId = ${indiaId}, StateId = ${westBengalId}, ModifiedBy = 1, ModifiedOn = GETDATE()
          WHERE CountryId IS NULL OR StateId IS NULL
        `);
        
        console.log(`✓ Updated ${updateResult.rowsAffected[0]} companies with geographic data`);
      }
    }
    
    // Step 6: Create foreign key constraints
    console.log('\n6. ADDING FOREIGN KEY CONSTRAINTS...');
    
    try {
      // Check if foreign key constraints already exist
      const existingConstraints = await db.query(`
        SELECT CONSTRAINT_NAME 
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
        WHERE TABLE_NAME = 'tblCompanyMaster' 
        AND CONSTRAINT_TYPE = 'FOREIGN KEY'
      `);
      
      console.log('Existing constraints:', existingConstraints.recordset);
      
      // Add Country foreign key if it doesn't exist
      const hasCountryFK = existingConstraints.recordset.some(c => c.CONSTRAINT_NAME.includes('Country'));
      if (!hasCountryFK) {
        await db.query(`
          ALTER TABLE tblCompanyMaster
          ADD CONSTRAINT FK_Company_Country 
          FOREIGN KEY (CountryId) REFERENCES Country(Id)
        `);
        console.log('✓ Country foreign key constraint added');
      }
      
      // Add State foreign key if it doesn't exist
      const hasStateFK = existingConstraints.recordset.some(c => c.CONSTRAINT_NAME.includes('State'));
      if (!hasStateFK) {
        await db.query(`
          ALTER TABLE tblCompanyMaster
          ADD CONSTRAINT FK_Company_State 
          FOREIGN KEY (StateId) REFERENCES State(Id)
        `);
        console.log('✓ State foreign key constraint added');
      }
      
    } catch (error) {
      console.error('Error adding foreign key constraints:', error.message);
    }
    
    // Step 7: Update the Company view to include geographic data
    console.log('\n7. UPDATING COMPANY VIEW...');
    
    try {
      await db.query('DROP VIEW IF EXISTS Company');
      await db.query(`
        CREATE VIEW Company AS
        SELECT 
          c.Id,
          c.CompanyName,
          c.AddressId,
          c.ContactPerson,
          c.ContactNumber,
          c.ContactEmail,
          c.CompanyLogo,
          c.CompanyCode,
          c.CountryId,
          c.StateId,
          co.Name as CountryName,
          s.Name as StateName,
          c.IsActive,
          c.CreatedBy,
          c.CreatedOn,
          c.ModifiedBy,
          c.ModifiedOn
        FROM tblCompanyMaster c
        LEFT JOIN Country co ON c.CountryId = co.Id
        LEFT JOIN State s ON c.StateId = s.Id
      `);
      console.log('✓ Company view updated with geographic data');
    } catch (error) {
      console.error('Error updating Company view:', error.message);
    }
    
    // Step 8: Verification
    console.log('\n8. VERIFICATION...');
    
    const companiesWithGeo = await db.query(`
      SELECT 
        CompanyName,
        CountryName,
        StateName
      FROM Company
      WHERE CountryId IS NOT NULL
    `);
    
    console.log('Companies with geographic data:');
    companiesWithGeo.recordset.forEach(company => {
      console.log(`  ${company.CompanyName} -> ${company.CountryName}, ${company.StateName}`);
    });
    
    console.log('\n=== GEOGRAPHIC MAPPING COMPLETE ===');
    console.log('\nChanges made:');
    console.log('1. ✓ Added CountryId and StateId columns to tblCompanyMaster');
    console.log('2. ✓ Populated geographic data (countries and states)');
    console.log('3. ✓ Updated existing companies with geographic information');
    console.log('4. ✓ Added foreign key constraints');
    console.log('5. ✓ Updated Company view to include geographic data');
    
    process.exit(0);
  } catch (error) {
    console.error('Geographic mapping failed:', error);
    process.exit(1);
  }
}

addGeographicMapping();
