require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function verifyFilters() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Test different date ranges
    console.log('Testing date range filters:');
    
    // Today (June 2025)
    const juneStart = '2025-06-01T00:00:00.000Z';
    const juneEnd = '2025-06-30T23:59:59.999Z';
    const juneResult = await sql.query(`
      SELECT COUNT(*) as Count, SUM(ParkingFee + iTotalGSTFee) as Revenue
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${juneStart}' AND '${juneEnd}'
    `);
    console.log('Today (June 2025):');
    console.log('- Transactions:', juneResult.recordset[0].Count);
    console.log('- Revenue:', juneResult.recordset[0].Revenue);
    
    // Yesterday (May 2025)
    const mayStart = '2025-05-01T00:00:00.000Z';
    const mayEnd = '2025-05-31T23:59:59.999Z';
    const mayResult = await sql.query(`
      SELECT COUNT(*) as Count, SUM(ParkingFee + iTotalGSTFee) as Revenue
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${mayStart}' AND '${mayEnd}'
    `);
    console.log('\nYesterday (May 2025):');
    console.log('- Transactions:', mayResult.recordset[0].Count);
    console.log('- Revenue:', mayResult.recordset[0].Revenue);
    
    // Test plaza filter
    console.log('\nTesting plaza filters:');
    
    // Get list of plazas
    const plazasResult = await sql.query(`
      SELECT DISTINCT PlazaName, PlazaCode
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${juneStart}' AND '${juneEnd}'
      ORDER BY PlazaName
    `);
    
    // For each plaza, get transaction count and revenue
    for (const plaza of plazasResult.recordset) {
      const plazaResult = await sql.query(`
        SELECT COUNT(*) as Count, SUM(ParkingFee + iTotalGSTFee) as Revenue
        FROM tblParkwiz_Parking_Data
        WHERE ExitDateTime BETWEEN '${juneStart}' AND '${juneEnd}'
        AND PlazaCode = '${plaza.PlazaCode}'
      `);
      
      console.log(`\nPlaza: ${plaza.PlazaName} (Code: ${plaza.PlazaCode})`);
      console.log('- Transactions:', plazaResult.recordset[0].Count);
      console.log('- Revenue:', plazaResult.recordset[0].Revenue);
    }
    
    // Test payment method filter
    console.log('\nTesting payment method filters:');
    
    // Get list of payment methods
    const paymentMethodsResult = await sql.query(`
      SELECT DISTINCT PaymentMode
      FROM tblParkwiz_Parking_Data
      WHERE ExitDateTime BETWEEN '${juneStart}' AND '${juneEnd}'
      ORDER BY PaymentMode
    `);
    
    // For each payment method, get transaction count and revenue
    for (const method of paymentMethodsResult.recordset) {
      const methodResult = await sql.query(`
        SELECT COUNT(*) as Count, SUM(ParkingFee + iTotalGSTFee) as Revenue
        FROM tblParkwiz_Parking_Data
        WHERE ExitDateTime BETWEEN '${juneStart}' AND '${juneEnd}'
        AND PaymentMode = '${method.PaymentMode}'
      `);
      
      console.log(`\nPayment Method: ${method.PaymentMode}`);
      console.log('- Transactions:', methodResult.recordset[0].Count);
      console.log('- Revenue:', methodResult.recordset[0].Revenue);
    }

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

verifyFilters();