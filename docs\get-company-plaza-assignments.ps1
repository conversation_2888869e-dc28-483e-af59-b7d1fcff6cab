# PowerShell script to show plaza assignments by company
# This script shows how many plazas are assigned to which companies

param(
    [string]$Server = "parkwizvms.database.windows.net",
    [string]$Database = "ParkwizOps", 
    [string]$Username = "hparkwiz",
    [string]$Password = "Parkwiz@2020"
)

Write-Host "=== COMPANY-PLAZA ASSIGNMENTS REPORT ===" -ForegroundColor Cyan

# Build connection string
$connectionString = "Server=$Server;Database=$Database;User Id=$Username;Password=$Password;Encrypt=true;TrustServerCertificate=true;Connection Timeout=30;"

try {
    # Load System.Data.SqlClient
    Add-Type -AssemblyName System.Data
    
    # Create connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected to database: $Database" -ForegroundColor Green
    
    # Query to get all companies and their plazas
    $companyPlazaQuery = @"
    SELECT 
        c.Id as CompanyId,
        c.<PERSON>,
        c.<PERSON> as CompanyContact<PERSON>erson,
        c.<PERSON> as CompanyContact<PERSON><PERSON>ber,
        c.<PERSON> as CompanyEmail,
        p.Id as <PERSON><PERSON>d,
        p.<PERSON>,
        p.<PERSON>,
        p.<PERSON><PERSON>erson as PlazaContactPerson,
        p.ContactNumber as PlazaContactNumber,
        p.IsActive as PlazaActive,
        p.CreatedOn as PlazaCreatedOn
    FROM tblCompanyMaster c
    LEFT JOIN Plaza p ON c.Id = p.CompanyId
    WHERE c.IsActive = 1
    ORDER BY c.CompanyName, p.PlazaName
"@
    
    # Execute query
    $command = New-Object System.Data.SqlClient.SqlCommand($companyPlazaQuery, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $dataTable = New-Object System.Data.DataTable
    $adapter.Fill($dataTable)
    
    if ($dataTable.Rows.Count -gt 0) {
        Write-Host "`nCOMPANY-PLAZA ASSIGNMENTS:" -ForegroundColor Green
        
        # Group by company
        $companyGroups = $dataTable.Rows | Group-Object CompanyName
        
        $totalCompanies = 0
        $totalPlazas = 0
        $companiesWithPlazas = 0
        $companiesWithoutPlazas = 0
        
        foreach ($companyGroup in $companyGroups) {
            $company = $companyGroup.Group[0]
            $totalCompanies++
            
            Write-Host "`n--- COMPANY: $($company.CompanyName) ---" -ForegroundColor Magenta
            Write-Host "Company ID: $($company.CompanyId)" -ForegroundColor Gray
            Write-Host "Contact: $($company.CompanyContactPerson)" -ForegroundColor Gray
            Write-Host "Phone: $($company.CompanyContactNumber)" -ForegroundColor Gray
            Write-Host "Email: $($company.CompanyEmail)" -ForegroundColor Gray
            
            # Check if company has plazas
            $activePlazas = $companyGroup.Group | Where-Object { $_.PlazaId -ne [DBNull]::Value -and $_.PlazaActive -eq $true }
            $inactivePlazas = $companyGroup.Group | Where-Object { $_.PlazaId -ne [DBNull]::Value -and $_.PlazaActive -eq $false }
            $allPlazas = $companyGroup.Group | Where-Object { $_.PlazaId -ne [DBNull]::Value }
            
            if ($allPlazas.Count -gt 0) {
                $companiesWithPlazas++
                Write-Host "Plazas: $($allPlazas.Count) total ($($activePlazas.Count) active, $($inactivePlazas.Count) inactive)" -ForegroundColor Yellow
                
                foreach ($plaza in $allPlazas) {
                    $status = if ($plaza.PlazaActive) { "ACTIVE" } else { "INACTIVE" }
                    $statusColor = if ($plaza.PlazaActive) { "Green" } else { "Red" }
                    
                    Write-Host "  • $($plaza.PlazaName) (Code: $($plaza.PlazaCode)) - " -NoNewline -ForegroundColor White
                    Write-Host $status -ForegroundColor $statusColor
                    Write-Host "    Contact: $($plaza.PlazaContactPerson) - $($plaza.PlazaContactNumber)" -ForegroundColor Gray
                    Write-Host "    Created: $($plaza.PlazaCreatedOn)" -ForegroundColor Gray
                }
                $totalPlazas += $allPlazas.Count
            } else {
                $companiesWithoutPlazas++
                Write-Host "Plazas: 0 (No plazas assigned)" -ForegroundColor Red
            }
        }
        
        # Summary statistics
        Write-Host "`n=== SUMMARY STATISTICS ===" -ForegroundColor Cyan
        Write-Host "Total Companies: $totalCompanies" -ForegroundColor White
        Write-Host "Companies with Plazas: $companiesWithPlazas" -ForegroundColor Green
        Write-Host "Companies without Plazas: $companiesWithoutPlazas" -ForegroundColor Red
        Write-Host "Total Plazas: $totalPlazas" -ForegroundColor White
        
        # Top companies by plaza count
        Write-Host "`n=== TOP COMPANIES BY PLAZA COUNT ===" -ForegroundColor Cyan
        $companyPlazaCounts = @()
        foreach ($companyGroup in $companyGroups) {
            $plazaCount = ($companyGroup.Group | Where-Object { $_.PlazaId -ne [DBNull]::Value }).Count
            $activePlazaCount = ($companyGroup.Group | Where-Object { $_.PlazaId -ne [DBNull]::Value -and $_.PlazaActive -eq $true }).Count
            
            $companyPlazaCounts += [PSCustomObject]@{
                CompanyName = $companyGroup.Name
                TotalPlazas = $plazaCount
                ActivePlazas = $activePlazaCount
            }
        }
        
        $companyPlazaCounts | Sort-Object TotalPlazas -Descending | ForEach-Object {
            if ($_.TotalPlazas -gt 0) {
                Write-Host "$($_.CompanyName): $($_.TotalPlazas) plazas ($($_.ActivePlazas) active)" -ForegroundColor White
            }
        }
        
    } else {
        Write-Host "No company-plaza data found." -ForegroundColor Red
    }
    
    # Additional query: Show user assignments by company
    Write-Host "`n=== USER ASSIGNMENTS BY COMPANY ===" -ForegroundColor Cyan
    
    $userCompanyQuery = @"
    SELECT 
        c.CompanyName,
        u.Username,
        u.FirstName + ' ' + u.LastName as FullName,
        u.Email,
        r.Name as RoleName,
        p.PlazaName,
        p.PlazaCode
    FROM tblCompanyMaster c
    INNER JOIN Plaza p ON c.Id = p.CompanyId
    INNER JOIN UserPlaza up ON p.Id = up.PlazaId
    INNER JOIN Users u ON up.UserId = u.Id
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE c.IsActive = 1 AND p.IsActive = 1 AND u.IsActive = 1
    ORDER BY c.CompanyName, u.Username
"@
    
    $command2 = New-Object System.Data.SqlClient.SqlCommand($userCompanyQuery, $connection)
    $adapter2 = New-Object System.Data.SqlClient.SqlDataAdapter($command2)
    $dataTable2 = New-Object System.Data.DataTable
    $adapter2.Fill($dataTable2)
    
    if ($dataTable2.Rows.Count -gt 0) {
        $userCompanyGroups = $dataTable2.Rows | Group-Object CompanyName
        
        foreach ($companyGroup in $userCompanyGroups) {
            Write-Host "`n$($companyGroup.Name):" -ForegroundColor Yellow
            foreach ($assignment in $companyGroup.Group) {
                Write-Host "  • $($assignment.Username) ($($assignment.FullName)) - $($assignment.RoleName)" -ForegroundColor White
                Write-Host "    Plaza: $($assignment.PlazaName) ($($assignment.PlazaCode))" -ForegroundColor Gray
                Write-Host "    Email: $($assignment.Email)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "No user assignments found for any company plazas." -ForegroundColor Red
    }
    
    $connection.Close()
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host "`n=== REPORT COMPLETE ===" -ForegroundColor Cyan