require('dotenv').config({path: 'd:/PWVMS/backend/.env'});
const sql = require('mssql');

async function verifyCompanyFilter() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Get list of companies
    const companiesResult = await sql.query(`
      SELECT c.Id, c.CompanyName, COUNT(p.Id) as PlazaCount
      FROM Company c
      LEFT JOIN Plaza p ON c.Id = p.CompanyId
      GROUP BY c.Id, c.CompanyName
      ORDER BY c.CompanyName
    `);
    
    console.log('Companies and their plazas:');
    console.log(JSON.stringify(companiesResult.recordset, null, 2));
    
    // For each company, get transaction count and revenue
    console.log('\nTesting company filter:');
    
    const juneStart = '2025-06-01T00:00:00.000Z';
    const juneEnd = '2025-06-30T23:59:59.999Z';
    
    for (const company of companiesResult.recordset) {
      // Get plazas for this company
      const plazasResult = await sql.query(`
        SELECT p.Id, p.PlazaName, p.PlazaCode
        FROM Plaza p
        WHERE p.CompanyId = ${company.Id}
      `);
      
      if (plazasResult.recordset.length === 0) {
        console.log(`\nCompany: ${company.CompanyName} (ID: ${company.Id}) - No plazas`);
        continue;
      }
      
      // Get transaction data for all plazas of this company
      const plazaCodes = plazasResult.recordset.map(p => `'${p.PlazaCode}'`).join(',');
      
      if (!plazaCodes) {
        console.log(`\nCompany: ${company.CompanyName} (ID: ${company.Id}) - No plaza codes`);
        continue;
      }
      
      const companyResult = await sql.query(`
        SELECT COUNT(*) as Count, SUM(ParkingFee + iTotalGSTFee) as Revenue
        FROM tblParkwiz_Parking_Data
        WHERE ExitDateTime BETWEEN '${juneStart}' AND '${juneEnd}'
        AND PlazaCode IN (${plazaCodes})
      `);
      
      console.log(`\nCompany: ${company.CompanyName} (ID: ${company.Id})`);
      console.log('- Plazas:', plazasResult.recordset.map(p => p.PlazaName).join(', '));
      console.log('- Transactions:', companyResult.recordset[0].Count);
      console.log('- Revenue:', companyResult.recordset[0].Revenue);
    }

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

verifyCompanyFilter();