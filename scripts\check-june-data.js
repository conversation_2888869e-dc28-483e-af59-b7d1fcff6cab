require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function checkJuneData() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true,
        requestTimeout: 60000 // Increase timeout to 60 seconds
      }
    });

    // Check if there's data for June 2025
    const juneData = await sql.query(`
      SELECT TOP 5 * 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime >= '2025-06-01' AND ExitDateTime < '2025-07-01'
      ORDER BY ExitDateTime DESC
    `);
    
    console.log('Data for June 2025:');
    console.log(JSON.stringify(juneData.recordset, null, 2));
    
    // Count records for June 2025
    const juneCount = await sql.query(`
      SELECT COUNT(*) as Count
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime >= '2025-06-01' AND ExitDateTime < '2025-07-01'
    `);
    
    console.log(`\nTotal records for June 2025: ${juneCount.recordset[0].Count}`);

    // Check the most recent data
    const recentData = await sql.query(`
      SELECT TOP 5 * 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime IS NOT NULL
      ORDER BY ExitDateTime DESC
    `);
    
    console.log('\nMost recent data:');
    console.log(JSON.stringify(recentData.recordset, null, 2));

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkJuneData();