import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Building2 } from 'lucide-react';
import toast from 'react-hot-toast';
import api from '../../services/api'; // Your API instance

const BranchRegistration = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);

  const token = searchParams.get('token');
  const branchId = searchParams.get('branchId');

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  useEffect(() => {
    if (!token || !branchId) {
      toast.error('Invalid registration link');
      navigate('/');
    }
  }, [token, branchId, navigate]);

  const onSubmit = async (data) => {
    if (!token) return;

    setIsLoading(true);
    try {
      // Call your registration API endpoint
      await api.post('/auth/register/branch-head', {
        ...data,
        token,
        branchId,
      });

      toast.success('Registration successful! You can now log in.');
      navigate('/login'); // Redirect to login after successful registration
    } catch (error) {
      toast.error(error.response?.data?.error || 'Registration failed');
    } finally {
      setIsLoading(false);
    }
  };
  return (
    <div className="min-h-screen bg-[#FFFFFF] flex items-center justify-center p-6">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-lg">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 text-[#9AC8CE]">
            <Building2 size={48} />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-[#000000]">
            Branch Head Registration
          </h2>
          <p className="mt-2 text-sm text-[#727272]">
            Complete your registration to manage your branch
          </p>
        </div>

        {/* Form */}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            {/* Full Name */}
            <div>
              <label htmlFor="name" className="text-sm font-medium text-[#727272]">
                Full Name
              </label>
              <input
                id="name"
                type="text"
                {...register('name', { required: 'Name is required' })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#9AC8CE] focus:border-[#9AC8CE]"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            {/* Email Address */}
            <div>
              <label htmlFor="email" className="text-sm font-medium text-[#727272]">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#9AC8CE] focus:border-[#9AC8CE]"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="text-sm font-medium text-[#727272]">
                Password
              </label>
              <input
                id="password"
                type="password"
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 8,
                    message: 'Password must be at least 8 characters',
                  },
                })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-[#9AC8CE] focus:border-[#9AC8CE]"
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#9AC8CE] hover:bg-[#7BA9AF] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#9AC8CE] disabled:opacity-50"
            >
              {isLoading ? 'Registering...' : 'Complete Registration'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BranchRegistration;
