-- =============================================
-- CREATE MISSING VALET SYSTEM TABLES
-- Only creates the 2 missing tables
-- Database: ParkwizOps
-- =============================================

USE ParkwizOps;
GO

PRINT 'Creating missing Valet system tables...';
PRINT '========================================';

-- =============================================
-- 1. CREATE PaymentGatewayTransactions Table
-- =============================================
PRINT 'Creating PaymentGatewayTransactions table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PaymentGatewayTransactions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PaymentGatewayTransactions] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [TransactionId] INT NOT NULL,
        [GatewayType] NVARCHAR(50) NOT NULL, -- RAZORPAY, PHONEPE, CASH
        [GatewayOrderId] NVARCHAR(100) NOT NULL,
        [GatewayTransactionId] NVARCHAR(100) NULL,
        [Amount] DECIMAL(10,2) NOT NULL,
        [Currency] NVARCHAR(10) NOT NULL DEFAULT 'INR',
        [PaymentMethod] NVARCHAR(50) NOT NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'PENDING', -- PENDING, SUCCESS, FAILED, CANCELLED
        [CustomerName] NVARCHAR(100) NULL,
        [CustomerEmail] NVARCHAR(100) NULL,
        [CustomerMobile] NVARCHAR(15) NULL,
        [GatewayResponse] NVARCHAR(MAX) NULL,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [UpdatedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );

    -- Create indexes
    CREATE INDEX IX_PaymentGatewayTransactions_TransactionId ON PaymentGatewayTransactions(TransactionId);
    CREATE INDEX IX_PaymentGatewayTransactions_GatewayOrderId ON PaymentGatewayTransactions(GatewayOrderId);
    CREATE INDEX IX_PaymentGatewayTransactions_Status ON PaymentGatewayTransactions(Status);
    CREATE INDEX IX_PaymentGatewayTransactions_CreatedOn ON PaymentGatewayTransactions(CreatedOn);

    PRINT 'PaymentGatewayTransactions table created successfully!';
END
ELSE
BEGIN
    PRINT 'PaymentGatewayTransactions table already exists.';
END
GO

-- SMSNotifications table already exists - skipping

-- =============================================
-- 2. CREATE CustomerSessions Table
-- =============================================
PRINT 'Creating CustomerSessions table...';

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[CustomerSessions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[CustomerSessions] (
        [Id] INT IDENTITY(1,1) PRIMARY KEY,
        [CustomerId] INT NOT NULL,
        [PlazaValetPointId] INT NOT NULL,
        [SessionData] NVARCHAR(MAX) NOT NULL, -- JSON data containing form progress
        [CurrentStep] NVARCHAR(50) NOT NULL DEFAULT 'INITIALIZED',
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'ACTIVE', -- ACTIVE, COMPLETED, CANCELLED, EXPIRED
        [ExpiresAt] DATETIME NOT NULL DEFAULT DATEADD(HOUR, 2, GETDATE()),
        [CompletedAt] DATETIME NULL,
        [CreatedBy] INT NOT NULL,
        [CreatedOn] DATETIME NOT NULL DEFAULT GETDATE(),
        [ModifiedBy] INT NULL,
        [ModifiedOn] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1
    );

    -- Create indexes
    CREATE INDEX IX_CustomerSessions_CustomerId ON CustomerSessions(CustomerId);
    CREATE INDEX IX_CustomerSessions_PlazaValetPointId ON CustomerSessions(PlazaValetPointId);
    CREATE INDEX IX_CustomerSessions_Status ON CustomerSessions(Status);
    CREATE INDEX IX_CustomerSessions_CurrentStep ON CustomerSessions(CurrentStep);
    CREATE INDEX IX_CustomerSessions_CreatedOn ON CustomerSessions(CreatedOn);
    CREATE INDEX IX_CustomerSessions_ExpiresAt ON CustomerSessions(ExpiresAt);

    PRINT 'CustomerSessions table created successfully!';
END
ELSE
BEGIN
    PRINT 'CustomerSessions table already exists.';
END
GO

-- All other tables already exist - skipping

-- =============================================
-- VERIFICATION - Check missing tables created
-- =============================================
PRINT '';
PRINT 'Verifying missing Valet tables created...';
PRINT '==========================================';

SELECT
    TABLE_NAME as TableName,
    TABLE_TYPE as TableType
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_NAME IN (
    'PaymentGatewayTransactions',
    'CustomerSessions'
)
ORDER BY TABLE_NAME;

PRINT '';
PRINT 'Missing Valet tables created successfully!';
PRINT 'Now run CREATE_MISSING_VALET_STORED_PROCEDURES.sql';
PRINT '==================================================';
