import api from '../services/api'; // Shared Axios instance

export const branchApi = {
  /**
   * Fetches all branches (accessible by both owners and branch heads).
   * GET /branches
   */
  getBranches: async () => {
    const response = await api.get('/branches'); // Matches router.get('/')
    return response.data.branches;
  },

  /**
   * Fetches a specific branch by its ID (accessible by both owners and branch heads).
   * GET /branches/:id
   */
  getBranchById: async (id) => {
    const response = await api.get(`/branches/${id}`); // Matches router.get('/:id')
    return response.data.branch;
  },

  /**
   * Creates a new branch (accessible by owners only).
   * POST /branches
   */
  createBranch: async (data) => {
    const response = await api.post('/branches', data); // Matches router.post('/')
    return response.data.branch;
  },

  /**
   * Updates a branch by its ID (accessible by owners only).
   * PUT /branches/:id
   */
  updateBranch: async (id, data) => {
    const response = await api.put(`/branches/${id}`, data); // Matches router.put('/:id')
    return response.data.branch;
  },

  /**
   * Deletes a branch by its ID (accessible by owners only).
   * DELETE /branches/:id
   */
  deleteBranch: async (id) => {
    await api.delete(`/branches/${id}`); // Matches router.delete('/:id')
  },

  /**
  * Invites a branch head by sending an invitation email.
  * POST /auth/invite-branch-head
  */
  inviteBranchHead: async (data) => {
    const response = await api.post('/auth/invite/branch-head', data); // Ensure this matches your route
    return response.data;
  },

  /**
   * Assigns a branch head to a branch (accessible by owners only).
   * POST /branches/:id/assign-head
   */
  assignBranchHead: async (branchId, data) => {
    const response = await api.post(`/branches/${branchId}/assign-head`, data); // Matches router.post('/:id/assign-head')
    return response.data;
  },

  /**
   * Fetches companies for the owner (accessible by owners only).
   * GET /branches/owner/companies
   */
  getCompaniesForOwner: async () => {
    const response = await api.get('/branches/owner/companies'); // Matches router.get('/owner/companies')
    return response.data.companies;
  },
};
