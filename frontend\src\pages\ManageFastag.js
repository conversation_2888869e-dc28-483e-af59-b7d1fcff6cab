import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Plus, Filter } from 'lucide-react';
import { useToast } from '../hooks/useToast';
import { laneFastagConfigApi } from '../api/laneFastagConfigApi';
import { plazaApi } from '../api/plazaApi';
import { companyApi } from '../api/companyApi';
import { laneApi } from '../api/laneApi';
import FastagList from '../components/FastagConfig/FastagList';
import FastagDialog from '../components/FastagConfig/FastagDialog';
import { useAuth } from '../contexts/authContext';
import useModuleFilter from '../hooks/useModuleFilter';
import { PermissionButton } from '../components/auth/PermissionButton';
import ModuleFilters from '../components/filters/ModuleFilters';
import api from '../services/api';

export default function ManageFastag() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [selectedConfig, setSelectedConfig] = useState(null);
  const queryClient = useQueryClient();
  const toast = useToast();
  const { user } = useAuth();

  // Fetch configurations data
  const { data: configurations, isLoading: configurationsLoading, error: configurationsError } = useQuery({
    queryKey: ['laneFastagConfigs'],
    queryFn: async () => {
      console.log('Fetching FasTag configurations for user:', user);
      try {
        const data = await laneFastagConfigApi.getAllConfigurations();
        console.log('Fetched Fastag configurations:', data);
        if (!data || data.length === 0) {
          console.warn('No FasTag configurations returned from API');
        }
        return data;
      } catch (error) {
        console.error('Error fetching FasTag configurations:', error);
        throw error;
      }
    },
  });

  // Fetch plazas for dropdown
  const { data: plazas, isLoading: plazasLoading } = useQuery({
    queryKey: ['plazas'],
    queryFn: plazaApi.getAllPlazas,
    select: (data) => {
      // Handle the specific structure: {success: true, data: Array(2)}
      if (data && data.success === true && Array.isArray(data.data)) {
        return data.data;
      }

      // Handle other possible data structures
      if (data && Array.isArray(data)) {
        return data;
      } else if (data && data.plazas && Array.isArray(data.plazas)) {
        return data.plazas;
      } else if (data && typeof data === 'object') {
        // If it's an object but not an array, try to extract values
        const plazaArray = Object.values(data).filter(item =>
          item && typeof item === 'object' && (item.Id || item.id) && (item.PlazaName || item.plazaName || item.name)
        );
        if (plazaArray.length > 0) {
          return plazaArray;
        }
      }

      // Default fallback
      return [];
    }
  });

  // Fetch companies for dropdown
  const { data: companies, isLoading: companiesLoading } = useQuery({
    queryKey: ['companies'],
    queryFn: companyApi.getCompanies,
    select: (data) => {
      return Array.isArray(data) ? data : [];
    }
  });

  // Fetch lanes for dropdown
  const { data: lanes, isLoading: lanesLoading } = useQuery({
    queryKey: ['lanes'],
    queryFn: laneApi.getAllLanes,
  });

  // Process raw data - ensure we have arrays
  const rawConfigurations = Array.isArray(configurations) ? configurations : [];
  const processedPlazas = Array.isArray(plazas) ? plazas : [];
  const processedCompanies = Array.isArray(companies) ? companies : [];
  const processedLanes = Array.isArray(lanes) ? lanes : [];

  console.log('Fastag configurations before filtering:', rawConfigurations);

  // Apply filtering to fastag configurations data using the new module filter hook
  const {
    filteredData: filteredConfigurations,
    filters,
    setFilters,
    canCreate,
    canEdit,
    canDelete
  } = useModuleFilter({
    data: rawConfigurations,
    companies: processedCompanies,
    plazas: processedPlazas,
    lanes: processedLanes,
    companyIdField: 'CompanyID',
    plazaIdField: 'PlazaID',
    laneIdField: 'LaneID',
    module: 'Fastag'
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: laneFastagConfigApi.createConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laneFastagConfigs'] });
      toast.showCrudSuccess('create', 'Fastag configuration');
      setDialogOpen(false);
    },
    onError: (error) => toast.showError(`Failed to create configuration: ${error.message}`),
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }) => laneFastagConfigApi.updateConfiguration(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laneFastagConfigs'] });
      toast.showCrudSuccess('update', 'Fastag configuration');
      setDialogOpen(false);
      setEditingConfig(null);
    },
    onError: (error) => toast.showError(`Failed to update configuration: ${error.message}`),
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: laneFastagConfigApi.deleteConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['laneFastagConfigs'] });
      toast.showCrudSuccess('delete', 'Fastag configuration');
      if (selectedConfig) setSelectedConfig(null);
    },
    onError: (error) => toast.showError(`Failed to delete configuration: ${error.message}`),
  });

  const handleSubmit = (data) => {
    console.log('Handle submit with data:', data);

    // Add user tracking
    const dataWithUser = {
      ...data,
      UpdatedBy: user?.id || data.UpdatedBy || 'admin'
    };

    if (editingConfig) {
      // Check if user has permission to edit this configuration
      if (canEdit(editingConfig, 'Fastag')) {
        console.log('Updating config with ID:', editingConfig.RecordID);
        updateMutation.mutate({
          id: editingConfig.RecordID,
          data: dataWithUser
        });
      } else {
        toast.showError('You do not have permission to edit this Fastag configuration');
      }
    } else {
      // Check if user has permission to create configurations
      if (canCreate('Fastag')) {
        console.log('Creating new config');
        createMutation.mutate(dataWithUser);
      } else {
        toast.showError('You do not have permission to create Fastag configurations');
      }
    }
  };

  const handleEdit = (config) => {
    // Check if user has permission to edit this configuration
    if (!canEdit(config, 'Fastag')) {
      toast.showError('You do not have permission to edit this Fastag configuration');
      return;
    }

    console.log('Editing config:', config);
    setEditingConfig(config);
    setDialogOpen(true);
  };

  const handleDelete = (id) => {
    // Find the configuration by ID
    const config = rawConfigurations.find(c => c.RecordID === id);

    // Check if user has permission to delete this configuration
    if (config && canDelete(config, 'Fastag')) {
      if (window.confirm('Are you sure you want to delete this Fastag configuration?')) {
        deleteMutation.mutate(id);
      }
    } else {
      toast.showError('You do not have permission to delete this Fastag configuration');
    }
  };

  // Function to run diagnostic check
  const runDiagnosticCheck = async () => {
    try {
      console.log('Running diagnostic check...');
      const response = await api.get('/lane-fastag/diagnostic/check');
      console.log('Diagnostic check results:', response.data);
      
      // Display the results in an alert for now
      alert(
        `Diagnostic Results:\n\n` +
        `User: ${response.data.data.user.username} (${response.data.data.user.role})\n\n` +
        `Company Associations: ${response.data.data.companyAssociations.length}\n` +
        `${response.data.data.companyAssociations.map(c => 
          `- ${c.CompanyName || 'Unknown'} (ID: ${c.CompanyId}, Active: ${c.IsActive ? 'Yes' : 'No'})`
        ).join('\n')}\n\n` +
        `FasTag Configurations: ${response.data.data.userFastagConfigurations.length}\n` +
        `${response.data.data.userFastagConfigurations.slice(0, 5).map(f => 
          `- ${f.PlazaName || 'Unknown Plaza'}, Lane ${f.LaneNumber} (Company: ${f.CompanyName || 'Unknown'})`
        ).join('\n')}` +
        (response.data.data.userFastagConfigurations.length > 5 ? 
          `\n...and ${response.data.data.userFastagConfigurations.length - 5} more` : '')
      );
    } catch (error) {
      console.error('Error running diagnostic check:', error);
      alert('Error running diagnostic check. See console for details.');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Manage Fastag Configurations</h1>
        <div className="flex gap-2">
          {user && (user.role === 'SuperAdmin' || user.role === 'CompanyAdmin') && (
            <button
              onClick={runDiagnosticCheck}
              className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors"
            >
              <span>Run Diagnostic</span>
            </button>
          )}
          <PermissionButton
            requiredModule="Fastag"
            requiredPermissions={["Create"]}
            onClick={() => {
              setEditingConfig(null);
              setDialogOpen(true);
            }}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
          >
            <Plus size={20} />
            <span>Add Configuration</span>
          </PermissionButton>
        </div>
      </div>

      {/* Filter Section */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-lg font-semibold text-gray-700 flex items-center">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Filters
          </h2>
          <ModuleFilters
            companies={processedCompanies || []}
            plazas={processedPlazas || []}
            lanes={processedLanes || []}
            filters={filters}
            onFilterChange={setFilters}
            showCompanyFilter={true}
            showPlazaFilter={true}
            showLaneFilter={true}
            loading={configurationsLoading || plazasLoading || companiesLoading || lanesLoading}
          />
        </div>
      </div>

      {configurationsLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow">
          <FastagList
            configurations={filteredConfigurations || []}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onSelect={setSelectedConfig}
          />
        </div>
      )}

      <FastagDialog
        isOpen={dialogOpen}
        onClose={() => {
          setDialogOpen(false);
          setEditingConfig(null);
        }}
        onSubmit={handleSubmit}
        initialData={editingConfig}
        title={editingConfig ? 'Edit Fastag Configuration' : 'Add Fastag Configuration'}
        plazas={plazas || []}
        companies={companies || []}
        lanes={lanes || []}
      />
    </div>
  );
}