const sql = require('mssql');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, 'backend/.env') });

// Database configuration
const config = {
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    server: process.env.DB_SERVER,
    database: process.env.DB_NAME,
    port: parseInt(process.env.DB_PORT) || 1433,
    options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
    }
};

// Function to execute SQL script with GO statement handling
async function executeSQLScript(scriptPath, scriptName) {
    try {
        console.log(`\n=== Executing ${scriptName} ===`);
        
        // Read the SQL script
        const scriptContent = fs.readFileSync(scriptPath, 'utf8');
        
        // Split by GO statements (case insensitive)
        const batches = scriptContent
            .split(/\r?\n\s*GO\s*\r?\n/i)
            .filter(batch => batch.trim().length > 0);
        
        console.log(`Found ${batches.length} SQL batches to execute`);
        
        // Execute each batch
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i].trim();
            if (batch.length > 0) {
                try {
                    console.log(`Executing batch ${i + 1}/${batches.length}...`);
                    await sql.query(batch);
                    console.log(`✓ Batch ${i + 1} executed successfully`);
                } catch (batchError) {
                    console.error(`✗ Error in batch ${i + 1}:`, batchError.message);
                    // Continue with next batch instead of stopping
                }
            }
        }
        
        console.log(`✓ ${scriptName} execution completed`);
        
    } catch (error) {
        console.error(`✗ Error executing ${scriptName}:`, error.message);
        throw error;
    }
}

async function executeAllStoredProcedures() {
    try {
        console.log('Starting Valet Stored Procedures Execution...');
        console.log('Database:', config.database);
        console.log('Server:', config.server);
        
        // Connect to database
        await sql.connect(config);
        console.log('✓ Connected to database successfully');
        
        // Define stored procedure files to execute
        const storedProcedureFiles = [
            {
                path: path.join(__dirname, 'backend/database/Stored_Procedures/valet/customer_procedures.sql'),
                name: 'Customer Management Procedures'
            },
            {
                path: path.join(__dirname, 'backend/database/Stored_Procedures/valet/otp_procedures.sql'),
                name: 'OTP Management Procedures'
            },
            {
                path: path.join(__dirname, 'backend/database/Stored_Procedures/valet/transaction_procedures.sql'),
                name: 'Transaction Management Procedures'
            },
            {
                path: path.join(__dirname, 'backend/database/Stored_Procedures/valet/qr_procedures.sql'),
                name: 'QR Code Management Procedures'
            }
        ];
        
        // Execute each stored procedure file
        for (const file of storedProcedureFiles) {
            if (fs.existsSync(file.path)) {
                await executeSQLScript(file.path, file.name);
            } else {
                console.log(`⚠ File not found: ${file.path}`);
            }
        }
        
        console.log('\n=== Verification ===');
        
        // Verify stored procedures were created
        const verificationQuery = `
            SELECT 
                ROUTINE_NAME as ProcedureName,
                ROUTINE_TYPE as Type,
                CREATED as CreatedDate
            FROM INFORMATION_SCHEMA.ROUTINES 
            WHERE ROUTINE_NAME LIKE 'sp_Valet_%'
            ORDER BY ROUTINE_NAME
        `;
        
        const result = await sql.query(verificationQuery);
        
        if (result.recordset.length > 0) {
            console.log(`✓ Found ${result.recordset.length} valet stored procedures:`);
            result.recordset.forEach(proc => {
                console.log(`  - ${proc.ProcedureName} (${proc.Type})`);
            });
        } else {
            console.log('⚠ No valet stored procedures found');
        }
        
        console.log('\n✓ All valet stored procedures executed successfully!');
        
    } catch (error) {
        console.error('\n✗ Error during execution:', error.message);
        console.error('Stack trace:', error.stack);
    } finally {
        // Close database connection
        try {
            await sql.close();
            console.log('✓ Database connection closed');
        } catch (closeError) {
            console.error('Error closing database connection:', closeError.message);
        }
    }
}

// Execute the script
executeAllStoredProcedures();
