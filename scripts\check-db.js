const db = require('./src/config/database');

async function checkDatabase() {
  try {
    console.log('=== DATABASE CONNECTION TEST ===');
    
    // Test basic connection
    const connectionTest = await db.query('SELECT DB_NAME() as DatabaseName, GETDATE() as CurrentTime');
    console.log('Connected to:', connectionTest.recordset[0]);
    
    console.log('\n=== CHECKING TABLES ===');
    
    // Check if key tables exist
    const tablesQuery = `
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE' 
      AND TABLE_NAME IN ('Users', 'Roles', 'Modules', 'SubModules', 'RoleModules', 'UserPlaza', 'Company', 'Plaza', 'Country', 'State')
      ORDER BY TABLE_NAME
    `;
    
    const tables = await db.query(tablesQuery);
    console.log('Existing tables:', tables.recordset.map(t => t.TABLE_NAME));
    
    console.log('\n=== CHECKING ROLES ===');
    
    // Check roles
    const rolesQuery = 'SELECT * FROM Roles ORDER BY Id';
    const roles = await db.query(rolesQuery);
    console.log('Roles:', roles.recordset);
    
    console.log('\n=== CHECKING MODULES ===');
    
    // Check modules
    const modulesQuery = 'SELECT * FROM Modules ORDER BY Id';
    const modules = await db.query(modulesQuery);
    console.log('Modules:', modules.recordset);
    
    console.log('\n=== CHECKING SUBMODULES ===');
    
    // Check submodules count
    const submodulesCountQuery = 'SELECT COUNT(*) as SubModuleCount FROM SubModules';
    const submodulesCount = await db.query(submodulesCountQuery);
    console.log('Total SubModules:', submodulesCount.recordset[0].SubModuleCount);
    
    // Check first few submodules
    const submodulesQuery = 'SELECT TOP 10 * FROM SubModules ORDER BY Id';
    const submodules = await db.query(submodulesQuery);
    console.log('SubModules (first 10):', submodules.recordset);
    
    console.log('\n=== CHECKING ROLE PERMISSIONS ===');
    
    // Check role permissions count
    const rolePermissionsQuery = `
      SELECT
        r.Name as RoleName,
        COUNT(rm.Id) as PermissionCount
      FROM Roles r
      LEFT JOIN RoleModules rm ON r.Id = rm.RoleId
      GROUP BY r.Id, r.Name
      ORDER BY r.Id
    `;
    
    const rolePermissions = await db.query(rolePermissionsQuery);
    console.log('Role Permissions Count:', rolePermissions.recordset);
    
    console.log('\n=== CHECKING COMPANY-COUNTRY-STATE RELATIONSHIP ===');
    
    // Check Company table structure
    const companyStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Company'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('Company table structure:', companyStructure.recordset);
    
    // Check if Country and State tables exist and their structure
    const countryStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Country'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('Country table structure:', countryStructure.recordset);
    
    const stateStructure = await db.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'State'
      ORDER BY ORDINAL_POSITION
    `);
    console.log('State table structure:', stateStructure.recordset);
    
    // Check sample data
    console.log('\n=== SAMPLE DATA ===');

    // Check if Company table exists first
    const companyExists = tables.recordset.find(t => t.TABLE_NAME === 'Company');
    if (companyExists) {
      const sampleCompanies = await db.query('SELECT TOP 3 * FROM Company');
      console.log('Sample Companies:', sampleCompanies.recordset);
    } else {
      console.log('Company table does not exist');
    }

    const sampleCountries = await db.query('SELECT TOP 3 * FROM Country');
    console.log('Sample Countries:', sampleCountries.recordset);

    const sampleStates = await db.query('SELECT TOP 3 * FROM State');
    console.log('Sample States:', sampleStates.recordset);

    // Check detailed role permissions
    console.log('\n=== DETAILED ROLE PERMISSIONS ===');

    const detailedPermissions = await db.query(`
      SELECT
        r.Name as RoleName,
        m.Name as ModuleName,
        sm.Name as SubModuleName,
        sm.Path,
        rm.CanView,
        rm.CanCreate,
        rm.CanEdit,
        rm.CanDelete,
        rm.CanExport,
        rm.CanImport
      FROM Roles r
      LEFT JOIN RoleModules rm ON r.Id = rm.RoleId
      LEFT JOIN SubModules sm ON rm.SubModuleId = sm.Id
      LEFT JOIN Modules m ON sm.ModuleId = m.Id
      WHERE r.Name IN ('SuperAdmin', 'CompanyAdmin', 'PlazaManager')
      ORDER BY r.Id, m.DisplayOrder, sm.Id
    `);

    // Group by role
    const permissionsByRole = {};
    detailedPermissions.recordset.forEach(perm => {
      if (!permissionsByRole[perm.RoleName]) {
        permissionsByRole[perm.RoleName] = [];
      }
      if (perm.ModuleName) { // Only add if there's actually a permission
        permissionsByRole[perm.RoleName].push(perm);
      }
    });

    Object.keys(permissionsByRole).forEach(roleName => {
      console.log(`\n${roleName} Permissions (${permissionsByRole[roleName].length} total):`);
      permissionsByRole[roleName].slice(0, 5).forEach(perm => {
        console.log(`  ${perm.ModuleName} > ${perm.SubModuleName} (${perm.Path}): V:${perm.CanView} C:${perm.CanCreate} E:${perm.CanEdit} D:${perm.CanDelete}`);
      });
      if (permissionsByRole[roleName].length > 5) {
        console.log(`  ... and ${permissionsByRole[roleName].length - 5} more`);
      }
    });
    
    process.exit(0);
  } catch (error) {
    console.error('Database check failed:', error);
    process.exit(1);
  }
}

checkDatabase();
