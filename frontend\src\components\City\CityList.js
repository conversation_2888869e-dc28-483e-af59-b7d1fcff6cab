import React from 'react';
import { Edit2, Trash2 } from 'lucide-react';

export default function CityList({ cities, onEdit, onDelete, onSelect }) {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white">
        <thead>
          <tr className="bg-gray-50">
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200">
          {cities.map((city) => (
            <tr
              key={city._id}
              className="hover:bg-gray-50 cursor-pointer"
              onClick={() => onSelect(city._id)}
            >
               <td className="px-6 py-4 whitespace-nowrap">{city.name}</td>
              <td className="px-6 py-4 whitespace-nowrap">{city.code || 'No Code'}</td> {/* Fallback if code is missing */}
              <td className="px-6 py-4 whitespace-nowrap">{city.state ? city.state.name : 'No State'}</td> {/* Null check for state */}
              <td className="px-6 py-4 whitespace-nowrap">{city.country ? city.state.country.name : 'No Country'}</td> {/* Null check for country */}
              
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                  city.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {city.status}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(city);
                    }}
                    className="text-blue-600 hover:text-blue-900"
                  >
                    <Edit2 className="w-5 h-5" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(city._id);
                    }}
                    className="text-red-600 hover:text-red-900"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
