import { useState, useCallback, useRef } from 'react';

/**
 * Progressive Loading Hook
 * 
 * Manages progressive loading states for dashboard components
 * with individual component loading states and overall progress tracking
 */
export const useProgressiveLoading = () => {
  const [loadingStates, setLoadingStates] = useState({});
  const [progress, setProgress] = useState(0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const loadingTimeouts = useRef({});

  // Set loading state for a specific component
  const setComponentLoading = useCallback((componentId, isLoading, delay = 0) => {
    if (delay > 0) {
      // Clear any existing timeout for this component
      if (loadingTimeouts.current[componentId]) {
        clearTimeout(loadingTimeouts.current[componentId]);
      }
      
      // Set timeout for delayed loading
      loadingTimeouts.current[componentId] = setTimeout(() => {
        setLoadingStates(prev => ({
          ...prev,
          [componentId]: isLoading
        }));
        delete loadingTimeouts.current[componentId];
      }, delay);
    } else {
      setLoadingStates(prev => ({
        ...prev,
        [componentId]: isLoading
      }));
    }
  }, []);

  // Check if a specific component is loading
  const isComponentLoading = useCallback((componentId) => {
    return loadingStates[componentId] || false;
  }, [loadingStates]);

  // Check if any component is loading
  const isAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some(loading => loading);
  }, [loadingStates]);

  // Get overall loading progress (0-100)
  const getProgress = useCallback(() => {
    const totalComponents = Object.keys(loadingStates).length;
    if (totalComponents === 0) return 0;
    
    const loadedComponents = Object.values(loadingStates).filter(loading => !loading).length;
    return Math.round((loadedComponents / totalComponents) * 100);
  }, [loadingStates]);

  // Update progress
  const updateProgress = useCallback((newProgress) => {
    setProgress(Math.min(100, Math.max(0, newProgress)));
  }, []);

  // Reset all loading states
  const resetLoading = useCallback(() => {
    // Clear all timeouts
    Object.values(loadingTimeouts.current).forEach(timeout => {
      clearTimeout(timeout);
    });
    loadingTimeouts.current = {};
    
    setLoadingStates({});
    setProgress(0);
    setIsInitialLoad(true);
  }, []);

  // Mark initial load as complete
  const completeInitialLoad = useCallback(() => {
    setIsInitialLoad(false);
  }, []);

  // Simulate progressive loading for demo purposes
  const simulateProgressiveLoad = useCallback(async (components = [], totalDuration = 3000) => {
    const componentDelay = totalDuration / components.length;
    
    // Set all components to loading initially
    components.forEach(componentId => {
      setComponentLoading(componentId, true);
    });
    
    // Progressively complete loading for each component
    for (let i = 0; i < components.length; i++) {
      await new Promise(resolve => setTimeout(resolve, componentDelay));
      setComponentLoading(components[i], false);
      updateProgress(((i + 1) / components.length) * 100);
    }
    
    completeInitialLoad();
  }, [setComponentLoading, updateProgress, completeInitialLoad]);

  return {
    // State
    loadingStates,
    progress,
    isInitialLoad,
    
    // Actions
    setComponentLoading,
    isComponentLoading,
    isAnyLoading,
    getProgress,
    updateProgress,
    resetLoading,
    completeInitialLoad,
    simulateProgressiveLoad,
    
    // Computed values
    overallProgress: getProgress(),
    hasAnyLoading: isAnyLoading()
  };
};

/**
 * Component Loading States
 * 
 * Predefined component IDs for consistent loading state management
 */
export const COMPONENT_IDS = {
  SUMMARY_CARDS: 'summary-cards',
  REVENUE_CHART: 'revenue-chart',
  PAYMENT_CHART: 'payment-chart',
  ENTRY_EXIT_CHART: 'entry-exit-chart',
  FILTERS: 'filters',
  RECENT_TRANSACTIONS: 'recent-transactions',
  PEAK_HOURS: 'peak-hours'
};

/**
 * Loading Phases
 * 
 * Predefined loading phases for progressive loading
 */
export const LOADING_PHASES = {
  INITIAL: 'initial',
  FILTERS: 'filters',
  SUMMARY: 'summary',
  CHARTS: 'charts',
  DETAILS: 'details',
  COMPLETE: 'complete'
};
