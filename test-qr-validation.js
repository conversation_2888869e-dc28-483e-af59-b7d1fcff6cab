const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api/valet';

async function testQRValidation() {
  console.log('🧪 Testing QR Code Validation Endpoint...\n');

  try {
    // Test 1: Valid QR code validation
    console.log('📱 Testing QR Code Validation...');
    const response = await axios.post(`${BASE_URL}/qrcode/validate`, {
      qrData: 'https://valet.parkwiz.in/PARKWZ/14794'
    });

    console.log('✅ QR Validation Response:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ QR Validation Error:', error.response?.status || error.code);
    console.log('📄 Error Data:', JSON.stringify(error.response?.data || error.message, null, 2));
  }

  try {
    // Test 2: Invalid QR code validation
    console.log('\n📱 Testing Invalid QR Code...');
    const response = await axios.post(`${BASE_URL}/qrcode/validate`, {
      qrData: 'invalid-qr-code-data'
    });

    console.log('✅ Invalid QR Response:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ Invalid QR Error (Expected):', error.response?.status || error.code);
    console.log('📄 Error Data:', JSON.stringify(error.response?.data || error.message, null, 2));
  }

  try {
    // Test 3: Missing QR data
    console.log('\n📱 Testing Missing QR Data...');
    const response = await axios.post(`${BASE_URL}/qrcode/validate`, {});

    console.log('✅ Missing QR Response:', response.status);
    console.log('📄 Response Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.log('❌ Missing QR Error (Expected):', error.response?.status || error.code);
    console.log('📄 Error Data:', JSON.stringify(error.response?.data || error.message, null, 2));
  }
}

testQRValidation();
