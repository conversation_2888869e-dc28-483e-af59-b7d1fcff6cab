const sql = require('mssql');
require('dotenv').config({ path: './backend/.env' });

const config = {
    server: process.env.DB_SERVER,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    options: {
        encrypt: true,
        trustServerCertificate: true
    }
};

async function checkMissingComponents() {
    try {
        await sql.connect(config);
        console.log('🔍 COMPREHENSIVE VALET SYSTEM ANALYSIS - Finding Root Causes...\n');

        // 1. Check ALL valet-related stored procedures
        console.log('📋 1. STORED PROCEDURES VERIFICATION:');
        console.log('====================================');
        
        const allValetProcedures = [
            'sp_Valet_Customer_GetByMobile',
            'sp_Valet_Customer_Create',
            'sp_Valet_OTP_Generate',
            'sp_Valet_OTP_Verify',
            'sp_Valet_OTP_GetStatus',
            'sp_Valet_OTP_Cleanup',
            'sp_Valet_Payment_GetOptions',
            'sp_Valet_Payment_Initiate',
            'sp_Valet_Payment_UpdateStatus',
            'sp_Valet_SMS_Send',
            'sp_Valet_Transaction_Create',
            'sp_Valet_Transaction_GetByPNR',
            'sp_Valet_Transaction_GetByPin',
            'sp_Valet_Transaction_UpdateStatus'
        ];
        
        let missingProcedures = [];
        
        for (const procName of allValetProcedures) {
            try {
                const procCheck = await sql.query(`
                    SELECT ROUTINE_NAME, CREATED 
                    FROM INFORMATION_SCHEMA.ROUTINES 
                    WHERE ROUTINE_TYPE = 'PROCEDURE' AND ROUTINE_NAME = '${procName}'
                `);
                
                if (procCheck.recordset.length > 0) {
                    console.log(`   ✅ ${procName} - EXISTS (Created: ${procCheck.recordset[0].CREATED})`);
                } else {
                    console.log(`   ❌ ${procName} - MISSING`);
                    missingProcedures.push(procName);
                }
            } catch (error) {
                console.log(`   ❌ ${procName} - ERROR: ${error.message}`);
                missingProcedures.push(procName);
            }
        }

        // 2. Check critical valet tables exist and have data
        console.log('\n📋 2. VALET TABLES DATA VERIFICATION:');
        console.log('====================================');
        
        // Check PlazaValetPoints table
        try {
            const valetPointsCheck = await sql.query(`
                SELECT COUNT(*) as Count FROM PlazaValetPoints WHERE IsActive = 1
            `);
            console.log(`   ✅ PlazaValetPoints table - ${valetPointsCheck.recordset[0].Count} active records`);
            
            if (valetPointsCheck.recordset[0].Count === 0) {
                console.log('   ⚠️  WARNING: No active valet points found - this will cause API failures!');
            }
        } catch (error) {
            console.log(`   ❌ PlazaValetPoints table - ERROR: ${error.message}`);
        }

        // Check if we have sample data
        try {
            const sampleDataCheck = await sql.query(`
                SELECT TOP 3 
                    pvp.Id, pvp.PointName, p.PlazaName, c.CompanyName
                FROM PlazaValetPoints pvp
                INNER JOIN Plaza p ON pvp.PlazaId = p.Id
                INNER JOIN Company c ON p.CompanyId = c.Id
                WHERE pvp.IsActive = 1
            `);
            
            if (sampleDataCheck.recordset.length > 0) {
                console.log('   📊 Sample Valet Points:');
                sampleDataCheck.recordset.forEach(point => {
                    console.log(`      ID: ${point.Id} - ${point.PointName} (${point.PlazaName}, ${point.CompanyName})`);
                });
            }
        } catch (error) {
            console.log(`   ❌ Sample data check failed: ${error.message}`);
        }

        // 3. Check payment gateway tables
        console.log('\n📋 3. PAYMENT GATEWAY TABLES:');
        console.log('=============================');
        
        const paymentTables = [
            'PaymentGatewayTransactions',
            'PlazaRazorPayConfiguration', 
            'PlazaPhonePeConfiguration',
            'SMSNotifications'
        ];
        
        for (const tableName of paymentTables) {
            try {
                const tableCheck = await sql.query(`
                    SELECT COUNT(*) as Count 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_NAME = '${tableName}'
                `);
                
                if (tableCheck.recordset[0].Count > 0) {
                    console.log(`   ✅ ${tableName} - EXISTS`);
                } else {
                    console.log(`   ❌ ${tableName} - MISSING`);
                }
            } catch (error) {
                console.log(`   ❌ ${tableName} - ERROR: ${error.message}`);
            }
        }

        // 4. Test actual stored procedure execution
        console.log('\n📋 4. STORED PROCEDURE EXECUTION TEST:');
        console.log('=====================================');
        
        // Test OTP generation
        try {
            const otpTest = await sql.query(`
                EXEC sp_Valet_OTP_Generate 
                    @MobileNumber = '9999999999',
                    @OTPCode = '123456',
                    @ExpiryMinutes = 5,
                    @CreatedBy = 1
            `);
            console.log('   ✅ sp_Valet_OTP_Generate - WORKS');
        } catch (error) {
            console.log(`   ❌ sp_Valet_OTP_Generate - FAILED: ${error.message}`);
        }

        // Test customer creation
        try {
            const customerTest = await sql.query(`
                EXEC sp_Valet_Customer_Create
                    @MobileNumber = '9999999999',
                    @Name = 'Test Customer',
                    @AddressId = NULL,
                    @CreatedBy = 1
            `);
            console.log('   ✅ sp_Valet_Customer_Create - WORKS');
        } catch (error) {
            console.log(`   ❌ sp_Valet_Customer_Create - FAILED: ${error.message}`);
        }

        // 5. Check authentication middleware and JWT setup
        console.log('\n📋 5. AUTHENTICATION SETUP:');
        console.log('===========================');
        
        // Check if JWT_SECRET is set
        const jwtSecret = process.env.JWT_SECRET;
        if (jwtSecret) {
            console.log('   ✅ JWT_SECRET - CONFIGURED');
        } else {
            console.log('   ❌ JWT_SECRET - MISSING (This will cause auth failures!)');
        }

        // Check if we have valet users
        try {
            const valetUsers = await sql.query(`
                SELECT COUNT(*) as Count 
                FROM Users u
                INNER JOIN Roles r ON u.RoleId = r.Id
                WHERE r.Name IN ('ValetController', 'ValetDriver') AND u.IsActive = 1
            `);
            console.log(`   📊 Active Valet Users: ${valetUsers.recordset[0].Count}`);
            
            if (valetUsers.recordset[0].Count === 0) {
                console.log('   ⚠️  WARNING: No active valet users found - create test users for testing!');
            }
        } catch (error) {
            console.log(`   ❌ Valet users check failed: ${error.message}`);
        }

        // 6. Check environment variables
        console.log('\n📋 6. ENVIRONMENT CONFIGURATION:');
        console.log('================================');
        
        const requiredEnvVars = [
            'DB_SERVER', 'DB_NAME', 'DB_USER', 'DB_PASSWORD',
            'JWT_SECRET', 'PORT', 'NODE_ENV'
        ];
        
        requiredEnvVars.forEach(envVar => {
            if (process.env[envVar]) {
                console.log(`   ✅ ${envVar} - SET`);
            } else {
                console.log(`   ❌ ${envVar} - MISSING`);
            }
        });

        // 7. Check server routes registration
        console.log('\n📋 7. ROUTE REGISTRATION CHECK:');
        console.log('==============================');
        
        // This would require checking the actual server files
        console.log('   📝 Manual check required for:');
        console.log('      - backend/src/routes/valet/index.js');
        console.log('      - backend/src/app.js (valet routes registration)');
        console.log('      - All controller files in backend/src/controllers/valet/');

        // 8. SUMMARY AND ROOT CAUSE ANALYSIS
        console.log('\n📊 ROOT CAUSE ANALYSIS:');
        console.log('=======================');
        
        let rootCauses = [];
        
        if (missingProcedures.length > 0) {
            rootCauses.push(`Missing stored procedures: ${missingProcedures.join(', ')}`);
        }
        
        if (!process.env.JWT_SECRET) {
            rootCauses.push('JWT_SECRET not configured - authentication will fail');
        }
        
        // Check if PlazaValetPoints has data
        try {
            const valetPointsCount = await sql.query(`
                SELECT COUNT(*) as Count FROM PlazaValetPoints WHERE IsActive = 1
            `);
            if (valetPointsCount.recordset[0].Count === 0) {
                rootCauses.push('No active PlazaValetPoints - customer registration will fail');
            }
        } catch (error) {
            rootCauses.push('PlazaValetPoints table missing or inaccessible');
        }

        if (rootCauses.length === 0) {
            console.log('✅ NO CRITICAL ROOT CAUSES FOUND!');
            console.log('🎉 All major components are properly configured.');
            console.log('');
            console.log('🔧 LIKELY ISSUES FOR API FAILURES:');
            console.log('   1. Authentication tokens in API tests are invalid');
            console.log('   2. Parameter validation in controllers is strict');
            console.log('   3. Database timing issues (customer lookup after creation)');
            console.log('   4. Need to create test valet users for protected endpoints');
        } else {
            console.log('❌ CRITICAL ROOT CAUSES IDENTIFIED:');
            rootCauses.forEach((cause, index) => {
                console.log(`   ${index + 1}. ${cause}`);
            });
        }

        console.log('\n🚀 NEXT STEPS TO FIX:');
        console.log('=====================');
        console.log('1. Execute missing stored procedures if any');
        console.log('2. Set JWT_SECRET in .env file');
        console.log('3. Create sample PlazaValetPoints if missing');
        console.log('4. Create test valet users for API testing');
        console.log('5. Re-run API tests with proper authentication');

    } catch (error) {
        console.error('❌ Error in comprehensive analysis:', error);
    } finally {
        await sql.close();
    }
}

checkMissingComponents();
