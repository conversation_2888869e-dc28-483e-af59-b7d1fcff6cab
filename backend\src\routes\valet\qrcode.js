const express = require('express');
const router = express.Router();
const QRCodeController = require('../../controllers/valet/QRCodeController');
const { auth, authorizeRoles } = require('../../middleware/auth');

/**
 * QR Code Routes for Valet System
 * Public routes for customer QR scanning, admin routes require authentication
 */

// =============================================
// PUBLIC ROUTES (Customer App)
// =============================================

// Validate QR code for customer scanning (PUBLIC - no auth required)
// POST /api/valet/qrcode/validate
router.post('/validate', QRCodeController.validateQRCodeForCustomer);

// =============================================
// ADMIN ROUTES (Require Authentication)
// =============================================

// Generate QR code for valet point
// POST /api/valet/qrcode/generate
router.post('/generate',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  QRCodeController.generateQRCode
);

// Get QR code by data (for scanning)
// GET /api/valet/qrcode/scan/:qrData
router.get('/scan/:qrData',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController', 'ValetDriver']),
  QRCodeController.getQRCodeByData
);

// Get all QR codes for a plaza
// GET /api/valet/qrcode/plaza/:plazaId
router.get('/plaza/:plazaId',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  QRCodeController.getQRCodesByPlaza
);

// Deactivate QR code
// PUT /api/valet/qrcode/:qrCodeId/deactivate
router.put('/:qrCodeId/deactivate',
  authorizeRoles(['SuperAdmin', 'CompanyAdmin', 'PlazaManager', 'ValetController']),
  QRCodeController.deactivateQRCode
);

module.exports = router;
