# PWVMS - Parking and Toll Management System

## Project Structure

The project has been organized into the following structure:

```
PWVMS/
├── backend/             # Node.js Express backend
│   ├── src/             # Source code
│   ├── Uploads/         # Upload directories for files
│   └── .env             # Environment configuration
├── frontend/            # React frontend
│   ├── public/          # Static assets
│   ├── src/             # React source code
│   └── build/           # Production build (created during deployment)
├── docs/                # Documentation files
├── scripts/             # Utility scripts
├── sql/                 # SQL scripts for database setup and migrations
├── tests/               # Test files
├── web.config           # IIS configuration
└── deploy-to-iis.ps1    # Deployment script for IIS
```

## Deployment

### Prerequisites

1. **Windows Server with IIS installed**
   - IIS 10.0 or later recommended
   - Windows Server 2016/2019/2022 or Windows 10/11

2. **Required IIS Components:**
   - URL Rewrite Module: [Download here](https://www.iis.net/downloads/microsoft/url-rewrite)
   - Application Request Routing (ARR): [Download here](https://www.iis.net/downloads/microsoft/application-request-routing)
   - iisnode: [Download here](https://github.com/Azure/iisnode/releases)

3. **Node.js:**
   - Install Node.js LTS version on your server

4. **SQL Server:**
   - SQL Server instance (local or remote)
   - Database credentials configured in the .env file

### Deployment Steps

1. **Prepare Your Environment:**
   - Ensure all prerequisites are installed
   - Configure your database and update the `.env` file

2. **Run the Deployment Script:**
   - Open PowerShell as Administrator
   - Navigate to the project directory
   - Run: `.\deploy-to-iis.ps1`

3. **Verify Deployment:**
   - Open a browser and navigate to your server's IP or hostname
   - Test the application functionality

## Development

### Backend

```bash
cd backend
npm install
npm start
```

The backend server will run on http://localhost:5000

### Frontend

```bash
cd frontend
npm install
npm start
```

The frontend development server will run on http://localhost:3000

## Database Configuration

The application uses SQL Server. Configure the connection in the `.env` file:

```
DB_USER=your_username
DB_PASSWORD=your_password
DB_SERVER=your_server
DB_NAME=your_database
DB_PORT=1433
DB_ENCRYPT=true
DB_TRUST_CERT=true
```

## Additional Documentation

See the `docs/` directory for detailed documentation on specific features and components.