import React from "react"
import { useState, useEffect, useMemo } from "react"
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import {
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Download,
  Edit,
  MapPin,
  MoreHorizontal,
  Plus,
  Search,
  SlidersHorizontal,
  Trash2,
} from "lucide-react"
import axios from "axios"

import { Button } from "../../components/ui/button"
import { Checkbox } from "../../components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../components/ui/dropdown-menu"
import { Input } from "../../components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../../components/ui/tooltip"
import { Badge } from "../../components/ui/badge"
import { Skeleton } from "../../components/ui/skeleton"
import { AddressDialog } from "../../components/Address/AddressDialog"
import { toast } from "../../components/ui/use-toast"

// Function to format date
const formatDate = (dateString) => {
  const date = new Date(dateString)
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date)
}

// Function to get Google Maps URL
const getGoogleMapsUrl = (lat, lng) => {
  return `https://www.google.com/maps?q=${lat},${lng}`
}

export function AddressList() {
  // State
  const [sorting, setSorting] = useState([])
  const [columnFilters, setColumnFilters] = useState([])
  const [columnVisibility, setColumnVisibility] = useState({})
  const [rowSelection, setRowSelection] = useState({})
  const [globalFilter, setGlobalFilter] = useState("")
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  })
  const [isLoading, setIsLoading] = useState(true)
  const [addresses, setAddresses] = useState([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedAddress, setSelectedAddress] = useState(undefined)
  const [countryFilter, setCountryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [countries, setCountries] = useState([])

  // Fetch addresses from API
  const fetchAddresses = async () => {
    setIsLoading(true)
    try {
      const response = await axios.get("/api/addresses")
      setAddresses(response.data)
    } catch (error) {
      console.error("Error fetching addresses:", error)
      toast({
        title: "Error fetching addresses",
        description: "There was a problem retrieving address data.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch countries for filtering
  const fetchCountries = async () => {
    try {
      // This would be replaced with your actual API endpoint
      // For now we'll extract unique countries from the addresses
      const uniqueCountries = {};
      addresses.forEach(address => {
        if (!uniqueCountries[address.countryId]) {
          uniqueCountries[address.countryId] = {
            id: address.countryId,
            name: address.countryName
          }
        }
      })
      setCountries(Object.values(uniqueCountries))
    } catch (error) {
      console.error("Error fetching countries:", error)
    }
  }

  // Initial data load
  useEffect(() => {
    fetchAddresses()
  }, [])

  // Extract countries after addresses are loaded
  useEffect(() => {
    if (addresses.length > 0) {
      fetchCountries()
    }
  }, [addresses])

  // Delete address
  const handleDeleteAddress = async (id) => {
    try {
      await axios.delete(`/api/addresses/${id}`)
      toast({
        title: "Address deleted",
        description: "The address has been successfully deleted.",
      })
      fetchAddresses() // Refresh the list
    } catch (error) {
      console.error("Error deleting address:", error)
      toast({
        title: "Error deleting address",
        description: "There was a problem deleting the address.",
        variant: "destructive",
      })
    }
  }

  // Toggle active status
  const handleToggleStatus = async (address) => {
    try {
      await axios.put(`/api/addresses/${address.id}`, {
        ...address,
        isActive: !address.isActive
      })
      toast({
        title: "Status updated",
        description: `Address status set to ${!address.isActive ? "active" : "inactive"}.`,
      })
      fetchAddresses() // Refresh the list
    } catch (error) {
      console.error("Error updating address status:", error)
      toast({
        title: "Error updating status",
        description: "There was a problem updating the address status.",
        variant: "destructive",
      })
    }
  }

  // Table columns
  const columns = useMemo(
    () => [
      {
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
            onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
            aria-label="Select all"
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
          />
        ),
        enableSorting: false,
        enableHiding: false,
      },
      {
        accessorKey: "fullName",
        header: "Name",
        cell: ({ row }) => {
          const firstName = row.original.firstName
          const lastName = row.original.lastName
          return (
            <div className="font-medium">
              {firstName} {lastName}
            </div>
          )
        },
      },
      {
        accessorKey: "contact",
        header: "Contact",
        cell: ({ row }) => {
          return (
            <div className="space-y-1">
              <div className="text-sm">{row.original.email}</div>
              <div className="text-xs text-muted-foreground">{row.original.mobile}</div>
            </div>
          )
        },
      },
      {
        accessorKey: "location",
        header: "Location",
        cell: ({ row }) => {
          const country = row.original.countryName
          const state = row.original.stateName
          return (
            <div className="space-y-1">
              <div className="text-sm">{country}</div>
              <div className="text-xs text-muted-foreground">{state}</div>
            </div>
          )
        },
      },
      {
        accessorKey: "address",
        header: "Address",
        cell: ({ row }) => {
          const address = row.original
          const fullAddress = `${address.addressLine1}${address.addressLine2 ? `, ${address.addressLine2}` : ""}, ${address.landmarkCity}, ${address.pincode}`
          return (
            <div className="max-w-[300px] truncate" title={fullAddress}>
              {fullAddress}
            </div>
          )
        },
      },
      {
        accessorKey: "coordinates",
        header: "Coordinates",
        cell: ({ row }) => {
          const lat = row.original.latitude
          const lng = row.original.longitude
          const isRequired = row.original.isLatLongRequired

          if (!lat || !lng) {
            return (
              <div className="text-sm text-muted-foreground">
                {isRequired ? "Required but not set" : "Not required"}
              </div>
            )
          }

          return (
            <div className="flex items-center space-x-2">
              <div className="text-sm truncate">
                {lat}, {lng}
              </div>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <a
                      href={getGoogleMapsUrl(lat, lng)}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:text-primary/80"
                    >
                      <MapPin className="h-4 w-4" />
                    </a>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>View on Google Maps</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          )
        },
      },
      {
        accessorKey: "isActive",
        header: "Status",
        cell: ({ row }) => {
          const isActive = row.original.isActive
          return <Badge variant={isActive ? "default" : "secondary"}>{isActive ? "Active" : "Inactive"}</Badge>
        },
      },
      {
        accessorKey: "createdOn",
        header: "Created",
        cell: ({ row }) => {
          return <div className="text-sm">{formatDate(row.original.createdOn)}</div>
        },
      },
      {
        id: "actions",
        cell: ({ row }) => {
          const address = row.original

          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() => {
                    setSelectedAddress(address)
                    setIsDialogOpen(true)
                  }}
                >
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    handleDeleteAddress(address.id)
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => {
                    handleToggleStatus(address)
                  }}
                >
                  <Checkbox checked={address.isActive} className="mr-2" onCheckedChange={() => {}} />
                  {address.isActive ? "Set Inactive" : "Set Active"}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )
        },
      },
    ],
    [],
  )

  // Filter functions
  const handleGlobalFilterChange = (e) => {
    setGlobalFilter(e.target.value)
  }

  const handleCountryFilterChange = (value) => {
    setCountryFilter(value)
    if (value && value !== "all") {
      table.getColumn("location")?.setFilterValue(value)
    } else {
      table.getColumn("location")?.setFilterValue(undefined)
    }
  }

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value)
    if (value === "active") {
      table.getColumn("isActive")?.setFilterValue(true)
    } else if (value === "inactive") {
      table.getColumn("isActive")?.setFilterValue(false)
    } else if (value === "all") {
      table.getColumn("isActive")?.setFilterValue(undefined)
    }
  }

  // Initialize table
  const table = useReactTable({
    data: addresses,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      pagination,
      globalFilter,
    },
  })

  // Handle dialog close and form submission
  const handleDialogClose = (saved) => {
    setIsDialogOpen(false)
    setSelectedAddress(undefined)

    if (saved) {
      fetchAddresses() // Refresh data when saved
    }
  }

  // Handle add new address
  const handleAddNew = () => {
    setSelectedAddress(undefined)
    setIsDialogOpen(true)
  }

  // Export selected addresses to CSV
  const handleExport = () => {
    const selectedRows = table.getFilteredSelectedRowModel().rows
    if (selectedRows.length === 0) {
      toast({
        title: "No addresses selected",
        description: "Please select at least one address to export.",
        variant: "destructive",
      })
      return
    }

    const selectedData = selectedRows.map(row => row.original)
    const csvContent = convertToCSV(selectedData)
    
    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.setAttribute('href', url)
    link.setAttribute('download', 'addresses.csv')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    toast({
      title: "Export complete",
      description: `Successfully exported ${selectedRows.length} addresses.`
    })
  }

  // Convert data to CSV format
  const convertToCSV = (data) => {
    const headers = ["First Name", "Last Name", "Email", "Mobile", "Country", "State", "Address Line 1", "Address Line 2", "City", "Pincode", "Status"]
    const rows = data.map(item => [
      item.firstName,
      item.lastName,
      item.email,
      item.mobile,
      item.countryName,
      item.stateName,
      item.addressLine1,
      item.addressLine2 || "",
      item.landmarkCity,
      item.pincode,
      item.isActive ? "Active" : "Inactive"
    ])
    
    return [
      headers.join(","),
      ...rows.map(row => row.join(","))
    ].join("\n")
  }

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Address Management</CardTitle>
          <CardDescription>Loading address records...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-10 w-[250px]" />
              <Skeleton className="h-10 w-[100px]" />
            </div>
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, index) => (
                <Skeleton key={index} className="h-16 w-full" />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <CardTitle>Address Management</CardTitle>
            <CardDescription>Manage user address records with advanced filtering and sorting.</CardDescription>
          </div>
          <Button onClick={handleAddNew}>
            <Plus className="mr-2 h-4 w-4" /> Add Address
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col gap-4 sm:flex-row">
            <div className="flex items-center w-full sm:w-auto">
              <Input
                placeholder="Search addresses..."
                value={globalFilter}
                onChange={handleGlobalFilterChange}
                className="max-w-sm"
              />
              <div className="ml-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="icon">
                        <Search className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Search by name, email, address, etc.</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
            <div className="flex flex-1 items-center gap-2">
              <Select value={countryFilter} onValueChange={handleCountryFilterChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by country" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Countries</SelectItem>
                  {countries.map((country) => (
                    <SelectItem key={country.id} value={country.id}>
                      {country.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="ml-auto">
                    <SlidersHorizontal className="mr-2 h-4 w-4" />
                    View
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {table
                    .getAllColumns()
                    .filter((column) => column.getCanHide())
                    .map((column) => {
                      return (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          className="capitalize"
                          checked={column.getIsVisible()}
                          onCheckedChange={(value) => column.toggleVisibility(!!value)}
                        >
                          {column.id}
                        </DropdownMenuCheckboxItem>
                      )
                    })}
                </DropdownMenuContent>
              </DropdownMenu>
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      )
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex-1 text-sm text-muted-foreground">
              {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s)
              selected.
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(0)}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <span className="flex items-center gap-1">
                <div>Page</div>
                <strong>
                  {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
                </strong>
              </span>
              <Button variant="outline" size="sm" onClick={() => table.nextPage()} disabled={!table.getCanNextPage()}>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => table.setPageIndex(table.getPageCount() - 1)}
                disabled={!table.getCanNextPage()}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
              <Select
                value={`${table.getState().pagination.pageSize}`}
                onValueChange={(value) => {
                  table.setPageSize(Number(value))
                }}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue placeholder={table.getState().pagination.pageSize} />
                </SelectTrigger>
                <SelectContent side="top">
                  {[10, 20, 30, 40, 50].map((pageSize) => (
                    <SelectItem key={pageSize} value={`${pageSize}`}>
                      {pageSize}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </CardContent>
      <AddressDialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            handleDialogClose(false)
          }
        }}
        initialData={selectedAddress}
        onSave={() => handleDialogClose(true)}
      />
    </Card>
  )
}