const db = require('../config/database');
const { responseHandler } = require('../Utils/ResponseHandler');
const bcrypt = require('bcrypt');

/**
 * User Controller
 * Handles CRUD operations for users and user-related data
 */
class UserController {
  /**
   * Get all users with role information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getAllUsers(req, res) {
    try {
      // Get query parameters for filtering
      const { role, company, plaza, search } = req.query;

      console.log('UserController.getAllUsers - User role:', req.user?.role);
      console.log('UserController.getAllUsers - Query params:', { role, company, plaza, search });

      // Check if user is CompanyAdmin
      const isCompanyAdmin = req.user && req.user.role === 'CompanyAdmin';
      console.log('UserController.getAllUsers - Is CompanyAdmin:', isCompanyAdmin);

      // If CompanyAdmin, get their company IDs
      let companyAdminCompanyIds = [];
      if (isCompanyAdmin && req.user.companies) {
        companyAdminCompanyIds = req.user.companies.map(c => c.Id);
        console.log('UserController.getAllUsers - CompanyAdmin company IDs:', companyAdminCompanyIds);
      }

      // Base query
      let query = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.IsActive = 1
      `;

      const queryParams = {};

      // Add filters if provided
      if (role) {
        query += ` AND r.Name = @role`;
        queryParams.role = role;
      }

      // Filter by company if provided
      if (company) {
        query += `
          AND (
            u.Id IN (
              SELECT UserId FROM UserCompany
              WHERE CompanyId = @companyId AND IsActive = 1
            )
            OR r.Name = 'SuperAdmin'
          )
        `;
        queryParams.companyId = company;
      }
      // If user is CompanyAdmin, filter by their companies
      else if (isCompanyAdmin && companyAdminCompanyIds.length > 0) {
        const companyIdsList = companyAdminCompanyIds.join(',');
        query += `
          AND (
            u.Id IN (
              SELECT UserId FROM UserCompany
              WHERE CompanyId IN (${companyIdsList}) AND IsActive = 1
            )
            AND r.Name != 'SuperAdmin'
          )
        `;
        console.log('UserController.getAllUsers - Filtering by CompanyAdmin companies:', companyIdsList);
      }

      // Filter by plaza if provided
      if (plaza) {
        query += `
          AND (
            u.Id IN (
              SELECT UserId FROM UserPlaza
              WHERE PlazaId = @plazaId AND IsActive = 1
            )
            OR r.Name = 'SuperAdmin'
            OR (
              r.Name = 'CompanyAdmin'
              AND u.Id IN (
                SELECT uc.UserId
                FROM UserCompany uc
                JOIN Plaza p ON uc.CompanyId = p.CompanyId
                WHERE p.Id = @plazaId AND uc.IsActive = 1
              )
            )
          )
        `;
        queryParams.plazaId = plaza;
      }

      // Search by name, email, or username
      if (search) {
        query += `
          AND (
            u.FirstName LIKE @search
            OR u.LastName LIKE @search
            OR u.Email LIKE @search
            OR u.Username LIKE @search
          )
        `;
        queryParams.search = `%${search}%`;
      }

      // Order by
      query += ` ORDER BY u.FirstName, u.LastName`;

      const result = await db.query(query, queryParams);

      // Get company and plaza access for each user
      const users = await Promise.all(result.recordset.map(async (user) => {
        // Get companies for user
        let companies = [];

        if (user.RoleName === 'SuperAdmin') {
          const companiesQuery = `SELECT Id, CompanyName FROM tblCompanyMaster WHERE IsActive = 1`;
          const companiesResult = await db.query(companiesQuery);
          companies = companiesResult.recordset;
        } else {
          const companiesQuery = `
            SELECT c.Id, c.CompanyName
            FROM UserCompany uc
            JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
            WHERE uc.UserId = @userId AND uc.IsActive = 1 AND c.IsActive = 1
          `;
          const companiesResult = await db.query(companiesQuery, { userId: user.Id });
          companies = companiesResult.recordset;
        }

        // Get plazas for user
        let plazas = [];

        if (user.RoleName === 'SuperAdmin') {
          const plazasQuery = `SELECT Id, PlazaName FROM Plaza WHERE IsActive = 1`;
          const plazasResult = await db.query(plazasQuery);
          plazas = plazasResult.recordset;
        } else if (user.RoleName === 'CompanyAdmin') {
          const plazasQuery = `
            SELECT p.Id, p.PlazaName
            FROM Plaza p
            JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
            WHERE uc.UserId = @userId AND uc.IsActive = 1 AND p.IsActive = 1
          `;
          const plazasResult = await db.query(plazasQuery, { userId: user.Id });
          plazas = plazasResult.recordset;
        } else {
          const plazasQuery = `
            SELECT p.Id, p.PlazaName
            FROM UserPlaza up
            JOIN Plaza p ON up.PlazaId = p.Id
            WHERE up.UserId = @userId AND up.IsActive = 1 AND p.IsActive = 1
          `;
          const plazasResult = await db.query(plazasQuery, { userId: user.Id });
          plazas = plazasResult.recordset;
        }

        return {
          ...user,
          companies,
          plazas,
          // Don't return password
          Password: undefined
        };
      }));

      return responseHandler.success(res, users, 'Users retrieved successfully');
    } catch (error) {
      console.error('Error in getAllUsers controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Get user by ID with role, company, and plaza information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getUserById(req, res) {
    try {
      const userId = req.params.id;

      // Get user with role
      const userQuery = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Id = @userId AND u.IsActive = 1
      `;

      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = userResult.recordset[0];

      // Get companies for user
      let companies = [];

      if (user.RoleName === 'SuperAdmin') {
        const companiesQuery = `SELECT Id, CompanyName FROM tblCompanyMaster WHERE IsActive = 1`;
        const companiesResult = await db.query(companiesQuery);
        companies = companiesResult.recordset;
      } else {
        const companiesQuery = `
          SELECT c.Id, c.CompanyName
          FROM UserCompany uc
          JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND c.IsActive = 1
        `;
        const companiesResult = await db.query(companiesQuery, { userId });
        companies = companiesResult.recordset;
      }

      // Get plazas for user
      let plazas = [];

      if (user.RoleName === 'SuperAdmin') {
        const plazasQuery = `SELECT Id, PlazaName FROM Plaza WHERE IsActive = 1`;
        const plazasResult = await db.query(plazasQuery);
        plazas = plazasResult.recordset;
      } else if (user.RoleName === 'CompanyAdmin') {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName
          FROM Plaza p
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId });
        plazas = plazasResult.recordset;
      } else {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName
          FROM UserPlaza up
          JOIN Plaza p ON up.PlazaId = p.Id
          WHERE up.UserId = @userId AND up.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId });
        plazas = plazasResult.recordset;
      }

      // Return user with companies and plazas
      return responseHandler.success(res, {
        ...user,
        companies,
        plazas,
        // Don't return password
        Password: undefined
      }, 'User retrieved successfully');
    } catch (error) {
      console.error('Error in getUserById controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Create a new user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async createUser(req, res) {
    try {
      const {
        roleId, username, password, firstName, lastName, mobile, email,
        isActive, userCode, companyIds, plazaIds
      } = req.body;

      // Validate required fields
      if (!roleId || !username || !password || !firstName || !lastName || !email) {
        return responseHandler.badRequest(res, 'Required fields missing');
      }

      // Check if username or email already exists
      const checkQuery = `
        SELECT 1 FROM Users
        WHERE (Username = @username OR Email = @email) AND IsActive = 1
      `;

      const checkResult = await db.query(checkQuery, { username, email });

      if (checkResult.recordset.length > 0) {
        return responseHandler.badRequest(res, 'Username or email already exists');
      }

      // Get role to validate company and plaza assignments
      const roleQuery = `SELECT Name FROM Roles WHERE Id = @roleId`;
      const roleResult = await db.query(roleQuery, { roleId });

      if (roleResult.recordset.length === 0) {
        return responseHandler.badRequest(res, 'Invalid role ID');
      }

      const roleName = roleResult.recordset[0].Name;

      // Validate company and plaza assignments based on role
      if (roleName === 'CompanyAdmin' && (!companyIds || companyIds.length === 0)) {
        return responseHandler.badRequest(res, 'Company Admin must be assigned to at least one company');
      }

      if (roleName === 'PlazaManager' && (!plazaIds || plazaIds.length === 0)) {
        return responseHandler.badRequest(res, 'Plaza Manager must be assigned to at least one plaza');
      }

      // Get current user ID for CreatedBy
      const createdBy = req.user ? req.user.id : 1; // Default to 1 if no user in request

      // Hash the password using bcrypt
      const hashedPassword = await bcrypt.hash(password, 10);

      // Insert user
      const insertQuery = `
        INSERT INTO Users (
          RoleId, Username, Password, FirstName, LastName, Mobile, Email,
          IsActive, CreatedBy, CreatedOn, UserCode
        )
        VALUES (
          @roleId, @username, @password, @firstName, @lastName, @mobile, @email,
          @isActive, @createdBy, GETDATE(), @userCode
        );
        SELECT SCOPE_IDENTITY() AS Id;
      `;

      const insertResult = await db.query(insertQuery, {
        roleId,
        username,
        password: hashedPassword,
        firstName,
        lastName,
        mobile,
        email,
        isActive: isActive !== undefined ? isActive : 1,
        createdBy,
        userCode
      });

      const userId = insertResult.recordset[0].Id;

      // Assign companies if provided
      if (companyIds && companyIds.length > 0) {
        for (const companyId of companyIds) {
          await db.query(`
            INSERT INTO UserCompany (UserId, CompanyId, IsActive, CreatedBy, CreatedOn)
            VALUES (@userId, @companyId, 1, @createdBy, GETDATE())
          `, {
            userId,
            companyId,
            createdBy
          });
        }
      }

      // Assign plazas if provided
      if (plazaIds && plazaIds.length > 0) {
        for (const plazaId of plazaIds) {
          await db.query(`
            INSERT INTO UserPlaza (UserId, PlazaId, IsActive, CreatedBy, CreatedOn)
            VALUES (@userId, @plazaId, 1, @createdBy, GETDATE())
          `, {
            userId,
            plazaId,
            createdBy
          });
        }
      }

      return responseHandler.created(res, { userId }, 'User created successfully');
    } catch (error) {
      console.error('Error in createUser controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Get current user information
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getCurrentUser(req, res) {
    try {
      const userId = req.user.id;

      // Query to get user with role information
      const query = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Id = @userId AND u.IsActive = 1
      `;

      const result = await db.query(query, { userId });

      if (result.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = result.recordset[0];

      // Get user permissions
      const permissionsQuery = `
        SELECT DISTINCT sm.Name as SubModule, p.Name as Permission
        FROM RolePermissions rp
        JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
        JOIN Permissions p ON smp.PermissionId = p.Id
        JOIN SubModules sm ON smp.SubModuleId = sm.Id
        WHERE rp.RoleId = @roleId AND rp.IsActive = 1
      `;

      const permissionsResult = await db.query(permissionsQuery, { roleId: user.RoleId });

      // Format permissions as an object for easier frontend use
      const permissions = {};
      permissionsResult.recordset.forEach(item => {
        if (!permissions[item.SubModule]) {
          permissions[item.SubModule] = [];
        }
        permissions[item.SubModule].push(item.Permission);
      });

      // Get companies for user
      let companies = [];

      if (user.RoleName === 'SuperAdmin') {
        const companiesQuery = `SELECT Id, CompanyName FROM tblCompanyMaster WHERE IsActive = 1`;
        const companiesResult = await db.query(companiesQuery);
        companies = companiesResult.recordset;
      } else {
        const companiesQuery = `
          SELECT c.Id, c.CompanyName
          FROM UserCompany uc
          JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND c.IsActive = 1
        `;
        const companiesResult = await db.query(companiesQuery, { userId });
        companies = companiesResult.recordset;
      }

      // Get plazas for user
      let plazas = [];

      if (user.RoleName === 'SuperAdmin') {
        const plazasQuery = `SELECT Id, PlazaName, CompanyId FROM Plaza WHERE IsActive = 1`;
        const plazasResult = await db.query(plazasQuery);
        plazas = plazasResult.recordset;
      } else if (user.RoleName === 'CompanyAdmin') {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName, p.CompanyId
          FROM Plaza p
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE uc.UserId = @userId AND uc.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId });
        plazas = plazasResult.recordset;
      } else {
        const plazasQuery = `
          SELECT p.Id, p.PlazaName, p.CompanyId
          FROM UserPlaza up
          JOIN Plaza p ON up.PlazaId = p.Id
          WHERE up.UserId = @userId AND up.IsActive = 1 AND p.IsActive = 1
        `;
        const plazasResult = await db.query(plazasQuery, { userId });
        plazas = plazasResult.recordset;
      }

      // Return user data
      return responseHandler.success(res, {
        id: user.Id,
        username: user.Username,
        firstName: user.FirstName,
        lastName: user.LastName,
        email: user.Email,
        role: user.RoleName,
        permissions: permissions,
        companies: companies,
        plazas: plazas
      }, 'User retrieved successfully');
    } catch (error) {
      console.error('Error in getCurrentUser controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Update an existing user
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async updateUser(req, res) {
    try {
      const userId = req.params.id;
      const {
        roleId, firstName, lastName, mobile, email,
        isActive, password, userCode, companyIds, plazaIds
      } = req.body;

      // Validate required fields
      if (!roleId || !firstName || !lastName || !email) {
        return responseHandler.badRequest(res, 'Required fields missing');
      }

      // Check if user exists
      const userQuery = `SELECT * FROM Users WHERE Id = @userId`;
      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const existingUser = userResult.recordset[0];

      // Check if email is already used by another user
      if (email !== existingUser.Email) {
        const emailCheckQuery = `
          SELECT 1 FROM Users
          WHERE Email = @email AND Id != @userId AND IsActive = 1
        `;

        const emailCheckResult = await db.query(emailCheckQuery, { email, userId });

        if (emailCheckResult.recordset.length > 0) {
          return responseHandler.badRequest(res, 'Email is already in use');
        }
      }

      // Get role to validate company and plaza assignments
      const roleQuery = `SELECT Name FROM Roles WHERE Id = @roleId`;
      const roleResult = await db.query(roleQuery, { roleId });

      if (roleResult.recordset.length === 0) {
        return responseHandler.badRequest(res, 'Invalid role ID');
      }

      const roleName = roleResult.recordset[0].Name;

      // Validate company and plaza assignments based on role
      if (roleName === 'CompanyAdmin' && (!companyIds || companyIds.length === 0)) {
        return responseHandler.badRequest(res, 'Company Admin must be assigned to at least one company');
      }

      if (roleName === 'PlazaManager' && (!plazaIds || plazaIds.length === 0)) {
        return responseHandler.badRequest(res, 'Plaza Manager must be assigned to at least one plaza');
      }

      // Get current user ID for ModifiedBy
      const modifiedBy = req.user ? req.user.id : 1; // Default to 1 if no user in request

      // Prepare password update if provided
      let passwordUpdate = '';
      let passwordParam = {};

      if (password) {
        // Hash the password using bcrypt
        const hashedPassword = await bcrypt.hash(password, 10);
        passwordUpdate = ', Password = @password';
        passwordParam = { password: hashedPassword };
      }

      // Update user
      const updateQuery = `
        UPDATE Users
        SET
          RoleId = @roleId,
          FirstName = @firstName,
          LastName = @lastName,
          Mobile = @mobile,
          Email = @email,
          IsActive = @isActive,
          ModifiedBy = @modifiedBy,
          ModifiedOn = GETDATE(),
          UserCode = @userCode
          ${passwordUpdate}
        WHERE Id = @userId
      `;

      await db.query(updateQuery, {
        userId,
        roleId,
        firstName,
        lastName,
        mobile,
        email,
        isActive: isActive !== undefined ? isActive : existingUser.IsActive,
        modifiedBy,
        userCode,
        ...passwordParam
      });

      // Update company assignments if provided
      if (companyIds) {
        // First, deactivate all existing company assignments
        await db.query(`
          UPDATE UserCompany
          SET IsActive = 0, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
          WHERE UserId = @userId
        `, { userId, modifiedBy });

        // Then, add or reactivate company assignments
        for (const companyId of companyIds) {
          // Check if assignment already exists
          const companyCheckQuery = `
            SELECT Id, IsActive FROM UserCompany
            WHERE UserId = @userId AND CompanyId = @companyId
          `;

          const companyCheckResult = await db.query(companyCheckQuery, { userId, companyId });

          if (companyCheckResult.recordset.length > 0) {
            // Update existing assignment
            const companyAssignmentId = companyCheckResult.recordset[0].Id;

            await db.query(`
              UPDATE UserCompany
              SET IsActive = 1, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
              WHERE Id = @id
            `, { id: companyAssignmentId, modifiedBy });
          } else {
            // Create new assignment
            await db.query(`
              INSERT INTO UserCompany (UserId, CompanyId, IsActive, CreatedBy, CreatedOn)
              VALUES (@userId, @companyId, 1, @modifiedBy, GETDATE())
            `, { userId, companyId, modifiedBy });
          }
        }
      }

      // Update plaza assignments if provided
      if (plazaIds) {
        // First, deactivate all existing plaza assignments
        await db.query(`
          UPDATE UserPlaza
          SET IsActive = 0, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
          WHERE UserId = @userId
        `, { userId, modifiedBy });

        // Then, add or reactivate plaza assignments
        for (const plazaId of plazaIds) {
          // Check if assignment already exists
          const plazaCheckQuery = `
            SELECT Id, IsActive FROM UserPlaza
            WHERE UserId = @userId AND PlazaId = @plazaId
          `;

          const plazaCheckResult = await db.query(plazaCheckQuery, { userId, plazaId });

          if (plazaCheckResult.recordset.length > 0) {
            // Update existing assignment
            const plazaAssignmentId = plazaCheckResult.recordset[0].Id;

            await db.query(`
              UPDATE UserPlaza
              SET IsActive = 1, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
              WHERE Id = @id
            `, { id: plazaAssignmentId, modifiedBy });
          } else {
            // Create new assignment
            await db.query(`
              INSERT INTO UserPlaza (UserId, PlazaId, IsActive, CreatedBy, CreatedOn)
              VALUES (@userId, @plazaId, 1, @modifiedBy, GETDATE())
            `, { userId, plazaId, modifiedBy });
          }
        }
      }

      return responseHandler.success(res, { userId }, 'User updated successfully');
    } catch (error) {
      console.error('Error in updateUser controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Delete a user (soft delete)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async deleteUser(req, res) {
    try {
      const userId = req.params.id;

      // Check if user exists
      const userQuery = `SELECT * FROM Users WHERE Id = @userId AND IsActive = 1`;
      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      // Get current user ID for ModifiedBy
      const modifiedBy = req.user ? req.user.id : 1; // Default to 1 if no user in request

      // Soft delete user
      const deleteQuery = `
        UPDATE Users
        SET IsActive = 0, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
        WHERE Id = @userId
      `;

      await db.query(deleteQuery, { userId, modifiedBy });

      // Deactivate company assignments
      await db.query(`
        UPDATE UserCompany
        SET IsActive = 0, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
        WHERE UserId = @userId
      `, { userId, modifiedBy });

      // Deactivate plaza assignments
      await db.query(`
        UPDATE UserPlaza
        SET IsActive = 0, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
        WHERE UserId = @userId
      `, { userId, modifiedBy });

      return responseHandler.success(res, null, 'User deleted successfully');
    } catch (error) {
      console.error('Error in deleteUser controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Change user's password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async changePassword(req, res) {
    try {
      const userId = req.params.id;
      const { currentPassword, newPassword } = req.body;

      // Validate required fields
      if (!currentPassword || !newPassword) {
        return responseHandler.badRequest(res, 'Current password and new password are required');
      }

      // Check if user exists
      const userQuery = `SELECT * FROM Users WHERE Id = @userId AND IsActive = 1`;
      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = userResult.recordset[0];

      // Check if current user is authorized to change this password
      // Only the user themselves or an admin can change a password
      if (req.user.id !== parseInt(userId) && req.user.role !== 'SuperAdmin') {
        return responseHandler.forbidden(res, 'You are not authorized to change this password');
      }

      // Verify current password
      // Verify current password using bcrypt
      const isPasswordValid = await bcrypt.compare(currentPassword, user.Password);
      if (!isPasswordValid) {
        return responseHandler.badRequest(res, 'Current password is incorrect');
      }

      // Get current user ID for ModifiedBy
      const modifiedBy = req.user ? req.user.id : 1; // Default to 1 if no user in request

      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update password
      const updateQuery = `
        UPDATE Users
        SET Password = @password, ModifiedBy = @modifiedBy, ModifiedOn = GETDATE()
        WHERE Id = @userId
      `;

      await db.query(updateQuery, {
        userId,
        password: hashedPassword,
        modifiedBy
      });

      return responseHandler.success(res, null, 'Password changed successfully');
    } catch (error) {
      console.error('Error in changePassword controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Get all roles
   * @param {Object} _ - Express request object (not used)
   * @param {Object} res - Express response object
   */
  static async getAllRoles(_, res) {
    try {
      const query = `SELECT * FROM Roles WHERE IsActive = 1 ORDER BY Name`;
      const result = await db.query(query);

      return responseHandler.success(res, result.recordset, 'Roles retrieved successfully');
    } catch (error) {
      console.error('Error in getAllRoles controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Get user's company assignments
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getUserCompanies(req, res) {
    try {
      const userId = req.params.id;

      // Check if user exists
      const userQuery = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Id = @userId AND u.IsActive = 1
      `;

      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = userResult.recordset[0];

      // Get companies for user
      let companies = [];

      if (user.RoleName === 'SuperAdmin') {
        const companiesQuery = `
          SELECT c.Id, c.CompanyName, 1 as IsAssigned
          FROM tblCompanyMaster c
          WHERE c.IsActive = 1
          ORDER BY c.CompanyName
        `;
        const companiesResult = await db.query(companiesQuery);
        companies = companiesResult.recordset;
      } else {
        // Get all companies and mark assigned ones
        const companiesQuery = `
          SELECT
            c.Id,
            c.CompanyName,
            CASE WHEN uc.Id IS NULL THEN 0 ELSE 1 END as IsAssigned
          FROM tblCompanyMaster c
          LEFT JOIN UserCompany uc ON c.Id = uc.CompanyId AND uc.UserId = @userId AND uc.IsActive = 1
          WHERE c.IsActive = 1
          ORDER BY c.CompanyName
        `;
        const companiesResult = await db.query(companiesQuery, { userId });
        companies = companiesResult.recordset;
      }

      return responseHandler.success(res, companies, 'User companies retrieved successfully');
    } catch (error) {
      console.error('Error in getUserCompanies controller:', error);
      return responseHandler.error(res, error.message);
    }
  }

  /**
   * Get user's plaza assignments
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  static async getUserPlazas(req, res) {
    try {
      const userId = req.params.id;

      // Check if user exists
      const userQuery = `
        SELECT u.*, r.Name as RoleName
        FROM Users u
        JOIN Roles r ON u.RoleId = r.Id
        WHERE u.Id = @userId AND u.IsActive = 1
      `;

      const userResult = await db.query(userQuery, { userId });

      if (userResult.recordset.length === 0) {
        return responseHandler.notFound(res, 'User not found');
      }

      const user = userResult.recordset[0];

      // Get plazas for user
      let plazas = [];

      if (user.RoleName === 'SuperAdmin') {
        const plazasQuery = `
          SELECT
            p.Id,
            p.PlazaName,
            p.CompanyId,
            c.CompanyName,
            1 as IsAssigned
          FROM Plaza p
          JOIN tblCompanyMaster c ON p.CompanyId = c.Id
          WHERE p.IsActive = 1 AND c.IsActive = 1
          ORDER BY c.CompanyName, p.PlazaName
        `;
        const plazasResult = await db.query(plazasQuery);
        plazas = plazasResult.recordset;
      } else if (user.RoleName === 'CompanyAdmin') {
        // Get all plazas for companies the user has access to and mark assigned ones
        const plazasQuery = `
          SELECT
            p.Id,
            p.PlazaName,
            p.CompanyId,
            c.CompanyName,
            1 as IsAssigned
          FROM Plaza p
          JOIN tblCompanyMaster c ON p.CompanyId = c.Id
          JOIN UserCompany uc ON p.CompanyId = uc.CompanyId
          WHERE uc.UserId = @userId AND uc.IsActive = 1
            AND p.IsActive = 1 AND c.IsActive = 1
          ORDER BY c.CompanyName, p.PlazaName
        `;
        const plazasResult = await db.query(plazasQuery, { userId });
        plazas = plazasResult.recordset;
      } else {
        // Get all plazas and mark assigned ones
        const plazasQuery = `
          SELECT
            p.Id,
            p.PlazaName,
            p.CompanyId,
            c.CompanyName,
            CASE WHEN up.Id IS NULL THEN 0 ELSE 1 END as IsAssigned
          FROM Plaza p
          JOIN tblCompanyMaster c ON p.CompanyId = c.Id
          LEFT JOIN UserPlaza up ON p.Id = up.PlazaId AND up.UserId = @userId AND up.IsActive = 1
          WHERE p.IsActive = 1 AND c.IsActive = 1
          ORDER BY c.CompanyName, p.PlazaName
        `;
        const plazasResult = await db.query(plazasQuery, { userId });
        plazas = plazasResult.recordset;
      }

      return responseHandler.success(res, plazas, 'User plazas retrieved successfully');
    } catch (error) {
      console.error('Error in getUserPlazas controller:', error);
      return responseHandler.error(res, error.message);
    }
  }
}

module.exports = UserController;