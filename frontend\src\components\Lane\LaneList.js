import React, { useState, useRef } from 'react';
import {
  Search,
  Edit,
  Trash2,
  ToggleLeft,
  ToggleRight,
  Download,
  Columns,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight
} from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';
import { isStatusActive, getStatusLabel } from '../../utils/statusUtils';

/**
 * ===============================================================================
 * # Lane List Component
 * ===============================================================================
 *
 * A comprehensive data table component for displaying and managing lanes in the
 * parking management system. Features include:
 *
 * - Sortable columns with ascending/descending toggle
 * - Customizable column visibility
 * - Search functionality across multiple fields
 * - Pagination with configurable items per page
 * - CSV export capability
 * - Responsive design for mobile and desktop
 * - Row actions for editing, deleting, and toggling lane status
 *
 * @param {Array} lanes - Array of lane objects to display
 * @param {Function} onEdit - Callback when edit button is clicked
 * @param {Function} onDelete - Callback when delete button is clicked
 * @param {Function} onSelect - Callback when a lane row is clicked
 * @param {Function} onToggleStatus - Callback when status toggle button is clicked
 * @returns {JSX.Element} The rendered lane list component
 */
export default function LaneList({ lanes, onEdit, onDelete, onSelect, onToggleStatus }) {
  // -------------------------------------------------------------------------------
  // State Management
  // -------------------------------------------------------------------------------

  /**
   * Search term for filtering lanes
   * Used to filter lanes by LaneNumber, LaneType, PlazaName, VehicleType, and LaneIP
   */
  const [searchTerm, setSearchTerm] = useState('');

  /**
   * Configuration for sorting the lane table
   * - key: The column to sort by
   * - direction: 'ascending' or 'descending'
   */
  const [sortConfig, setSortConfig] = useState({ key: 'LaneNumber', direction: 'ascending' });

  /**
   * Current page number for pagination
   */
  const [currentPage, setCurrentPage] = useState(1);

  /**
   * Number of items to display per page
   */
  const [itemsPerPage, setItemsPerPage] = useState(10);

  /**
   * Configuration for which columns are visible in the table
   * Each property corresponds to a column and its visibility state
   */
  const [visibleColumns, setVisibleColumns] = useState({
    LaneNumber: true,   // Lane number column
    LaneType: true,     // Lane type column (Entry/Exit)
    PlazaName: true,    // Plaza name column
    VehicleType: true,  // Vehicle type column
    LaneIP: true,       // Lane IP address column
    ActiveStatus: true, // Status column (Active/Inactive)
    Actions: true       // Actions column (edit, delete, toggle)
  });

  /**
   * Whether the column selector modal is visible
   */
  const [showColumnSelector, setShowColumnSelector] = useState(false);

  /**
   * Reference to the table element for scrolling
   */
  const tableRef = useRef(null);

  // -------------------------------------------------------------------------------
  // Data Processing
  // -------------------------------------------------------------------------------

  /**
   * Filter lanes based on search term
   * Searches across multiple fields: LaneNumber, LaneType, PlazaName, VehicleType, LaneIP
   */
  const filteredLanes = lanes.filter(lane =>
    String(lane.LaneNumber).toLowerCase().includes(searchTerm.toLowerCase()) ||
    (lane.LaneType && lane.LaneType.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (lane.PlazaName && lane.PlazaName.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (lane.VehicleType && lane.VehicleType.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (lane.LaneIP && lane.LaneIP.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  /**
   * Sort lanes based on sortConfig
   * Handles null values by placing them at the end of the list
   */
  const sortedLanes = [...filteredLanes].sort((a, b) => {
    // Handle cases where one or both values are null/undefined
    if (!a[sortConfig.key] && !b[sortConfig.key]) return 0;
    if (!a[sortConfig.key]) return 1; // Null values go to the end
    if (!b[sortConfig.key]) return -1;

    // Compare values based on sort direction
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === 'ascending' ? 1 : -1;
    }
    return 0; // Values are equal
  });

  // -------------------------------------------------------------------------------
  // Pagination Logic
  // -------------------------------------------------------------------------------

  /**
   * Calculate indices for pagination
   */
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;

  /**
   * Get current items to display based on pagination
   */
  const currentItems = sortedLanes.slice(indexOfFirstItem, indexOfLastItem);

  /**
   * Calculate total number of pages
   */
  const totalPages = Math.ceil(sortedLanes.length / itemsPerPage);

  /**
   * Navigate to a specific page
   * @param {number} pageNumber - The page number to navigate to
   */
  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  // -------------------------------------------------------------------------------
  // UI Interaction Handlers
  // -------------------------------------------------------------------------------

  /**
   * Handle column sort request
   * @param {string} key - The column key to sort by
   */
  const requestSort = (key) => {
    let direction = 'ascending';
    // If already sorting by this key, toggle direction
    if (sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  /**
   * Get sort indicator (↑/↓) for column headers
   * @param {string} key - The column key to check
   * @returns {string} Sort indicator arrow or empty string
   */
  const getSortIndicator = (key) => {
    if (sortConfig.key === key) {
      return sortConfig.direction === 'ascending' ? '↑' : '↓';
    }
    return '';
  };

  /**
   * Toggle visibility of a specific column
   * @param {string} column - The column key to toggle
   */
  const toggleColumnVisibility = (column) => {
    setVisibleColumns(prev => ({
      ...prev,
      [column]: !prev[column]
    }));
  };

  /**
   * Export visible lane data to CSV file
   * Excludes the Actions column and formats data appropriately
   */
  const exportToCSV = () => {
    // -------------------------------------------------------------------------------
    // Step 1: Prepare CSV data
    // -------------------------------------------------------------------------------

    // Get visible columns (excluding Actions)
    const headers = Object.keys(visibleColumns)
      .filter(col => visibleColumns[col] && col !== 'Actions')
      .map(col => col);

    // Create CSV header row
    let csvContent = headers.join(',') + '\n';

    // Add data rows
    sortedLanes.forEach(lane => {
      const row = headers.map(header => {
        // Format special fields
        if (header === 'ActiveStatus') {
          return getStatusLabel(lane[header]);
        }
        return lane[header] || ''; // Use empty string for null/undefined values
      }).join(',');
      csvContent += row + '\n';
    });

    // -------------------------------------------------------------------------------
    // Step 2: Create and download the file
    // -------------------------------------------------------------------------------
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `lanes_export_${new Date().toISOString().slice(0, 10)}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="overflow-hidden">
      {/* =============================================================================== */}
      {/* ## TOOLBAR SECTION */}
      {/* =============================================================================== */}
      <div className="p-4 border-b">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          {/* -------------------------------------------------------------------------------
           * Search Input
           * Allows filtering lanes by multiple fields
           * ------------------------------------------------------------------------------- */}
          <div className="relative w-full md:w-1/2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
            <input
              type="text"
              placeholder="Search lanes..."
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* -------------------------------------------------------------------------------
           * Table Controls
           * Contains pagination, column visibility, and export controls
           * ------------------------------------------------------------------------------- */}
          <div className="flex items-center space-x-2 w-full md:w-auto">
            {/* Items per page selector - Controls pagination */}
            <select
              className="border border-gray-300 rounded-md px-2 py-1 text-sm"
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1); // Reset to first page when changing items per page
              }}
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
            </select>

            {/* Column visibility toggle - Controls which columns are displayed */}
            <div className="relative">
              <button
                onClick={() => setShowColumnSelector(!showColumnSelector)}
                className="flex items-center space-x-1 bg-white border border-gray-300 rounded-md px-3 py-1 text-sm hover:bg-gray-50"
                title="Toggle columns"
              >
                <Columns size={16} />
                <span className="hidden md:inline">Columns</span>
              </button>

              {/* Full-screen modal for column selection */}
              {showColumnSelector && (
                <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
                  <div className="bg-white rounded-lg p-6 max-w-md w-full max-h-[80vh] overflow-auto">
                    {/* Modal header with title and close button */}
                    <div className="flex justify-between items-center mb-4">
                      <h3 className="text-lg font-medium text-gray-900">Toggle Columns</h3>
                      <button
                        onClick={() => setShowColumnSelector(false)}
                        className="text-gray-400 hover:text-gray-500"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>

                    {/* Column checkboxes */}
                    <div className="space-y-3">
                      {Object.keys(visibleColumns).map(column => (
                        <div key={column} className="flex items-center">
                          <input
                            type="checkbox"
                            id={`column-${column}`}
                            checked={visibleColumns[column]}
                            onChange={() => toggleColumnVisibility(column)}
                            className="h-5 w-5 text-blue-600 border-gray-300 rounded"
                          />
                          <label htmlFor={`column-${column}`} className="ml-3 text-sm font-medium text-gray-700">
                            {column}
                          </label>
                        </div>
                      ))}
                    </div>

                    {/* Modal footer with done button */}
                    <div className="mt-6 flex justify-end">
                      <button
                        onClick={() => setShowColumnSelector(false)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                      >
                        Done
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Export button - Exports visible data to CSV */}
            <button
              onClick={exportToCSV}
              className="flex items-center space-x-1 bg-green-600 text-white rounded-md px-3 py-1 text-sm hover:bg-green-700"
              title="Export to CSV"
            >
              <Download size={16} />
              <span className="hidden md:inline">Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* =============================================================================== */}
      {/* ## DATA TABLE SECTION */}
      {/* =============================================================================== */}
      <div className="overflow-x-auto" ref={tableRef}>
        <table className="min-w-full divide-y divide-gray-200">
          {/* -------------------------------------------------------------------------------
           * Table Header
           * Contains sortable column headers that respond to clicks
           * ------------------------------------------------------------------------------- */}
          <thead className="bg-gray-50">
            <tr>
              {/* Lane Number Column */}
              {visibleColumns.LaneNumber && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('LaneNumber')}
                >
                  Lane Number {getSortIndicator('LaneNumber')}
                </th>
              )}

              {/* Lane Type Column */}
              {visibleColumns.LaneType && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('LaneType')}
                >
                  Lane Type {getSortIndicator('LaneType')}
                </th>
              )}

              {/* Plaza Name Column */}
              {visibleColumns.PlazaName && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('PlazaName')}
                >
                  Plaza {getSortIndicator('PlazaName')}
                </th>
              )}

              {/* Vehicle Type Column */}
              {visibleColumns.VehicleType && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('VehicleType')}
                >
                  Vehicle Type {getSortIndicator('VehicleType')}
                </th>
              )}

              {/* Lane IP Column */}
              {visibleColumns.LaneIP && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('LaneIP')}
                >
                  Lane IP {getSortIndicator('LaneIP')}
                </th>
              )}

              {/* Active Status Column */}
              {visibleColumns.ActiveStatus && (
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                  onClick={() => requestSort('ActiveStatus')}
                >
                  Status {getSortIndicator('ActiveStatus')}
                </th>
              )}

              {/* Actions Column - Not sortable */}
              {visibleColumns.Actions && (
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              )}
            </tr>
          </thead>

          {/* -------------------------------------------------------------------------------
           * Table Body
           * Displays lane data with interactive rows
           * ------------------------------------------------------------------------------- */}
          <tbody className="bg-white divide-y divide-gray-200">
            {currentItems.length > 0 ? (
              currentItems.map((lane) => (
                <tr
                  key={lane.LaneID}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onSelect(lane.LaneID)}
                >
                  {/* Lane Number Cell */}
                  {visibleColumns.LaneNumber && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{lane.LaneNumber}</td>
                  )}

                  {/* Lane Type Cell */}
                  {visibleColumns.LaneType && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {lane.LaneType === 'Entry' ? 'Entry' : lane.LaneType === 'Exit' ? 'Exit' : lane.LaneType}
                    </td>
                  )}

                  {/* Plaza Name Cell */}
                  {visibleColumns.PlazaName && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{lane.PlazaName}</td>
                  )}

                  {/* Vehicle Type Cell */}
                  {visibleColumns.VehicleType && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{lane.VehicleType}</td>
                  )}

                  {/* Lane IP Cell */}
                  {visibleColumns.LaneIP && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{lane.LaneIP}</td>
                  )}

                  {/* Active Status Cell - With colored badge */}
                  {visibleColumns.ActiveStatus && (
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          isStatusActive(lane.ActiveStatus)
                            ? 'bg-green-100 text-green-800' // Green for active
                            : 'bg-red-100 text-red-800'     // Red for inactive
                        }`}
                      >
                        {getStatusLabel(lane.ActiveStatus)}
                      </span>
                    </td>
                  )}

                  {/* Actions Cell - Contains toggle, edit, and delete buttons */}
                  {visibleColumns.Actions && (
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex space-x-2 justify-end">
                        {/* Toggle Status Button */}
                        <PermissionButton
                          requiredModule="Lanes"
                          requiredPermissions={["Edit"]}
                          companyId={lane.CompanyID}
                          plazaId={lane.PlazaID}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click event
                            // Pass the lane object instead of just the ID
                            onToggleStatus(lane);
                          }}
                          className={`text-gray-600 hover:text-gray-900 ${
                            isStatusActive(lane.ActiveStatus)
                              ? 'text-green-600'
                              : 'text-red-600'
                          }`}
                          title={
                            isStatusActive(lane.ActiveStatus)
                              ? 'Deactivate'
                              : 'Activate'
                          }
                        >
                          {isStatusActive(lane.ActiveStatus)
                            ? <ToggleRight size={18} />
                            : <ToggleLeft size={18} />}
                        </PermissionButton>

                        {/* Edit Button */}
                        <PermissionButton
                          requiredModule="Lanes"
                          requiredPermissions={["Edit"]}
                          companyId={lane.CompanyID}
                          plazaId={lane.PlazaID}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click event
                            onEdit(lane);
                          }}
                          className="text-blue-600 hover:text-blue-900"
                          title="Edit"
                        >
                          <Edit size={18} />
                        </PermissionButton>

                        {/* Delete Button */}
                        <PermissionButton
                          requiredModule="Lanes"
                          requiredPermissions={["Delete"]}
                          companyId={lane.CompanyID}
                          plazaId={lane.PlazaID}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent row click event
                            onDelete(lane.LaneID);
                          }}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <Trash2 size={18} />
                        </PermissionButton>
                      </div>
                    </td>
                  )}
                </tr>
              ))
            ) : (
              /* Empty state row - Displayed when no lanes match the filter */
              <tr>
                <td colSpan={Object.values(visibleColumns).filter(Boolean).length} className="px-6 py-4 text-center text-sm text-gray-500">
                  No lanes found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* =============================================================================== */}
      {/* ## PAGINATION SECTION */}
      {/* =============================================================================== */}
      {sortedLanes.length > 0 && (
        <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          {/* -------------------------------------------------------------------------------
           * Desktop Pagination
           * Full-featured pagination with page numbers and navigation controls
           * ------------------------------------------------------------------------------- */}
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            {/* Results summary - Shows current range and total */}
            <div>
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{indexOfFirstItem + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(indexOfLastItem, sortedLanes.length)}
                </span>{' '}
                of <span className="font-medium">{sortedLanes.length}</span> results
              </p>
            </div>

            {/* Pagination controls */}
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                {/* First page button */}
                <button
                  onClick={() => paginate(1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <span className="sr-only">First</span>
                  <ChevronsLeft className="h-5 w-5" aria-hidden="true" />
                </button>

                {/* Previous page button */}
                <button
                  onClick={() => paginate(currentPage - 1)}
                  disabled={currentPage === 1}
                  className={`relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <span className="sr-only">Previous</span>
                  <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                </button>

                {/* -------------------------------------------------------------------------------
                 * Page number buttons
                 * Shows current page, first/last pages, and pages around current page
                 * Uses ellipsis (...) for large page ranges
                 * ------------------------------------------------------------------------------- */}
                {[...Array(totalPages).keys()].map(number => {
                  // Show current page, 2 pages before and after current page, and first/last pages
                  if (
                    number + 1 === 1 ||                                                // Always show first page
                    number + 1 === totalPages ||                                       // Always show last page
                    (number + 1 >= currentPage - 2 && number + 1 <= currentPage + 2)   // Show pages around current page
                  ) {
                    return (
                      <button
                        key={number + 1}
                        onClick={() => paginate(number + 1)}
                        className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                          currentPage === number + 1
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'  // Highlight current page
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                        }`}
                      >
                        {number + 1}
                      </button>
                    );
                  } else if (
                    // Show ellipsis for page gaps
                    (number + 1 === currentPage - 3 && currentPage > 4) ||             // Show ellipsis before current page range
                    (number + 1 === currentPage + 3 && currentPage < totalPages - 3)   // Show ellipsis after current page range
                  ) {
                    return (
                      <span
                        key={number + 1}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                      >
                        ...
                      </span>
                    );
                  }
                  return null; // Hide other pages
                })}

                {/* Next page button */}
                <button
                  onClick={() => paginate(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <span className="sr-only">Next</span>
                  <ChevronRight className="h-5 w-5" aria-hidden="true" />
                </button>

                {/* Last page button */}
                <button
                  onClick={() => paginate(totalPages)}
                  disabled={currentPage === totalPages}
                  className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                    currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  <span className="sr-only">Last</span>
                  <ChevronsRight className="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>

          {/* -------------------------------------------------------------------------------
           * Mobile Pagination
           * Simplified pagination for small screens with previous/next buttons
           * ------------------------------------------------------------------------------- */}
          <div className="flex items-center justify-between w-full sm:hidden">
            {/* Previous button */}
            <button
              onClick={() => paginate(currentPage - 1)}
              disabled={currentPage === 1}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                currentPage === 1 ? 'text-gray-300 bg-gray-100' : 'text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              Previous
            </button>

            {/* Page indicator */}
            <span className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>

            {/* Next button */}
            <button
              onClick={() => paginate(currentPage + 1)}
              disabled={currentPage === totalPages}
              className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md ${
                currentPage === totalPages ? 'text-gray-300 bg-gray-100' : 'text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
