-- Update the QR Code validation stored procedure
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_QRCode_GetByData]
    @QRCodeData NVARCHAR(500)
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        -- Validation
        IF @QRCodeData IS NULL OR LTRIM(RTRIM(@QRCodeData)) = ''
        BEGIN
            RAISERROR('QR Code data is required', 16, 1);
            RETURN -1;
        END

        -- Get QR code details with plaza and valet point information
        SELECT
            vqr.[Id],
            vqr.[PlazaValetPointId],
            vqr.[PlazaId],
            vqr.[QRCodeData],
            vqr.[IsActive],
            vqr.[CreatedBy],
            vqr.[CreatedOn],
            p.[PlazaName],
            p.[Id] as PlazaId,
            c.[CompanyName],
            c.[Id] as CompanyId,
            pvp.[ValetPointName],
            pvp.[Id] as ValetPointId,
            pvp.[ParkingFee],
            pvp.[IsActive] as ValetPointActive
        FROM [dbo].[ValetQRCodes] vqr
        JOIN [dbo].[Plaza] p ON vqr.[PlazaId] = p.[Id]
        JOIN [dbo].[tblCompanyMaster] c ON p.[CompanyId] = c.[Id]
        LEFT JOIN [dbo].[PlazaValetPoint] pvp ON vqr.[PlazaValetPointId] = pvp.[Id]
        WHERE vqr.[QRCodeData] = @QRCodeData AND vqr.[IsActive] = 1 AND p.[IsActive] = 1
        ORDER BY vqr.[CreatedOn] DESC;

        -- Check if QR code exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'QR Code not found' AS Message, 0 AS Success;
        END

    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            ERROR_SEVERITY() AS ErrorSeverity,
            ERROR_STATE() AS ErrorState,
            ERROR_PROCEDURE() AS ErrorProcedure,
            ERROR_LINE() AS ErrorLine;
    END CATCH
END
GO
