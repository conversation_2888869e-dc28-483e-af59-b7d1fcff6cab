# PowerShell script to check accr user's plaza assignments using sqlcmd
# Using credentials from .env file

Write-Host "=== CHECKING ACCR USER PLAZA ASSIGNMENTS ===" -ForegroundColor Cyan

# Database credentials from .env file
$Server = "parkwizvms.database.windows.net"
$Database = "ParkwizOps"
$Username = "hparkwiz"
$Password = "Parkwiz@2020"

Write-Host "Connecting to database: $Database on $Server" -ForegroundColor Yellow

# SQL query to check accr user's details and plaza assignments
$SqlQuery = @"
-- Check accr user details
SELECT 'USER DETAILS:' as Section, '' as Data1, '' as Data2, '' as Data3, '' as Data4, '' as Data5
UNION ALL
SELECT 'Username', u.Username, u.FirstName, u.LastName, u.Email, r.Name as RoleName
FROM Users u
LEFT JOIN Roles r ON u.RoleId = r.Id
WHERE u.Username = 'accr'

UNION ALL
SELECT '', '', '', '', '', ''

UNION ALL
SELECT 'PLAZA ASSIGNMENTS:' as Section, '' as Data1, '' as Data2, '' as Data3, '' as Data4, '' as Data5

UNION ALL
SELECT 
    'Plaza Assignment',
    p.PlazaName,
    p.PlazaCode,
    c.CompanyName,
    CASE WHEN up.IsActive = 1 THEN 'ACTIVE' ELSE 'INACTIVE' END,
    CONVERT(varchar, up.CreatedOn, 120)
FROM Users u
INNER JOIN UserPlaza up ON u.Id = up.UserId
INNER JOIN Plaza p ON up.PlazaId = p.Id
INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
WHERE u.Username = 'accr'

UNION ALL
SELECT '', '', '', '', '', ''

UNION ALL
SELECT 'COMPANY ASSIGNMENTS:' as Section, '' as Data1, '' as Data2, '' as Data3, '' as Data4, '' as Data5

UNION ALL
SELECT 
    'Company Assignment',
    c.CompanyName,
    c.ContactPerson,
    c.ContactEmail,
    CASE WHEN uc.IsActive = 1 THEN 'ACTIVE' ELSE 'INACTIVE' END,
    CONVERT(varchar, uc.CreatedOn, 120)
FROM Users u
LEFT JOIN UserCompany uc ON u.Id = uc.UserId
LEFT JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
WHERE u.Username = 'accr'

ORDER BY Section DESC, Data1;
"@

try {
    # Use sqlcmd to execute the query
    Write-Host "Executing SQL query..." -ForegroundColor Green
    
    $result = sqlcmd -S $Server -d $Database -U $Username -P $Password -Q $SqlQuery -h -1 -s "|" -W
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nQuery Results:" -ForegroundColor Green
        Write-Host "============================================" -ForegroundColor Cyan
        
        # Parse and format the results
        $lines = $result | Where-Object { $_ -and $_.Trim() -ne "" }
        
        foreach ($line in $lines) {
            $fields = $line -split '\|'
            if ($fields.Count -ge 6) {
                $section = $fields[0].Trim()
                $data1 = $fields[1].Trim()
                $data2 = $fields[2].Trim()
                $data3 = $fields[3].Trim()
                $data4 = $fields[4].Trim()
                $data5 = $fields[5].Trim()
                
                if ($section -like "*DETAILS*" -or $section -like "*ASSIGNMENTS*") {
                    Write-Host "`n$section" -ForegroundColor Magenta
                    Write-Host "--------------------------------------------" -ForegroundColor Gray
                } elseif ($section -eq "Username") {
                    Write-Host "Username: $data1" -ForegroundColor White
                    Write-Host "Name: $data2 $data3" -ForegroundColor White
                    Write-Host "Email: $data4" -ForegroundColor White
                    Write-Host "Role: $data5" -ForegroundColor Yellow
                } elseif ($section -eq "Plaza Assignment") {
                    Write-Host "• Plaza: $data1 (Code: $data2)" -ForegroundColor White
                    Write-Host "  Company: $data3" -ForegroundColor Gray
                    Write-Host "  Status: $data4" -ForegroundColor $(if ($data4 -eq "ACTIVE") { "Green" } else { "Red" })
                    Write-Host "  Assigned On: $data5" -ForegroundColor Gray
                    Write-Host ""
                } elseif ($section -eq "Company Assignment") {
                    Write-Host "• Company: $data1" -ForegroundColor White
                    Write-Host "  Contact: $data2" -ForegroundColor Gray
                    Write-Host "  Email: $data3" -ForegroundColor Gray
                    Write-Host "  Status: $data4" -ForegroundColor $(if ($data4 -eq "ACTIVE") { "Green" } else { "Red" })
                    Write-Host "  Assigned On: $data5" -ForegroundColor Gray
                    Write-Host ""
                }
            }
        }
    } else {
        Write-Host "Error executing SQL query. Exit code: $LASTEXITCODE" -ForegroundColor Red
        Write-Host "Output: $result" -ForegroundColor Red
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Alternative query using direct SQL connection if sqlcmd fails
Write-Host "`n=== ALTERNATIVE METHOD (Direct SQL Connection) ===" -ForegroundColor Cyan

try {
    # Load System.Data.SqlClient
    Add-Type -AssemblyName System.Data
    
    # Build connection string
    $connectionString = "Server=$Server;Database=$Database;User Id=$Username;Password=$Password;Encrypt=true;TrustServerCertificate=true;Connection Timeout=30;"
    
    # Create connection
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "Connected successfully!" -ForegroundColor Green
    
    # Query for user details
    $userQuery = @"
    SELECT 
        u.Id,
        u.Username,
        u.FirstName,
        u.LastName,
        u.Email,
        r.Name as RoleName,
        u.IsActive
    FROM Users u
    LEFT JOIN Roles r ON u.RoleId = r.Id
    WHERE u.Username = 'accr'
"@
    
    $command = New-Object System.Data.SqlClient.SqlCommand($userQuery, $connection)
    $adapter = New-Object System.Data.SqlClient.SqlDataAdapter($command)
    $userTable = New-Object System.Data.DataTable
    $adapter.Fill($userTable)
    
    if ($userTable.Rows.Count -gt 0) {
        $user = $userTable.Rows[0]
        Write-Host "`nUSER DETAILS:" -ForegroundColor Magenta
        Write-Host "ID: $($user.Id)" -ForegroundColor White
        Write-Host "Username: $($user.Username)" -ForegroundColor White
        Write-Host "Name: $($user.FirstName) $($user.LastName)" -ForegroundColor White
        Write-Host "Email: $($user.Email)" -ForegroundColor White
        Write-Host "Role: $($user.RoleName)" -ForegroundColor Yellow
        Write-Host "Active: $($user.IsActive)" -ForegroundColor $(if ($user.IsActive) { "Green" } else { "Red" })
        
        $userId = $user.Id
        
        # Query for plaza assignments
        $plazaQuery = @"
        SELECT 
            p.Id as PlazaId,
            p.PlazaName,
            p.PlazaCode,
            c.CompanyName,
            c.Id as CompanyId,
            up.IsActive as AssignmentActive,
            up.CreatedOn as AssignedOn,
            p.IsActive as PlazaActive
        FROM UserPlaza up
        INNER JOIN Plaza p ON up.PlazaId = p.Id
        INNER JOIN tblCompanyMaster c ON p.CompanyId = c.Id
        WHERE up.UserId = @userId
        ORDER BY up.CreatedOn DESC
"@
        
        $command2 = New-Object System.Data.SqlClient.SqlCommand($plazaQuery, $connection)
        $command2.Parameters.AddWithValue("@userId", $userId)
        $adapter2 = New-Object System.Data.SqlClient.SqlDataAdapter($command2)
        $plazaTable = New-Object System.Data.DataTable
        $adapter2.Fill($plazaTable)
        
        Write-Host "`nPLAZA ASSIGNMENTS:" -ForegroundColor Magenta
        if ($plazaTable.Rows.Count -gt 0) {
            Write-Host "Total assignments: $($plazaTable.Rows.Count)" -ForegroundColor Cyan
            
            foreach ($plaza in $plazaTable.Rows) {
                $assignmentStatus = if ($plaza.AssignmentActive) { "ACTIVE" } else { "INACTIVE" }
                $plazaStatus = if ($plaza.PlazaActive) { "ACTIVE" } else { "INACTIVE" }
                
                Write-Host "`n• Plaza: $($plaza.PlazaName) (Code: $($plaza.PlazaCode))" -ForegroundColor White
                Write-Host "  Plaza ID: $($plaza.PlazaId)" -ForegroundColor Gray
                Write-Host "  Company: $($plaza.CompanyName) (ID: $($plaza.CompanyId))" -ForegroundColor Gray
                Write-Host "  Assignment Status: $assignmentStatus" -ForegroundColor $(if ($plaza.AssignmentActive) { "Green" } else { "Red" })
                Write-Host "  Plaza Status: $plazaStatus" -ForegroundColor $(if ($plaza.PlazaActive) { "Green" } else { "Red" })
                Write-Host "  Assigned On: $($plaza.AssignedOn)" -ForegroundColor Gray
            }
        } else {
            Write-Host "No plaza assignments found for user 'accr'" -ForegroundColor Red
        }
        
        # Query for company assignments
        $companyQuery = @"
        SELECT 
            c.Id as CompanyId,
            c.CompanyName,
            c.ContactPerson,
            c.ContactEmail,
            uc.IsActive as AssignmentActive,
            uc.CreatedOn as AssignedOn
        FROM UserCompany uc
        INNER JOIN tblCompanyMaster c ON uc.CompanyId = c.Id
        WHERE uc.UserId = @userId
        ORDER BY uc.CreatedOn DESC
"@
        
        $command3 = New-Object System.Data.SqlClient.SqlCommand($companyQuery, $connection)
        $command3.Parameters.AddWithValue("@userId", $userId)
        $adapter3 = New-Object System.Data.SqlClient.SqlDataAdapter($command3)
        $companyTable = New-Object System.Data.DataTable
        $adapter3.Fill($companyTable)
        
        Write-Host "`nCOMPANY ASSIGNMENTS:" -ForegroundColor Magenta
        if ($companyTable.Rows.Count -gt 0) {
            Write-Host "Total assignments: $($companyTable.Rows.Count)" -ForegroundColor Cyan
            
            foreach ($company in $companyTable.Rows) {
                $status = if ($company.AssignmentActive) { "ACTIVE" } else { "INACTIVE" }
                
                Write-Host "`n• Company: $($company.CompanyName) (ID: $($company.CompanyId))" -ForegroundColor White
                Write-Host "  Contact: $($company.ContactPerson)" -ForegroundColor Gray
                Write-Host "  Email: $($company.ContactEmail)" -ForegroundColor Gray
                Write-Host "  Status: $status" -ForegroundColor $(if ($company.AssignmentActive) { "Green" } else { "Red" })
                Write-Host "  Assigned On: $($company.AssignedOn)" -ForegroundColor Gray
            }
        } else {
            Write-Host "No company assignments found for user 'accr'" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "User 'accr' not found in the database" -ForegroundColor Red
    }
    
    $connection.Close()
    
} catch {
    Write-Host "Error with direct SQL connection: $($_.Exception.Message)" -ForegroundColor Red
    if ($connection.State -eq 'Open') {
        $connection.Close()
    }
}

Write-Host "`n=== CHECK COMPLETE ===" -ForegroundColor Cyan