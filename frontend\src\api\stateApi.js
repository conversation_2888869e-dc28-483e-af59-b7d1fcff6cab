import api from '../services/api'; // Using the shared axios instance

export const stateApi = {
  /**
   * Fetches the list of all states.
   * GET /states
   */
  getStates: async () => {
    const response = await api.get('/states'); // Matches router.get('/')
    return response.data.states;
  },

  /**
   * Fetches the details of a single state by its ID.
   * GET /states/:id
   */
  getStateById: async (id) => {
    const response = await api.get(`/states/${id}`); // Matches router.get('/:id')
    return response.data;
  },

  /**
   * Creates a new state.
   * POST /states
   */
  createState: async (data) => {
    // Convert frontend field names to match backend expectations
    const formattedData = {
      name: data.Name,
      countryId: data.CountryId,
      isActive: data.IsActive
    };
    const response = await api.post('/states', formattedData); // Matches router.post('/')
    return response.data.state;
  },

  /**
   * Updates a state by ID.
   * PUT /states/:id
   */
  updateState: async (id, data) => {
    // Convert frontend field names to match backend expectations
    const formattedData = {
      name: data.Name,
      countryId: data.CountryId,
      isActive: data.IsActive
    };
    const response = await api.put(`/states/${id}`, formattedData); // Matches router.put('/:id')
    return response.data.state;
  },

  /**
   * Deletes a state by ID.
   * DELETE /states/:id
   */
  deleteState: async (id) => {
    await api.delete(`/states/${id}`); // Matches router.delete('/:id')
  },

  /**
   * Fetches statistics for a specific state by ID.
   * GET /states/:stateId/stats
   */
  getStateStats: async (stateId) => {
    const response = await api.get(`/state-stats/${stateId}/stats`); // Matches router.get('/:stateId/stats')
    return response.data;
  },
};
