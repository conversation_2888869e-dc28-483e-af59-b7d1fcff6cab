-- Create sp_Valet_Payment_AcceptCash stored procedure
USE ParkwizOps;
GO

-- Create the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Payment_AcceptCash]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Payment_AcceptCash];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Payment_AcceptCash]
    @PaymentId INT,
    @AcceptedBy INT,
    @Remarks NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PaymentId IS NULL OR @PaymentId <= 0
        BEGIN
            RAISERROR('Payment ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @AcceptedBy IS NULL OR @AcceptedBy <= 0
        BEGIN
            RAISERROR('AcceptedBy is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if payment exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[PaymentGatewayTransactions] WHERE [Id] = @PaymentId)
        BEGIN
            RAISERROR('Payment not found', 16, 1);
            RETURN -1;
        END
        
        -- Update payment status
        UPDATE [dbo].[PaymentGatewayTransactions]
        SET
            [Status] = 'SUCCESS',
            [GatewayTransactionId] = 'CASH_' + CONVERT(NVARCHAR(20), @PaymentId),
            [GatewayResponse] = '{"method":"CASH","acceptedBy":"' + CONVERT(NVARCHAR(20), @AcceptedBy) + '","remarks":"' + ISNULL(@Remarks, '') + '"}',
            [UpdatedOn] = GETDATE()
        WHERE [Id] = @PaymentId;
        
        -- Return success with updated payment details
        SELECT 
            @PaymentId AS PaymentId,
            'SUCCESS' AS Status,
            @AcceptedBy AS AcceptedBy,
            GETDATE() AS AcceptedOn,
            @Remarks AS Remarks,
            'Cash payment accepted successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_Payment_AcceptCash';