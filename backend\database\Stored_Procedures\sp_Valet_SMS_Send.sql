-- =============================================
-- Author: Valet System
-- Create date: 2024-12-07
-- Description: Log SMS sending attempt and return SMS ID
-- =============================================
CREATE OR ALTER PROCEDURE sp_Valet_SMS_Send
    @MobileNumber NVARCHAR(15),
    @Message NVARCHAR(MAX),
    @SMSType NVARCHAR(50),
    @CustomerId INT = NULL,
    @TransactionId INT = NULL,
    @CreatedBy INT = NULL,
    @SMSId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validate mobile number
        IF @MobileNumber IS NULL OR LEN(@MobileNumber) < 10
        BEGIN
            RAISERROR('Invalid mobile number', 16, 1);
            RETURN;
        END
        
        -- Validate message
        IF @Message IS NULL OR LEN(@Message) = 0
        BEGIN
            RAISERROR('Message cannot be empty', 16, 1);
            RETURN;
        END
        
        -- Insert SMS record
        INSERT INTO SMSNotifications (
            MobileNumber,
            Message,
            SMSType,
            CustomerId,
            TransactionId,
            Status,
            Provider,
            CreatedBy,
            CreatedOn
        )
        VALUES (
            @MobileNumber,
            @Message,
            ISNULL(@SMSType, 'GENERAL'),
            @CustomerId,
            @TransactionId,
            'PENDING',
            'SYSTEM',
            ISNULL(@CreatedBy, 1),
            GETDATE()
        );
        
        SET @SMSId = SCOPE_IDENTITY();
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
