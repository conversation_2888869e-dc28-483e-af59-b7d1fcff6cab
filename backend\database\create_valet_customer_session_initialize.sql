-- Create sp_Valet_CustomerSession_Initialize stored procedure
USE ParkwizOps;
GO

-- Create the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_CustomerSession_Initialize]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_CustomerSession_Initialize];
GO

-- Create the ValetCustomerSessions table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ValetCustomerSessions]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ValetCustomerSessions](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [CustomerId] [decimal](18, 0) NOT NULL,
        [PlazaValetPointId] [decimal](18, 0) NOT NULL,
        [SessionData] [nvarchar](max) NULL,
        [CreatedBy] [decimal](18, 0) NOT NULL,
        [CreatedOn] [datetime] NOT NULL,
        [UpdatedOn] [datetime] NULL,
        [IsActive] [bit] NOT NULL,
        CONSTRAINT [PK_ValetCustomerSessions] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
END
GO

CREATE PROCEDURE [dbo].[sp_Valet_CustomerSession_Initialize]
    @CustomerId DECIMAL(18,0),
    @PlazaValetPointId DECIMAL(18,0),
    @SessionData NVARCHAR(MAX) = NULL,
    @CreatedBy DECIMAL(18,0),
    @SessionId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @CustomerId IS NULL OR @CustomerId <= 0
        BEGIN
            RAISERROR('Customer ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @PlazaValetPointId IS NULL OR @PlazaValetPointId <= 0
        BEGIN
            RAISERROR('Plaza valet point ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        -- Check if customer exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[ValetCustomers] WHERE [Id] = @CustomerId AND [IsActive] = 1)
        BEGIN
            RAISERROR('Customer not found or inactive', 16, 1);
            RETURN -1;
        END
        
        -- Check if plaza valet point exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[PlazaValetPoint] WHERE [Id] = @PlazaValetPointId AND [IsActive] = 1)
        BEGIN
            RAISERROR('Plaza valet point not found or inactive', 16, 1);
            RETURN -1;
        END
        
        -- Check if there's an active session for this customer
        DECLARE @ExistingSessionId INT;
        SELECT @ExistingSessionId = Id 
        FROM [dbo].[ValetCustomerSessions] 
        WHERE [CustomerId] = @CustomerId 
        AND [IsActive] = 1;
        
        IF @ExistingSessionId IS NOT NULL
        BEGIN
            -- Update existing session
            UPDATE [dbo].[ValetCustomerSessions]
            SET
                [PlazaValetPointId] = @PlazaValetPointId,
                [SessionData] = @SessionData,
                [UpdatedOn] = GETDATE()
            WHERE [Id] = @ExistingSessionId;
            
            SET @SessionId = @ExistingSessionId;
        END
        ELSE
        BEGIN
            -- Create new session
            INSERT INTO [dbo].[ValetCustomerSessions]
            (
                [CustomerId],
                [PlazaValetPointId],
                [SessionData],
                [CreatedBy],
                [CreatedOn],
                [IsActive]
            )
            VALUES
            (
                @CustomerId,
                @PlazaValetPointId,
                @SessionData,
                @CreatedBy,
                GETDATE(),
                1
            );
            
            SET @SessionId = SCOPE_IDENTITY();
        END
        
        -- Return session details
        SELECT 
            s.[Id] AS SessionId,
            s.[CustomerId],
            s.[PlazaValetPointId],
            s.[SessionData],
            s.[CreatedOn],
            s.[UpdatedOn],
            c.[Name] AS CustomerName,
            c.[MobileNumber] AS CustomerMobileNumber,
            p.[PlazaName],
            pvp.[ValetPointName]
        FROM [dbo].[ValetCustomerSessions] s
        JOIN [dbo].[ValetCustomers] c ON s.[CustomerId] = c.[Id]
        JOIN [dbo].[PlazaValetPoint] pvp ON s.[PlazaValetPointId] = pvp.[Id]
        JOIN [dbo].[Plaza] p ON pvp.[PlazaId] = p.[Id]
        WHERE s.[Id] = @SessionId;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
            
        SET @SessionId = -1;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_CustomerSession_Initialize';