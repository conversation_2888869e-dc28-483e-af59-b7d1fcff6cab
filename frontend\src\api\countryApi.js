import api from '../services/api'; // Using the shared axios instance

export const countryApi = {
  /**
   * Fetches the list of all countries.
   * GET /countries
   */
  getCountries: async () => {
    const response = await api.get('/countries'); // Matches router.get('/')
    return response.data.countries;
  },

  /**
   * Fetches the details of a single country by its ID.
   * GET /countries/:id
   */
  getCountryById: async (id) => {
    const response = await api.get(`/countries/${id}`); // Matches router.get('/:id')
    return response.data;
  },

  /**
   * Creates a new country.
   * POST /countries
   */
  createCountry: async (data) => {
    const response = await api.post('/countries', data); // Matches router.post('/')
    return response.data.country;
  },

  /**
   * Updates a country by ID.
   * PUT /countries/:id
   */
  updateCountry: async (id, data) => {
    const response = await api.put(`/countries/${id}`, data); // Matches router.put('/:id')
    return response.data.country;
  },

  /**
   * Deletes a country by ID.
   * DELETE /countries/:id
   */
  deleteCountry: async (id) => {
    await api.delete(`/countries/${id}`); // Matches router.delete('/:id')
  }
};

  /**
   * Fetches statistics for a specific country by ID.
   * GET /countries/:countryId/stats
   */
  // getCountryStats: async (countryId) => {
  //   const response = await api.get(`/countries/${countryId}/stats`); // Matches router.get('/:countryId/stats')
  //   return response.data;
  // },

  /**
   * Fetches a list of states associated with a specific country.
   * GET /countries/:countryId/states
   */
//   getStatesList: async (countryId) => {
//     const response = await api.get(`/countries/${countryId}/states`); // Matches router.get('/:countryId/states')
//     return response.data.states;
//   },
// };
