import api from '../services/api'; // Shared Axios instance

export const laneApi = {
  /**
   * Fetches the list of all lanes.
   * GET /lanes
   */
  getAllLanes: async (retryCount = 2) => {
    try {
      // Add a cache-busting parameter to prevent caching
      const timestamp = new Date().getTime();
      const response = await api.get(`/lanes?_=${timestamp}`); // Matches router.get('/')

      // Handle different response structures
      if (response.data && response.data.lanes && Array.isArray(response.data.lanes)) {
        return response.data.lanes;
      } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
        return response.data.data;
      } else if (Array.isArray(response.data)) {
        return response.data;
      }

      // Default fallback
      console.warn('Unexpected lanes API response structure:', response.data);
      return [];
    } catch (error) {
      console.error('Error fetching lanes:', error);
      
      // If we have retries left, wait and try again
      if (retryCount > 0) {
        console.log(`Retrying getAllLanes, ${retryCount} attempts left`);
        // Wait for 1 second before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
        return laneApi.getAllLanes(retryCount - 1);
      }
      
      return [];
    }
  },

  /**
   * Fetches the details of a single lane by its ID.
   * GET /lanes/:id
   */
  getLaneById: async (id) => {
    const response = await api.get(`/lanes/${id}`); // Matches router.get('/:id')
    return response.data.lane;
  },

  /**
   * Fetches lanes by plaza ID.
   * GET /lanes/plaza/:plazaId
   */
  getLanesByPlaza: async (plazaId, retryCount = 2) => {
    try {
      // Add a cache-busting parameter to prevent caching
      const timestamp = new Date().getTime();
      const response = await api.get(`/lanes/plaza/${plazaId}?_=${timestamp}`); // Matches router.get('/plaza/:plazaId')
      return response.data.lanes;
    } catch (error) {
      console.error(`Error fetching lanes for plaza ${plazaId}:`, error);
      
      // If we have retries left, wait and try again
      if (retryCount > 0) {
        console.log(`Retrying getLanesByPlaza, ${retryCount} attempts left`);
        // Wait for 1 second before retrying
        await new Promise(resolve => setTimeout(resolve, 1000));
        return laneApi.getLanesByPlaza(plazaId, retryCount - 1);
      }
      
      return [];
    }
  },

  /**
   * Creates a new lane.
   * POST /lanes
   */
  createLane: async (data) => {
    const response = await api.post('/lanes', data); // Matches router.post('/')
    return response.data;
  },

  /**
   * Updates a lane by ID.
   * PUT /lanes/:id
   */
  updateLane: async (id, data) => {
    const response = await api.put(`/lanes/${id}`, data); // Matches router.put('/:id')
    return response.data;
  },

  /**
   * Deletes a lane by ID.
   * DELETE /lanes/:id
   */
  deleteLane: async (id) => {
    await api.delete(`/lanes/${id}`); // Matches router.delete('/:id')
  },

  /**
   * Toggles the active status of a lane.
   * PATCH /lanes/:id/toggle-status
   */
  toggleLaneStatus: async (id, updatedBy) => {
    try {
      // Make sure we have valid parameters
      if (!id) {
        throw new Error('Lane ID is required');
      }

      if (!updatedBy) {
        updatedBy = 'admin'; // Default value if not provided
      }

      console.log('Toggling lane status:', { id, updatedBy });

      // Use axios instead of fetch for consistency with other API calls
      const response = await api.patch(`/lanes/${id}/toggle-status`, {
        UpdatedBy: updatedBy
      });

      console.log('Toggle response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Toggle status error:', error);
      throw error;
    }
  }
};
