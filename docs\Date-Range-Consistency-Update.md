# Dashboard Controller - Date Range Consistency Update

## 🎯 **Issue Identified and Fixed**

You were absolutely correct! I found that while all three endpoints (Dashboard Summary, Payment Method, Daily Revenue) were using the same `calculateDateRange` function with 6:00 AM to 6:00 AM operational day logic, there was an inconsistency in the date range optimization.

## ⚠️ **Problem Found**

### **Dashboard Summary Query:**
- ✅ Used `calculateDateRange()` for 6:00 AM to 6:00 AM logic
- ✅ Applied 90-day optimization for performance
- ✅ Used `optimizedStartDate` and `optimizedEndDate` in SQL query

### **Payment Method Query:**
- ✅ Used `calculateDateRange()` for 6:00 AM to 6:00 AM logic
- ❌ **NO optimization applied** - used original `startDate` and `endDate`
- ❌ **Potential inconsistency** for date ranges > 90 days

### **Daily Revenue Query:**
- ✅ Used `calculateDateRange()` for 6:00 AM to 6:00 AM logic
- ❌ **NO optimization applied** - used original `startDate` and `endDate`
- ❌ **Potential inconsistency** for date ranges > 90 days

## 🔧 **Solution Implemented**

I have updated both Payment Method and Daily Revenue queries to use the same date range optimization as the Dashboard Summary, ensuring complete consistency.

## 📅 **6:00 AM to 6:00 AM Operational Day Logic**

The `calculateDateRange` function implements proper operational day logic:

### **"Today" Logic:**
```javascript
if (isAfter6AM) {
  // After 6 AM - current operational day started today at 6 AM
  startDate = createLocalDate(today);        // Today 6:00 AM
  endDate = createLocalDate(addDays(today, 1)); // Tomorrow 6:00 AM
} else {
  // Before 6 AM - current operational day started yesterday at 6 AM
  const yesterday = addDays(today, -1);
  startDate = createLocalDate(yesterday);    // Yesterday 6:00 AM
  endDate = createLocalDate(today);          // Today 6:00 AM
}
```

### **Example Scenarios:**
- **Current Time: 2024-01-15 10:30 AM** (After 6 AM)
  - "Today" = 2024-01-15 06:00:00 to 2024-01-16 06:00:00

- **Current Time: 2024-01-15 04:30 AM** (Before 6 AM)
  - "Today" = 2024-01-14 06:00:00 to 2024-01-15 06:00:00

## 🔧 **Updates Made**

### **1. Payment Method Query - Added Optimization**
```javascript
// Calculate date range
const { startDate, endDate } = calculateDateRange(dateRange);

// Limit date range for large queries to improve performance (same as Dashboard Summary)
let optimizedStartDate = startDate;
let optimizedEndDate = endDate;

// If date range is more than 90 days, limit to 90 days for better performance
const maxRangeDays = 90; // 3 months max
const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));

if (requestedRangeDays > maxRangeDays) {
  // Create new date for optimization, maintaining the 6:00 AM time
  const optimizedEndDateStr = optimizedEndDate.toISOString().substring(0, 10);
  const tempStartDate = new Date(optimizedEndDateStr + 'T00:00:00');
  tempStartDate.setDate(tempStartDate.getDate() - maxRangeDays);
  const startDateStr = tempStartDate.toISOString().substring(0, 10);
  optimizedStartDate = new Date(startDateStr + 'T06:00:00');
  
  console.log('⚠️ Payment Method Date range optimized for performance:');
  console.log('  - Requested range:', requestedRangeDays, 'days');
  console.log('  - Optimized to:', maxRangeDays, 'days');
}

// Base query parameters
const queryParams = { 
  startDate: formatDateForSQL(optimizedStartDate), 
  endDate: formatDateForSQL(optimizedEndDate)
};
```

### **2. Daily Revenue Query - Added Optimization**
```javascript
// Calculate date range
const { startDate, endDate } = calculateDateRange(dateRange);

// Limit date range for large queries to improve performance (same as Dashboard Summary)
let optimizedStartDate = startDate;
let optimizedEndDate = endDate;

// If date range is more than 90 days, limit to 90 days for better performance
const maxRangeDays = 90; // 3 months max
const requestedRangeDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));

if (requestedRangeDays > maxRangeDays) {
  // Create new date for optimization, maintaining the 6:00 AM time
  const optimizedEndDateStr = optimizedEndDate.toISOString().substring(0, 10);
  const tempStartDate = new Date(optimizedEndDateStr + 'T00:00:00');
  tempStartDate.setDate(tempStartDate.getDate() - maxRangeDays);
  const startDateStr = tempStartDate.toISOString().substring(0, 10);
  optimizedStartDate = new Date(startDateStr + 'T06:00:00');
  
  console.log('⚠️ Daily Revenue Date range optimized for performance:');
  console.log('  - Requested range:', requestedRangeDays, 'days');
  console.log('  - Optimized to:', maxRangeDays, 'days');
}

// Base query parameters
const queryParams = {
  startDate: formatDateForSQL(optimizedStartDate),
  endDate: formatDateForSQL(optimizedEndDate)
};
```

### **3. Enhanced Logging**
All three endpoints now log comprehensive date range information:

```javascript
console.log('📅 [Endpoint] Date Range Parameters:');
console.log('  - Start Date (SQL):', queryParams.startDate);
console.log('  - End Date (SQL):', queryParams.endDate);
console.log('  - Start Date (JS Original):', startDate);
console.log('  - End Date (JS Original):', endDate);
console.log('  - Start Date (JS Optimized):', optimizedStartDate);
console.log('  - End Date (JS Optimized):', optimizedEndDate);
console.log('  - Date Range Days:', requestedRangeDays);
console.log('  - Using 6:00 AM to 6:00 AM operational day logic');
```

## 📊 **Consistency Verification**

### **All Three Endpoints Now:**
1. ✅ **Use same `calculateDateRange()` function**
2. ✅ **Apply same 6:00 AM to 6:00 AM operational day logic**
3. ✅ **Apply same 90-day optimization for performance**
4. ✅ **Use same `optimizedStartDate` and `optimizedEndDate`**
5. ✅ **Log comprehensive date range information**

### **Expected Log Output:**
```
📅 Dashboard Summary Date Range Parameters:
  - Start Date (SQL): 2024-01-15 06:00:00
  - End Date (SQL): 2024-01-16 06:00:00
  - Start Date (JS Original): Mon Jan 15 2024 06:00:00 GMT+0530
  - End Date (JS Original): Tue Jan 16 2024 06:00:00 GMT+0530
  - Start Date (JS Optimized): Mon Jan 15 2024 06:00:00 GMT+0530
  - End Date (JS Optimized): Tue Jan 16 2024 06:00:00 GMT+0530
  - Date Range Days: 1
  - Using 6:00 AM to 6:00 AM operational day logic

📅 Payment Method Date Range Parameters:
  - Start Date (SQL): 2024-01-15 06:00:00
  - End Date (SQL): 2024-01-16 06:00:00
  - Start Date (JS Original): Mon Jan 15 2024 06:00:00 GMT+0530
  - End Date (JS Original): Tue Jan 16 2024 06:00:00 GMT+0530
  - Start Date (JS Optimized): Mon Jan 15 2024 06:00:00 GMT+0530
  - End Date (JS Optimized): Tue Jan 16 2024 06:00:00 GMT+0530
  - Date Range Days: 1
  - Using 6:00 AM to 6:00 AM operational day logic

📅 Daily Revenue Date Range Parameters:
  - Start Date (SQL): 2024-01-15 06:00:00
  - End Date (SQL): 2024-01-16 06:00:00
  - Start Date (JS Original): Mon Jan 15 2024 06:00:00 GMT+0530
  - End Date (JS Original): Tue Jan 16 2024 06:00:00 GMT+0530
  - Start Date (JS Optimized): Mon Jan 15 2024 06:00:00 GMT+0530
  - End Date (JS Optimized): Tue Jan 16 2024 06:00:00 GMT+0530
  - Date Range Days: 1
  - Using 6:00 AM to 6:00 AM operational day logic
```

## 🔍 **Exact Query Examples**

### **All Three Endpoints Will Now Use Identical Date Ranges:**

**Dashboard Summary Query:**
```sql
SELECT 
  ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
  -- ... rest of query
FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
WHERE (t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' OR t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00')
```

**Payment Method Query:**
```sql
SELECT
  ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
  ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalRevenue,
  COUNT(*) as transactionCount
FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
WHERE t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00'
GROUP BY t.PaymentMode
```

**Daily Revenue Query:**
```sql
SELECT
  CAST(t.ExitDateTime AS DATE) as date,
  ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as revenue,
  COUNT(*) as transactions
FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
WHERE t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00'
GROUP BY CAST(t.ExitDateTime AS DATE)
```

## 🛠️ **Testing Instructions**

1. **Test with same date range on all endpoints:**
   ```bash
   # All should use identical date ranges
   curl "http://localhost:3000/api/dashboard/summary?dateRange=today&companyId=11"
   curl "http://localhost:3000/api/dashboard/revenue-by-payment?dateRange=today&companyId=11"
   curl "http://localhost:3000/api/dashboard/daily-revenue?dateRange=today&companyId=11"
   ```

2. **Verify date ranges in logs:**
   - Check that all three endpoints show identical SQL date parameters
   - Verify 6:00 AM to 6:00 AM logic is applied consistently

3. **Test with large date ranges (>90 days):**
   ```bash
   # Test optimization consistency
   curl "http://localhost:3000/api/dashboard/summary?dateRange=year&companyId=11"
   curl "http://localhost:3000/api/dashboard/revenue-by-payment?dateRange=year&companyId=11"
   curl "http://localhost:3000/api/dashboard/daily-revenue?dateRange=year&companyId=11"
   ```

4. **Copy exact queries from logs and compare in SSMS:**
   - All three queries should use identical date ranges
   - Revenue totals should be consistent across endpoints

## ✅ **Benefits Achieved**

1. **Complete Date Range Consistency**: All endpoints use identical date calculations
2. **6:00 AM to 6:00 AM Logic**: Proper operational day boundaries maintained
3. **Performance Optimization**: All endpoints apply same 90-day limit for large queries
4. **Debugging Capability**: Comprehensive logging shows exact date ranges used
5. **Data Integrity**: Eliminates potential discrepancies between dashboard components

The dashboard now ensures that all revenue calculations use exactly the same date ranges with proper 6:00 AM to 6:00 AM operational day logic, eliminating any potential data inconsistencies.

---

**Update Completed**: January 2024  
**Impact**: Complete date range consistency across all dashboard endpoints  
**Status**: All endpoints now use identical 6:00 AM to 6:00 AM operational day logic