// import React from 'react';
// import { useForm } from 'react-hook-form';
// import { X } from 'lucide-react';
// import toast from 'react-hot-toast';

// const BranchDialog = ({
//   isOpen,
//   onClose,
//   onSubmit,
//   companies,
//   initialData,
//   title,
// }) => {
//   const {
//     register,
//     handleSubmit,
//     formState: { errors },
//   } = useForm({
//     defaultValues: initialData || { name: '', location: '', company: '' },
//   });

//   if (!isOpen) return null;

//   const onFormSubmit = async (data) => {
//     try {
//       // Use onSubmit callback for parent-controlled API logic
//       if (onSubmit) {
//         await onSubmit({ ...data, branchId: initialData?.id || null });
//         toast.success(
//           initialData
//             ? 'Branch updated successfully'
//             : 'Branch created successfully'
//         );
//         onClose(); // Close the dialog on success
//       } else {
//         toast.error('onSubmit callback is not provided');
//       }
//     } catch (error) {
//       toast.error(error.message || 'Failed to submit branch');
//     }
//   };

//   return (
//     <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
//       <div className="bg-white rounded-lg p-6 w-full max-w-md relative">
//         {/* Close Button */}
//         <button
//           onClick={onClose}
//           className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
//         >
//           <X size={20} />
//         </button>

//         {/* Title */}
//         <h2 className="text-2xl font-bold mb-4 text-[#000000]">{title}</h2>

//         {/* Form */}
//         <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
//           {/* Branch Name */}
//           <div>
//             <label className="block text-sm font-medium text-[#727272] mb-1">
//               Branch Name
//             </label>
//             <input
//               {...register('name', { required: 'Branch name is required' })}
//               className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
//             />
//             {errors.name && (
//               <span className="text-red-500 text-sm">{errors.name.message}</span>
//             )}
//           </div>

//           {/* Location */}
//           <div>
//             <label className="block text-sm font-medium text-[#727272] mb-1">
//               Location
//             </label>
//             <input
//               {...register('location', { required: 'Location is required' })}
//               className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
//             />
//             {errors.location && (
//               <span className="text-red-500 text-sm">{errors.location.message}</span>
//             )}
//           </div>

//           {/* Company Selection */}
//           <div>
//             <label className="block text-sm font-medium text-[#727272] mb-1">
//               Company
//             </label>
//             <select
//               {...register('company', { required: 'Company is required' })}
//               className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
//             >
//               <option value="">Select a company</option>
//               {companies.map((company) => (
//                 <option key={company.id} value={company.id}>
//                   {company.name}
//                 </option>
//               ))}
//             </select>
//             {errors.company && (
//               <span className="text-red-500 text-sm">{errors.company.message}</span>
//             )}
//           </div>

//           {/* Action Buttons */}
//           <div className="flex justify-end space-x-3 mt-6">
//             <button
//               type="button"
//               onClick={onClose}
//               className="px-4 py-2 text-[#727272] hover:text-[#000000] transition-colors"
//             >
//               Cancel
//             </button>
//             <button
//               type="submit"
//               className="px-4 py-2 bg-[#9AC8CE] text-white rounded hover:bg-[#7BA9AF] transition-colors focus:ring-2 focus:ring-offset-2 focus:ring-[#9AC8CE]"
//             >
//               {initialData ? 'Update' : 'Create'}
//             </button>
//           </div>
//         </form>
//       </div>
//     </div>
//   );
// };

// export default BranchDialog;


import React from 'react';
        import { useForm } from 'react-hook-form';
        import { X } from 'lucide-react';
        import toast from 'react-hot-toast';
        
        const BranchDialog = ({
          isOpen,
          onClose,
          onSubmit, // Parent-controlled submit logic
          companies, // List of companies for dropdown
          initialData, // Pre-fill data for editing
          title, // Dialog title
        }) => {
          const {
            register,
            handleSubmit,
            formState: { errors },
          } = useForm({
            defaultValues: initialData || { name: '', location: '', company: '' },
          });
        
          if (!isOpen) return null;

  // const onFormSubmit = async (data) => {
  //   try {
  //     // Use parent-provided submission handler
  //     if (onSubmit) {
  //       await onSubmit(data); // Submit the form data to the parent
  //       toast.success(
  //         initialData
  //           ? 'Branch updated successfully'
  //           : 'Branch created successfully'
  //       );
  //       onClose(); // Close dialog on success
  //     } else {
  //       toast.error('No onSubmit handler provided!');
  //     }
  //   } catch (error) {
  //     // Display API error
  //     toast.error(
  //       error?.response?.data?.message || error.message || 'Failed to submit branch'
  //     );
  //   }
  // };


  // const onFormSubmit = async (data) => {
  //   console.log('Submitting data:', data); // Log the form data
  //   try {
  //     if (onSubmit) {
  //       await onSubmit(data); // Send data to parent handler
  //       toast.success(
  //         initialData
  //           ? 'Branch updated successfully'
  //           : 'Branch created successfully'
  //       );
  //       onClose(); // Close dialog on success
  //     } else {
  //       toast.error('No onSubmit handler provided!');
  //     }
  //   } catch (error) {
  //     console.error('Error submitting form:', error.response?.data || error.message);
  //     toast.error(
  //       error?.response?.data?.message || error.message || 'Failed to submit branch'
  //     );
  //   }
  // };

  const onFormSubmit = async (data) => {
    // Update company field to use company ID instead of name
    const companyId = data.company;
    data.company = companyId;
  
    try {
      // Use parent-provided submission handler
      if (onSubmit) {
        await onSubmit(data); // Submit the data with the correct company ID
        toast.success(
          initialData
            ? 'Branch updated successfully'
            : 'Branch created successfully'
        );
        onClose(); // Close dialog on success
      } else {
        toast.error('No onSubmit handler provided!');
      }
    } catch (error) {
      // Check if error contains response.data.errors and log it
      if (error?.response?.data?.errors) {
        // Log each error
        console.error('Validation errors:', error.response.data.errors);
        error.response.data.errors.forEach((err) => {
          toast.error(err.message || 'Unknown error occurred');
        });
      } else {
        console.error('Error creating branch:', error);
        toast.error(error?.message || 'Failed to submit branch');
      }
    }
  };
  
  

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute right-4 top-4 text-gray-500 hover:text-gray-700"
        >
          <X size={20} />
        </button>

        {/* Title */}
        <h2 className="text-2xl font-bold mb-4 text-[#000000]">{title}</h2>

        {/* Form */}
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-4">
          {/* Branch Name */}
          <div>
            <label className="block text-sm font-medium text-[#727272] mb-1">
              Branch Name
            </label>
            <input
              {...register('name', { required: 'Branch name is required' })}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
            />
            {errors.name && (
              <span className="text-red-500 text-sm">{errors.name.message}</span>
            )}
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-[#727272] mb-1">
              Location
            </label>
            <input
              {...register('location', { required: 'Location is required' })}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
            />
            {errors.location && (
              <span className="text-red-500 text-sm">{errors.location.message}</span>
            )}
          </div>

          {/* Company Selection */}
          <div>
            <label className="block text-sm font-medium text-[#727272] mb-1">
              Company
            </label>
            <select
              {...register('company', { required: 'Company is required' })}
              className="w-full p-2 border rounded focus:ring-2 focus:ring-[#9AC8CE] focus:border-transparent"
            >
              <option value="">Select a company</option>
              {companies?.map((company) => (
                <option key={company._id} value={company._id}>
                  {company.name}
                </option>
              ))}
            </select>
            {errors.company && (
              <span className="text-red-500 text-sm">{errors.company.message}</span>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-[#727272] hover:text-[#000000] transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#9AC8CE] text-white rounded hover:bg-[#7BA9AF] transition-colors focus:ring-2 focus:ring-offset-2 focus:ring-[#9AC8CE]"
            >
              {initialData ? 'Update' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BranchDialog;







