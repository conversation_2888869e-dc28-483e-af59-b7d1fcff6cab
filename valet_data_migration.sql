-- =============================================
-- Valet System Data Migration Script
-- COPY data from pwvms to ParkwizOps (original data remains in pwvms)
-- =============================================

USE ParkwizOps;
GO

PRINT 'Starting Valet System Data Migration...';
PRINT '======================================';
PRINT 'NOTE: This script COPIES data from pwvms database.';
PRINT 'Original data in pwvms will remain intact.';
PRINT '';

-- =============================================
-- Step 1: Migrate PlazaValetPoint Data (Controllers' Desks)
-- =============================================
PRINT 'Migrating PlazaValetPoint data (valet controller desks)...';

-- Check if PlazaValetPoint table exists in current database
IF EXISTS (SELECT * FROM sysobjects WHERE name='PlazaValetPoint' AND xtype='U')
BEGIN
    -- Check if data already exists
    IF NOT EXISTS (SELECT TOP 1 1 FROM PlazaValetPoint)
    BEGIN
        INSERT INTO PlazaValetPoint (
            PlazaId, ValetPointName, IsActive, CreatedBy, CreatedOn,
            ModifiedBy, ModifiedOn, CompanyId, Latitude, Longitude
        )
        SELECT
            PlazaId, ValetPointName, IsActive, CreatedBy, CreatedOn,
            ModifiedBy, ModifiedOn, CompanyId, Latitude, Longitude
        FROM [pwvms].[dbo].[PlazaValetPoint]
        WHERE IsActive = 1;

        PRINT '✅ PlazaValetPoint data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
    END
    ELSE
    BEGIN
        PRINT '⚠️ PlazaValetPoint data already exists. Skipping migration.';
    END
END
ELSE
BEGIN
    PRINT '⚠️ PlazaValetPoint table does not exist in current database. Please run table creation scripts first.';
END

-- =============================================
-- Step 2: Migrate Customer Data
-- =============================================
PRINT 'Migrating Customer data...';

-- Check if data already exists
IF NOT EXISTS (SELECT TOP 1 1 FROM Customer)
BEGIN
    INSERT INTO Customer (
        Name, MobileNumber, AddressId, IsActive,
        CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    )
    SELECT
        Name, MobileNumber, AddressId, IsActive,
        CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    FROM [pwvms].[dbo].[Customer]
    WHERE IsActive = 1;

    PRINT '✅ Customer data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ Customer data already exists. Skipping migration.';
END

-- =============================================
-- Step 3: Migrate CustomerVehicle Data
-- =============================================
PRINT 'Migrating CustomerVehicle data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM CustomerVehicle)
BEGIN
    INSERT INTO CustomerVehicle (
        CustomerId, VehicleNumber, IsActive,
        CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    )
    SELECT
        CustomerId, VehicleNumber, IsActive,
        CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    FROM [pwvms].[dbo].[CustomerVehicle]
    WHERE IsActive = 1;

    PRINT '✅ CustomerVehicle data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ CustomerVehicle data already exists. Skipping migration.';
END

-- =============================================
-- Step 4: Migrate ParkingZone Data
-- =============================================
PRINT 'Migrating ParkingZone data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM ParkingZone)
BEGIN
    INSERT INTO ParkingZone (
        CompanyId, PlazaId, ParkingZoneName, NumberOfParking,
        Latitude, Longitude, IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    )
    SELECT
        CompanyId, PlazaId, ParkingZoneName, NumberOfParking,
        Latitude, Longitude, IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    FROM [pwvms].[dbo].[ParkingZone]
    WHERE IsActive = 1;

    PRINT '✅ ParkingZone data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ ParkingZone data already exists. Skipping migration.';
END

-- =============================================
-- Step 5: Migrate ParkingBay Data
-- =============================================
PRINT 'Migrating ParkingBay data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM ParkingBay)
BEGIN
    INSERT INTO ParkingBay (
        CompanyId, PlazaId, ParkingZoneId, ParkingBayName,
        Latitude, Longitude, IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    )
    SELECT
        CompanyId, PlazaId, ParkingZoneId, ParkingBayName,
        Latitude, Longitude, IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    FROM [pwvms].[dbo].[ParkingBay]
    WHERE IsActive = 1;

    PRINT '✅ ParkingBay data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ ParkingBay data already exists. Skipping migration.';
END

-- =============================================
-- Step 6: Migrate AllowDriverLogin Data
-- =============================================
PRINT 'Migrating AllowDriverLogin data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM AllowDriverLogin)
BEGIN
    INSERT INTO AllowDriverLogin (
        DriverId, EntryDateTime, EntryBy, ExitDateTime, ExitBy,
        IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    )
    SELECT
        DriverId, EntryDateTime, EntryBy, ExitDateTime, ExitBy,
        IsActive, CreatedBy, CreatedOn, ModifiedBy, ModifiedOn
    FROM [pwvms].[dbo].[AllowDriverLogin]
    WHERE IsActive = 1;

    PRINT '✅ AllowDriverLogin data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ AllowDriverLogin data already exists. Skipping migration.';
END

-- =============================================
-- Step 7: Migrate Recent ParkingTransactions Data (Last 6 months)
-- =============================================
PRINT 'Migrating recent ParkingTransactions data (last 6 months)...';

IF NOT EXISTS (SELECT TOP 1 1 FROM ParkingTransactions)
BEGIN
    -- Migrate only recent transactions to avoid performance issues
    INSERT INTO ParkingTransactions (
        PNRNumber, ParkingPin, CompanyId, PlazaId, CustomerVehicleNumber,
        CustomerMobileNumber, CustomerName, IsAnyValuableItem, AnyValuableItem,
        ValetFee, ParkingFee, TotalFee, Source, PayAt, PaymentType,
        IsPaymentCompleted, IsPromoCodeTransaction, PromocodeId, VehicleType,
        EntryBy, EntryDateTime, ZoneId, DriverId, DriverAssignedBy, DriverAssignedOn,
        ParkingBayId, ParkedBayAssignedBy, ParkedBayAssignedOn, RequestMyVehicleDateTime,
        IsRequestMyVehicleLater, RequestMyVehicleLaterTimeInterval, RequestMyVehicleLaterTime,
        RequestMyVehicleBy, PlazaValetPointId, PickupDriverId, AssignDriverOn,
        PickupVehicleOn, ArrivedAtPlazaValetPointOn, AssignToControllerId,
        AssignToControllerBy, AssignToControllerOn, PhoneUPITransactionId,
        PhoneUPITransactionNumber, IsVoid, ExitDateTime, ExitBy,
        IsTransactionCanceled, CancelTransactionReason, CancelBy, CancelOn,
        IsCancelTransactionRefunded, CancelCancelTransactionRefundedPaymentType,
        CancelCancelTransactionRefundedPaymentDateTime, CancelTransactionRemark,
        TransactionStatus, IsKeyHandover, KeyAcceptedBy, razorPayTransactionId,
        razorPayTransactionNumber
    )
    SELECT 
        PNRNumber, ParkingPin, CompanyId, PlazaId, CustomerVehicleNumber,
        CustomerMobileNumber, CustomerName, IsAnyValuableItem, AnyValuableItem,
        ValetFee, ParkingFee, TotalFee, Source, PayAt, PaymentType,
        IsPaymentCompleted, IsPromoCodeTransaction, PromocodeId, VehicleType,
        EntryBy, EntryDateTime, ZoneId, DriverId, DriverAssignedBy, DriverAssignedOn,
        ParkingBayId, ParkedBayAssignedBy, ParkedBayAssignedOn, RequestMyVehicleDateTime,
        IsRequestMyVehicleLater, RequestMyVehicleLaterTimeInterval, RequestMyVehicleLaterTime,
        RequestMyVehicleBy, PlazaValetPointId, PickupDriverId, AssignDriverOn,
        PickupVehicleOn, ArrivedAtPlazaValetPointOn, AssignToControllerId,
        AssignToControllerBy, AssignToControllerOn, PhoneUPITransactionId,
        PhoneUPITransactionNumber, IsVoid, ExitDateTime, ExitBy,
        IsTransactionCanceled, CancelTransactionReason, CancelBy, CancelOn,
        IsCancelTransactionRefunded, CancelCancelTransactionRefundedPaymentType,
        CancelCancelTransactionRefundedPaymentDateTime, CancelTransactionRemark,
        TransactionStatus, IsKeyHandover, KeyAcceptedBy, razorPayTransactionId,
        razorPayTransactionNumber
    FROM [pwvms].[dbo].[ParkingTransactions]
    WHERE EntryDateTime >= DATEADD(MONTH, -6, GETDATE())
    AND TransactionStatus IN (1, 2, 3); -- Active transactions only
    
    PRINT '✅ ParkingTransactions data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ ParkingTransactions data already exists. Skipping migration.';
END

-- =============================================
-- Step 8: Migrate ParkingTransactionCashPayments Data
-- =============================================
PRINT 'Migrating ParkingTransactionCashPayments data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM ParkingTransactionCashPayments)
BEGIN
    INSERT INTO ParkingTransactionCashPayments (
        TransactionId, Cash, IsRecived, RecivedBy, RecivedOn
    )
    SELECT 
        p.TransactionId, p.Cash, p.IsRecived, p.RecivedBy, p.RecivedOn
    FROM [pwvms].[dbo].[ParkingTransactionCashPayments] p
    INNER JOIN ParkingTransactions pt ON p.TransactionId = pt.Id
    WHERE p.IsRecived = 1;
    
    PRINT '✅ ParkingTransactionCashPayments data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ ParkingTransactionCashPayments data already exists. Skipping migration.';
END

-- =============================================
-- Step 9: Migrate Recent Payment Transactions
-- =============================================
PRINT 'Migrating recent RazorPayTransactions data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM RazorPayTransactions)
BEGIN
    INSERT INTO RazorPayTransactions (
        Id, RequestType, Request, RequestDateTime, RequestResponse,
        RequestResponseDateTime, Response, ResponseDateTime, Status, Amount,
        ClientTransactionId, IsClientTransactionSuccess, ClientTransactionDateTime,
        ResponseSuccess, IsRefunded, RefundResponse, RefundResponseDateTime
    )
    SELECT TOP 1000
        Id, RequestType, Request, RequestDateTime, RequestResponse,
        RequestResponseDateTime, Response, ResponseDateTime, Status, Amount,
        ClientTransactionId, IsClientTransactionSuccess, ClientTransactionDateTime,
        ResponseSuccess, IsRefunded, RefundResponse, RefundResponseDateTime
    FROM [pwvms].[dbo].[RazorPayTransactions]
    WHERE RequestDateTime >= DATEADD(MONTH, -3, GETDATE())
    ORDER BY RequestDateTime DESC;
    
    PRINT '✅ RazorPayTransactions data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ RazorPayTransactions data already exists. Skipping migration.';
END

PRINT 'Migrating recent PhonePePaymentTransactions data...';

IF NOT EXISTS (SELECT TOP 1 1 FROM PhonePePaymentTransactions)
BEGIN
    INSERT INTO PhonePePaymentTransactions (
        StageURL, RequestType, XVerify, XCallbackUrl, XProviderId, MerchantId,
        TransactionId, MerchantOrderId, Amount, ExpiresIn, StoreId, TerminalId,
        RequestDateTime, RequestInitSuccess, RequestInitCode, RequestInitMessage,
        RequestInitTransactionId, RequestInitAmount, RequestInitMerchantId,
        RequestInitQRString, RequestInitMobileNumber, RequestInitPayLink,
        RequestInitUpiIntent, RequestInitDateTime, ResponseSuccess, ResponseCode,
        ResponseMessage, ResponseMerchantId, ResponseTransactionId,
        ResponseProviderReferenceId, ResponseAmount, ResponsePaymentState,
        ResponsePayResponseCode, ResponseDateTime, ParkingTransactionId,
        IsParkingTransactionSuccess, ParkingTransactionSuccessDateTime
    )
    SELECT TOP 500
        StageURL, RequestType, XVerify, XCallbackUrl, XProviderId, MerchantId,
        TransactionId, MerchantOrderId, Amount, ExpiresIn, StoreId, TerminalId,
        RequestDateTime, RequestInitSuccess, RequestInitCode, RequestInitMessage,
        RequestInitTransactionId, RequestInitAmount, RequestInitMerchantId,
        RequestInitQRString, RequestInitMobileNumber, RequestInitPayLink,
        RequestInitUpiIntent, RequestInitDateTime, ResponseSuccess, ResponseCode,
        ResponseMessage, ResponseMerchantId, ResponseTransactionId,
        ResponseProviderReferenceId, ResponseAmount, ResponsePaymentState,
        ResponsePayResponseCode, ResponseDateTime, ParkingTransactionId,
        IsParkingTransactionSuccess, ParkingTransactionSuccessDateTime
    FROM [pwvms].[dbo].[PhonePePaymentTransactions]
    WHERE RequestDateTime >= DATEADD(MONTH, -3, GETDATE())
    ORDER BY RequestDateTime DESC;
    
    PRINT '✅ PhonePePaymentTransactions data migrated successfully. Records: ' + CAST(@@ROWCOUNT AS NVARCHAR(10));
END
ELSE
BEGIN
    PRINT '⚠️ PhonePePaymentTransactions data already exists. Skipping migration.';
END

PRINT '';
PRINT '✅ Valet System Data Migration Completed Successfully!';
PRINT '';
PRINT 'Migration Summary:';
PRINT '- Customer data migrated';
PRINT '- CustomerVehicle data migrated';
PRINT '- ParkingZone and ParkingBay data migrated';
PRINT '- Recent transaction data migrated (last 6 months)';
PRINT '- Payment transaction data migrated (last 3 months)';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Create database indexes for performance';
PRINT '2. Set up stored procedures';
PRINT '3. Configure module permissions';
PRINT '4. Test data integrity';
PRINT '';
