# Script to install iisnode v0.2.26
# Run this script as administrator

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Please run this script as Administrator!"
    exit
}

# Check if Chocolatey is installed
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Chocolatey is not installed. Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
    
    Write-Host "Chocolatey installed successfully." -ForegroundColor Green
} else {
    Write-Host "Chocolatey is already installed." -ForegroundColor Green
}

# Install iisnode v0.2.26 using Chocolatey
Write-Host "Installing iisnode v0.2.26..." -ForegroundColor Yellow

# Determine system architecture
$is64bit = [Environment]::Is64BitOperatingSystem
$architecture = if ($is64bit) { "x64" } else { "x86" }
$downloadUrl = if ($is64bit) {
    "https://github.com/Azure/iisnode/releases/download/v0.2.26/iisnode-full-v0.2.26-x64.msi"
} else {
    "https://github.com/Azure/iisnode/releases/download/v0.2.26/iisnode-full-v0.2.26-x86.msi"
}

# Create a temporary directory for the download
$tempDir = Join-Path $env:TEMP "iisnode-install"
if (-not (Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
}

# Download the installer
$installerPath = Join-Path $tempDir "iisnode-full-v0.2.26-$architecture.msi"
Write-Host "Downloading iisnode v0.2.26 for $architecture..." -ForegroundColor Yellow
Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath

# Install iisnode
Write-Host "Installing iisnode..." -ForegroundColor Yellow
Start-Process -FilePath "msiexec.exe" -ArgumentList "/i `"$installerPath`" /quiet /norestart" -Wait

# Check if installation was successful
$iisnodePath = "C:\Program Files\iisnode\iisnode.dll"
if (Test-Path $iisnodePath) {
    Write-Host "iisnode v0.2.26 installed successfully!" -ForegroundColor Green
} else {
    Write-Host "iisnode installation failed. Please try installing manually." -ForegroundColor Red
    Write-Host "Download URL: $downloadUrl" -ForegroundColor Yellow
}

# Clean up
Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue

# Verify IIS features
Write-Host "Checking IIS features..." -ForegroundColor Yellow
$iisFeatures = @(
    "IIS-WebServerRole",
    "IIS-WebServer",
    "IIS-CommonHttpFeatures",
    "IIS-HttpErrors",
    "IIS-ApplicationDevelopment",
    "IIS-ISAPIExtensions",
    "IIS-ISAPIFilter",
    "IIS-NetFxExtensibility45",
    "IIS-ASPNET45"
)

$missingFeatures = @()
foreach ($feature in $iisFeatures) {
    $featureState = (Get-WindowsOptionalFeature -Online -FeatureName $feature).State
    if ($featureState -ne "Enabled") {
        $missingFeatures += $feature
    }
}

if ($missingFeatures.Count -gt 0) {
    Write-Host "Some required IIS features are not installed. Installing now..." -ForegroundColor Yellow
    foreach ($feature in $missingFeatures) {
        Write-Host "Enabling $feature..." -ForegroundColor Yellow
        Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
    }
    Write-Host "IIS features installed successfully!" -ForegroundColor Green
} else {
    Write-Host "All required IIS features are already installed." -ForegroundColor Green
}

# Check URL Rewrite Module
$rewriteModule = Get-WebGlobalModule | Where-Object { $_.Name -eq "RewriteModule" }
if ($null -eq $rewriteModule) {
    Write-Host "URL Rewrite Module is not installed. Installing now..." -ForegroundColor Yellow
    choco install urlrewrite -y
    Write-Host "URL Rewrite Module installed successfully!" -ForegroundColor Green
} else {
    Write-Host "URL Rewrite Module is already installed." -ForegroundColor Green
}

Write-Host "Setup completed successfully!" -ForegroundColor Green
Write-Host "You can now deploy your PWVMS application to IIS." -ForegroundColor Green