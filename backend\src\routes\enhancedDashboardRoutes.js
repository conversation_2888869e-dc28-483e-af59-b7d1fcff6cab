// backend/src/routes/enhancedDashboardRoutes.js
const express = require('express');
const router = express.Router();
const enhancedDashboardController = require('../controllers/EnhancedDashboardController');
const auth = require('../middleware/auth');
const { dashboardCache, rateLimit, invalidateCache } = require('../middleware/cache');

/**
 * ===============================================================================
 * # Enhanced Dashboard Routes with Redis Caching
 * ===============================================================================
 *
 * High-performance dashboard API routes with intelligent caching,
 * rate limiting, and real-time capabilities for PWVMS.
 */

// Apply rate limiting to all dashboard routes
router.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // limit each IP to 200 requests per windowMs
  message: 'Too many dashboard requests, please try again later.'
}));

/**
 * @route GET /api/dashboard/enhanced/summary
 * @desc Get enhanced dashboard summary with caching
 * @access Private
 * @cache 5 minutes for historical data, 1 minute for today's data
 */
router.get('/summary', 
  auth(), 
  dashboardCache(300), // 5 minutes cache
  enhancedDashboardController.getDashboardSummary
);

/**
 * @route GET /api/dashboard/enhanced/revenue/by-payment-method
 * @desc Get revenue breakdown by payment method with caching
 * @access Private
 * @cache 10 minutes for chart data
 */
router.get('/revenue/by-payment-method', 
  auth(), 
  dashboardCache(600), // 10 minutes cache
  enhancedDashboardController.getRevenueByPaymentMethod
);

/**
 * @route GET /api/dashboard/enhanced/realtime
 * @desc Get real-time dashboard data (no cache)
 * @access Private
 * @cache None - always fresh data
 */
router.get('/realtime', 
  auth(), 
  enhancedDashboardController.getRealTimeDashboard
);

/**
 * @route POST /api/dashboard/enhanced/cache/clear
 * @desc Clear dashboard cache
 * @access Private
 * @invalidates All user dashboard cache
 */
router.post('/cache/clear', 
  auth(), 
  enhancedDashboardController.clearDashboardCache
);

/**
 * @route GET /api/dashboard/enhanced/cache/status
 * @desc Get cache status and Redis info
 * @access Private (SuperAdmin only)
 */
router.get('/cache/status', 
  auth(['SuperAdmin']), 
  enhancedDashboardController.getCacheStatus
);

module.exports = router;