# Complete Valet Customer Flow Implementation Plan

## Overview
This document outlines the implementation of the customer-facing web application that customers access via QR code URL, following the complete 29-step valet process flow.

## Customer Flow URL Structure

### QR Code URL Pattern
```
https://yourdomain.com/valet/customer/{plazaValetPointId}
```

### Customer Journey Pages
```
1. /valet/customer/{plazaValetPointId}           # Landing page (QR scan entry)
2. /valet/customer/{plazaValetPointId}/mobile    # Mobile number entry
3. /valet/customer/{plazaValetPointId}/otp       # OTP verification
4. /valet/customer/{plazaValetPointId}/details   # Customer details form
5. /valet/customer/{plazaValetPointId}/payment   # Payment selection
6. /valet/customer/{plazaValetPointId}/gateway   # Payment gateway
7. /valet/customer/{plazaValetPointId}/success   # Payment success
8. /valet/customer/{plazaValetPointId}/tracking  # Vehicle tracking
9. /valet/customer/{plazaValetPointId}/pickup    # Pickup request
```

## Frontend Implementation Structure

### Customer Web Application Components
```
frontend/src/components/valet/customer/
├── CustomerLanding.js          # Step 1: QR code landing page
├── MobileEntry.js              # Step 2: Mobile number input
├── OTPVerification.js          # Step 3-4: OTP input and verification
├── CustomerDetailsForm.js      # Step 5: Name, vehicle, address form
├── PaymentSelection.js         # Step 6: Payment method selection
├── PaymentGateway.js           # Step 7-8: Payment processing
├── PaymentSuccess.js           # Step 9: Success confirmation
├── VehicleTracking.js          # Step 10-15: Real-time tracking
├── PickupRequest.js            # Step 16: Pickup request form
├── PickupTracking.js           # Step 17-19: Pickup tracking
└── CustomerLayout.js           # Common layout wrapper
```

### Customer Pages Structure
```
frontend/src/pages/valet/customer/
├── CustomerLandingPage.js      # Main entry point
├── MobileEntryPage.js          # Mobile registration
├── OTPVerificationPage.js      # OTP verification
├── CustomerDetailsPage.js      # Customer information
├── PaymentSelectionPage.js     # Payment options
├── PaymentGatewayPage.js       # Payment processing
├── PaymentSuccessPage.js       # Success confirmation
├── VehicleTrackingPage.js      # Vehicle status tracking
├── PickupRequestPage.js        # Request vehicle pickup
└── PickupTrackingPage.js       # Track pickup process
```

## Backend API Structure for Customer Flow

### Customer Flow Controllers
```
backend/src/controllers/valet/customer/
├── CustomerFlowController.js   # Main customer flow management
├── CustomerRegistrationController.js  # Registration process
├── CustomerPaymentController.js       # Payment handling
├── CustomerTrackingController.js      # Vehicle tracking
└── CustomerPickupController.js        # Pickup requests
```

### Customer Flow APIs
```javascript
// Step 1-2: Landing and Mobile Entry
GET  /api/valet/customer/landing/:plazaValetPointId
POST /api/valet/customer/register-mobile

// Step 3-4: OTP Process
POST /api/valet/customer/send-otp
POST /api/valet/customer/verify-otp
POST /api/valet/customer/resend-otp

// Step 5: Customer Details
POST /api/valet/customer/submit-details
GET  /api/valet/customer/get-details/:customerId

// Step 6-8: Payment Process
GET  /api/valet/customer/payment-options/:plazaId
POST /api/valet/customer/initiate-payment
POST /api/valet/customer/process-payment
GET  /api/valet/customer/payment-status/:transactionId

// Step 9-15: Vehicle Tracking
GET  /api/valet/customer/vehicle-status/:transactionId
GET  /api/valet/customer/track-vehicle/:transactionId
POST /api/valet/customer/update-tracking

// Step 16-19: Pickup Process
POST /api/valet/customer/request-pickup
GET  /api/valet/customer/pickup-options/:plazaId
GET  /api/valet/customer/pickup-status/:transactionId
POST /api/valet/customer/confirm-pickup
```

## Driver Mobile Application Structure

### Driver App Components
```
frontend/src/components/valet/driver/
├── DriverLogin.js              # Step 11: Driver authentication
├── VehicleAssignmentList.js    # Step 12: Available vehicles
├── VehicleAcceptance.js        # Step 13: Accept vehicle assignment
├── ParkingVerification.js      # Step 14: Verify parking zone/bay
├── VehicleImageCapture.js      # Step 15: Capture vehicle images
├── KeyDeposit.js               # Step 16: Key deposit confirmation
├── PickupAlerts.js             # Step 17: Pickup notifications
├── PickupAcceptance.js         # Step 18: Accept pickup requests
├── VehicleHandover.js          # Step 19-20: Handover process
└── DriverDashboard.js          # Main driver interface
```

### Driver App APIs
```javascript
// Driver Authentication
POST /api/valet/driver/login
POST /api/valet/driver/logout
GET  /api/valet/driver/profile

// Vehicle Assignment (Steps 12-16)
GET  /api/valet/driver/assigned-vehicles
POST /api/valet/driver/accept-vehicle/:transactionId
POST /api/valet/driver/verify-parking
POST /api/valet/driver/upload-vehicle-image
POST /api/valet/driver/deposit-key

// Pickup Process (Steps 17-20)
GET  /api/valet/driver/pickup-requests
POST /api/valet/driver/accept-pickup/:transactionId
POST /api/valet/driver/collect-key
POST /api/valet/driver/reach-pickup-point
POST /api/valet/driver/handover-vehicle
```

## Controller Dashboard Structure

### Controller Dashboard Components
```
frontend/src/components/valet/controller/
├── ControllerDashboard.js      # Step 21: Main dashboard
├── TransactionMonitor.js       # Real-time transaction monitoring
├── DriverAssignment.js         # Step 22: Assign drivers
├── CashPaymentAcceptance.js    # Step 23: Accept cash payments
├── DriverManagement.js         # Step 24-25: Manage drivers
├── VehicleRequestHandler.js    # Step 26: Handle vehicle requests
├── VehicleHandoverManager.js   # Step 27: Manage handovers
├── VehicleHistory.js           # Step 28: Vehicle transaction history
├── DailySalesReport.js         # Step 29: Daily sales reporting
└── DetailedReports.js          # Step 30: Detailed transaction reports
```

### Controller Dashboard APIs
```javascript
// Dashboard Overview (Step 21)
GET  /api/valet/controller/dashboard-overview
GET  /api/valet/controller/active-transactions
GET  /api/valet/controller/transaction-stats

// Driver Management (Steps 22, 24-25)
GET  /api/valet/controller/available-drivers
POST /api/valet/controller/assign-driver
POST /api/valet/controller/add-driver
PUT  /api/valet/controller/toggle-driver-status
GET  /api/valet/controller/driver-performance

// Payment Management (Step 23)
POST /api/valet/controller/accept-cash-payment
GET  /api/valet/controller/pending-cash-payments
PUT  /api/valet/controller/update-payment-status

// Vehicle Management (Steps 26-27)
POST /api/valet/controller/request-vehicle
POST /api/valet/controller/handover-to-customer
GET  /api/valet/controller/handover-queue

// Reporting (Steps 28-30)
GET  /api/valet/controller/daily-sales-report
GET  /api/valet/controller/transaction-details/:transactionId
GET  /api/valet/controller/vehicle-history/:vehicleNumber
POST /api/valet/controller/generate-detailed-report
```

## Database Enhancements for Customer Flow

### Additional Tables Needed
```sql
-- Customer Session Management
CREATE TABLE CustomerSessions (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    SessionId NVARCHAR(100) UNIQUE NOT NULL,
    PlazaValetPointId INT NOT NULL,
    MobileNumber NVARCHAR(15),
    CustomerId INT NULL,
    TransactionId DECIMAL(18,0) NULL,
    CurrentStep NVARCHAR(50) NOT NULL, -- 'MOBILE_ENTRY', 'OTP_VERIFICATION', etc.
    SessionData NVARCHAR(MAX), -- JSON data for form persistence
    ExpiresAt DATETIME NOT NULL,
    CreatedOn DATETIME DEFAULT GETDATE(),
    ModifiedOn DATETIME DEFAULT GETDATE()
);

-- Payment Gateway Transactions
CREATE TABLE PaymentGatewayTransactions (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    TransactionId DECIMAL(18,0) NOT NULL,
    GatewayType NVARCHAR(20) NOT NULL, -- 'RAZORPAY', 'PHONEPE'
    GatewayTransactionId NVARCHAR(100),
    GatewayOrderId NVARCHAR(100),
    Amount DECIMAL(10,2) NOT NULL,
    Currency NVARCHAR(3) DEFAULT 'INR',
    PaymentMethod NVARCHAR(50), -- 'UPI', 'CARD', 'NETBANKING'
    Status NVARCHAR(20) DEFAULT 'PENDING', -- 'SUCCESS', 'FAILED', 'PENDING'
    GatewayResponse NVARCHAR(MAX), -- JSON response from gateway
    CreatedOn DATETIME DEFAULT GETDATE(),
    UpdatedOn DATETIME DEFAULT GETDATE()
);

-- Vehicle Pickup Requests
CREATE TABLE VehiclePickupRequests (
    Id INT IDENTITY(1,1) PRIMARY KEY,
    TransactionId DECIMAL(18,0) NOT NULL,
    CustomerId INT NOT NULL,
    PickupPointId INT NOT NULL,
    RequestedDeliveryTime DATETIME NOT NULL,
    ActualPickupTime DATETIME NULL,
    Status NVARCHAR(20) DEFAULT 'REQUESTED', -- 'REQUESTED', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED'
    AssignedDriverId INT NULL,
    DriverAcceptedOn DATETIME NULL,
    CustomerVRN NVARCHAR(20) NULL, -- For verification during handover
    CreatedOn DATETIME DEFAULT GETDATE(),
    CompletedOn DATETIME NULL
);
```

## SMS Templates and Notifications

### SMS Message Templates
```javascript
const SMS_TEMPLATES = {
    OTP_VERIFICATION: "Your one time authentication code is: {OTP}",
    VRN_NOTIFICATION: "Your vehicle {VEHICLE_NUMBER} has been parked. VRN: {VRN}. Track: {TRACKING_URL}",
    PICKUP_READY: "Your vehicle {VEHICLE_NUMBER} is ready for pickup. Click: {PICKUP_URL}",
    PICKUP_ASSIGNED: "Driver assigned for pickup. ETA: {ETA} minutes. Track: {TRACKING_URL}",
    PICKUP_COMPLETED: "Vehicle {VEHICLE_NUMBER} has been delivered. Thank you for using our valet service!",
    PAYMENT_SUCCESS: "Payment of ₹{AMOUNT} successful. Transaction ID: {TRANSACTION_ID}",
    CASH_PAYMENT_PENDING: "Please visit valet desk to complete cash payment of ₹{AMOUNT}"
};
```

### Voice Alert System for Drivers
```javascript
const VOICE_ALERTS = {
    PICKUP_REQUEST: "New pickup request for vehicle {VEHICLE_NUMBER}",
    URGENT_PICKUP: "Urgent pickup request - Customer waiting",
    ASSIGNMENT_NOTIFICATION: "New vehicle assigned: {VEHICLE_NUMBER}"
};
```

## Implementation Priority Order

### Phase 1: Customer Web Application (Weeks 1-2)
1. Customer landing page and mobile entry
2. OTP verification system
3. Customer details form
4. Basic payment selection

### Phase 2: Payment Integration (Week 3)
5. RazorPay integration
6. PhonePe integration
7. Cash payment handling
8. Payment success flow

### Phase 3: Vehicle Tracking (Week 4)
9. Real-time status updates
10. Customer tracking interface
11. SMS notification system

### Phase 4: Driver Mobile App (Week 5)
12. Driver login and authentication
13. Vehicle assignment acceptance
14. Image capture and key deposit
15. Pickup request handling

### Phase 5: Controller Dashboard (Week 6)
16. Transaction monitoring
17. Driver assignment interface
18. Cash payment acceptance
19. Reporting system

### Phase 6: Advanced Features (Week 7-8)
20. Real-time tracking
21. Voice alerts
22. Advanced reporting
23. Performance optimization

## Technical Implementation Details

### Customer Flow State Management
```javascript
// Customer session state progression
const CUSTOMER_FLOW_STATES = {
    LANDING: 'LANDING',
    MOBILE_ENTRY: 'MOBILE_ENTRY',
    OTP_SENT: 'OTP_SENT',
    OTP_VERIFIED: 'OTP_VERIFIED',
    DETAILS_FORM: 'DETAILS_FORM',
    PAYMENT_SELECTION: 'PAYMENT_SELECTION',
    PAYMENT_PROCESSING: 'PAYMENT_PROCESSING',
    PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
    VEHICLE_SUBMITTED: 'VEHICLE_SUBMITTED',
    DRIVER_ASSIGNED: 'DRIVER_ASSIGNED',
    VEHICLE_PARKED: 'VEHICLE_PARKED',
    PICKUP_AVAILABLE: 'PICKUP_AVAILABLE',
    PICKUP_REQUESTED: 'PICKUP_REQUESTED',
    PICKUP_IN_PROGRESS: 'PICKUP_IN_PROGRESS',
    COMPLETED: 'COMPLETED'
};
```

### Customer Flow Routing Configuration
```javascript
// React Router configuration for customer flow
const customerRoutes = [
    {
        path: '/valet/customer/:plazaValetPointId',
        element: <CustomerFlowLayout />,
        children: [
            { index: true, element: <CustomerLandingPage /> },
            { path: 'mobile', element: <MobileEntryPage /> },
            { path: 'otp', element: <OTPVerificationPage /> },
            { path: 'details', element: <CustomerDetailsPage /> },
            { path: 'payment', element: <PaymentSelectionPage /> },
            { path: 'gateway', element: <PaymentGatewayPage /> },
            { path: 'success', element: <PaymentSuccessPage /> },
            { path: 'tracking', element: <VehicleTrackingPage /> },
            { path: 'pickup', element: <PickupRequestPage /> }
        ]
    }
];
```

### Payment Gateway Integration Structure
```javascript
// Payment gateway service structure
class PaymentGatewayService {
    async initiateRazorPayPayment(amount, customerId, transactionId) {
        // RazorPay payment initiation
    }

    async initiatePhonePePayment(amount, customerId, transactionId) {
        // PhonePe payment initiation
    }

    async handlePaymentCallback(gatewayType, callbackData) {
        // Handle payment gateway callbacks
    }

    async verifyPaymentStatus(gatewayTransactionId) {
        // Verify payment status with gateway
    }
}
```

### Real-time Status Updates
```javascript
// WebSocket integration for real-time updates
const SOCKET_EVENTS = {
    CUSTOMER_REGISTERED: 'customer_registered',
    PAYMENT_COMPLETED: 'payment_completed',
    DRIVER_ASSIGNED: 'driver_assigned',
    VEHICLE_PARKED: 'vehicle_parked',
    PICKUP_REQUESTED: 'pickup_requested',
    PICKUP_ASSIGNED: 'pickup_assigned',
    VEHICLE_DELIVERED: 'vehicle_delivered'
};
```

### Mobile-First Responsive Design
```css
/* Customer flow mobile-first CSS structure */
.customer-flow-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

.step-progress-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.form-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
```

This comprehensive structure covers the complete 29-step valet process with proper URL-based customer flow, mobile applications for drivers, and a complete controller dashboard system.
