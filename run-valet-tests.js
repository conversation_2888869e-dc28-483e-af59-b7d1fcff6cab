#!/usr/bin/env node

/**
 * Valet API Test Runner
 * Simple script to run valet endpoint tests
 */

const { runAllTests } = require('./test-valet-endpoints');

console.log('🧪 Valet API Test Runner');
console.log('========================');
console.log('');
console.log('⚠️  Prerequisites:');
console.log('   1. Server must be running on http://localhost:3000');
console.log('   2. Database must be connected and have valet tables');
console.log('   3. At least one PlazaValetPoint must exist with ID = 1');
console.log('');
console.log('📝 Note: Some tests may fail if authentication is required');
console.log('   or if test data doesn\'t exist. This is expected for initial testing.');
console.log('');

// Add a delay before starting tests
setTimeout(() => {
  console.log('🚀 Starting tests in 3 seconds...');
  setTimeout(() => {
    runAllTests();
  }, 3000);
}, 1000);
