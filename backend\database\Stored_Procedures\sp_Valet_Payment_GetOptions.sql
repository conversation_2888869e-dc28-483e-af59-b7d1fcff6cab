-- =============================================
-- Author: Valet System
-- Create date: 2024-12-07
-- Description: Get payment options for a plaza
-- =============================================
CREATE OR ALTER PROCEDURE sp_Valet_Payment_GetOptions
    @plazaId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validate plaza exists and is active
        IF NOT EXISTS (SELECT 1 FROM Plaza WHERE Id = @plazaId AND IsActive = 1)
        BEGIN
            RAISERROR('Plaza not found or inactive', 16, 1);
            RETURN;
        END
        
        -- Get payment configuration for the plaza
        SELECT 
            p.Id as PlazaId,
            p.PlazaName,
            p.ValetCharges,
            CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as RazorPayEnabled,
            CASE WHEN pp.Id IS NOT NULL AND pp.IsActive = 1 THEN 1 ELSE 0 END as PhonePeEnabled,
            1 as CashEnabled, -- Cash is always enabled
            CASE WHEN (rp.Id IS NOT NULL AND rp.IsActive = 1) OR (pp.Id IS NOT NULL AND pp.IsActive = 1) THEN 1 ELSE 0 END as UPIEnabled,
            CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as CardEnabled,
            CASE WHEN rp.Id IS NOT NULL AND rp.IsActive = 1 THEN 1 ELSE 0 END as NetBankingEnabled,
            rp.KeyId as RazorPayKeyId,
            pp.MerchantId as PhonePeMerchantId
        FROM Plaza p
        LEFT JOIN PlazaRazorPayConfiguration rp ON p.Id = rp.PlazaId AND rp.IsActive = 1
        LEFT JOIN PlazaPhonePeConfiguration pp ON p.Id = pp.PlazaId AND pp.IsActive = 1
        WHERE p.Id = @plazaId AND p.IsActive = 1;
        
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END
