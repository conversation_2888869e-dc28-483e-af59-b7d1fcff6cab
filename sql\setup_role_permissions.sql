-- Setup Role Permissions for RBAC system

-- Assign permissions to SuperAdmin role
-- SuperAdmin has all permissions on all modules
IF NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = (SELECT Id FROM Roles WHERE Name = 'SuperAdmin'))
BEGIN
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        (SELECT Id FROM Roles WHERE Name = 'SuperAdmin'),
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    WHERE smp.IsActive = 1;

    PRINT 'SuperAdmin permissions assigned successfully';
END
ELSE
BEGIN
    PRINT 'SuperAdmin permissions already assigned';
END

-- Assign permissions to CompanyAdmin role
-- CompanyAdmin has all permissions except on system-level configuration
IF NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = (SELECT Id FROM Roles WHERE Name = 'CompanyAdmin'))
BEGIN
    -- First, get all SubModulePermissions except those for system-level configuration
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        (SELECT Id FROM Roles WHERE Name = 'CompanyAdmin'),
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    JOIN Modules m ON sm.ModuleId = m.Id
    WHERE smp.IsActive = 1
    AND sm.Name NOT IN ('System Settings', 'Countries', 'States', 'Cities', 'Roles', 'Permissions')
    AND m.Name NOT IN ('Configuration');

    PRINT 'CompanyAdmin permissions assigned successfully';
END
ELSE
BEGIN
    PRINT 'CompanyAdmin permissions already assigned';
END

-- Assign permissions to PlazaManager role
-- PlazaManager has limited permissions focused on plaza and lane management
IF NOT EXISTS (SELECT 1 FROM RolePermissions WHERE RoleId = (SELECT Id FROM Roles WHERE Name = 'PlazaManager'))
BEGIN
    -- Dashboard permissions (View only)
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        (SELECT Id FROM Roles WHERE Name = 'PlazaManager'),
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    JOIN Modules m ON sm.ModuleId = m.Id
    JOIN Permissions p ON smp.PermissionId = p.Id
    WHERE smp.IsActive = 1
    AND m.Name = 'Dashboard'
    AND p.Name = 'View';

    -- Plaza Management permissions (View only)
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        (SELECT Id FROM Roles WHERE Name = 'PlazaManager'),
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    JOIN Modules m ON sm.ModuleId = m.Id
    JOIN Permissions p ON smp.PermissionId = p.Id
    WHERE smp.IsActive = 1
    AND m.Name = 'Plaza Management'
    AND p.Name = 'View';

    -- Lane Management permissions (All permissions)
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        (SELECT Id FROM Roles WHERE Name = 'PlazaManager'),
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    JOIN Modules m ON sm.ModuleId = m.Id
    WHERE smp.IsActive = 1
    AND m.Name = 'Lane Management';

    -- Reports permissions (View and Export only)
    INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
    SELECT 
        (SELECT Id FROM Roles WHERE Name = 'PlazaManager'),
        smp.Id,
        1,
        1,
        GETDATE()
    FROM SubModulePermissions smp
    JOIN SubModules sm ON smp.SubModuleId = sm.Id
    JOIN Modules m ON sm.ModuleId = m.Id
    JOIN Permissions p ON smp.PermissionId = p.Id
    WHERE smp.IsActive = 1
    AND m.Name = 'Reports'
    AND p.Name IN ('View', 'Export');

    PRINT 'PlazaManager permissions assigned successfully';
END
ELSE
BEGIN
    PRINT 'PlazaManager permissions already assigned';
END

-- Create a view to easily see all permissions by role
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_RolePermissions')
BEGIN
    DROP VIEW vw_RolePermissions;
    PRINT 'Dropped existing vw_RolePermissions view';
END

EXEC('
CREATE VIEW vw_RolePermissions AS
SELECT 
    r.Name AS RoleName,
    m.Name AS ModuleName,
    sm.Name AS SubModuleName,
    p.Name AS PermissionName,
    rp.IsActive
FROM RolePermissions rp
JOIN SubModulePermissions smp ON rp.SubModulePermissionId = smp.Id
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Modules m ON sm.ModuleId = m.Id
JOIN Permissions p ON smp.PermissionId = p.Id
JOIN Roles r ON rp.RoleId = r.Id
WHERE rp.IsActive = 1
');

PRINT 'Created vw_RolePermissions view';
