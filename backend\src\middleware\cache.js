// backend/src/middleware/cache.js
const redisService = require('../services/RedisService');
const { CacheTTL } = require('../config/redis');

/**
 * ===============================================================================
 * # Cache Middleware
 * ===============================================================================
 * 
 * Express middleware for automatic caching of API responses.
 * Provides flexible caching strategies for different endpoints.
 */

/**
 * Generic cache middleware
 */
const cache = (options = {}) => {
  const {
    ttl = CacheTTL.STATIC_DATA,
    keyGenerator = null,
    skipCache = false,
    skipCacheCondition = null
  } = options;

  return async (req, res, next) => {
    // Skip caching if disabled or condition met
    if (skipCache || (skipCacheCondition && skipCacheCondition(req))) {
      return next();
    }

    try {
      // Generate cache key
      const cacheKey = keyGenerator 
        ? keyGenerator(req) 
        : generateDefaultCacheKey(req);

      // Try to get cached response
      const cachedResponse = await redisService.get(cacheKey);
      
      if (cachedResponse) {
        console.log(`✅ Cache HIT for key: ${cacheKey}`);
        return res.json({
          ...cachedResponse,
          _cached: true,
          _cacheKey: cacheKey
        });
      }

      console.log(`❌ Cache MISS for key: ${cacheKey}`);

      // Store original res.json method
      const originalJson = res.json;

      // Override res.json to cache the response
      res.json = function(data) {
        // Cache the response data
        redisService.set(cacheKey, data, ttl).catch(error => {
          console.error(`Failed to cache response for key ${cacheKey}:`, error.message);
        });

        // Add cache metadata
        const responseWithMeta = {
          ...data,
          _cached: false,
          _cacheKey: cacheKey
        };

        // Call original json method
        return originalJson.call(this, responseWithMeta);
      };

      next();
    } catch (error) {
      console.error('Cache middleware error:', error.message);
      next(); // Continue without caching on error
    }
  };
};

/**
 * Dashboard-specific cache middleware
 */
const dashboardCache = (ttl = CacheTTL.DASHBOARD_SUMMARY) => {
  return cache({
    ttl,
    keyGenerator: (req) => {
      const { id: userId, role } = req.user;
      const filters = {
        dateRange: req.query.dateRange || 'today',
        companyId: req.query.companyId,
        plazaId: req.query.plazaId,
        laneId: req.query.laneId
      };
      
      return `dashboard:${req.route.path}:${role}:${userId}:${JSON.stringify(filters)}`;
    },
    skipCacheCondition: (req) => {
      // Skip cache for real-time requests
      return req.query.realtime === 'true';
    }
  });
};

/**
 * User-specific cache middleware
 */
const userCache = (ttl = CacheTTL.USER_PERMISSIONS) => {
  return cache({
    ttl,
    keyGenerator: (req) => {
      const { id: userId, role } = req.user;
      return `user:${req.route.path}:${userId}:${role}`;
    }
  });
};

/**
 * Static data cache middleware
 */
const staticCache = (ttl = CacheTTL.STATIC_DATA) => {
  return cache({
    ttl,
    keyGenerator: (req) => {
      return `static:${req.route.path}:${JSON.stringify(req.query)}`;
    }
  });
};

/**
 * Rate limiting middleware using Redis
 */
const rateLimit = (options = {}) => {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 100, // limit each IP to 100 requests per windowMs
    message = 'Too many requests from this IP, please try again later.',
    skipSuccessfulRequests = false,
    skipFailedRequests = false
  } = options;

  return async (req, res, next) => {
    try {
      const ip = req.ip || req.connection.remoteAddress;
      const endpoint = req.route?.path || req.path;
      
      const result = await redisService.checkRateLimit(
        ip, 
        endpoint, 
        max, 
        Math.floor(windowMs / 1000)
      );

      // Add rate limit headers
      res.set({
        'X-RateLimit-Limit': max,
        'X-RateLimit-Remaining': result.remaining,
        'X-RateLimit-Reset': new Date(Date.now() + windowMs)
      });

      if (result.exceeded) {
        return res.status(429).json({
          success: false,
          message,
          retryAfter: windowMs
        });
      }

      next();
    } catch (error) {
      console.error('Rate limit middleware error:', error.message);
      next(); // Continue without rate limiting on error
    }
  };
};

/**
 * Cache invalidation middleware
 */
const invalidateCache = (patterns = []) => {
  return async (req, res, next) => {
    // Store original res.json method
    const originalJson = res.json;

    // Override res.json to invalidate cache after successful response
    res.json = function(data) {
      // Only invalidate cache for successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Invalidate cache patterns
        patterns.forEach(pattern => {
          if (typeof pattern === 'function') {
            pattern(req, redisService);
          } else {
            redisService.del(pattern).catch(error => {
              console.error(`Failed to invalidate cache pattern ${pattern}:`, error.message);
            });
          }
        });
      }

      // Call original json method
      return originalJson.call(this, data);
    };

    next();
  };
};

/**
 * Generate default cache key
 */
const generateDefaultCacheKey = (req) => {
  const userId = req.user?.id || 'anonymous';
  const role = req.user?.role || 'guest';
  const path = req.route?.path || req.path;
  const query = JSON.stringify(req.query);
  
  return `default:${path}:${role}:${userId}:${query}`;
};

/**
 * Cache warming utility
 */
const warmCache = async (endpoints = []) => {
  console.log('🔥 Starting cache warming...');
  
  for (const endpoint of endpoints) {
    try {
      // This would typically make internal API calls to warm the cache
      console.log(`Warming cache for: ${endpoint}`);
      // Implementation depends on specific endpoints
    } catch (error) {
      console.error(`Failed to warm cache for ${endpoint}:`, error.message);
    }
  }
  
  console.log('✅ Cache warming completed');
};

module.exports = {
  cache,
  dashboardCache,
  userCache,
  staticCache,
  rateLimit,
  invalidateCache,
  warmCache
};