require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

/**
 * <PERSON>ript to verify which table the dashboard is actually using
 * and compare the data availability between both tables
 */

/**
 * Helper function to calculate date range (copied from DashboardController)
 */
function calculateDateRange(dateRange) {
  const referenceDate = new Date();
  let startDate, endDate;
  
  switch(dateRange) {
    case 'today':
      startDate = new Date(referenceDate);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'yesterday':
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 1);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setDate(endDate.getDate() - 1);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'week':
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 6);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'month':
      startDate = new Date(referenceDate);
      startDate.setDate(startDate.getDate() - 29);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
      break;
    default:
      startDate = new Date(referenceDate);
      startDate.setHours(0, 0, 0, 0);
      endDate = new Date(referenceDate);
      endDate.setHours(23, 59, 59, 999);
  }
  
  return { startDate, endDate };
}

async function verifyDashboardTableUsage() {
  try {
    console.log('=== DASHBOARD TABLE USAGE VERIFICATION ===');
    console.log(`Analysis Time: ${new Date().toISOString()}`);
    console.log();

    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });
    console.log('Connected to database successfully!');

    const dateRanges = ['today', 'yesterday', 'week', 'month'];
    
    for (const dateRange of dateRanges) {
      const { startDate, endDate } = calculateDateRange(dateRange);
      
      console.log(`\n=== ${dateRange.toUpperCase()} ===`);
      console.log(`Date Range: ${startDate.toISOString()} to ${endDate.toISOString()}`);
      
      // Test the EXACT query that dashboard controller uses (NEW table only)
      const dashboardQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
          COUNT(*) as TransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK, INDEX(0))
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      `;
      
      const request = new sql.Request();
      request.input('startDate', sql.DateTime, startDate);
      request.input('endDate', sql.DateTime, endDate);
      
      const dashboardResult = await request.query(dashboardQuery);
      const dashboardData = dashboardResult.recordset[0];
      
      console.log('DASHBOARD CONTROLLER RESULTS (NEW table only):');
      console.log(`  Total Revenue: ₹${dashboardData.TotalRevenue.toFixed(2)}`);
      console.log(`  Transaction Count: ${dashboardData.TransactionCount}`);
      console.log(`  Vehicle Count: ${dashboardData.VehicleCount}`);
      console.log(`  Avg Duration: ${dashboardData.AvgDuration.toFixed(2)} minutes`);
      
      // Compare with OLD table data
      const oldTableQuery = `
        SELECT
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as TotalRevenue,
          COUNT(*) as TransactionCount,
          COUNT(DISTINCT CASE WHEN t.VehicleNumber <> 'NA' AND t.VehicleNumber IS NOT NULL THEN t.VehicleNumber ELSE NULL END) as VehicleCount,
          ISNULL(AVG(ISNULL(t.ParkedDuration, 0)), 0) as AvgDuration
        FROM tblParkwiz_Parking_Data_OLD t
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      `;
      
      const oldTableResult = await request.query(oldTableQuery);
      const oldTableData = oldTableResult.recordset[0];
      
      console.log('OLD TABLE DATA (not used by dashboard):');
      console.log(`  Total Revenue: ₹${oldTableData.TotalRevenue.toFixed(2)}`);
      console.log(`  Transaction Count: ${oldTableData.TransactionCount}`);
      console.log(`  Vehicle Count: ${oldTableData.VehicleCount}`);
      console.log(`  Avg Duration: ${oldTableData.AvgDuration.toFixed(2)} minutes`);
      
      // Combined data (what we COULD get if using both tables)
      const combinedRevenue = dashboardData.TotalRevenue + oldTableData.TotalRevenue;
      const combinedTransactions = dashboardData.TransactionCount + oldTableData.TransactionCount;
      const combinedVehicles = dashboardData.VehicleCount + oldTableData.VehicleCount;
      
      console.log('COMBINED DATA (if using both tables):');
      console.log(`  Total Revenue: ₹${combinedRevenue.toFixed(2)}`);
      console.log(`  Transaction Count: ${combinedTransactions}`);
      console.log(`  Vehicle Count: ${combinedVehicles}`);
      
      // Show the difference
      const missingRevenue = oldTableData.TotalRevenue;
      const missingTransactions = oldTableData.TransactionCount;
      const missingPercentage = combinedTransactions > 0 ? (missingTransactions / combinedTransactions * 100) : 0;
      
      console.log('MISSING DATA (from OLD table):');
      console.log(`  Missing Revenue: ₹${missingRevenue.toFixed(2)}`);
      console.log(`  Missing Transactions: ${missingTransactions} (${missingPercentage.toFixed(1)}% of total)`);
      
      if (missingTransactions > 0) {
        console.log('  ⚠️  WARNING: Dashboard is missing significant historical data!');
      } else {
        console.log('  ✅ No missing data for this date range');
      }
    }

    // Test payment method query (also uses NEW table only)
    console.log('\n=== PAYMENT METHOD ANALYSIS ===');
    const { startDate: monthStart, endDate: monthEnd } = calculateDateRange('month');
    
    const paymentMethodQuery = `
      SELECT
        ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
        ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
        COUNT(*) as transactionCount
      FROM tblParkwiz_Parking_Data t
      WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
      GROUP BY t.PaymentMode
      ORDER BY totalRevenue DESC
    `;
    
    const paymentRequest = new sql.Request();
    paymentRequest.input('startDate', sql.DateTime, monthStart);
    paymentRequest.input('endDate', sql.DateTime, monthEnd);
    
    const paymentResult = await paymentRequest.query(paymentMethodQuery);
    
    console.log('PAYMENT METHOD DATA (NEW table only - what dashboard shows):');
    paymentResult.recordset.forEach(method => {
      console.log(`  ${method.paymentMode}: ${method.transactionCount} transactions, ₹${method.totalRevenue.toFixed(2)}`);
    });

    await sql.close();
    console.log('\nDatabase connection closed.');
    
  } catch (err) {
    console.error('Error:', err);
    if (sql.connected) {
      await sql.close();
    }
  }
}

verifyDashboardTableUsage();