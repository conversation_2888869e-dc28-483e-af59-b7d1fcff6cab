# ===============================================================================
# IIS Deployment Script for PWVMS with Redis Integration
# ===============================================================================
# 
# This script deploys PWVMS to IIS with Redis caching support
# Run this script as administrator
#
# Prerequisites:
# - IIS with Node.js support (iisnode)
# - Redis (Memurai for Windows)
# - URL Rewrite Module
#
# ===============================================================================

param(
    [string]$SiteName = "PWVMS",
    [string]$AppPoolName = "PWVMS_AppPool", 
    [string]$SitePath = "C:\inetpub\wwwroot\PWVMS",
    [int]$Port = 80,
    [string]$SourceDir = $PSScriptRoot,
    [switch]$SkipRedisInstall = $false
)

# Colors for output
$Green = "Green"
$Yellow = "Yellow" 
$Red = "Red"
$Cyan = "Cyan"

Write-Host "🚀 PWVMS IIS Deployment with Redis Integration" -ForegroundColor $Cyan
Write-Host "=" * 60 -ForegroundColor $Cyan

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Please run this script as Administrator!" -ForegroundColor $Red
    Write-Host "   Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor $Yellow
    exit 1
}

Write-Host "✅ Running as Administrator" -ForegroundColor $Green

# ===============================================================================
# STEP 1: Check Prerequisites
# ===============================================================================
Write-Host "`n📋 STEP 1: Checking Prerequisites..." -ForegroundColor $Cyan

# Check if IIS is installed
Write-Host "   Checking IIS installation..." -ForegroundColor $Yellow
if ((Get-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole).State -ne "Enabled") {
    Write-Host "   Installing IIS features..." -ForegroundColor $Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-ManagementConsole, IIS-HttpErrors, IIS-HttpRedirect, IIS-StaticContent, IIS-DefaultDocument, IIS-ApplicationDevelopment, IIS-NetFxExtensibility45, IIS-ASPNET45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-WebSockets, IIS-ApplicationInit, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic -All
    Write-Host "   ✅ IIS installed successfully" -ForegroundColor $Green
} else {
    Write-Host "   ✅ IIS is already installed" -ForegroundColor $Green
}

# Check URL Rewrite Module
Write-Host "   Checking URL Rewrite Module..." -ForegroundColor $Yellow
$rewriteModule = Get-WebGlobalModule | Where-Object { $_.Name -eq "RewriteModule" }
if ($null -eq $rewriteModule) {
    Write-Host "   ❌ URL Rewrite Module is not installed" -ForegroundColor $Red
    Write-Host "   📥 Download from: https://www.iis.net/downloads/microsoft/url-rewrite" -ForegroundColor $Yellow
    Write-Host "   After installing, restart this script." -ForegroundColor $Yellow
    exit 1
} else {
    Write-Host "   ✅ URL Rewrite Module is installed" -ForegroundColor $Green
}

# Check iisnode
Write-Host "   Checking iisnode..." -ForegroundColor $Yellow
$iisnodePath = "C:\Program Files\iisnode\iisnode.dll"
if (-not (Test-Path $iisnodePath)) {
    Write-Host "   ❌ iisnode is not installed" -ForegroundColor $Red
    Write-Host "   📥 Download from: https://github.com/Azure/iisnode/releases" -ForegroundColor $Yellow
    Write-Host "   After installing, restart this script." -ForegroundColor $Yellow
    exit 1
} else {
    Write-Host "   ✅ iisnode is installed" -ForegroundColor $Green
}

# Check Node.js
Write-Host "   Checking Node.js..." -ForegroundColor $Yellow
try {
    $nodeVersion = node --version
    Write-Host "   ✅ Node.js is installed: $nodeVersion" -ForegroundColor $Green
} catch {
    Write-Host "   ❌ Node.js is not installed or not in PATH" -ForegroundColor $Red
    Write-Host "   📥 Download from: https://nodejs.org/" -ForegroundColor $Yellow
    exit 1
}

# ===============================================================================
# STEP 2: Redis Installation and Configuration
# ===============================================================================
Write-Host "`n🔴 STEP 2: Redis Setup..." -ForegroundColor $Cyan

if (-not $SkipRedisInstall) {
    # Check if Redis (Memurai) is installed
    Write-Host "   Checking Redis (Memurai) installation..." -ForegroundColor $Yellow
    $memuraiPath = "C:\Program Files\Memurai\memurai.exe"
    $memuraiCliPath = "C:\Program Files\Memurai\memurai-cli.exe"
    
    if (-not (Test-Path $memuraiPath)) {
        Write-Host "   ❌ Redis (Memurai) is not installed" -ForegroundColor $Red
        Write-Host "   📥 Installing Memurai (Redis for Windows)..." -ForegroundColor $Yellow
        
        # Download and install Memurai
        $memuraiUrl = "https://www.memurai.com/get-memurai"
        Write-Host "   Please download and install Memurai from: $memuraiUrl" -ForegroundColor $Yellow
        Write-Host "   After installation, restart this script with -SkipRedisInstall flag" -ForegroundColor $Yellow
        exit 1
    } else {
        Write-Host "   ✅ Redis (Memurai) is installed" -ForegroundColor $Green
    }
    
    # Check if Redis service is running
    Write-Host "   Checking Redis service..." -ForegroundColor $Yellow
    $redisService = Get-Service -Name "Memurai" -ErrorAction SilentlyContinue
    if ($redisService) {
        if ($redisService.Status -ne "Running") {
            Write-Host "   🔄 Starting Redis service..." -ForegroundColor $Yellow
            Start-Service -Name "Memurai"
            Start-Sleep -Seconds 3
        }
        Write-Host "   ✅ Redis service is running" -ForegroundColor $Green
    } else {
        Write-Host "   ❌ Redis service not found" -ForegroundColor $Red
        exit 1
    }
    
    # Test Redis connection
    Write-Host "   Testing Redis connection..." -ForegroundColor $Yellow
    try {
        $pingResult = & $memuraiCliPath ping
        if ($pingResult -eq "PONG") {
            Write-Host "   ✅ Redis is responding: $pingResult" -ForegroundColor $Green
        } else {
            Write-Host "   ❌ Redis ping failed" -ForegroundColor $Red
            exit 1
        }
    } catch {
        Write-Host "   ❌ Could not connect to Redis" -ForegroundColor $Red
        exit 1
    }
} else {
    Write-Host "   ⏭️ Skipping Redis installation (flag provided)" -ForegroundColor $Yellow
}

# ===============================================================================
# STEP 3: Prepare Deployment Directories
# ===============================================================================
Write-Host "`n📁 STEP 3: Preparing Deployment Directories..." -ForegroundColor $Cyan

# Create site directory if it doesn't exist
if (-not (Test-Path $SitePath)) {
    Write-Host "   Creating site directory at $SitePath" -ForegroundColor $Yellow
    New-Item -ItemType Directory -Path $SitePath -Force | Out-Null
    Write-Host "   ✅ Site directory created" -ForegroundColor $Green
} else {
    Write-Host "   ✅ Site directory exists" -ForegroundColor $Green
}

# Create deployment directories
$deploymentDirs = @(
    "$SitePath\backend",
    "$SitePath\backend\src",
    "$SitePath\backend\Uploads",
    "$SitePath\backend\Uploads\Companies", 
    "$SitePath\backend\Uploads\Plazas",
    "$SitePath\frontend\build",
    "$SitePath\logs"
)

Write-Host "   Creating deployment directories..." -ForegroundColor $Yellow
foreach ($dir in $deploymentDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "   ✅ Created: $dir" -ForegroundColor $Green
    }
}

# ===============================================================================
# STEP 4: Build and Deploy Frontend
# ===============================================================================
Write-Host "`n⚛️ STEP 4: Building React Frontend..." -ForegroundColor $Cyan

if (Test-Path "$SourceDir\frontend") {
    Write-Host "   Installing frontend dependencies..." -ForegroundColor $Yellow
    Set-Location -Path "$SourceDir\frontend"
    npm install
    
    Write-Host "   Building React application..." -ForegroundColor $Yellow
    npm run build
    
    Write-Host "   Copying frontend build files..." -ForegroundColor $Yellow
    Copy-Item -Path "$SourceDir\frontend\build\*" -Destination "$SitePath\frontend\build" -Recurse -Force
    Write-Host "   ✅ Frontend deployed successfully" -ForegroundColor $Green
    
    Set-Location -Path $SourceDir
} else {
    Write-Host "   ❌ Frontend directory not found at $SourceDir\frontend" -ForegroundColor $Red
    exit 1
}

# ===============================================================================
# STEP 5: Deploy Backend with Redis Support
# ===============================================================================
Write-Host "`n🔧 STEP 5: Deploying Backend with Redis Support..." -ForegroundColor $Cyan

if (Test-Path "$SourceDir\backend") {
    Write-Host "   Copying backend files..." -ForegroundColor $Yellow
    Copy-Item -Path "$SourceDir\backend\src\*" -Destination "$SitePath\backend\src" -Recurse -Force
    Copy-Item -Path "$SourceDir\backend\package.json" -Destination "$SitePath\backend\package.json" -Force
    Copy-Item -Path "$SourceDir\backend\package-lock.json" -Destination "$SitePath\backend\package-lock.json" -Force -ErrorAction SilentlyContinue
    
    # Handle environment file
    Write-Host "   Configuring environment..." -ForegroundColor $Yellow
    if (Test-Path "$SourceDir\backend\.env.production") {
        Copy-Item -Path "$SourceDir\backend\.env.production" -Destination "$SitePath\backend\.env" -Force
        Write-Host "   ✅ Production environment file copied" -ForegroundColor $Green
    } elseif (Test-Path "$SourceDir\backend\.env") {
        Copy-Item -Path "$SourceDir\backend\.env" -Destination "$SitePath\backend\.env" -Force
        Write-Host "   ✅ Development environment file copied" -ForegroundColor $Green
    } else {
        Write-Host "   ⚠️ No environment file found - creating template" -ForegroundColor $Yellow
        $envTemplate = @"
# Database Configuration
DB_SERVER=your_sql_server
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=ParkwizOps

# Redis Configuration (for caching and sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Application Configuration
NODE_ENV=production
PORT=5000
JWT_SECRET=your_jwt_secret_here

# Session Configuration
SESSION_SECRET=your_session_secret_here

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./Uploads
"@
        $envTemplate | Out-File -FilePath "$SitePath\backend\.env" -Encoding UTF8
        Write-Host "   ✅ Environment template created - please update with your values" -ForegroundColor $Yellow
    }
    
    # Install backend dependencies
    Write-Host "   Installing backend dependencies..." -ForegroundColor $Yellow
    Set-Location -Path "$SitePath\backend"
    npm install --production
    Write-Host "   ✅ Backend dependencies installed" -ForegroundColor $Green
    
    Set-Location -Path $SourceDir
} else {
    Write-Host "   ❌ Backend directory not found at $SourceDir\backend" -ForegroundColor $Red
    exit 1
}

# ===============================================================================
# STEP 6: Create IIS Configuration
# ===============================================================================
Write-Host "`n🌐 STEP 6: Configuring IIS..." -ForegroundColor $Cyan

# Create web.config with Redis support
Write-Host "   Creating web.config..." -ForegroundColor $Yellow
$webConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <!-- Node.js configuration -->
    <handlers>
      <add name="iisnode" path="backend/src/server.js" verb="*" modules="iisnode" />
    </handlers>
    
    <!-- URL Rewrite rules -->
    <rewrite>
      <rules>
        <!-- API routes to Node.js backend -->
        <rule name="API" stopProcessing="true">
          <match url="^api/(.*)$" />
          <action type="Rewrite" url="backend/src/server.js" />
        </rule>
        
        <!-- Static files (frontend build) -->
        <rule name="StaticFiles" stopProcessing="true">
          <match url="^(static|images|css|js|fonts)/(.*)$" />
          <action type="Rewrite" url="frontend/build/{R:0}" />
        </rule>
        
        <!-- React Router - serve index.html for all other routes -->
        <rule name="ReactRouter" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/api/" negate="true" />
          </conditions>
          <action type="Rewrite" url="frontend/build/index.html" />
        </rule>
      </rules>
    </rewrite>
    
    <!-- iisnode configuration -->
    <iisnode 
      node_env="production"
      nodeProcessCountPerApplication="1"
      maxConcurrentRequestsPerProcess="1024"
      maxNamedPipeConnectionRetry="3"
      namedPipeConnectionRetryDelay="2000"
      maxNamedPipeConnectionPoolSize="512"
      maxNamedPipePooledConnectionAge="30000"
      asyncCompletionThreadCount="0"
      initialRequestBufferSize="4096"
      maxRequestBufferSize="65536"
      watchedFiles="*.js;*.json;.env"
      uncFileChangesPollingInterval="5000"
      gracefulShutdownTimeout="60000"
      loggingEnabled="true"
      logDirectory="logs"
      debuggingEnabled="false"
      devErrorsEnabled="false"
    />
    
    <!-- Security headers -->
    <httpProtocol>
      <customHeaders>
        <add name="X-Content-Type-Options" value="nosniff" />
        <add name="X-Frame-Options" value="DENY" />
        <add name="X-XSS-Protection" value="1; mode=block" />
        <add name="Referrer-Policy" value="strict-origin-when-cross-origin" />
      </customHeaders>
    </httpProtocol>
    
    <!-- Error pages -->
    <httpErrors errorMode="Detailed" />
    
    <!-- Default document -->
    <defaultDocument>
      <files>
        <clear />
        <add value="frontend/build/index.html" />
      </files>
    </defaultDocument>
    
    <!-- Static content compression -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    
  </system.webServer>
</configuration>
"@

$webConfig | Out-File -FilePath "$SitePath\web.config" -Encoding UTF8
Write-Host "   ✅ web.config created" -ForegroundColor $Green

# Create application pool
Write-Host "   Configuring application pool..." -ForegroundColor $Yellow
if (-not (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue)) {
    New-WebAppPool -Name $AppPoolName
    Write-Host "   ✅ Application pool created: $AppPoolName" -ForegroundColor $Green
} else {
    Write-Host "   ✅ Application pool exists: $AppPoolName" -ForegroundColor $Green
}

# Configure application pool
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value ""
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"

# Create website
Write-Host "   Configuring website..." -ForegroundColor $Yellow
if (-not (Get-Website -Name $SiteName -ErrorAction SilentlyContinue)) {
    New-Website -Name $SiteName -PhysicalPath $SitePath -ApplicationPool $AppPoolName -Port $Port -Force
    Write-Host "   ✅ Website created: $SiteName" -ForegroundColor $Green
} else {
    Set-ItemProperty -Path "IIS:\Sites\$SiteName" -Name "physicalPath" -Value $SitePath
    Set-ItemProperty -Path "IIS:\Sites\$SiteName" -Name "applicationPool" -Value $AppPoolName
    Write-Host "   ✅ Website updated: $SiteName" -ForegroundColor $Green
}

# ===============================================================================
# STEP 7: Set Permissions
# ===============================================================================
Write-Host "`n🔐 STEP 7: Setting Permissions..." -ForegroundColor $Cyan

Write-Host "   Setting folder permissions..." -ForegroundColor $Yellow
$acl = Get-Acl $SitePath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$AppPoolName", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl $SitePath $acl

# Set permissions for upload directories
$uploadDirs = @(
    "$SitePath\backend\Uploads",
    "$SitePath\backend\Uploads\Companies",
    "$SitePath\backend\Uploads\Plazas",
    "$SitePath\logs"
)

foreach ($dir in $uploadDirs) {
    if (Test-Path $dir) {
        $acl = Get-Acl $dir
        $acl.SetAccessRule($accessRule)
        Set-Acl $dir $acl
        Write-Host "   ✅ Permissions set for: $dir" -ForegroundColor $Green
    }
}

# ===============================================================================
# STEP 8: Final Verification
# ===============================================================================
Write-Host "`n✅ STEP 8: Final Verification..." -ForegroundColor $Cyan

# Test Redis connection from deployment location
Write-Host "   Testing Redis connection from deployment..." -ForegroundColor $Yellow
try {
    Set-Location -Path "$SitePath\backend"
    $testScript = @"
const redis = require('ioredis');
const client = new redis({
    host: 'localhost',
    port: 6379,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
});

client.ping().then(() => {
    console.log('✅ Redis connection successful');
    process.exit(0);
}).catch((err) => {
    console.log('❌ Redis connection failed:', err.message);
    process.exit(1);
});
"@
    $testScript | Out-File -FilePath "test-redis.js" -Encoding UTF8
    $redisTest = node test-redis.js
    Write-Host "   $redisTest" -ForegroundColor $Green
    Remove-Item "test-redis.js" -Force
    Set-Location -Path $SourceDir
} catch {
    Write-Host "   ⚠️ Could not test Redis from deployment location" -ForegroundColor $Yellow
}

# Check if website is accessible
Write-Host "   Checking website accessibility..." -ForegroundColor $Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$Port" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✅ Website is accessible" -ForegroundColor $Green
    }
} catch {
    Write-Host "   ⚠️ Website may need a few moments to start" -ForegroundColor $Yellow
}

# ===============================================================================
# DEPLOYMENT COMPLETE
# ===============================================================================
Write-Host "`n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor $Green
Write-Host "=" * 60 -ForegroundColor $Green

Write-Host "`n📋 DEPLOYMENT SUMMARY:" -ForegroundColor $Cyan
Write-Host "   🌐 Website: $SiteName" -ForegroundColor $Yellow
Write-Host "   📁 Path: $SitePath" -ForegroundColor $Yellow
Write-Host "   🔗 URL: http://localhost:$Port" -ForegroundColor $Yellow
Write-Host "   🏊 App Pool: $AppPoolName" -ForegroundColor $Yellow
Write-Host "   🔴 Redis: Enabled (localhost:6379)" -ForegroundColor $Yellow

Write-Host "`n🔧 NEXT STEPS:" -ForegroundColor $Cyan
Write-Host "   1. Update .env file with your database credentials:" -ForegroundColor $Yellow
Write-Host "      $SitePath\backend\.env" -ForegroundColor $Yellow
Write-Host "   2. Restart the application pool if needed:" -ForegroundColor $Yellow
Write-Host "      Restart-WebAppPool -Name '$AppPoolName'" -ForegroundColor $Yellow
Write-Host "   3. Check logs if there are issues:" -ForegroundColor $Yellow
Write-Host "      $SitePath\logs" -ForegroundColor $Yellow

Write-Host "`n🚀 Your PWVMS application with Redis caching is now deployed!" -ForegroundColor $Green
Write-Host "   Redis will provide significant performance improvements for dashboard data." -ForegroundColor $Green