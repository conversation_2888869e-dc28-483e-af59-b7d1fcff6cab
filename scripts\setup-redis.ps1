# PowerShell script to set up Redis for PWVMS on Windows
# Run this script as Administrator

Write-Host "🚀 Setting up Redis for PWVMS..." -ForegroundColor Green

# Check if running as Administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if Chocolatey is installed
if (-not (Test-Command choco)) {
    Write-Host "📦 Installing Chocolatey..." -ForegroundColor Yellow
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    
    # Refresh environment variables
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path","User")
}

# Install Redis using Chocolatey
Write-Host "📦 Installing Redis..." -ForegroundColor Yellow
choco install redis-64 -y

# Wait for installation to complete
Start-Sleep -Seconds 5

# Check if Redis was installed successfully
if (Test-Command redis-server) {
    Write-Host "✅ Redis installed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Redis installation failed!" -ForegroundColor Red
    exit 1
}

# Create Redis configuration directory
$redisConfigDir = "C:\Redis\config"
if (-not (Test-Path $redisConfigDir)) {
    New-Item -ItemType Directory -Path $redisConfigDir -Force
    Write-Host "📁 Created Redis config directory: $redisConfigDir" -ForegroundColor Green
}

# Create Redis configuration file
$redisConfigFile = "$redisConfigDir\redis.conf"
$redisConfig = @"
# Redis configuration for PWVMS
port 6379
bind 127.0.0.1
timeout 0
keepalive 300

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Logging
loglevel notice
logfile "C:\Redis\logs\redis.log"

# Security
# requirepass your_password_here

# Performance
tcp-backlog 511
databases 16
"@

$redisConfig | Out-File -FilePath $redisConfigFile -Encoding UTF8
Write-Host "📝 Created Redis configuration file: $redisConfigFile" -ForegroundColor Green

# Create Redis logs directory
$redisLogsDir = "C:\Redis\logs"
if (-not (Test-Path $redisLogsDir)) {
    New-Item -ItemType Directory -Path $redisLogsDir -Force
    Write-Host "📁 Created Redis logs directory: $redisLogsDir" -ForegroundColor Green
}

# Create Redis data directory
$redisDataDir = "C:\Redis\data"
if (-not (Test-Path $redisDataDir)) {
    New-Item -ItemType Directory -Path $redisDataDir -Force
    Write-Host "📁 Created Redis data directory: $redisDataDir" -ForegroundColor Green
}

# Install Redis as Windows Service
Write-Host "🔧 Installing Redis as Windows Service..." -ForegroundColor Yellow

# Stop Redis if it's running
try {
    Stop-Service -Name "Redis" -ErrorAction SilentlyContinue
} catch {
    # Service might not exist yet
}

# Install Redis service
$redisServicePath = (Get-Command redis-server).Source
$serviceArgs = "--service-install --service-name Redis --port 6379 --service-run"

try {
    & $redisServicePath $serviceArgs
    Write-Host "✅ Redis service installed successfully!" -ForegroundColor Green
} catch {
    Write-Host "⚠️ Redis service installation may have failed. Trying alternative method..." -ForegroundColor Yellow
    
    # Alternative method using sc.exe
    $serviceName = "Redis"
    $serviceDisplayName = "Redis Server for PWVMS"
    $servicePath = "`"$redisServicePath`" --service-run"
    
    sc.exe create $serviceName binPath= $servicePath DisplayName= $serviceDisplayName start= auto
}

# Start Redis service
Write-Host "🚀 Starting Redis service..." -ForegroundColor Yellow
try {
    Start-Service -Name "Redis"
    Write-Host "✅ Redis service started successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start Redis service. You may need to start it manually." -ForegroundColor Red
}

# Test Redis connection
Write-Host "🧪 Testing Redis connection..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

try {
    $testResult = redis-cli ping
    if ($testResult -eq "PONG") {
        Write-Host "✅ Redis is running and responding to commands!" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Redis may not be responding correctly." -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ Could not test Redis connection. Please verify manually." -ForegroundColor Yellow
}

# Install Node.js dependencies
Write-Host "📦 Installing Node.js Redis dependencies..." -ForegroundColor Yellow
Set-Location -Path "d:\PWVMS\backend"

if (Test-Path "package.json") {
    npm install ioredis redis node-cron
    Write-Host "✅ Node.js Redis dependencies installed!" -ForegroundColor Green
} else {
    Write-Host "⚠️ package.json not found. Please run 'npm install ioredis redis node-cron' in your backend directory." -ForegroundColor Yellow
}

# Create environment file template
$envTemplate = @"
# Redis Configuration for PWVMS
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Cache TTL Settings (in seconds)
CACHE_TTL_DASHBOARD_SUMMARY=300
CACHE_TTL_DASHBOARD_CHARTS=600
CACHE_TTL_USER_SESSION=3600
CACHE_TTL_USER_PERMISSIONS=1800
CACHE_TTL_LIVE_DATA=30
CACHE_TTL_STATIC_DATA=86400

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Real-time Features
REALTIME_ENABLED=true
PUBSUB_ENABLED=true
NOTIFICATIONS_ENABLED=true
"@

$envFile = "d:\PWVMS\.env.redis"
$envTemplate | Out-File -FilePath $envFile -Encoding UTF8
Write-Host "📝 Created environment template: $envFile" -ForegroundColor Green

# Final instructions
Write-Host ""
Write-Host "🎉 Redis setup completed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Copy the Redis environment variables from .env.redis to your main .env file" -ForegroundColor White
Write-Host "2. Restart your PWVMS backend server" -ForegroundColor White
Write-Host "3. Check the health-check endpoint to verify Redis connection" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Redis Management Commands:" -ForegroundColor Cyan
Write-Host "- Start Redis: Start-Service Redis" -ForegroundColor White
Write-Host "- Stop Redis: Stop-Service Redis" -ForegroundColor White
Write-Host "- Redis CLI: redis-cli" -ForegroundColor White
Write-Host "- Test connection: redis-cli ping" -ForegroundColor White
Write-Host ""
Write-Host "📁 Redis Files:" -ForegroundColor Cyan
Write-Host "- Config: C:\Redis\config\redis.conf" -ForegroundColor White
Write-Host "- Logs: C:\Redis\logs\redis.log" -ForegroundColor White
Write-Host "- Data: C:\Redis\data\" -ForegroundColor White
Write-Host ""
Write-Host "✅ Setup complete! Your PWVMS now has Redis caching capabilities." -ForegroundColor Green