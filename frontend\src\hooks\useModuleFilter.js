// frontend/src/hooks/useModuleFilter.js
import { useState, useMemo } from 'react';
import { useAuth } from '../contexts/authContext';
import usePermissionFilter from './usePermissionFilter';

/**
 * Custom hook for filtering module data based on user permissions and selected filters
 *
 * @param {Object} options - Hook options
 * @param {Array} options.data - The data array to filter
 * @param {Array} options.companies - Array of all companies
 * @param {Array} options.plazas - Array of all plazas
 * @param {Array} options.lanes - Array of all lanes
 * @param {string} options.companyIdField - The field name for company ID in the data objects
 * @param {string} options.plazaIdField - The field name for plaza ID in the data objects
 * @param {string} options.laneIdField - The field name for lane ID in the data objects
 * @param {string} options.module - The module name for permission checking
 * @returns {Object} - The filtered data and helper functions
 */
const useModuleFilter = ({
  data = [],
  companies = [],
  plazas = [],
  lanes = [],
  companyIdField = 'CompanyID',
  plazaIdField = 'PlazaID',
  laneIdField = 'LaneID',
  module = ''
}) => {
  const { user } = useAuth();
  const [filters, setFilters] = useState({});

  // Log the incoming data for debugging
  console.log(`useModuleFilter for ${module} module:`, {
    dataLength: data?.length || 0,
    companiesLength: companies?.length || 0,
    plazasLength: plazas?.length || 0,
    lanesLength: lanes?.length || 0,
    userRole: user?.role
  });

  // First apply permission filtering to ensure user only sees data they have access to
  // Always include inactive items by setting includeInactive to true
  const {
    filteredData: permissionFilteredData,
    canCreate,
    canEdit,
    canDelete
  } = usePermissionFilter(
    data,
    { companyIdField, plazaIdField, includeInactive: true }
  );
  
  // Log the filtered data
  console.log(`After permission filtering for ${module}:`, {
    filteredDataLength: permissionFilteredData?.length || 0
  });

  // Then apply user-selected filters
  const filteredData = useMemo(() => {
    if (!permissionFilteredData || !Array.isArray(permissionFilteredData)) {
      return [];
    }

    let result = [...permissionFilteredData];

    // Filter by company
    if (filters.companyId) {
      const companyIdNum = parseInt(filters.companyId, 10);
      if (!isNaN(companyIdNum)) {
        result = result.filter(item => {
          if (!item || item[companyIdField] === undefined || item[companyIdField] === null) {
            return false;
          }

          const itemCompanyId = typeof item[companyIdField] === 'string'
            ? parseInt(item[companyIdField], 10)
            : item[companyIdField];
          return itemCompanyId === companyIdNum;
        });
      }
    }

    // Filter by plaza
    if (filters.plazaId) {
      const plazaIdNum = parseInt(filters.plazaId, 10);
      if (!isNaN(plazaIdNum)) {
        result = result.filter(item => {
          if (!item || item[plazaIdField] === undefined || item[plazaIdField] === null) {
            return false;
          }

          const itemPlazaId = typeof item[plazaIdField] === 'string'
            ? parseInt(item[plazaIdField], 10)
            : item[plazaIdField];
          return itemPlazaId === plazaIdNum;
        });
      }
    }

    // Filter by lane
    if (filters.laneId) {
      const laneIdNum = parseInt(filters.laneId, 10);
      if (!isNaN(laneIdNum)) {
        result = result.filter(item => {
          if (!item || item[laneIdField] === undefined || item[laneIdField] === null) {
            return false;
          }

          const itemLaneId = typeof item[laneIdField] === 'string'
            ? parseInt(item[laneIdField], 10)
            : item[laneIdField];
          return itemLaneId === laneIdNum;
        });
      }
    }

    // Filter by search term
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(item => {
        if (!item) return false;

        // Check common fields that might exist in different modules
        return Object.keys(item).some(key => {
          const value = item[key];
          if (value === null || value === undefined) return false;

          // Convert to string and check if it includes the search term
          return String(value).toLowerCase().includes(searchTerm);
        });
      });
    }

    return result;
  }, [permissionFilteredData, filters, companyIdField, plazaIdField, laneIdField]);

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
  };

  return {
    filteredData,
    filters,
    setFilters: handleFilterChange,
    canCreate,
    canEdit,
    canDelete
  };
};

export default useModuleFilter;
