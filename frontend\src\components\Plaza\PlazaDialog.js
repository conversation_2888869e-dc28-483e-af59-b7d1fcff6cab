import React, { useState, useEffect } from 'react';
import { X, Upload } from 'lucide-react';

import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';

export default function PlazaDialog({ isOpen, onClose, onSubmit, initialData, title, companies }) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    CompanyId: '',
    PlazaName: '',
    AddressId: '',
    ContactPerson: '',
    ContactNumber: '',
    ContactEmail: '',
    PlazaDisplayName: '',
    PlazaCode: '',
    GstNumber: '',
    SmsSenderHeader: '',
    MessageSenderKey: '',
    PayAtEntryOrExit: '0',
    IsValetFee: false,
    ValetFee: 0,
    IsParkingFee: false,
    ParkingFeeType: 'Fixed',
    FixParkingFee: 0,
    PerHourParkingFee: 0,
    HelplineNumber: '',
    OpenTime: '',
    CloseTime: '',
    IsActive: true,
    IsCashActive: true,
    IsPhonePe: false,
    IsAutoExitPreviousDayTransaction: false,
    isPaymentReceivedMessageEnabled: true,
    isHandoverMessageEnabled: true,
    isParkedSafelyEnabled: true,
    isVRNEnabled: false
  });

  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState('');
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (initialData) {
      setFormData({
        CompanyId: initialData.CompanyId || '',
        PlazaName: initialData.PlazaName || '',
        AddressId: initialData.AddressId || '',
        ContactPerson: initialData.ContactPerson || '',
        ContactNumber: initialData.ContactNumber || '',
        ContactEmail: initialData.ContactEmail || '',
        PlazaDisplayName: initialData.PlazaDisplayName || '',
        PlazaCode: initialData.PlazaCode || '',
        GstNumber: initialData.GstNumber || '',
        SmsSenderHeader: initialData.SmsSenderHeader || '',
        MessageSenderKey: initialData.MessageSenderKey || '',
        PayAtEntryOrExit: initialData.PayAtEntryOrExit || '0',
        IsValetFee: initialData.IsValetFee || false,
        ValetFee: initialData.ValetFee || 0,
        IsParkingFee: initialData.IsParkingFee || false,
        ParkingFeeType: initialData.ParkingFeeType || 'Fixed',
        FixParkingFee: initialData.FixParkingFee || 0,
        PerHourParkingFee: initialData.PerHourParkingFee || 0,
        HelplineNumber: initialData.HelplineNumber || '',
        OpenTime: initialData.OpenTime || '',
        CloseTime: initialData.CloseTime || '',
        IsActive: initialData.IsActive !== undefined ? initialData.IsActive : true,
        IsCashActive: initialData.IsCashActive !== undefined ? initialData.IsCashActive : true,
        IsPhonePe: initialData.IsPhonePe || false,
        IsAutoExitPreviousDayTransaction: initialData.IsAutoExitPreviousDayTransaction || false,
        isPaymentReceivedMessageEnabled: initialData.isPaymentReceivedMessageEnabled !== undefined ? initialData.isPaymentReceivedMessageEnabled : true,
        isHandoverMessageEnabled: initialData.isHandoverMessageEnabled !== undefined ? initialData.isHandoverMessageEnabled : true,
        isParkedSafelyEnabled: initialData.isParkedSafelyEnabled !== undefined ? initialData.isParkedSafelyEnabled : true,
        isVRNEnabled: initialData.isVRNEnabled || false
      });

      if (initialData.Logo) {
        setLogoPreview(initialData.Logo);
      }
    } else {
      resetForm();
    }
  }, [initialData, isOpen]);

  const resetForm = () => {
    setFormData({
      CompanyId: '',
      PlazaName: '',
      AddressId: '',
      ContactPerson: '',
      ContactNumber: '',
      ContactEmail: '',
      PlazaDisplayName: '',
      PlazaCode: '',
      GstNumber: '',
      SmsSenderHeader: '',
      MessageSenderKey: '',
      PayAtEntryOrExit: '0',
      IsValetFee: false,
      ValetFee: 0,
      IsParkingFee: false,
      ParkingFeeType: '0',
      FixParkingFee: 0,
      PerHourParkingFee: 0,
      HelplineNumber: '',
      OpenTime: '',
      CloseTime: '',
      IsActive: true,
      IsCashActive: true,
      IsPhonePe: false,
      IsAutoExitPreviousDayTransaction: false,
      isPaymentReceivedMessageEnabled: true,
      isHandoverMessageEnabled: true,
      isParkedSafelyEnabled: true,
      isVRNEnabled: false
    });
    setLogoFile(null);
    setLogoPreview('');
    setErrors({});
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : (name === 'PayAtEntryOrExit' || name === 'ParkingFeeType' ? parseInt(value) : value)
    }));

    // Clear error for this field if any
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const handleNumberChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value === '' ? 0 : Number(value)
    }));
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.CompanyId) newErrors.CompanyId = 'Company is required';
    if (!formData.PlazaName) newErrors.PlazaName = 'Plaza name is required';
    if (!formData.PlazaCode) newErrors.PlazaCode = 'Plaza code is required';
    if (!formData.ContactPerson) newErrors.ContactPerson = 'Contact person is required';
    if (!formData.ContactNumber) newErrors.ContactNumber = 'Contact number is required';
    if (formData.ContactEmail && !/^\S+@\S+\.\S+$/.test(formData.ContactEmail)) {
      newErrors.ContactEmail = 'Invalid email format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Create a copy of the form data
      const dataToSubmit = { ...formData };

      // Add current user ID for tracking who created/modified the record
      if (user && user.id) {
        if (initialData) {
          // For updates, set ModifiedBy
          dataToSubmit.ModifiedBy = user.id;
        } else {
          // For new records, set CreatedBy
          dataToSubmit.CreatedBy = user.id;
        }
      }

      // Call parent's onSubmit with form data and logo file
      onSubmit(dataToSubmit, logoFile);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-auto">
        <div className="flex justify-between items-center px-6 py-4 border-b">
          <h2 className="text-lg font-medium">{title}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Company Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company*
              </label>
              <select
                name="CompanyId"
                value={formData.CompanyId}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.CompanyId ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select Company</option>
                {companies.map((company) => (
                  <option key={company.Id} value={company.Id}>
                    {company.CompanyName}
                  </option>
                ))}
              </select>
              {errors.CompanyId && (
                <p className="mt-1 text-sm text-red-500">{errors.CompanyId}</p>
              )}
            </div>

            {/* Plaza Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plaza Name*
              </label>
              <input
                type="text"
                name="PlazaName"
                value={formData.PlazaName}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.PlazaName ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.PlazaName && (
                <p className="mt-1 text-sm text-red-500">{errors.PlazaName}</p>
              )}
            </div>

            {/* Plaza Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plaza Code*
              </label>
              <input
                type="text"
                name="PlazaCode"
                value={formData.PlazaCode}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.PlazaCode ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.PlazaCode && (
                <p className="mt-1 text-sm text-red-500">{errors.PlazaCode}</p>
              )}
            </div>

            {/* Plaza Display Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Display Name
              </label>
              <input
                type="text"
                name="PlazaDisplayName"
                value={formData.PlazaDisplayName}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Contact Person */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Person*
              </label>
              <input
                type="text"
                name="ContactPerson"
                value={formData.ContactPerson}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.ContactPerson ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.ContactPerson && (
                <p className="mt-1 text-sm text-red-500">{errors.ContactPerson}</p>
              )}
            </div>

            {/* Contact Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Number*
              </label>
              <input
                type="text"
                name="ContactNumber"
                value={formData.ContactNumber}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.ContactNumber ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.ContactNumber && (
                <p className="mt-1 text-sm text-red-500">{errors.ContactNumber}</p>
              )}
            </div>

            {/* Contact Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Email
              </label>
              <input
                type="email"
                name="ContactEmail"
                value={formData.ContactEmail}
                onChange={handleChange}
                className={`w-full px-3 py-2 border rounded-md ${
                  errors.ContactEmail ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.ContactEmail && (
                <p className="mt-1 text-sm text-red-500">{errors.ContactEmail}</p>
              )}
            </div>

            {/* GST Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                GST Number
              </label>
              <input
                type="text"
                name="GstNumber"
                value={formData.GstNumber}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Helpline Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Helpline Number
              </label>
              <input
                type="text"
                name="HelplineNumber"
                value={formData.HelplineNumber}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Open Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Open Time
              </label>
              <input
                type="time"
                name="OpenTime"
                value={formData.OpenTime}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Close Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Close Time
              </label>
              <input
                type="time"
                name="CloseTime"
                value={formData.CloseTime}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* SMS Sender Header */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SMS Sender Header
              </label>
              <input
                type="text"
                name="SmsSenderHeader"
                value={formData.SmsSenderHeader}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Message Sender Key */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Message Sender Key
              </label>
              <input
                type="text"
                name="MessageSenderKey"
                value={formData.MessageSenderKey}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>

            {/* Pay At Entry or Exit */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Pay At
              </label>
              <select
                name="PayAtEntryOrExit"
                value={formData.PayAtEntryOrExit}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="0">Entry</option>
                <option value="1">Exit</option>
              </select>
            </div>

            {/* Logo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Logo
              </label>
              <div className="flex items-center space-x-4">
                {logoPreview && (
                  <div className="w-16 h-16 border rounded-md overflow-hidden">
                    <img
                      src={logoPreview}
                      alt="Logo Preview"
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <label className="cursor-pointer flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                  <Upload size={18} className="mr-2" />
                  Upload Logo
                  <input
                    type="file"
                    onChange={handleLogoChange}
                    accept="image/*"
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          </div>

          {/* Fees Section */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Fee Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Valet Fee Configuration */}
              <div className="border p-4 rounded-md">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="isValetFee"
                    name="IsValetFee"
                    checked={formData.IsValetFee}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="isValetFee" className="ml-2 text-sm font-medium text-gray-700">
                    Enable Valet Fee
                  </label>
                </div>

                <div className={`${formData.IsValetFee ? 'opacity-100' : 'opacity-50'}`}>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valet Fee Amount
                  </label>
                  <input
                    type="number"
                    name="ValetFee"
                    value={formData.ValetFee}
                    onChange={handleNumberChange}
                    disabled={!formData.IsValetFee}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>

              {/* Parking Fee Configuration */}
              <div className="border p-4 rounded-md">
                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="isParkingFee"
                    name="IsParkingFee"
                    checked={formData.IsParkingFee}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <label htmlFor="isParkingFee" className="ml-2 text-sm font-medium text-gray-700">
                    Enable Parking Fee
                  </label>
                </div>

                <div className={`${formData.IsParkingFee ? 'opacity-100' : 'opacity-50'}`}>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Parking Fee Type
                    </label>
                    <select
                      name="ParkingFeeType"
                      value={formData.ParkingFeeType}
                      onChange={handleChange}
                      disabled={!formData.IsParkingFee}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="0">Fixed</option>
                      <option value="1">Hourly</option>
                    </select>
                  </div>

                  {formData.ParkingFeeType === 'Fixed' ? (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Fixed Fee Amount
                      </label>
                      <input
                        type="number"
                        name="FixParkingFee"
                        value={formData.FixParkingFee}
                        onChange={handleNumberChange}
                        disabled={!formData.IsParkingFee}
                        min="0"
                        step="0.01"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  ) : (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Per Hour Fee Amount
                      </label>
                      <input
                        type="number"
                        name="PerHourParkingFee"
                        value={formData.PerHourParkingFee}
                        onChange={handleNumberChange}
                        disabled={!formData.IsParkingFee}
                        min="0"
                        step="0.01"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Payment Options */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Options</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isCashActive"
                  name="IsCashActive"
                  checked={formData.IsCashActive}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isCashActive" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Cash Payment
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPhonePe"
                  name="IsPhonePe"
                  checked={formData.IsPhonePe}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isPhonePe" className="ml-2 text-sm font-medium text-gray-700">
                  Enable PhonePe Payment
                </label>
              </div>
            </div>
          </div>

          {/* Additional Settings */}
          <div className="mt-6 border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="IsActive"
                  checked={formData.IsActive}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isActive" className="ml-2 text-sm font-medium text-gray-700">
                  Active
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isAutoExitPreviousDayTransaction"
                  name="IsAutoExitPreviousDayTransaction"
                  checked={formData.IsAutoExitPreviousDayTransaction}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isAutoExitPreviousDayTransaction" className="ml-2 text-sm font-medium text-gray-700">
                  Auto Exit Previous Day Transaction
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPaymentReceivedMessageEnabled"
                  name="isPaymentReceivedMessageEnabled"
                  checked={formData.isPaymentReceivedMessageEnabled}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isPaymentReceivedMessageEnabled" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Payment Received Message
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isHandoverMessageEnabled"
                  name="isHandoverMessageEnabled"
                  checked={formData.isHandoverMessageEnabled}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isHandoverMessageEnabled" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Handover Message
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isParkedSafelyEnabled"
                  name="isParkedSafelyEnabled"
                  checked={formData.isParkedSafelyEnabled}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isParkedSafelyEnabled" className="ml-2 text-sm font-medium text-gray-700">
                  Enable Parked Safely Message
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isVRNEnabled"
                  name="isVRNEnabled"
                  checked={formData.isVRNEnabled}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                />
                <label htmlFor="isVRNEnabled" className="ml-2 text-sm font-medium text-gray-700">
                  Enable VRN
                </label>
              </div>
            </div>
          </div>

          <div className="mt-8 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <PermissionButton
              type="submit"
              className="px-4 py-2 bg-blue-600 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              requiredModule="Plazas"
              requiredPermissions={initialData ? ["Edit"] : ["Create"]}
              companyId={initialData?.CompanyId}
              plazaId={initialData?.Id}
            >
              {initialData ? 'Update' : 'Create'}
            </PermissionButton>
          </div>
        </form>
      </div>
    </div>
  );
}