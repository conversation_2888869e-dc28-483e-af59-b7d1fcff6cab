-- Update sp_Valet_Payment_UpdateStatus stored procedure to match controller expectations and table structure
USE ParkwizOps;
GO

-- Drop and recreate the stored procedure
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_Valet_Payment_UpdateStatus]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_Valet_Payment_UpdateStatus];
GO

CREATE PROCEDURE [dbo].[sp_Valet_Payment_UpdateStatus]
    @PaymentId INT,
    @Status NVARCHAR(50),
    @GatewayTransactionId NVARCHAR(100) = NULL,
    @GatewayResponse NVARCHAR(MAX) = NULL,
    @UpdatedBy INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @PaymentId IS NULL OR @PaymentId <= 0
        BEGIN
            RAISERROR('Payment ID is required and must be greater than 0', 16, 1);
            RETURN -1;
        END
        
        IF @Status IS NULL OR LTRIM(RTRIM(@Status)) = ''
        BEGIN
            RAISERROR('Status is required', 16, 1);
            RETURN -1;
        END
        
        -- Check if payment exists
        IF NOT EXISTS(SELECT 1 FROM [dbo].[PaymentGatewayTransactions] WHERE [Id] = @PaymentId)
        BEGIN
            RAISERROR('Payment not found', 16, 1);
            RETURN -1;
        END
        
        -- Update payment status
        UPDATE [dbo].[PaymentGatewayTransactions]
        SET
            [Status] = @Status,
            [GatewayTransactionId] = @GatewayTransactionId,
            [GatewayResponse] = @GatewayResponse,
            [UpdatedOn] = GETDATE()
        WHERE [Id] = @PaymentId;
        
        -- If payment is successful, update the transaction
        IF @Status = 'SUCCESS'
        BEGIN
            DECLARE @TransactionId INT;
            
            -- Get transaction ID
            SELECT @TransactionId = TransactionId FROM [dbo].[PaymentGatewayTransactions] WHERE [Id] = @PaymentId;
            
            -- Update transaction payment status
            UPDATE [dbo].[ParkingTransactions]
            SET
                [IsPaymentCompleted] = 1
            WHERE [Id] = @TransactionId;
        END
        
        -- Return success with updated payment details
        SELECT 
            @PaymentId AS PaymentId,
            @Status AS Status,
            'Payment status updated successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END;
GO

-- Verify the stored procedure was created
SELECT 
    ROUTINE_NAME as ProcedureName,
    CREATED as CreatedDate,
    LAST_ALTERED as LastModifiedDate
FROM INFORMATION_SCHEMA.ROUTINES
WHERE ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME = 'sp_Valet_Payment_UpdateStatus';