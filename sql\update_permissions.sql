-- Update permissions script for ParkwizOps database
-- This script updates the permissions for CompanyAdmin and PlazaManager roles
-- to match the permissions from the PWVMS database

-- First, clear existing permissions for CompanyAdmin (RoleId = 7) and PlazaManager (RoleId = 8)
DELETE FROM RolePermissions WHERE RoleId IN (7, 8);

-- CompanyAdmin Permissions (RoleId = 7)
-- Based on the documentation and PWVMS database

-- User Management permissions
-- Can create, view, edit, and delete users within their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Users' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Roles permissions - View only
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Roles' AND p.Name = 'View';

-- Company Management permissions
-- Can view company details for their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Companies' AND p.Name = 'View';

-- Company Settings permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Company Settings' AND p.Name IN ('View', 'Edit');

-- Plaza Management permissions
-- Full CRUD for plazas within their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Plazas' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Plaza Settings permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Plaza Settings' AND p.Name IN ('View', 'Edit');

-- Lane Management permissions
-- Full CRUD for lanes within plazas of their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Lanes' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Lane Settings permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Lane Settings' AND p.Name IN ('View', 'Edit');

-- Digital Payment permissions
-- Full CRUD for digital payment configurations within their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Digital Pay' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Fastag permissions
-- Full CRUD for fastag configurations within their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Fastag' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- ANPR permissions
-- Full CRUD for ANPR configurations within their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'ANPR' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- UHF Reader permissions
-- Full CRUD for UHF Reader configurations
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'UHF Reader' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Pass Registration permissions
-- Full CRUD for Pass Registration
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Pass Registration' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Configuration permissions
-- Limited to company-specific configurations
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('System Settings', 'Email Templates', 'SMS Templates') AND p.Name = 'View';

-- Reports permissions
-- Access to all reports for their assigned companies
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Traffic Reports', 'Revenue Reports', 'User Activity') AND p.Name IN ('View', 'Export');

-- Dashboard permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Dashboard', 'Analytics') AND p.Name = 'View';

-- Transaction permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Payment Transactions', 'Vehicle Entries', 'Transaction History') AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Monitoring permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 7, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Lane Status', 'System Health', 'Alerts') AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- PlazaManager Permissions (RoleId = 8)
-- Based on the documentation and PWVMS database

-- User Management permissions
-- Limited view access to users assigned to their plazas
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Users' AND p.Name = 'View';

-- Company Management permissions
-- View-only access to their company details
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Companies' AND p.Name = 'View';

-- Plaza Management permissions
-- View and edit details for their assigned plazas
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Plazas' AND p.Name IN ('View', 'Edit');

-- Plaza Settings permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Plaza Settings' AND p.Name IN ('View', 'Edit');

-- Lane Management permissions
-- Full CRUD for lanes within their assigned plazas
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Lanes' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Lane Settings permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Lane Settings' AND p.Name IN ('View', 'Edit');

-- Digital Payment permissions
-- Full CRUD for digital payment configurations within their assigned plazas
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Digital Pay' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Fastag permissions
-- Full CRUD for fastag configurations within their assigned plazas
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Fastag' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- ANPR permissions
-- Full CRUD for ANPR configurations within their assigned plazas
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'ANPR' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- UHF Reader permissions
-- Full CRUD for UHF Reader configurations
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'UHF Reader' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Pass Registration permissions
-- Full CRUD for Pass Registration
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name = 'Pass Registration' AND p.Name IN ('View', 'Create', 'Edit', 'Delete');

-- Reports permissions
-- Access to reports for their assigned plazas only
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Traffic Reports', 'Revenue Reports', 'User Activity') AND p.Name = 'View';

-- Dashboard permissions
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Dashboard', 'Analytics') AND p.Name = 'View';

-- Transaction permissions - View only
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Payment Transactions', 'Vehicle Entries', 'Transaction History') AND p.Name = 'View';

-- Monitoring permissions - View only
INSERT INTO RolePermissions (RoleId, SubModulePermissionId, IsActive, CreatedBy, CreatedOn)
SELECT 8, smp.Id, 1, 1, GETDATE()
FROM SubModulePermissions smp
JOIN SubModules sm ON smp.SubModuleId = sm.Id
JOIN Permissions p ON smp.PermissionId = p.Id
WHERE sm.Name IN ('Lane Status', 'System Health', 'Alerts') AND p.Name = 'View';