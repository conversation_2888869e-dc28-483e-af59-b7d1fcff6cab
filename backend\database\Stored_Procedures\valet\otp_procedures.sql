-- =============================================
-- Valet OTP Management Stored Procedures
-- Created for comprehensive OTP management
-- =============================================

-- =============================================
-- 1. CREATE - Generate OTP
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_OTP_Generate]
    @MobileNumber NVARCHAR(15),
    @OTPCode NVARCHAR(6),
    @ExpiryMinutes INT = 5,
    @CreatedBy INT = 1,
    @NewId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @MobileNumber IS NULL OR LTRIM(RTRIM(@MobileNumber)) = ''
        BEGIN
            RAISERROR('Mobile number is required', 16, 1);
            RETURN -1;
        END
        
        -- Validate mobile number format (10 digits)
        IF LEN(@MobileNumber) != 10 OR @MobileNumber NOT LIKE '[0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9][0-9]'
        BEGIN
            RAISERROR('Mobile number must be exactly 10 digits', 16, 1);
            RETURN -1;
        END
        
        IF @OTPCode IS NULL OR LTRIM(RTRIM(@OTPCode)) = ''
        BEGIN
            RAISERROR('OTP code is required', 16, 1);
            RETURN -1;
        END
        
        -- Validate OTP format (6 digits)
        IF LEN(@OTPCode) != 6 OR @OTPCode NOT LIKE '[0-9][0-9][0-9][0-9][0-9][0-9]'
        BEGIN
            RAISERROR('OTP must be exactly 6 digits', 16, 1);
            RETURN -1;
        END
        
        IF @ExpiryMinutes <= 0 OR @ExpiryMinutes > 60
        BEGIN
            RAISERROR('Expiry minutes must be between 1 and 60', 16, 1);
            RETURN -1;
        END
        
        -- Check for spam protection (max 3 OTPs per mobile in last 10 minutes)
        DECLARE @RecentOTPCount INT;
        SELECT @RecentOTPCount = COUNT(*)
        FROM [dbo].[OTP]
        WHERE [MobileNumber] = @MobileNumber
        AND [CreatedOn] >= DATEADD(MINUTE, -10, GETDATE());
        
        IF @RecentOTPCount >= 3
        BEGIN
            RAISERROR('Too many OTP requests. Please try again after 10 minutes', 16, 1);
            RETURN -1;
        END
        
        -- Deactivate any existing active OTPs for this mobile number
        UPDATE [dbo].[OTP]
        SET [IsActive] = 0, [ModifiedOn] = GETDATE()
        WHERE [MobileNumber] = @MobileNumber AND [IsActive] = 1;
        
        -- Calculate expiry time (store as minutes from now)
        DECLARE @ExpiryTime INT = @ExpiryMinutes;

        -- Insert new OTP
        INSERT INTO [dbo].[OTP]
        (
            [MobileNumber],
            [OTP],
            [ExpireTime],
            [IsActive],
            [CreatedBy],
            [CreatedOn]
        )
        VALUES
        (
            LTRIM(RTRIM(@MobileNumber)),
            @OTPCode,
            @ExpiryTime,
            1,
            @CreatedBy,
            GETDATE()
        );
        
        SET @NewId = SCOPE_IDENTITY();
        
        -- Return success with OTP details
        SELECT
            @NewId AS Id,
            @MobileNumber AS MobileNumber,
            @OTPCode AS OTPCode,
            @ExpiryTime AS ExpiryMinutes,
            'OTP generated successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
        
        SET @NewId = -1;
    END CATCH
END
GO

-- =============================================
-- 2. VERIFY - Verify OTP
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_OTP_Verify]
    @MobileNumber NVARCHAR(15),
    @OTPCode NVARCHAR(6),
    @ModifiedBy INT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @MobileNumber IS NULL OR LTRIM(RTRIM(@MobileNumber)) = ''
        BEGIN
            RAISERROR('Mobile number is required', 16, 1);
            RETURN -1;
        END
        
        IF @OTPCode IS NULL OR LTRIM(RTRIM(@OTPCode)) = ''
        BEGIN
            RAISERROR('OTP code is required', 16, 1);
            RETURN -1;
        END
        
        -- Find the OTP record
        DECLARE @OTPId INT, @ExpireTime INT, @CreatedOn DATETIME;

        SELECT TOP 1
            @OTPId = [Id],
            @ExpireTime = [ExpireTime],
            @CreatedOn = [CreatedOn]
        FROM [dbo].[OTP]
        WHERE [MobileNumber] = @MobileNumber
        AND [OTP] = @OTPCode
        AND [IsActive] = 1
        ORDER BY [CreatedOn] DESC;
        
        -- Check if OTP exists
        IF @OTPId IS NULL
        BEGIN
            SELECT 'Invalid OTP code' AS Message, 0 AS Success, 'INVALID_OTP' AS ErrorCode;
            RETURN -1;
        END
        
        -- Check if expired (ExpireTime is in minutes from CreatedOn)
        IF DATEADD(MINUTE, @ExpireTime, @CreatedOn) < GETDATE()
        BEGIN
            -- Deactivate expired OTP
            UPDATE [dbo].[OTP]
            SET [IsActive] = 0, [ModifiedOn] = GETDATE()
            WHERE [Id] = @OTPId;

            SELECT 'OTP has expired' AS Message, 0 AS Success, 'EXPIRED_OTP' AS ErrorCode;
            RETURN -1;
        END

        -- Deactivate OTP after successful verification
        UPDATE [dbo].[OTP]
        SET
            [IsActive] = 0,
            [ModifiedBy] = @ModifiedBy,
            [ModifiedOn] = GETDATE()
        WHERE [Id] = @OTPId;
        
        -- Return success
        SELECT 
            @OTPId AS Id,
            @MobileNumber AS MobileNumber,
            'OTP verified successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 3. CLEANUP - Remove expired OTPs
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_OTP_Cleanup]
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Deactivate expired OTPs (ExpireTime is in minutes from CreatedOn)
        UPDATE [dbo].[OTP]
        SET [IsActive] = 0, [ModifiedOn] = GETDATE()
        WHERE [IsActive] = 1
        AND DATEADD(MINUTE, [ExpireTime], [CreatedOn]) < GETDATE();
        
        DECLARE @DeactivatedCount INT = @@ROWCOUNT;
        
        -- Delete old OTP records (older than 24 hours)
        DELETE FROM [dbo].[OTP]
        WHERE [CreatedOn] < DATEADD(HOUR, -24, GETDATE());
        
        DECLARE @DeletedCount INT = @@ROWCOUNT;
        
        -- Return cleanup summary
        SELECT 
            @DeactivatedCount AS DeactivatedCount,
            @DeletedCount AS DeletedCount,
            'OTP cleanup completed successfully' AS Message,
            1 AS Success;
            
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO

-- =============================================
-- 4. READ - Get OTP status
-- =============================================
CREATE OR ALTER PROCEDURE [dbo].[sp_Valet_OTP_GetStatus]
    @MobileNumber NVARCHAR(15)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Validation
        IF @MobileNumber IS NULL OR LTRIM(RTRIM(@MobileNumber)) = ''
        BEGIN
            RAISERROR('Mobile number is required', 16, 1);
            RETURN -1;
        END
        
        -- Get latest OTP status for mobile number
        SELECT TOP 1
            [Id],
            [MobileNumber],
            [OTP] AS OTPCode,
            [ExpireTime],
            [IsActive],
            [CreatedOn],
            CASE
                WHEN DATEADD(MINUTE, [ExpireTime], [CreatedOn]) < GETDATE() THEN 'EXPIRED'
                WHEN [IsActive] = 1 THEN 'ACTIVE'
                ELSE 'INACTIVE'
            END AS Status,
            CASE
                WHEN DATEADD(MINUTE, [ExpireTime], [CreatedOn]) > GETDATE() THEN
                    DATEDIFF(SECOND, GETDATE(), DATEADD(MINUTE, [ExpireTime], [CreatedOn]))
                ELSE 0
            END AS RemainingSeconds
        FROM [dbo].[OTP]
        WHERE [MobileNumber] = @MobileNumber
        ORDER BY [CreatedOn] DESC;
        
        -- Check if any OTP exists
        IF @@ROWCOUNT = 0
        BEGIN
            SELECT 'No OTP found for this mobile number' AS Message, 0 AS Success;
        END
        
    END TRY
    BEGIN CATCH
        -- Handle errors
        SELECT 
            ERROR_NUMBER() AS ErrorNumber,
            ERROR_MESSAGE() AS ErrorMessage,
            0 AS Success;
    END CATCH
END
GO
