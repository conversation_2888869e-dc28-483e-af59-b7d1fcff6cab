// "use client";
// import { useState, useEffect } from "react";
// import { Button } from "../../components/ui/button";
// import {
//   Dialog,
//   DialogContent,
//   DialogDescription,
//   DialogHeader,
//   DialogTitle,
//   DialogTrigger,
// } from "../../components/ui/dialog";
// import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage,FormDescription } from "../../components/ui/form";
// import { Input } from "../../components/ui/input";
// import { Switch } from "../../components/ui/switch";
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../components/ui/select";
// import { toast } from "../../components/ui/use-toast";
// import { Tabs, TabsContent, <PERSON>bsList, TabsTrigger } from "../../components/ui/tabs";
// import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "../../components/ui/card";
// import { useForm } from "react-hook-form";

// import { z } from "zod";
// import { zodResolver } from "@hookform/resolvers/zod";
// import { addressApi } from "../../api/addressApi";

// // Define the Zod schema for validation
// const formSchema = z.object({
//   CompanyName: z.string().min(2, { message: "Company name must be at least 2 characters" }),
//   AddressId: z.string().min(1, { message: "Please select an address" }),
//   ContactPerson: z.string().optional(),
//   ContactNumber: z.string().optional(),
//   ContactEmail: z.string().email({ message: "Please enter a valid email address" }).optional(),
//   CompanyCode: z.string().min(2, { message: "Company code must be at least 2 characters" }),
//   IsActive: z.boolean().default(true),
// });

// // Default values for the form
// const defaultValues = {
//   CompanyName: "",
//   AddressId: "",
//   ContactPerson: "",
//   ContactNumber: "",
//   ContactEmail: "",
//   CompanyCode: "",
//   IsActive: true,
// };

// function CompanyDialog({
//   open,
//   onOpenChange,
//   initialData,
//   onSave,
// }) {
//   const [isDialogOpen, setIsDialogOpen] = useState(open || false);
//   const [activeTab, setActiveTab] = useState("company");
//   const [addresses, setAddresses] = useState([]);
//   const [loading, setLoading] = useState(false);

//   // Update internal state when open prop changes
//   useEffect(() => {
//     if (open !== undefined) {
//       setIsDialogOpen(open);
//     }
//   }, [open]);

//   // Convert initialData to form values
//   const getInitialFormValues = () => {
//     if (!initialData) return defaultValues;
//     return {
//       CompanyName: initialData.CompanyName || "",
//       AddressId: initialData.AddressId || "",
//       ContactPerson: initialData.ContactPerson || "",
//       ContactNumber: initialData.ContactNumber || "",
//       ContactEmail: initialData.ContactEmail || "",
//       CompanyCode: initialData.CompanyCode || "",
//       IsActive: initialData.IsActive !== undefined ? initialData.IsActive : true,
//     };
//   };

//   const form = useForm({
//     resolver: zodResolver(formSchema),
//     defaultValues: getInitialFormValues(),
//   });

//   // Fetch addresses on component mount
//   useEffect(() => {
//     async function fetchAddresses() {
//       try {
//         setLoading(true);
//         const addressesData = await addressApi.getAddresses();
//         setAddresses(addressesData);
//       } catch (error) {
//         console.error("Failed to fetch addresses:", error);
//         toast({
//           title: "Error",
//           description: "Failed to load addresses. Please try again.",
//           variant: "destructive",
//         });
//       } finally {
//         setLoading(false);
//       }
//     }
//     fetchAddresses();
//   }, []);

//   // Update form when initialData changes
//   useEffect(() => {
//     if (initialData) {
//       const formValues = getInitialFormValues();
//       Object.entries(formValues).forEach(([key, value]) => {
//         form.setValue(key, value);
//       });
//     } else {
//       form.reset(defaultValues);
//     }
//   }, [initialData, form]);

//   function onSubmit(data) {
//     console.log(data);
//     toast({
//       title: "Company saved successfully",
//       description: `Company ${data.CompanyName} has been ${initialData ? "updated" : "created"}.`,
//     });
//     if (onSave) {
//       onSave();
//     }
//     if (onOpenChange) {
//       onOpenChange(false);
//     } else {
//       setIsDialogOpen(false);
//     }
//   }

//   const handleOpenChange = (open) => {
//     if (onOpenChange) {
//       onOpenChange(open);
//     } else {
//       setIsDialogOpen(open);
//     }
//   };

//   const isControlled = open !== undefined && onOpenChange !== undefined;

//   return (
//     <Dialog
//       open={isControlled ? open : isDialogOpen}
//       onOpenChange={handleOpenChange}
//       modal={true}
//     >
//       {!isControlled && (
//         <DialogTrigger asChild>
//           <Button variant="outline">Add Company</Button>
//         </DialogTrigger>
//       )}
//       <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto bg-white border shadow-lg">
//         <DialogHeader>
//           <DialogTitle>{initialData ? "Edit Company" : "Add New Company"}</DialogTitle>
//           <DialogDescription>
//             Fill in the details to {initialData ? "update" : "create"} a company record.
//           </DialogDescription>
//         </DialogHeader>
//         <Tabs defaultValue="company" value={activeTab} onValueChange={setActiveTab}>
//           <TabsList className="grid w-full grid-cols-3">
//             <TabsTrigger value="company">Company Info</TabsTrigger>
//             <TabsTrigger value="contact">Contact Details</TabsTrigger>
//             <TabsTrigger value="settings">Settings</TabsTrigger>
//           </TabsList>
//           <Form {...form}>
//             <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-6">
//               {/* Company Info Tab */}
//               <TabsContent value="company">
//                 <Card className="bg-white">
//                   <CardHeader>
//                     <CardTitle>Company Information</CardTitle>
//                     <CardDescription>Enter the basic details of the company.</CardDescription>
//                   </CardHeader>
//                   <CardContent className="space-y-4">
//                     <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
//                       <FormField
//                         control={form.control}
//                         name="CompanyName"
//                         render={({ field }) => (
//                           <FormItem>
//                             <FormLabel>Company Name</FormLabel>
//                             <FormControl>
//                               <Input placeholder="Acme Corp" {...field} />
//                             </FormControl>
//                             <FormMessage />
//                           </FormItem>
//                         )}
//                       />
//                       <FormField
//                         control={form.control}
//                         name="CompanyCode"
//                         render={({ field }) => (
//                           <FormItem>
//                             <FormLabel>Company Code</FormLabel>
//                             <FormControl>
//                               <Input placeholder="ACM123" {...field} />
//                             </FormControl>
//                             <FormMessage />
//                           </FormItem>
//                         )}
//                       />
//                     </div>
//                   </CardContent>
//                   <CardFooter className="flex justify-between">
//                     <Button variant="outline" onClick={() => handleOpenChange(false)}>
//                       Cancel
//                     </Button>
//                     <Button type="button" onClick={() => setActiveTab("contact")}>
//                       Next
//                     </Button>
//                   </CardFooter>
//                 </Card>
//               </TabsContent>

//               {/* Contact Details Tab */}
//               <TabsContent value="contact">
//                 <Card className="bg-white">
//                   <CardHeader>
//                     <CardTitle>Contact Details</CardTitle>
//                     <CardDescription>Provide contact information for the company.</CardDescription>
//                   </CardHeader>
//                   <CardContent className="space-y-4">
//                     <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
//                       <FormField
//                         control={form.control}
//                         name="ContactPerson"
//                         render={({ field }) => (
//                           <FormItem>
//                             <FormLabel>Contact Person</FormLabel>
//                             <FormControl>
//                               <Input placeholder="John Doe" {...field} />
//                             </FormControl>
//                             <FormMessage />
//                           </FormItem>
//                         )}
//                       />
//                       <FormField
//                         control={form.control}
//                         name="ContactNumber"
//                         render={({ field }) => (
//                           <FormItem>
//                             <FormLabel>Contact Number</FormLabel>
//                             <FormControl>
//                               <Input placeholder="+1 (555) 123-4567" {...field} />
//                             </FormControl>
//                             <FormMessage />
//                           </FormItem>
//                         )}
//                       />
//                       <FormField
//                         control={form.control}
//                         name="ContactEmail"
//                         render={({ field }) => (
//                           <FormItem>
//                             <FormLabel>Contact Email</FormLabel>
//                             <FormControl>
//                               <Input type="email" placeholder="<EMAIL>" {...field} />
//                             </FormControl>
//                             <FormMessage />
//                           </FormItem>
//                         )}
//                       />
//                       <FormField
//                         control={form.control}
//                         name="AddressId"
//                         render={({ field }) => (
//                           <FormItem>
//                             <FormLabel>Address</FormLabel>
//                             <Select
//                               onValueChange={field.onChange}
//                               value={field.value}
//                               disabled={loading}
//                             >
//                               <FormControl>
//                                 <SelectTrigger>
//                                   <SelectValue placeholder="Select an address" />
//                                 </SelectTrigger>
//                               </FormControl>
//                               <SelectContent className="bg-white">
//                                 {loading ? (
//                                   <SelectItem value="loading" disabled>
//                                     Loading...
//                                   </SelectItem>
//                                 ) : (
//                                   addresses.map((address) => (
//                                     <SelectItem key={address.Id} value={address.Id}>
//                                       {address.AddressLine1}, {address.City}
//                                     </SelectItem>
//                                   ))
//                                 )}
//                               </SelectContent>
//                             </Select>
//                             <FormMessage />
//                           </FormItem>
//                         )}
//                       />
//                     </div>
//                   </CardContent>
//                   <CardFooter className="flex justify-between">
//                     <Button variant="outline" type="button" onClick={() => setActiveTab("company")}>
//                       Back
//                     </Button>
//                     <Button type="button" onClick={() => setActiveTab("settings")}>
//                       Next
//                     </Button>
//                   </CardFooter>
//                 </Card>
//               </TabsContent>

//               {/* Settings Tab */}
//               <TabsContent value="settings">
//                 <Card className="bg-white">
//                   <CardHeader>
//                     <CardTitle>Additional Settings</CardTitle>
//                     <CardDescription>Configure company status settings.</CardDescription>
//                   </CardHeader>
//                   <CardContent className="space-y-4">
//                     <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
//                       <FormField
//                         control={form.control}
//                         name="IsActive"
//                         render={({ field }) => (
//                           <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
//                             <div className="space-y-0.5">
//                               <FormLabel>Active Status</FormLabel>
//                               <FormDescription>Set the company as active or inactive</FormDescription>
//                             </div>
//                             <FormControl>
//                               <Switch checked={field.value} onCheckedChange={field.onChange} />
//                             </FormControl>
//                           </FormItem>
//                         )}
//                       />
//                     </div>
//                   </CardContent>
//                   <CardFooter className="flex justify-between">
//                     <Button variant="outline" type="button" onClick={() => setActiveTab("contact")}>
//                       Back
//                     </Button>
//                     <Button type="submit">Save Company</Button>
//                   </CardFooter>
//                 </Card>
//               </TabsContent>
//             </form>
//           </Form>
//         </Tabs>
//       </DialogContent>
//     </Dialog>
//   );
// }

// export { CompanyDialog };

// components/Company/CompanyDialog.js
import React, { useState, useEffect } from 'react';
import { X, Upload } from 'lucide-react';
import { useAuth } from '../../contexts/authContext';
import { PermissionButton } from '../auth/PermissionButton';

export default function CompanyDialog({ isOpen, onClose, onSubmit, initialData, title }) {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    Id: initialData?.Id || 0,
    CompanyName: initialData?.CompanyName || '',
    CompanyCode: initialData?.CompanyCode || '',
    ContactPerson: initialData?.ContactPerson || '',
    ContactNumber: initialData?.ContactNumber || '',
    ContactEmail: initialData?.ContactEmail || '',
    CompanyLogo: initialData?.CompanyLogo || '',
    IsActive: initialData?.IsActive !== undefined ? initialData.IsActive : true,
  });
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState(initialData?.Id ? `/api/companies/${initialData.Id}/logo` : '');

  useEffect(() => {
    if (initialData) {
      setFormData({
        Id: initialData.Id || 0,
        CompanyName: initialData.CompanyName || '',
        CompanyCode: initialData.CompanyCode || '',
        ContactPerson: initialData.ContactPerson || '',
        ContactNumber: initialData.ContactNumber || '',
        ContactEmail: initialData.ContactEmail || '',
        CompanyLogo: initialData.CompanyLogo || '',
        IsActive: initialData.IsActive !== undefined ? initialData.IsActive : true,
      });

      // Set logo preview URL to the API endpoint if company has an ID
      if (initialData.Id && initialData.CompanyLogo) {
        setLogoPreview(`/api/companies/${initialData.Id}/logo?v=${Date.now()}`);
      } else {
        setLogoPreview('');
      }
    }
  }, [initialData]);

  if (!isOpen) return null;

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Create a simple data object for submission
    const dataToSubmit = { ...formData };

    // Add current user ID for tracking who created/modified the record
    if (user && user.id) {
      if (initialData) {
        // For updates, set ModifiedBy
        dataToSubmit.ModifiedBy = user.id;
      } else {
        // For new records, set CreatedBy
        dataToSubmit.CreatedBy = user.id;
      }
    }

    // Remove CompanyLogo field since we're not using it for now
    delete dataToSubmit.CompanyLogo;

    // Log the data for debugging
    console.log('Data being submitted:', dataToSubmit);

    onSubmit(dataToSubmit);
  };

  const formatPhoneNumber = (value) => {
    // Basic formatting for phone numbers
    if (!value) return value;
    const phoneNumber = value.replace(/[^\d]/g, '');
    if (phoneNumber.length < 4) return phoneNumber;
    if (phoneNumber.length < 7) {
      return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3)}`;
    }
    return `${phoneNumber.slice(0, 3)}-${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
  };

  const handlePhoneChange = (e) => {
    const formattedPhone = formatPhoneNumber(e.target.value);
    setFormData({ ...formData, ContactNumber: formattedPhone });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg w-full max-w-md" role="dialog" aria-labelledby="company-dialog-title">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 id="company-dialog-title" className="text-xl font-semibold">{title}</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full"
            aria-label="Close dialog"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          <div>
            <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
            <input
              id="companyName"
              type="text"
              value={formData.CompanyName}
              onChange={(e) => setFormData({ ...formData, CompanyName: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="companyCode" className="block text-sm font-medium text-gray-700 mb-1">Company Code</label>
            <input
              id="companyCode"
              type="text"
              value={formData.CompanyCode}
              onChange={(e) => setFormData({ ...formData, CompanyCode: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="contactPerson" className="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
            <input
              id="contactPerson"
              type="text"
              value={formData.ContactPerson}
              onChange={(e) => setFormData({ ...formData, ContactPerson: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="contactNumber" className="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
            <input
              id="contactNumber"
              type="text"
              value={formData.ContactNumber}
              onChange={handlePhoneChange}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">Contact Email</label>
            <input
              id="contactEmail"
              type="email"
              value={formData.ContactEmail}
              onChange={(e) => setFormData({ ...formData, ContactEmail: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Company Branding</label>
            <div className="flex items-center space-x-4">
              {/* Display company initials in a colored circle */}
              <div className="w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center text-blue-700 font-bold text-xl">
                {formData.CompanyName ? formData.CompanyName.substring(0, 2).toUpperCase() : 'CO'}
              </div>

              {/* Keep the file upload for future use, but it won't be displayed for now */}
              <input
                type="file"
                accept="image/*"
                onChange={handleLogoChange}
                className="hidden"
                id="logo-upload"
              />

              <div className="text-sm text-gray-500">
                <p>Company initials will be displayed as shown.</p>
                <p className="mt-1">Custom logo upload will be available in a future update.</p>
              </div>
            </div>
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isActive"
                checked={formData.IsActive}
                onChange={(e) => setFormData({ ...formData, IsActive: e.target.checked })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span htmlFor="isActive" className="text-sm text-gray-700">
                Active
              </span>
            </label>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <PermissionButton
              type="submit"
              className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
              requiredModule="Companies"
              requiredPermissions={initialData ? ["Edit"] : ["Create"]}
              companyId={initialData?.Id}
            >
              Save
            </PermissionButton>
          </div>
        </form>
      </div>
    </div>
  );
}