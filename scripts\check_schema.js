// Script to check the schema of the tables
const sql = require('mssql');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables from backend/.env
dotenv.config({ path: path.join(__dirname, 'backend', '.env') });

async function checkSchema() {
  try {
    console.log('Connecting to database...');
    const pool = await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: process.env.DB_ENCRYPT === 'true',
        trustServerCertificate: process.env.DB_TRUST_CERT === 'true'
      }
    });
    console.log('Connected to database successfully');

    // Check tblLaneDetails schema
    console.log('\nChecking tblLaneDetails schema:');
    const laneDetailsResult = await pool.request().query(`
      SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tblLaneDetails'
    `);
    console.log(laneDetailsResult.recordset);

    await sql.close();
    console.log('Database connection closed');
  } catch (err) {
    console.error('Error:', err);
  }
}

checkSchema();