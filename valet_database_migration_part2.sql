-- =============================================
-- Valet System Database Migration Script - Part 2
-- Create additional valet tables and new tables
-- =============================================

USE ParkwizOps;
GO

PRINT 'Starting Valet System Database Migration Part 2...';
PRINT '=================================================';

-- =============================================
-- Step 9: Create VehicleEntries Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='VehicleEntries' AND xtype='U')
BEGIN
    PRINT 'Creating VehicleEntries table...';
    CREATE TABLE VehicleEntries (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        VehicleNumber NVARCHAR(50) NULL,
        VehicleClass NVARCHAR(50) NULL,
        CompanyId NVARCHAR(50) NULL,
        SiteName NVARCHAR(100) NULL,
        Location NVARCHAR(100) NULL,
        Type NVARCHAR(50) NULL,
        DeviceName NVARCHAR(100) NULL,
        GroupId NVARCHAR(100) NULL,
        VisitedDateTime DATETIME NULL,
        VehicleImage NVARCHAR(MAX) NULL,
        NumberPlate NVARCHAR(MAX) NULL
    );
    PRINT '✅ VehicleEntries table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ VehicleEntries table already exists.';
END

-- =============================================
-- Step 10: Create AllowDriverLogin Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AllowDriverLogin' AND xtype='U')
BEGIN
    PRINT 'Creating AllowDriverLogin table...';
    CREATE TABLE AllowDriverLogin (
        Id DECIMAL(18,0) IDENTITY(1,1) PRIMARY KEY,
        DriverId INT NULL,
        EntryDateTime DATETIME NULL,
        EntryBy DECIMAL(18,0) NULL,
        ExitDateTime DATETIME NULL,
        ExitBy DECIMAL(18,0) NULL,
        IsActive BIT NULL,
        CreatedBy INT NULL,
        CreatedOn DATETIME NULL,
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL
    );
    PRINT '✅ AllowDriverLogin table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ AllowDriverLogin table already exists.';
END

-- =============================================
-- Step 11: Create PhonePePaymentTransactions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PhonePePaymentTransactions' AND xtype='U')
BEGIN
    PRINT 'Creating PhonePePaymentTransactions table...';
    CREATE TABLE PhonePePaymentTransactions (
        Id DECIMAL(18,0) IDENTITY(1,1) PRIMARY KEY,
        StageURL NVARCHAR(500) NULL,
        RequestType NVARCHAR(500) NULL,
        XVerify NVARCHAR(500) NULL,
        XCallbackUrl NVARCHAR(500) NULL,
        XProviderId NVARCHAR(500) NULL,
        MerchantId NVARCHAR(500) NULL,
        TransactionId NVARCHAR(100) NULL,
        MerchantOrderId NVARCHAR(100) NULL,
        Amount INT NULL,
        ExpiresIn INT NULL,
        StoreId NVARCHAR(500) NULL,
        TerminalId NVARCHAR(500) NULL,
        RequestDateTime DATETIME NULL,
        RequestInitSuccess BIT NULL,
        RequestInitCode NVARCHAR(500) NULL,
        RequestInitMessage NVARCHAR(500) NULL,
        RequestInitTransactionId NVARCHAR(100) NULL,
        RequestInitAmount INT NULL,
        RequestInitMerchantId NVARCHAR(500) NULL,
        RequestInitQRString NVARCHAR(500) NULL,
        RequestInitMobileNumber NVARCHAR(500) NULL,
        RequestInitPayLink NVARCHAR(500) NULL,
        RequestInitUpiIntent NVARCHAR(500) NULL,
        RequestInitDateTime DATETIME NULL,
        ResponseSuccess BIT NULL,
        ResponseCode NVARCHAR(500) NULL,
        ResponseMessage NVARCHAR(500) NULL,
        ResponseMerchantId NVARCHAR(500) NULL,
        ResponseTransactionId NVARCHAR(100) NULL,
        ResponseProviderReferenceId NVARCHAR(500) NULL,
        ResponseAmount INT NULL,
        ResponsePaymentState NVARCHAR(500) NULL,
        ResponsePayResponseCode NVARCHAR(500) NULL,
        ResponseDateTime DATETIME NULL,
        ParkingTransactionId DECIMAL(18,0) NULL,
        IsParkingTransactionSuccess BIT NOT NULL DEFAULT 0,
        ParkingTransactionSuccessDateTime DATETIME NULL
    );
    PRINT '✅ PhonePePaymentTransactions table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ PhonePePaymentTransactions table already exists.';
END

-- =============================================
-- Step 12: Create RazorPayTransactions Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RazorPayTransactions' AND xtype='U')
BEGIN
    PRINT 'Creating RazorPayTransactions table...';
    CREATE TABLE RazorPayTransactions (
        UId DECIMAL(18,0) IDENTITY(1,1) PRIMARY KEY,
        Id NVARCHAR(500) NULL,
        RequestType INT NULL,
        Request TEXT NULL,
        RequestDateTime DATETIME NULL,
        RequestResponse TEXT NULL,
        RequestResponseDateTime DATETIME NULL,
        Response TEXT NULL,
        ResponseDateTime DATETIME NULL,
        Status NVARCHAR(100) NULL,
        Amount INT NULL,
        ClientTransactionId DECIMAL(18,0) NULL,
        IsClientTransactionSuccess BIT NULL,
        ClientTransactionDateTime DATETIME NULL,
        ResponseSuccess BIT NULL,
        IsRefunded BIT NULL,
        RefundResponse NVARCHAR(500) NULL,
        RefundResponseDateTime DATETIME NULL
    );
    PRINT '✅ RazorPayTransactions table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ RazorPayTransactions table already exists.';
END

-- =============================================
-- Step 13: Create NEW Valet-Specific Tables
-- =============================================

-- ValetDrivers Table (Associated with PlazaValetPoint - each driver assigned to a valet desk)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ValetDrivers' AND xtype='U')
BEGIN
    PRINT 'Creating ValetDrivers table...';
    CREATE TABLE ValetDrivers (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        DriverLicenseNumber NVARCHAR(50) NULL,
        PhoneNumber NVARCHAR(15) NOT NULL,
        IsActive BIT DEFAULT 1,
        CompanyId INT NOT NULL,
        PlazaId INT NOT NULL,
        PlazaValetPointId INT NULL, -- Each driver can be assigned to a specific valet desk/point
        CreatedBy INT NULL,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL,

        FOREIGN KEY (UserId) REFERENCES Users(Id),
        FOREIGN KEY (CompanyId) REFERENCES tblCompanyMaster(Id),
        FOREIGN KEY (PlazaId) REFERENCES Plaza(Id),
        FOREIGN KEY (PlazaValetPointId) REFERENCES PlazaValetPoint(Id)
    );
    PRINT '✅ ValetDrivers table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ValetDrivers table already exists.';
END

-- ValetControllers Table (Each controller is assigned to a specific PlazaValetPoint/desk)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ValetControllers' AND xtype='U')
BEGIN
    PRINT 'Creating ValetControllers table...';
    CREATE TABLE ValetControllers (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        UserId INT NOT NULL,
        PlazaValetPointId INT NOT NULL, -- Each controller is assigned to a specific valet desk
        CompanyId INT NOT NULL,
        PlazaId INT NOT NULL,
        IsActive BIT DEFAULT 1,
        CreatedBy INT NULL,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL,

        FOREIGN KEY (UserId) REFERENCES Users(Id),
        FOREIGN KEY (PlazaValetPointId) REFERENCES PlazaValetPoint(Id),
        FOREIGN KEY (CompanyId) REFERENCES tblCompanyMaster(Id),
        FOREIGN KEY (PlazaId) REFERENCES Plaza(Id)
    );
    PRINT '✅ ValetControllers table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ValetControllers table already exists.';
END

-- ValetQRCodes Table (Associated with PlazaValetPoint)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ValetQRCodes' AND xtype='U')
BEGIN
    PRINT 'Creating ValetQRCodes table...';
    CREATE TABLE ValetQRCodes (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        PlazaValetPointId INT NOT NULL, -- QR codes are specific to valet points
        PlazaId INT NOT NULL,
        QRCodeData NVARCHAR(500) NOT NULL,
        QRCodeImage VARBINARY(MAX) NULL,
        IsActive BIT DEFAULT 1,
        CreatedBy INT NULL,
        CreatedOn DATETIME DEFAULT GETDATE(),

        FOREIGN KEY (PlazaValetPointId) REFERENCES PlazaValetPoint(Id),
        FOREIGN KEY (PlazaId) REFERENCES Plaza(Id)
    );
    PRINT '✅ ValetQRCodes table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ ValetQRCodes table already exists.';
END

-- VehicleStatusTracking Table (Track vehicle status throughout valet process)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='VehicleStatusTracking' AND xtype='U')
BEGIN
    PRINT 'Creating VehicleStatusTracking table...';
    CREATE TABLE VehicleStatusTracking (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        TransactionId INT NOT NULL,
        Status NVARCHAR(50) NOT NULL, -- 'REGISTERED', 'PAYMENT_COMPLETED', 'DRIVER_ASSIGNED', 'PARKED', 'PICKUP_REQUESTED', 'IN_TRANSIT', 'DELIVERED'
        UpdatedBy INT NOT NULL,
        UpdatedOn DATETIME DEFAULT GETDATE(),
        Remarks NVARCHAR(500) NULL,

        -- Note: Foreign key constraints will be added after data migration
        -- FOREIGN KEY (TransactionId) REFERENCES ParkingTransactions(Id),
        -- FOREIGN KEY (UpdatedBy) REFERENCES Users(Id)
    );
    PRINT '✅ VehicleStatusTracking table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ VehicleStatusTracking table already exists.';
END

-- SMSNotifications Table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SMSNotifications' AND xtype='U')
BEGIN
    PRINT 'Creating SMSNotifications table...';
    CREATE TABLE SMSNotifications (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        MobileNumber NVARCHAR(15) NOT NULL,
        Message NVARCHAR(500) NOT NULL,
        SMSType NVARCHAR(50) NULL, -- 'OTP', 'VRN', 'PICKUP_ALERT'
        Status NVARCHAR(20) DEFAULT 'PENDING', -- 'SENT', 'FAILED', 'PENDING'
        TransactionId DECIMAL(18,0) NULL,
        CreatedOn DATETIME DEFAULT GETDATE(),
        SentOn DATETIME NULL,

        -- Note: Foreign key constraints will be added after data migration
        -- FOREIGN KEY (TransactionId) REFERENCES ParkingTransactions(Id)
    );
    PRINT '✅ SMSNotifications table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ SMSNotifications table already exists.';
END



-- PlazaPaymentConfiguration Tables
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlazaRazorPayConfiguration' AND xtype='U')
BEGIN
    PRINT 'Creating PlazaRazorPayConfiguration table...';
    CREATE TABLE PlazaRazorPayConfiguration (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        PlazaId INT NOT NULL,
        KeyId NVARCHAR(500) NOT NULL,
        KeySecret NVARCHAR(500) NOT NULL,
        IsActive BIT DEFAULT 1,
        CreatedBy INT NULL,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL,
        
        FOREIGN KEY (PlazaId) REFERENCES Plaza(Id)
    );
    PRINT '✅ PlazaRazorPayConfiguration table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ PlazaRazorPayConfiguration table already exists.';
END

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PlazaPhonePeConfiguration' AND xtype='U')
BEGIN
    PRINT 'Creating PlazaPhonePeConfiguration table...';
    CREATE TABLE PlazaPhonePeConfiguration (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        PlazaId INT NOT NULL,
        MerchantId NVARCHAR(500) NOT NULL,
        SaltKey NVARCHAR(500) NOT NULL,
        IsActive BIT DEFAULT 1,
        CreatedBy INT NULL,
        CreatedOn DATETIME DEFAULT GETDATE(),
        ModifiedBy INT NULL,
        ModifiedOn DATETIME NULL,
        
        FOREIGN KEY (PlazaId) REFERENCES Plaza(Id)
    );
    PRINT '✅ PlazaPhonePeConfiguration table created successfully.';
END
ELSE
BEGIN
    PRINT '⚠️ PlazaPhonePeConfiguration table already exists.';
END

PRINT '';
PRINT '✅ Valet System Database Migration Part 2 Completed Successfully!';
PRINT '';
PRINT 'Next Steps:';
PRINT '1. Run data migration scripts to transfer data from pwvms';
PRINT '2. Create indexes for performance optimization';
PRINT '3. Set up stored procedures';
PRINT '4. Configure module permissions';
PRINT '';
