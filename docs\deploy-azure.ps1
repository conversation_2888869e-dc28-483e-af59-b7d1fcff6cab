# Simplified IIS Deployment Script for PWVMS on Azure
# Run this script as administrator

# Configuration
$siteName = "PWVMS"
$appPoolName = "PWVMS_AppPool"
$sitePath = "C:\inetpub\wwwroot\PWVMS"
$port = 80
$sourceDir = $PSScriptRoot  # Current directory where the script is located

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Please run this script as Administrator!"
    exit
}

# Check if IIS is installed
if ((Get-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole).State -ne "Enabled") {
    Write-Host "Installing IIS..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-ManagementConsole, IIS-HttpErrors, IIS-HttpRedirect, IIS-StaticContent, IIS-DefaultDocument, IIS-ApplicationDevelopment, IIS-NetFxExtensibility45, IIS-ASPNET45, IIS-ISAPIExtensions, IIS-ISAPIFilter, IIS-WebSockets, IIS-ApplicationInit, IIS-HttpCompressionStatic, IIS-HttpCompressionDynamic -All
}

# Check if URL Rewrite module is installed
$rewriteModule = Get-WebGlobalModule | Where-Object { $_.Name -eq "RewriteModule" }
if ($null -eq $rewriteModule) {
    Write-Host "URL Rewrite Module is not installed. Please install it from: https://www.iis.net/downloads/microsoft/url-rewrite" -ForegroundColor Red
    Write-Host "After installing, restart this script." -ForegroundColor Red
    exit
}

# Check if iisnode is installed
$iisnodePath = "C:\Program Files\iisnode\iisnode.dll"
if (-not (Test-Path $iisnodePath)) {
    Write-Host "iisnode is not installed. Please install it from: https://github.com/Azure/iisnode/releases" -ForegroundColor Red
    Write-Host "After installing, restart this script." -ForegroundColor Red
    exit
}

# Create site directory if it doesn't exist
if (-not (Test-Path $sitePath)) {
    Write-Host "Creating site directory at $sitePath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $sitePath -Force
}

# Create deployment directories
$deploymentDirs = @(
    "$sitePath\backend",
    "$sitePath\backend\src",
    "$sitePath\backend\Uploads",
    "$sitePath\backend\Uploads\Companies",
    "$sitePath\backend\Uploads\Plazas",
    "$sitePath\frontend\build"
)

foreach ($dir in $deploymentDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating directory: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

# SKIP FRONTEND BUILD - We'll use the pre-built files
Write-Host "Skipping frontend build - using pre-built files..." -ForegroundColor Yellow

# Copy backend files
Write-Host "Copying backend files..." -ForegroundColor Yellow
# Check for backend/src directory
if (Test-Path "$sourceDir\backend\src") {
    Copy-Item -Path "$sourceDir\backend\src\*" -Destination "$sitePath\backend\src" -Recurse -Force
} 
# Check for src directory directly
elseif (Test-Path "$sourceDir\src") {
    Copy-Item -Path "$sourceDir\src\*" -Destination "$sitePath\backend\src" -Recurse -Force
} else {
    Write-Host "Warning: Backend source directory not found. Looking for individual server files..." -ForegroundColor Yellow
    # Try to find server.js in various locations
    if (Test-Path "$sourceDir\backend\server.js") {
        Copy-Item -Path "$sourceDir\backend\server.js" -Destination "$sitePath\backend\src\server.js" -Force
    } elseif (Test-Path "$sourceDir\server.js") {
        Copy-Item -Path "$sourceDir\server.js" -Destination "$sitePath\backend\src\server.js" -Force
    } else {
        Write-Host "Error: Could not find server.js file. The application may not function correctly." -ForegroundColor Red
    }
}

# Copy package.json and package-lock.json if they exist
Write-Host "Copying package files..." -ForegroundColor Yellow
# Check for package.json
if (Test-Path "$sourceDir\backend\package.json") {
    Copy-Item -Path "$sourceDir\backend\package.json" -Destination "$sitePath\backend\package.json" -Force
} elseif (Test-Path "$sourceDir\package.json") {
    Copy-Item -Path "$sourceDir\package.json" -Destination "$sitePath\backend\package.json" -Force
} else {
    Write-Host "Warning: package.json not found. Will attempt to continue without it." -ForegroundColor Yellow
}

# Check for package-lock.json
if (Test-Path "$sourceDir\backend\package-lock.json") {
    Copy-Item -Path "$sourceDir\backend\package-lock.json" -Destination "$sitePath\backend\package-lock.json" -Force
} elseif (Test-Path "$sourceDir\package-lock.json") {
    Copy-Item -Path "$sourceDir\package-lock.json" -Destination "$sitePath\backend\package-lock.json" -Force
} else {
    Write-Host "Note: package-lock.json not found. This is not critical." -ForegroundColor Yellow
}

# Copy node_modules if they exist (to avoid npm install)
if (Test-Path "$sourceDir\backend\node_modules") {
    Write-Host "Copying node_modules (this may take a while)..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\node_modules" -Destination "$sitePath\backend\node_modules" -Recurse -Force
} elseif (Test-Path "$sourceDir\node_modules") {
    Write-Host "Copying node_modules from root directory (this may take a while)..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\node_modules" -Destination "$sitePath\backend\node_modules" -Recurse -Force
} else {
    Write-Host "node_modules not found. Will need to run npm install." -ForegroundColor Yellow
}

# Copy .env file or create from template
if (Test-Path "$sourceDir\backend\.env.production") {
    Write-Host "Copying production environment file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env.production" -Destination "$sitePath\backend\.env" -Force
} elseif (Test-Path "$sourceDir\backend\.env") {
    Write-Host "Copying development environment file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env" -Destination "$sitePath\backend\.env" -Force
} elseif (Test-Path "$sourceDir\backend\.env.template") {
    Write-Host "Copying environment template file..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\backend\.env.template" -Destination "$sitePath\backend\.env" -Force
    Write-Host "Warning: Using template environment file. Please update with production values." -ForegroundColor Yellow
} elseif (Test-Path "$sourceDir\.env") {
    Write-Host "Copying environment file from root directory..." -ForegroundColor Yellow
    Copy-Item -Path "$sourceDir\.env" -Destination "$sitePath\backend\.env" -Force
} else {
    Write-Host "No environment file found. Please create one manually." -ForegroundColor Red
}

# Copy frontend build files
Write-Host "Copying frontend build files..." -ForegroundColor Yellow
# Check for frontend/build directory
if (Test-Path "$sourceDir\frontend\build") {
    Copy-Item -Path "$sourceDir\frontend\build\*" -Destination "$sitePath\frontend\build" -Recurse -Force
} 
# Check for build directory directly
elseif (Test-Path "$sourceDir\build") {
    Copy-Item -Path "$sourceDir\build\*" -Destination "$sitePath\frontend\build" -Recurse -Force
} else {
    Write-Host "Warning: Frontend build directory not found." -ForegroundColor Yellow
    Write-Host "The application may not function correctly without frontend files." -ForegroundColor Red
}

# Copy web.config
Write-Host "Copying web.config..." -ForegroundColor Yellow
if (Test-Path "$sourceDir\web.config") {
    Copy-Item -Path "$sourceDir\web.config" -Destination "$sitePath\web.config" -Force
} else {
    Write-Host "Error: web.config not found. The application will not function correctly in IIS." -ForegroundColor Red
}

# Install backend dependencies if node_modules wasn't copied
if (-not (Test-Path "$sitePath\backend\node_modules")) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    Set-Location -Path "$sitePath\backend"
    
    # Check if package.json exists before running npm install
    if (Test-Path "package.json") {
        Write-Host "Installing production dependencies..." -ForegroundColor Yellow
        npm install --production
    } else {
        Write-Host "Error: package.json not found in $sitePath\backend" -ForegroundColor Red
        Write-Host "Attempting to continue deployment, but the application may not function correctly." -ForegroundColor Red
    }
    
    Set-Location -Path $sourceDir
} else {
    Write-Host "Skipping npm install as node_modules was copied directly." -ForegroundColor Green
}

# Create application pool if it doesn't exist
if (-not (Get-IISAppPool -Name $appPoolName -ErrorAction SilentlyContinue)) {
    Write-Host "Creating application pool $appPoolName" -ForegroundColor Yellow
    New-WebAppPool -Name $appPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
}

# Create website if it doesn't exist
if (-not (Get-Website -Name $siteName -ErrorAction SilentlyContinue)) {
    Write-Host "Creating website $siteName" -ForegroundColor Yellow
    New-Website -Name $siteName -PhysicalPath $sitePath -ApplicationPool $appPoolName -Port $port -Force
} else {
    Write-Host "Updating website $siteName" -ForegroundColor Yellow
    Set-ItemProperty -Path "IIS:\Sites\$siteName" -Name "physicalPath" -Value $sitePath
    Set-ItemProperty -Path "IIS:\Sites\$siteName" -Name "applicationPool" -Value $appPoolName
}

# Set permissions
Write-Host "Setting folder permissions..." -ForegroundColor Yellow
$acl = Get-Acl $sitePath
$accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$appPoolName", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
$acl.SetAccessRule($accessRule)
Set-Acl $sitePath $acl

# Create uploads directories if they don't exist and set permissions
$uploadDirs = @(
    "$sitePath\backend\Uploads",
    "$sitePath\backend\Uploads\Companies",
    "$sitePath\backend\Uploads\Plazas"
)

foreach ($dir in $uploadDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Creating upload directory: $dir" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
    
    # Set permissions for upload directories
    $acl = Get-Acl $dir
    $acl.SetAccessRule($accessRule)
    Set-Acl $dir $acl
}

Write-Host "Deployment completed successfully!" -ForegroundColor Green
Write-Host "Your application should be accessible at http://localhost:$port" -ForegroundColor Green
Write-Host "Note: You may need to update the .env file in $sitePath\backend with your production database credentials." -ForegroundColor Yellow