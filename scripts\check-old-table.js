require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function checkOldTable() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    // Get data from the old table
    const result = await sql.query('SELECT TOP 5 * FROM tblParkwiz_Parking_Data_OLD');
    console.log('Old table data sample:');
    console.log(JSON.stringify(result.recordset, null, 2));
    
    const countResult = await sql.query('SELECT COUNT(*) as TotalRecords FROM tblParkwiz_Parking_Data_OLD');
    console.log(`\nTotal records in tblParkwiz_Parking_Data_OLD: ${countResult.recordset[0].TotalRecords}`);
    
    const dateRangeResult = await sql.query(`
      SELECT 
        MIN(ExitDateTime) as EarliestDate, 
        MAX(ExitDateTime) as LatestDate 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime IS NOT NULL
    `);
    
    console.log('\nDate range:');
    if (dateRangeResult.recordset[0].EarliestDate) {
      console.log(`- Earliest date: ${dateRangeResult.recordset[0].EarliestDate.toISOString()}`);
      console.log(`- Latest date: ${dateRangeResult.recordset[0].LatestDate.toISOString()}`);
    } else {
      console.log('No records with ExitDateTime found');
    }

    // Get recent data (June 2025)
    const juneResult = await sql.query(`
      SELECT 
        COUNT(*) as Count, 
        SUM(ParkingFee + iTotalGSTFee) as Revenue 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime >= '2025-06-01' 
        AND ExitDateTime <= '2025-06-30'
    `);
    
    console.log('\nJune 2025 data:');
    console.log('- Transactions:', juneResult.recordset[0].Count);
    console.log('- Revenue:', juneResult.recordset[0].Revenue);

    // Get payment methods
    const paymentMethodsResult = await sql.query(`
      SELECT 
        PaymentMode, 
        COUNT(*) as Count, 
        SUM(ParkingFee + iTotalGSTFee) as Revenue 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime >= '2025-06-01' 
        AND ExitDateTime <= '2025-06-30' 
      GROUP BY PaymentMode 
      ORDER BY Revenue DESC
    `);
    
    console.log('\nPayment methods for June 2025:');
    console.log(JSON.stringify(paymentMethodsResult.recordset, null, 2));

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

checkOldTable();