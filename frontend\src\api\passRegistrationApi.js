import api from '../services/api';

export const passRegistrationApi = {
  // Get all pass registrations with pagination and filtering
  getAll: async (params = {}) => {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.search) queryParams.append('search', params.search);
    if (params.plazaCode) queryParams.append('plazaCode', params.plazaCode);
    if (params.passType) queryParams.append('passType', params.passType);

    const response = await api.get(`/pass-registrations?${queryParams.toString()}`);
    console.log('API Response:', response.data); // Debug log
    
    // Return the data in a consistent format
    // The API returns { success, message, data: { data: [...], pagination: {...} } }
    return response.data;
  },

  // Get pass registration by ID
  getById: async (id) => {
    const response = await api.get(`/pass-registrations/${id}`);
    return response.data.data;
  },

  // Search pass registrations by vehicle number
  searchByVehicle: async (vehicleNo) => {
    const response = await api.get(`/pass-registrations/search/vehicle/${vehicleNo}`);
    return response.data.data;
  },

  // Create new pass registration
  create: async (data) => {
    const response = await api.post('/pass-registrations', data);
    return response.data;
  },

  // Update pass registration
  update: async (id, data) => {
    const response = await api.put(`/pass-registrations/${id}`, data);
    return response.data;
  },

  // Delete pass registration
  delete: async (id) => {
    const response = await api.delete(`/pass-registrations/${id}`);
    return response.data;
  }
};
