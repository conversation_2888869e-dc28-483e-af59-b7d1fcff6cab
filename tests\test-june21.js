require('dotenv').config({path: './backend/.env'});
const sql = require('mssql');

async function testJune21Data() {
  try {
    await sql.connect({
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      server: process.env.DB_SERVER,
      database: process.env.DB_NAME,
      options: {
        encrypt: true,
        trustServerCertificate: true
      }
    });

    const june21 = new Date('2025-06-21T00:00:00.000Z');
    const startDate = new Date(june21);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(june21);
    endDate.setHours(23, 59, 59, 999);

    console.log('Testing data for June 21, 2025:');
    console.log('- Start date:', startDate.toISOString());
    console.log('- End date:', endDate.toISOString());

    const request = new sql.Request();
    request.input('startDate', sql.DateTime, startDate);
    request.input('endDate', sql.DateTime, endDate);
    
    const summaryResult = await request.query(`
      SELECT 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as TotalRevenue, 
        COUNT(*) as TransactionCount 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate
    `);

    console.log('\nDashboard Summary for June 21, 2025:');
    console.log('- Total Revenue:', summaryResult.recordset[0].TotalRevenue);
    console.log('- Transaction Count:', summaryResult.recordset[0].TransactionCount);

    const paymentMethodResult = await request.query(`
      SELECT 
        PaymentMode, 
        COUNT(*) as TransactionCount, 
        ISNULL(SUM(ISNULL(ParkingFee, 0) + ISNULL(iTotalGSTFee, 0)), 0) as Revenue 
      FROM tblParkwiz_Parking_Data_OLD 
      WHERE ExitDateTime BETWEEN @startDate AND @endDate 
      GROUP BY PaymentMode 
      ORDER BY Revenue DESC
    `);

    console.log('\nRevenue by Payment Method for June 21, 2025:');
    paymentMethodResult.recordset.forEach(method => {
      console.log(`- ${method.PaymentMode}: ${method.TransactionCount} transactions, ₹${method.Revenue.toFixed(2)}`);
    });

    await sql.close();
  } catch (err) {
    console.error('Error:', err);
  }
}

testJune21Data();