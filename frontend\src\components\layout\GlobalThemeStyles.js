// frontend/src/components/layout/GlobalThemeStyles.js
import React from 'react';
import { useTheme } from '../../contexts/themeContext';

/**
 * GlobalThemeStyles component applies global CSS styles based on the current theme
 * This helps fix any components that might not be properly styled with CSS variables
 */
const GlobalThemeStyles = () => {
  const { theme } = useTheme();

  // These styles will be injected into the DOM
  const globalStyles = `
    /* Fix for inputs and form elements */
    input, select, textarea {
      background-color: var(--color-bg-input);
      color: var(--color-text-primary);
      border-color: var(--color-border);
    }
    
    input::placeholder, textarea::placeholder {
      color: var(--color-text-placeholder);
    }
    
    /* Fix for buttons */
    button:not([class*="bg-"]):not([style*="background"]) {
      background-color: var(--color-bg-button);
      color: ${theme === 'saffron' ? 'var(--color-text-inverted)' : 'var(--color-text-primary)'};
    }
    
    /* Fix for tables */
    table {
      color: var(--color-text-primary);
      border-color: var(--color-border);
    }
    
    th {
      background-color: var(--color-bg-table-header);
    }
    
    tr:nth-child(even) {
      background-color: var(--color-bg-table-row-even);
    }
    
    tr:nth-child(odd) {
      background-color: var(--color-bg-table-row-odd);
    }
    
    /* Fix for links */
    a:not([class*="text-"]):not([style*="color"]) {
      color: var(--color-text-link);
    }
    
    a:hover:not([class*="text-"]):not([style*="color"]) {
      color: var(--color-text-link-hover);
    }
    
    /* Fix for modals and dialogs */
    .modal, .dialog, [role="dialog"] {
      background-color: var(--color-bg-card);
      color: var(--color-text-primary);
      border-color: var(--color-border);
    }
    
    /* Fix for cards and panels */
    .card, .panel, [class*="card"], [class*="panel"] {
      background-color: var(--color-bg-card);
      color: var(--color-text-primary);
      border-color: var(--color-border);
    }
    
    /* Fix for dropdowns and menus */
    .dropdown, .menu, [role="menu"] {
      background-color: var(--color-bg-dropdown);
      color: var(--color-text-primary);
      border-color: var(--color-border);
    }
    
    /* Fix for tooltips */
    .tooltip, [role="tooltip"] {
      background-color: var(--color-bg-dropdown);
      color: var(--color-text-primary);
      border-color: var(--color-border);
    }

    /* Saffron theme specific styles */
    ${theme === 'saffron' ? `
      .sidebar {
        background-color: var(--color-bg-sidebar);
        color: var(--color-text-inverted);
      }
      
      .sidebar a, .sidebar button {
        color: var(--color-text-inverted);
      }
      
      .sidebar a:hover, .sidebar button:hover {
        background-color: var(--color-accent-hover);
      }
      
      .header {
        border-bottom: 2px solid var(--color-accent);
      }
      
      .primary-button, .btn-primary {
        background-color: var(--color-accent);
        color: var(--color-text-inverted);
        border-color: var(--color-accent-dark);
      }
      
      .primary-button:hover, .btn-primary:hover {
        background-color: var(--color-accent-hover);
      }
    ` : ''}
  `;

  return <style>{globalStyles}</style>;
};

export default GlobalThemeStyles;
