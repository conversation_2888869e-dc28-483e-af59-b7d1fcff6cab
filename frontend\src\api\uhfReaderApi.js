import api from '../services/api';

export const uhfReaderApi = {
  // Get all UHF reader configurations
  getAll: async () => {
    const response = await api.get('/uhf-readers');
    return response.data.data;
  },

  // Get UHF reader configuration by ID
  getById: async (id) => {
    const response = await api.get(`/uhf-readers/${id}`);
    return response.data.data;
  },

  // Get UHF reader configurations by lane ID
  getByLane: async (laneId) => {
    const response = await api.get(`/uhf-readers/lane/${laneId}`);
    return response.data.data;
  },

  // Create new UHF reader configuration
  create: async (data) => {
    const response = await api.post('/uhf-readers', data);
    return response.data;
  },

  // Update UHF reader configuration
  update: async (id, data) => {
    const response = await api.put(`/uhf-readers/${id}`, data);
    return response.data;
  },

  // Delete UHF reader configuration
  delete: async (id) => {
    const response = await api.delete(`/uhf-readers/${id}`);
    return response.data;
  },

  // Toggle UHF reader enable/disable status
  toggleStatus: async (id, enabled) => {
    const response = await api.patch(`/uhf-readers/${id}/toggle`, { enabled });
    return response.data;
  }
};
