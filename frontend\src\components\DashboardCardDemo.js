import React from 'react';
import { DashboardCard } from './DashboardCard';
import { CreditCard, Activity, Car, Clock } from 'lucide-react';

/**
 * Demo component to test dashboard cards with various data sizes
 */
export const DashboardCardDemo = () => {
  const testData = [
    {
      title: "Small Revenue",
      value: "₹1,234",
      trend: 5.2,
      icon: CreditCard,
      color: "bg-blue-500"
    },
    {
      title: "Medium Revenue",
      value: "₹12.5K",
      trend: -2.1,
      icon: CreditCard,
      color: "bg-blue-500"
    },
    {
      title: "Large Revenue",
      value: "₹1.2M",
      trend: 15.7,
      icon: CreditCard,
      color: "bg-blue-500"
    },
    {
      title: "Very Large Revenue",
      value: "₹123,456,789",
      trend: 8.9,
      icon: CreditCard,
      color: "bg-blue-500"
    },
    {
      title: "Transactions",
      value: "45,678",
      trend: 12.3,
      icon: Activity,
      color: "bg-green-500"
    },
    {
      title: "Vehicles",
      value: "8,901",
      trend: -1.5,
      icon: Car,
      color: "bg-purple-500"
    },
    {
      title: "Avg Duration",
      value: "2h 45m",
      trend: 3.2,
      icon: Clock,
      color: "bg-yellow-500"
    },
    {
      title: "Long Title Example",
      value: "₹987,654,321",
      trend: 0,
      icon: CreditCard,
      color: "bg-red-500"
    }
  ];

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold mb-6">Dashboard Card Responsiveness Test</h1>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {testData.map((card, index) => (
          <DashboardCard
            key={index}
            title={card.title}
            value={card.value}
            trend={card.trend}
            icon={card.icon}
            color={card.color}
          />
        ))}
      </div>
      
      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">Test Instructions:</h2>
        <ul className="list-disc list-inside space-y-2 text-gray-700">
          <li>Resize your browser window to test responsiveness</li>
          <li>Check that icons stay in place with large revenue numbers</li>
          <li>Verify text sizes adjust appropriately</li>
          <li>Ensure cards maintain structure on mobile devices</li>
          <li>Test with different screen sizes (mobile, tablet, desktop)</li>
        </ul>
      </div>
    </div>
  );
};