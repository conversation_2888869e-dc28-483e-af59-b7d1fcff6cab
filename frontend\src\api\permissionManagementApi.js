import api from '../services/api';

/**
 * Permission Management API
 * Handles all API calls related to permission management
 */
const permissionManagementApi = {
  /**
   * Get complete modules tree with submodules and permissions
   * GET /permission-management/modules-tree
   */
  getModulesTree: async () => {
    try {
      console.log('Fetching modules tree for permission management');
      const response = await api.get('/permission-management/modules-tree');
      console.log('Modules tree response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in getModulesTree:', error);
      throw error;
    }
  },

  /**
   * Get all roles
   * GET /permission-management/roles
   */
  getRoles: async () => {
    try {
      console.log('Fetching all roles');
      const response = await api.get('/permission-management/roles');
      console.log('Roles response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in getRoles:', error);
      throw error;
    }
  },

  /**
   * Get permissions for a specific role
   * GET /permission-management/roles/:roleId/permissions
   */
  getRolePermissions: async (roleId) => {
    try {
      console.log('Fetching permissions for role:', roleId);
      const response = await api.get(`/permission-management/roles/${roleId}/permissions`);
      console.log('Role permissions response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in getRolePermissions:', error);
      throw error;
    }
  },

  /**
   * Update permissions for a specific role
   * PUT /permission-management/roles/:roleId/permissions
   */
  updateRolePermissions: async (roleId, permissions) => {
    try {
      console.log('Updating permissions for role:', roleId, 'Permissions:', permissions);
      const response = await api.put(`/permission-management/roles/${roleId}/permissions`, {
        permissions
      });
      console.log('Update role permissions response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in updateRolePermissions:', error);
      throw error;
    }
  },

  /**
   * Get permission matrix for all roles
   * GET /permission-management/matrix
   */
  getPermissionMatrix: async () => {
    try {
      console.log('Fetching permission matrix');
      const response = await api.get('/permission-management/matrix');
      console.log('Permission matrix response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error in getPermissionMatrix:', error);
      throw error;
    }
  },

  /**
   * Bulk update permissions for a role
   * Helper function to update multiple permissions at once
   */
  bulkUpdateRolePermissions: async (roleId, updates) => {
    try {
      console.log('Bulk updating permissions for role:', roleId, 'Updates:', updates);
      
      // Transform updates into the format expected by the API
      const permissions = updates.map(update => ({
        subModulePermissionId: update.subModulePermissionId,
        isActive: update.isActive
      }));

      return await permissionManagementApi.updateRolePermissions(roleId, permissions);
    } catch (error) {
      console.error('Error in bulkUpdateRolePermissions:', error);
      throw error;
    }
  },

  /**
   * Grant all permissions for a role
   * Helper function to grant all permissions to a role
   */
  grantAllPermissions: async (roleId, subModulePermissionIds) => {
    try {
      console.log('Granting all permissions for role:', roleId);
      
      const permissions = subModulePermissionIds.map(id => ({
        subModulePermissionId: id,
        isActive: true
      }));

      return await permissionManagementApi.updateRolePermissions(roleId, permissions);
    } catch (error) {
      console.error('Error in grantAllPermissions:', error);
      throw error;
    }
  },

  /**
   * Revoke all permissions for a role
   * Helper function to revoke all permissions from a role
   */
  revokeAllPermissions: async (roleId, subModulePermissionIds) => {
    try {
      console.log('Revoking all permissions for role:', roleId);
      
      const permissions = subModulePermissionIds.map(id => ({
        subModulePermissionId: id,
        isActive: false
      }));

      return await permissionManagementApi.updateRolePermissions(roleId, permissions);
    } catch (error) {
      console.error('Error in revokeAllPermissions:', error);
      throw error;
    }
  },

  /**
   * Copy permissions from one role to another
   * Helper function to copy permissions between roles
   */
  copyRolePermissions: async (fromRoleId, toRoleId) => {
    try {
      console.log('Copying permissions from role:', fromRoleId, 'to role:', toRoleId);
      
      // Get permissions from source role
      const sourcePermissions = await permissionManagementApi.getRolePermissions(fromRoleId);
      
      // Transform to the format needed for update
      const permissions = sourcePermissions.data.map(perm => ({
        subModulePermissionId: perm.SubModulePermissionId,
        isActive: true
      }));

      return await permissionManagementApi.updateRolePermissions(toRoleId, permissions);
    } catch (error) {
      console.error('Error in copyRolePermissions:', error);
      throw error;
    }
  },

  /**
   * Toggle a specific permission for a role
   * Helper function to toggle a single permission
   */
  togglePermission: async (roleId, subModulePermissionId, isActive) => {
    try {
      console.log('Toggling permission for role:', roleId, 'Permission:', subModulePermissionId, 'Active:', isActive);
      
      const permissions = [{
        subModulePermissionId: subModulePermissionId,
        isActive: isActive
      }];

      return await permissionManagementApi.updateRolePermissions(roleId, permissions);
    } catch (error) {
      console.error('Error in togglePermission:', error);
      throw error;
    }
  },

  /**
   * Get permissions summary for all roles
   * Helper function to get a summary of permissions across all roles
   */
  getPermissionsSummary: async () => {
    try {
      console.log('Fetching permissions summary');
      
      const [modulesTree, roles, matrix] = await Promise.all([
        permissionManagementApi.getModulesTree(),
        permissionManagementApi.getRoles(),
        permissionManagementApi.getPermissionMatrix()
      ]);

      return {
        modules: modulesTree.data,
        roles: roles.data,
        matrix: matrix.data
      };
    } catch (error) {
      console.error('Error in getPermissionsSummary:', error);
      throw error;
    }
  }
};

export default permissionManagementApi;
