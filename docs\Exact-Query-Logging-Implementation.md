# Dashboard Controller - Exact Query Logging Implementation

## 🎯 Implementation Summary

I have successfully implemented comprehensive exact query logging that shows the complete SQL queries with all parameters substituted. This will help you see exactly what queries are being executed against the database and identify where data mismatches might be occurring.

## 🔧 Key Features Implemented

### 1. **Exact Query Generator Helper Function**
```javascript
function generateExactQuery(queryTemplate, queryParams) {
  let exactQuery = queryTemplate;
  
  Object.keys(queryParams).forEach(key => {
    const value = queryParams[key];
    const paramPlaceholder = `@${key}`;
    
    // Handle different data types appropriately
    let replacementValue;
    if (value === null || value === undefined) {
      replacementValue = 'NULL';
    } else if (typeof value === 'string') {
      // Escape single quotes in strings and wrap in quotes
      replacementValue = `'${value.replace(/'/g, "''")}'`;
    } else if (typeof value === 'boolean') {
      replacementValue = value ? '1' : '0';
    } else if (typeof value === 'number') {
      replacementValue = value.toString();
    } else {
      // For other types, convert to string and wrap in quotes
      replacementValue = `'${String(value).replace(/'/g, "''")}'`;
    }
    
    // Replace all occurrences of the parameter placeholder
    exactQuery = exactQuery.replace(new RegExp(paramPlaceholder, 'g'), replacementValue);
  });
  
  return exactQuery;
}
```

### 2. **Enhanced Logging for All Three Key Endpoints**

#### Dashboard Summary Query
- ✅ **SQL Template Logging**: Shows the parameterized query
- ✅ **Parameter Logging**: Shows all @parameter values and types
- ✅ **Exact Query**: Complete query with parameters substituted
- ✅ **SSMS Ready**: Copy-paste ready query for SQL Server Management Studio
- ✅ **Record Count Validation**: Warns if no results or multiple rows returned
- ✅ **Execution Timing**: Performance monitoring

#### Revenue by Payment Method Query
- ✅ **Enhanced SQL with Debug Fields**: Additional columns for component analysis
- ✅ **Complete Parameter Substitution**: All filters and date ranges
- ✅ **Exact Query Generation**: Ready-to-execute SQL
- ✅ **Performance Timing**: Query execution monitoring
- ✅ **Result Analysis**: Detailed breakdown of each payment method

#### Daily Revenue Data Query
- ✅ **Daily Aggregation Query**: Complete with date casting and grouping
- ✅ **Parameter Substitution**: All role-based and entity filters
- ✅ **Component Breakdown**: Individual parking fee and GST components
- ✅ **Exact Query Output**: Complete executable SQL
- ✅ **Data Transformation Tracking**: Before/after formatting

## 📋 Log Output Examples

### Dashboard Summary Query Log:
```
🎯 Executing Dashboard Summary Query:
📝 SQL Query Template: 
        SELECT 
          -- Total Revenue (only from exits with parking fee)
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          
          -- Four Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          
          -- Two Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          
          -- Total Counts
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN @startDate AND @endDate THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode
        WHERE (t.EntryDateTime BETWEEN @startDate AND @endDate OR t.ExitDateTime BETWEEN @startDate AND @endDate)
        AND p.CompanyId = @companyId
        
        
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)

📝 Final Query Parameters: { startDate: '2024-01-15 06:00:00', endDate: '2024-01-16 06:00:00', companyId: '11' }

🔍 EXACT DASHBOARD SUMMARY SQL QUERY WITH VALUES:
=================================================
        SELECT 
          -- Total Revenue (only from exits with parking fee)
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue,
          
          -- Four Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount,
          
          -- Two Wheeler Data
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount,
          ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount,
          
          -- Total Counts
          ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TotalEntryCount,
          ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TotalExitCount
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode
        WHERE (t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' OR t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00')
        AND p.CompanyId = '11'
        
        
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
=================================================

📋 COPY-PASTE READY FOR SSMS:
------------------------------
SELECT ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TotalRevenue, ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS FourWheelerRevenue, ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS FourWheelerEntryCount, ISNULL(SUM(CASE WHEN t.VehicleType <> 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS FourWheelerExitCount, ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN ISNULL(t.ParkingFee, 0) ELSE 0 END), 0) AS TwoWheelerRevenue, ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TwoWheelerEntryCount, ISNULL(SUM(CASE WHEN t.VehicleType = 'Two Wheeler' AND t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TwoWheelerExitCount, ISNULL(SUM(CASE WHEN t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TotalEntryCount, ISNULL(SUM(CASE WHEN t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' THEN 1 ELSE 0 END), 0) AS TotalExitCount FROM tblParkwiz_Parking_Data t WITH (NOLOCK) INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode WHERE (t.EntryDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' OR t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00') AND p.CompanyId = '11' OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
------------------------------

📋 Dashboard Summary Query Parameters:
  @startDate = 2024-01-15 06:00:00 (string)
  @endDate = 2024-01-16 06:00:00 (string)
  @companyId = 11 (string)

⏱️ Dashboard Summary Query execution started at: 2024-01-15T10:30:00.000Z
⏱️ Dashboard Summary Query execution completed at: 2024-01-15T10:30:00.245Z
⏱️ Dashboard Summary Query execution time: 245 ms

✅ Dashboard Summary Query Results:
📊 Records Returned: 1
📊 Raw Database Results: { TotalRevenue: 15250.75, FourWheelerRevenue: 12200.50, TwoWheelerRevenue: 3050.25, FourWheelerEntryCount: 185, FourWheelerExitCount: 180, TwoWheelerEntryCount: 95, TwoWheelerExitCount: 90, TotalEntryCount: 280, TotalExitCount: 270 }
```

### Payment Method Query Log:
```
🎯 Executing Payment Method Query:
📝 SQL Query Template: 
        SELECT
          ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
          -- Revenue Calculation: ParkingFee + GST Fee
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          -- Individual components for debugging
          ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalParkingFee,
          ISNULL(SUM(ISNULL(t.iTotalGSTFee, 0)), 0) as totalGSTFee,
          COUNT(*) as transactionCount,
          -- Sample values for debugging
          AVG(ISNULL(t.ParkingFee, 0)) as avgParkingFee,
          AVG(ISNULL(t.iTotalGSTFee, 0)) as avgGSTFee,
          MIN(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as minRevenue,
          MAX(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as maxRevenue
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode
        WHERE t.ExitDateTime BETWEEN @startDate AND @endDate
        AND p.CompanyId = @companyId
        
        GROUP BY t.PaymentMode
        ORDER BY totalRevenue DESC
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)

🔍 EXACT PAYMENT METHOD SQL QUERY WITH VALUES:
===============================================
        SELECT
          ISNULL(t.PaymentMode, 'Unknown') as paymentMode,
          -- Revenue Calculation: ParkingFee + GST Fee
          ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue,
          -- Individual components for debugging
          ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalParkingFee,
          ISNULL(SUM(ISNULL(t.iTotalGSTFee, 0)), 0) as totalGSTFee,
          COUNT(*) as transactionCount,
          -- Sample values for debugging
          AVG(ISNULL(t.ParkingFee, 0)) as avgParkingFee,
          AVG(ISNULL(t.iTotalGSTFee, 0)) as avgGSTFee,
          MIN(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as minRevenue,
          MAX(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as maxRevenue
        FROM tblParkwiz_Parking_Data t WITH (NOLOCK)
        INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode
        WHERE t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00'
        AND p.CompanyId = '11'
        
        GROUP BY t.PaymentMode
        ORDER BY totalRevenue DESC
        OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
===============================================

📋 COPY-PASTE READY FOR SSMS:
------------------------------
SELECT ISNULL(t.PaymentMode, 'Unknown') as paymentMode, ISNULL(SUM(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)), 0) as totalRevenue, ISNULL(SUM(ISNULL(t.ParkingFee, 0)), 0) as totalParkingFee, ISNULL(SUM(ISNULL(t.iTotalGSTFee, 0)), 0) as totalGSTFee, COUNT(*) as transactionCount, AVG(ISNULL(t.ParkingFee, 0)) as avgParkingFee, AVG(ISNULL(t.iTotalGSTFee, 0)) as avgGSTFee, MIN(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as minRevenue, MAX(ISNULL(t.ParkingFee, 0) + ISNULL(t.iTotalGSTFee, 0)) as maxRevenue FROM tblParkwiz_Parking_Data t WITH (NOLOCK) INNER JOIN Plaza p WITH (NOLOCK) ON t.PlazaCode = p.PlazaCode WHERE t.ExitDateTime BETWEEN '2024-01-15 06:00:00' AND '2024-01-16 06:00:00' AND p.CompanyId = '11' GROUP BY t.PaymentMode ORDER BY totalRevenue DESC OPTION (OPTIMIZE FOR UNKNOWN, MAXDOP 2)
------------------------------
```

## 🔍 What This Logging Will Help You Identify

### 1. **Exact Query Execution**
- See the complete SQL query with all parameters substituted
- Copy and paste the query directly into SSMS for testing
- Verify that the correct date ranges and filters are being applied

### 2. **Parameter Validation**
- Confirm that all parameters are being passed correctly
- Check data types of parameters (string, number, etc.)
- Identify any NULL or undefined parameter issues

### 3. **Data Mismatch Detection**
- Compare the exact query results in SSMS vs API response
- Identify if the issue is in the query or in the data processing
- Verify that the correct tables and joins are being used

### 4. **Filter Verification**
- Confirm that role-based filters are applied correctly
- Verify company and plaza filters are working as expected
- Check that date range calculations are accurate

### 5. **Performance Analysis**
- Monitor query execution times
- Identify slow-performing queries
- Track record counts returned by each query

## 🛠️ How to Use This for Debugging

### Step 1: Run the API Endpoint
```bash
# Test dashboard summary
curl "http://localhost:3000/api/dashboard/summary?dateRange=today&companyId=11"

# Test payment method breakdown
curl "http://localhost:3000/api/dashboard/revenue-by-payment?dateRange=today&companyId=11"

# Test daily revenue data
curl "http://localhost:3000/api/dashboard/daily-revenue?dateRange=week&companyId=11"
```

### Step 2: Copy the Exact Query from Logs
Look for the "📋 COPY-PASTE READY FOR SSMS:" section in the logs and copy the single-line query.

### Step 3: Execute in SQL Server Management Studio
1. Open SSMS and connect to your database
2. Paste the exact query from the logs
3. Execute the query and compare results

### Step 4: Compare Results
- **API Response**: Check the JSON response from the API
- **SSMS Results**: Check the raw database results
- **Identify Differences**: Look for discrepancies in decimal values, counts, or calculations

### Step 5: Analyze Discrepancies
If you find differences:
- **Decimal Precision**: Check if decimals are being rounded or truncated
- **Data Types**: Verify that data types match between database and API
- **Calculations**: Ensure that SUM, AVG calculations are consistent
- **NULL Handling**: Check how NULL values are being processed

## 📈 Expected Benefits

With this exact query logging, you will be able to:

1. **Identify Query Issues**: See exactly what SQL is being executed
2. **Validate Parameters**: Confirm all parameters are correct
3. **Compare Results**: Direct comparison between SSMS and API results
4. **Debug Calculations**: Verify revenue calculations and aggregations
5. **Performance Monitoring**: Track query execution times and optimization
6. **Data Integrity**: Ensure data consistency between database and API

The logging provides complete transparency into the SQL execution process and will help you quickly identify where any data mismatches are occurring.

---

**Implementation Completed**: January 2024  
**Coverage**: Dashboard Summary, Revenue by Payment Method, Daily Revenue Data  
**Impact**: Complete SQL query transparency and debugging capability