// backend/src/config/emailConfig.js
const nodemailer = require('nodemailer');

const emailConfig = {
  // SMTP Configuration
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: process.env.SMTP_SECURE === 'true' || false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER || '<EMAIL>',
      pass: process.env.SMTP_PASS || 'your-app-password'
    },
    tls: {
      rejectUnauthorized: false
    }
  },

  // Default sender information
  from: {
    name: process.env.EMAIL_FROM_NAME || 'ParkwizOps System',
    address: process.env.EMAIL_FROM_ADDRESS || '<EMAIL>'
  },

  // Email templates configuration
  templates: {
    baseUrl: process.env.APP_BASE_URL || 'http://localhost:3000',
    logoUrl: process.env.EMAIL_LOGO_URL || 'https://your-domain.com/logo.png',
    supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
    companyName: 'Parkwiz'
  },

  // Retry configuration
  retry: {
    attempts: 3,
    delay: 1000, // milliseconds
    backoff: 2 // exponential backoff multiplier
  }
};

// Create transporter
let transporter = null;

const createTransporter = () => {
  if (!transporter) {
    transporter = nodemailer.createTransport(emailConfig.smtp);
  }
  return transporter;
};

// Test email connection
const testConnection = async () => {
  try {
    const testTransporter = createTransporter();
    await testTransporter.verify();
    console.log('✅ Email server connection verified');
    return true;
  } catch (error) {
    console.error('❌ Email server connection failed:', error.message);
    return false;
  }
};

// Email template helpers
const getEmailTemplate = (type) => {
  const templates = {
    welcome: {
      subject: 'Welcome to ParkwizOps - Your Account is Ready',
      template: 'welcome'
    },
    activity: {
      subject: 'Activity Notification - {action} performed',
      template: 'activity'
    },
    deletion: {
      subject: '⚠️ Deletion Alert - {entity} removed',
      template: 'deletion'
    },
    unauthorized: {
      subject: '🚨 Unauthorized Access Attempt',
      template: 'unauthorized'
    }
  };

  return templates[type] || templates.activity;
};

// Environment validation
const validateEmailConfig = () => {
  const required = ['SMTP_HOST', 'SMTP_USER', 'SMTP_PASS'];
  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    console.warn(`⚠️ Missing email configuration: ${missing.join(', ')}`);
    console.warn('Email notifications will use default values');
  }

  return missing.length === 0;
};

module.exports = {
  emailConfig,
  createTransporter,
  testConnection,
  getEmailTemplate,
  validateEmailConfig
};
