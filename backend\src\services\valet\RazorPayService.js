const crypto = require('crypto');
const axios = require('axios');
const db = require('../../config/database');

/**
 * RazorPay Service for Valet System
 * Handles RazorPay payment gateway integration, order creation, payment verification, and webhooks
 */

class RazorPayService {
  constructor() {
    this.baseURL = process.env.RAZORPAY_BASE_URL || 'https://api.razorpay.com/v1';
    this.keyId = process.env.RAZORPAY_KEY_ID;
    this.keySecret = process.env.RAZORPAY_KEY_SECRET;
    this.webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    
    if (!this.keyId || !this.keySecret) {
      console.warn('RazorPay credentials not configured. Payment processing will be disabled.');
    }
  }

  /**
   * Create RazorPay order
   */
  async createOrder(orderData) {
    try {
      if (!this.keyId || !this.keySecret) {
        throw new Error('RazorPay credentials not configured');
      }

      const {
        amount,
        currency = 'INR',
        receipt,
        notes = {}
      } = orderData;

      // Validate amount (RazorPay expects amount in paise)
      const amountInPaise = Math.round(amount * 100);
      
      if (amountInPaise < 100) {
        throw new Error('Minimum amount should be ₹1.00');
      }

      const orderPayload = {
        amount: amountInPaise,
        currency,
        receipt,
        notes,
        payment_capture: 1 // Auto capture payment
      };

      const auth = Buffer.from(`${this.keyId}:${this.keySecret}`).toString('base64');
      
      const response = await axios.post(
        `${this.baseURL}/orders`,
        orderPayload,
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      return {
        success: true,
        data: {
          orderId: response.data.id,
          amount: response.data.amount,
          currency: response.data.currency,
          receipt: response.data.receipt,
          status: response.data.status,
          createdAt: response.data.created_at
        }
      };

    } catch (error) {
      console.error('RazorPay order creation error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.description || error.message,
        code: error.response?.data?.error?.code || 'RAZORPAY_ERROR'
      };
    }
  }

  /**
   * Verify payment signature
   */
  verifyPaymentSignature(paymentData) {
    try {
      if (!this.keySecret) {
        throw new Error('RazorPay key secret not configured');
      }

      const {
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature
      } = paymentData;

      if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
        return {
          success: false,
          error: 'Missing required payment verification parameters'
        };
      }

      // Create signature
      const body = razorpay_order_id + '|' + razorpay_payment_id;
      const expectedSignature = crypto
        .createHmac('sha256', this.keySecret)
        .update(body.toString())
        .digest('hex');

      const isSignatureValid = expectedSignature === razorpay_signature;

      return {
        success: isSignatureValid,
        data: {
          orderId: razorpay_order_id,
          paymentId: razorpay_payment_id,
          signature: razorpay_signature,
          verified: isSignatureValid
        },
        error: isSignatureValid ? null : 'Invalid payment signature'
      };

    } catch (error) {
      console.error('RazorPay signature verification error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get payment details from RazorPay
   */
  async getPaymentDetails(paymentId) {
    try {
      if (!this.keyId || !this.keySecret) {
        throw new Error('RazorPay credentials not configured');
      }

      const auth = Buffer.from(`${this.keyId}:${this.keySecret}`).toString('base64');
      
      const response = await axios.get(
        `${this.baseURL}/payments/${paymentId}`,
        {
          headers: {
            'Authorization': `Basic ${auth}`
          },
          timeout: 30000
        }
      );

      return {
        success: true,
        data: {
          paymentId: response.data.id,
          orderId: response.data.order_id,
          amount: response.data.amount,
          currency: response.data.currency,
          status: response.data.status,
          method: response.data.method,
          captured: response.data.captured,
          createdAt: response.data.created_at,
          email: response.data.email,
          contact: response.data.contact,
          notes: response.data.notes
        }
      };

    } catch (error) {
      console.error('RazorPay payment details error:', error.response?.data || error.message);
      
      return {
        success: false,
        error: error.response?.data?.error?.description || error.message,
        code: error.response?.data?.error?.code || 'RAZORPAY_ERROR'
      };
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(payload, signature) {
    try {
      if (!this.webhookSecret) {
        console.warn('RazorPay webhook secret not configured');
        return { success: false, error: 'Webhook secret not configured' };
      }

      const expectedSignature = crypto
        .createHmac('sha256', this.webhookSecret)
        .update(payload)
        .digest('hex');

      const isSignatureValid = expectedSignature === signature;

      return {
        success: isSignatureValid,
        error: isSignatureValid ? null : 'Invalid webhook signature'
      };

    } catch (error) {
      console.error('RazorPay webhook verification error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process webhook event
   */
  async processWebhookEvent(eventData) {
    try {
      const { event, payload } = eventData;
      
      switch (event) {
        case 'payment.captured':
          return await this.handlePaymentCaptured(payload.payment.entity);
          
        case 'payment.failed':
          return await this.handlePaymentFailed(payload.payment.entity);
          
        case 'order.paid':
          return await this.handleOrderPaid(payload.order.entity);
          
        default:
          console.log(`Unhandled RazorPay webhook event: ${event}`);
          return { success: true, message: 'Event acknowledged but not processed' };
      }

    } catch (error) {
      console.error('RazorPay webhook processing error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle payment captured webhook
   */
  async handlePaymentCaptured(paymentEntity) {
    try {
      const { id: paymentId, order_id: orderId, amount, status } = paymentEntity;

      // Update payment status in database
      await db.query(`
        UPDATE PaymentGatewayTransactions
        SET Status = 'SUCCESS',
            GatewayTransactionId = @paymentId,
            GatewayResponse = @response,
            UpdatedOn = GETDATE()
        WHERE GatewayOrderId = @orderId
      `, {
        paymentId,
        orderId,
        response: JSON.stringify(paymentEntity)
      });

      // Update main transaction status
      const transactionQuery = `
        SELECT pt.Id as TransactionId
        FROM PaymentGatewayTransactions pgt
        JOIN ParkingTransactions pt ON pgt.TransactionId = pt.Id
        WHERE pgt.GatewayOrderId = @orderId
      `;
      
      const transactionResult = await db.query(transactionQuery, { orderId });
      
      if (transactionResult.recordset.length > 0) {
        const transactionId = transactionResult.recordset[0].TransactionId;
        
        await db.query(`
          UPDATE ParkingTransactions
          SET Status = 'PAYMENT_COMPLETED',
              ModifiedOn = GETDATE()
          WHERE Id = @transactionId
        `, { transactionId });
      }

      return {
        success: true,
        message: 'Payment captured successfully',
        data: { paymentId, orderId, amount, status }
      };

    } catch (error) {
      console.error('Handle payment captured error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle payment failed webhook
   */
  async handlePaymentFailed(paymentEntity) {
    try {
      const { id: paymentId, order_id: orderId, error_code, error_description } = paymentEntity;

      // Update payment status in database
      await db.query(`
        UPDATE PaymentGatewayTransactions
        SET Status = 'FAILED',
            GatewayTransactionId = @paymentId,
            GatewayResponse = @response,
            UpdatedOn = GETDATE()
        WHERE GatewayOrderId = @orderId
      `, {
        paymentId,
        orderId,
        response: JSON.stringify(paymentEntity)
      });

      return {
        success: true,
        message: 'Payment failure processed',
        data: { paymentId, orderId, error_code, error_description }
      };

    } catch (error) {
      console.error('Handle payment failed error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Handle order paid webhook
   */
  async handleOrderPaid(orderEntity) {
    try {
      const { id: orderId, amount, status } = orderEntity;

      console.log(`RazorPay order paid: ${orderId}, Amount: ${amount}, Status: ${status}`);

      return {
        success: true,
        message: 'Order paid event processed',
        data: { orderId, amount, status }
      };

    } catch (error) {
      console.error('Handle order paid error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get plaza RazorPay configuration
   */
  async getPlazaConfiguration(plazaId) {
    try {
      const configQuery = `
        SELECT KeyId, KeySecret, WebhookSecret, IsActive
        FROM PlazaRazorPayConfiguration
        WHERE PlazaId = @plazaId AND IsActive = 1
      `;

      const result = await db.query(configQuery, { plazaId });
      
      if (result.recordset.length === 0) {
        return {
          success: false,
          error: 'RazorPay not configured for this plaza'
        };
      }

      const config = result.recordset[0];
      
      return {
        success: true,
        data: {
          keyId: config.KeyId,
          keySecret: config.KeySecret,
          webhookSecret: config.WebhookSecret,
          isActive: config.IsActive
        }
      };

    } catch (error) {
      console.error('Get plaza RazorPay configuration error:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new RazorPayService();
