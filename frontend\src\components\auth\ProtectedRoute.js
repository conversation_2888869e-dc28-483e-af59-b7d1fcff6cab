// frontend/src/components/auth/ProtectedRoute.js
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/authContext';
import UnauthorizedPage from './UnauthorizedPage';

/**
 * ProtectedRoute component that checks authentication and permissions
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authorized
 * @param {string[]} [props.requiredPermissions] - Array of permissions required to access the route
 * @param {string} [props.requiredModule] - Module name for permission check
 * @param {string[]} [props.allowedRoles] - Array of roles allowed to access the route
 * @param {number} [props.companyId] - Company ID to check access for
 * @param {number} [props.plazaId] - Plaza ID to check access for
 * @returns {React.ReactNode} - The protected component or redirect
 */
const ProtectedRoute = ({
  children,
  requiredPermissions = [],
  requiredModule,
  allowedRoles = [],
  companyId,
  plazaId
}) => {
  const { user, loading, hasPermission, hasRole, hasCompanyAccess, hasPlazaAccess } = useAuth();
  const location = useLocation();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access if specified
  if (allowedRoles.length > 0 && !allowedRoles.some(role => hasRole(role))) {
    return <UnauthorizedPage message="You don't have the required role to access this page" />;
  }

  // Check permission-based access if specified
  if (requiredModule && requiredPermissions.length > 0) {
    const hasRequiredPermission = requiredPermissions.some(
      permission => hasPermission(requiredModule, permission)
    );

    if (!hasRequiredPermission) {
      return <UnauthorizedPage message="You don't have the required permissions to access this page" />;
    }
  }

  // Check company access if specified
  if (companyId && !hasCompanyAccess(companyId)) {
    return <UnauthorizedPage message="You don't have access to this company" />;
  }

  // Check plaza access if specified
  if (plazaId && !hasPlazaAccess(plazaId)) {
    return <UnauthorizedPage message="You don't have access to this plaza" />;
  }

  // User is authenticated and authorized
  return children;
};

// Export both as default and named export for backward compatibility
export { ProtectedRoute };
export default ProtectedRoute;
